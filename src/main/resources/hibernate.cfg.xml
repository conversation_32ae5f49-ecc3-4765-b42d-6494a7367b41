<!DOCTYPE hibernate-configuration PUBLIC
	"-//Hibernate/Hibernate Configuration DTD 3.0//EN"
	"http://www.hibernate.org/dtd/hibernate-configuration-3.0.dtd">
<hibernate-configuration>
<!-- Configuration des paramètres de connexion à la base de données -->
<session-factory>
<property name="hibernate.connection.driver_class">com.mysql.cj.jdbc.Driver</property>
<property name="hibernate.connection.url">**************************************</property>
<property name="hibernate.connection.username">root</property>
<property name="hibernate.connection.password"></property>
<!-- Dialecte pour MySQL 8.x -->
<property name="hibernate.dialect">org.hibernate.dialect.MySQLDialect</property>
<!-- Activation de la génération automatique du schéma -->
<property name="hibernate.hbm2ddl.auto">update</property>
<!-- Entités à scanner pour Hibernate -->
<mapping class="model.User"/>
<mapping class="model.Trajet"/>
<mapping class="model.Station"/>
<mapping class="model.Voyage"/>
<mapping class="model.Reservation"/>
<mapping class="model.Promotion"/>

</session-factory>
</hibernate-configuration>