package filter;

import java.io.IOException;
import jakarta.servlet.Filter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.FilterConfig;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.annotation.WebFilter;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

import model.User;

/**
 * Filter for authentication and authorization
 * Protects secured pages and redirects unauthenticated users to login
 */
@WebFilter("/*")
public class AuthenticationFilter implements Filter {

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        // Initialization code if needed
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, Filter<PERSON>hain chain)
            throws IOException, ServletException {
        
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        
        String requestURI = httpRequest.getRequestURI();
        String contextPath = httpRequest.getContextPath();
        
        // Remove context path from URI
        String path = requestURI.substring(contextPath.length());
        
        // Allow access to public resources
        if (isPublicResource(path)) {
            chain.doFilter(request, response);
            return;
        }
        
        // Check if user is authenticated
        HttpSession session = httpRequest.getSession(false);
        User user = null;
        if (session != null) {
            user = (User) session.getAttribute("user");
        }
        
        // If not authenticated, redirect to login
        if (user == null) {
            httpResponse.sendRedirect(contextPath + "/login");
            return;
        }
        
        // Check admin access
        if (isAdminResource(path) && !"admin".equals(user.getRole())) {
            httpResponse.sendError(HttpServletResponse.SC_FORBIDDEN, "Access denied");
            return;
        }
        
        // User is authenticated and authorized, continue
        chain.doFilter(request, response);
    }
    
    /**
     * Check if the resource is public (doesn't require authentication)
     */
    private boolean isPublicResource(String path) {
        return path.equals("/") ||
               path.equals("/login") ||
               path.equals("/login.jsp") ||
               path.equals("/register") ||
               path.equals("/register.jsp") ||
               path.equals("/index.jsp") ||
               path.startsWith("/css/") ||
               path.startsWith("/js/") ||
               path.startsWith("/images/") ||
               path.startsWith("/static/") ||
               path.startsWith("/error/") ||
               path.equals("/diagnostic") ||
               path.equals("/diagnostic.jsp") ||
               path.equals("/database-test");
    }
    
    /**
     * Check if the resource requires admin privileges
     */
    private boolean isAdminResource(String path) {
        return path.startsWith("/admin/") ||
               path.startsWith("/dashboard/") ||
               path.contains("admin");
    }

    @Override
    public void destroy() {
        // Cleanup code if needed
    }
}
