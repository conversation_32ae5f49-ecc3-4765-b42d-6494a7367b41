package controller;

import dao.*;
import model.*;
import util.HibernateUtil;
import org.hibernate.SessionFactory;

import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.time.LocalDate;
import java.util.List;

@WebServlet("/test-search")
public class TestSearchServlet extends HttpServlet {
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        response.setContentType("text/html;charset=UTF-8");
        PrintWriter out = response.getWriter();
        
        try {
            out.println("<html><head><title>Test de Recherche</title></head><body>");
            out.println("<h1>Test de Recherche de Trajets</h1>");
            
            SessionFactory sessionFactory = HibernateUtil.getSessionFactory();
            TrajetDAO trajetDAO = new TrajetDAO(sessionFactory);
            StationDAO stationDAO = new StationDAO(sessionFactory);
            VoyageDAO voyageDAO = new VoyageDAO(sessionFactory);
            
            // Test 1: Recherche par villes
            out.println("<h2>Test 1: Recherche Paris -> Lyon</h2>");
            List<Trajet> trajets = trajetDAO.findTrajets("Gare de Paris Nord", "Gare de Lyon", LocalDate.now());
            out.println("<p>Trajets trouvés: " + trajets.size() + "</p>");
            for (Trajet trajet : trajets) {
                out.println("<p>- " + trajet.getDepartStation().getName() + " → " + trajet.getArrivalStation().getName() + "</p>");
            }
            
            // Test 2: Recherche par noms de villes
            out.println("<h2>Test 2: Recherche par noms de villes</h2>");
            List<Station> stations = stationDAO.findAll();
            out.println("<p>Stations disponibles:</p>");
            for (Station station : stations) {
                out.println("<p>- " + station.getName() + " (" + station.getCity() + ")</p>");
            }
            
            // Test 3: Tous les voyages
            out.println("<h2>Test 3: Tous les voyages</h2>");
            List<Voyage> voyages = voyageDAO.findAll();
            out.println("<p>Voyages disponibles: " + voyages.size() + "</p>");
            for (Voyage voyage : voyages) {
                out.println("<p>- " + voyage.getTrajet().getDepartStation().getName() + " → " + 
                           voyage.getTrajet().getArrivalStation().getName() + " - " + 
                           voyage.getPrix() + "€ - " + voyage.getHeureDepartFormatted() + "</p>");
            }
            
            // Test 4: Recherche avec différentes variantes
            out.println("<h2>Test 4: Tests de recherche avec variantes</h2>");
            
            String[] departsTest = {"Paris", "Gare de Paris Nord", "paris"};
            String[] arrivesTest = {"Lyon", "Gare de Lyon", "lyon"};
            
            for (String depart : departsTest) {
                for (String arrive : arrivesTest) {
                    List<Trajet> resultats = trajetDAO.findTrajets(depart, arrive, LocalDate.now());
                    out.println("<p>Recherche '" + depart + "' → '" + arrive + "': " + resultats.size() + " résultats</p>");
                }
            }
            
            out.println("<hr>");
            out.println("<p><a href='recherche.jsp'>Aller à la page de recherche</a></p>");
            out.println("<p><a href='diagnostic'>Diagnostic complet</a></p>");
            
        } catch (Exception e) {
            out.println("<h2>Erreur</h2>");
            out.println("<p>Erreur: " + e.getMessage() + "</p>");
            out.println("<pre>");
            e.printStackTrace(out);
            out.println("</pre>");
        }
        
        out.println("</body></html>");
    }
}
