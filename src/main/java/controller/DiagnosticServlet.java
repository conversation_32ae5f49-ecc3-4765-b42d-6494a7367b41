package controller;

import dao.*;
import model.*;
import util.HibernateUtil;
import org.hibernate.SessionFactory;

import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.List;

@WebServlet("/diagnostic")
public class DiagnosticServlet extends HttpServlet {

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        try {
            SessionFactory sessionFactory = HibernateUtil.getSessionFactory();
            request.setAttribute("sessionFactoryStatus", "✅ SessionFactory créée avec succès");

            // Test des DAO
            UserDAO userDAO = new UserDAO(sessionFactory);
            StationDAO stationDAO = new StationDAO(sessionFactory);
            TrajetDAO trajetDAO = new TrajetDAO(sessionFactory);
            VoyageDAO voyageDAO = new VoyageDAO(sessionFactory);
            ReservationDAO reservationDAO = new ReservationDAO(sessionFactory);
            PromotionDAO promotionDAO = new PromotionDAO(sessionFactory);

            request.setAttribute("daoStatus", "✅ Tous les DAO créés avec succès");

            // Vérification des données
            List<User> users = userDAO.findAll();
            request.setAttribute("users", users);

            List<Station> stations = stationDAO.findAll();
            request.setAttribute("stations", stations);

            List<Trajet> trajets = trajetDAO.findAll();
            request.setAttribute("trajets", trajets);

            List<Voyage> voyages = voyageDAO.findAll();
            request.setAttribute("voyages", voyages);

            List<Promotion> promotions = promotionDAO.findAll();
            request.setAttribute("promotions", promotions);

            List<Reservation> reservations = reservationDAO.findAll();
            request.setAttribute("reservations", reservations);

            request.setAttribute("diagnosticSuccess", true);

        } catch (Exception e) {
            request.setAttribute("diagnosticSuccess", false);
            request.setAttribute("errorMessage", e.getMessage());
            request.setAttribute("stackTrace", e);
        }

        request.getRequestDispatcher("/diagnostic.jsp").forward(request, response);
    }
}
