package controller;

import dao.*;
import model.*;
import util.HibernateUtil;
import org.hibernate.SessionFactory;

import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.List;

@WebServlet("/diagnostic")
public class DiagnosticServlet extends HttpServlet {

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        response.setContentType("text/html;charset=UTF-8");
        PrintWriter out = response.getWriter();

        try {
            out.println("<html><head><title>Diagnostic Base de Données</title></head><body>");
            out.println("<h1>Diagnostic de la Base de Données</h1>");

            SessionFactory sessionFactory = HibernateUtil.getSessionFactory();
            out.println("<p>✅ SessionFactory créée avec succès</p>");

            // Test des DAO
            UserDAO userDAO = new UserDAO(sessionFactory);
            StationDAO stationDAO = new StationDAO(sessionFactory);
            TrajetDAO trajetDAO = new TrajetDAO(sessionFactory);
            VoyageDAO voyageDAO = new VoyageDAO(sessionFactory);
            ReservationDAO reservationDAO = new ReservationDAO(sessionFactory);
            PromotionDAO promotionDAO = new PromotionDAO(sessionFactory);

            out.println("<p>✅ Tous les DAO créés avec succès</p>");

            // Vérification des données
            List<User> users = userDAO.findAll();
            out.println("<h2>Utilisateurs (" + users.size() + ")</h2>");
            for (User user : users) {
                out.println("<p>- " + user.getUsername() + " (" + user.getRole() + ") - Points: " + user.getLoyaltyPoints() + "</p>");
            }

            List<Station> stations = stationDAO.findAll();
            out.println("<h2>Stations (" + stations.size() + ")</h2>");
            for (Station station : stations) {
                out.println("<p>- " + station.getName() + " (" + station.getCity() + ")</p>");
            }

            List<Trajet> trajets = trajetDAO.findAll();
            out.println("<h2>Trajets (" + trajets.size() + ")</h2>");
            for (Trajet trajet : trajets) {
                out.println("<p>- " + trajet.getDepartStation().getName() + " → " + trajet.getArrivalStation().getName() + "</p>");
            }

            List<Voyage> voyages = voyageDAO.findAll();
            out.println("<h2>Voyages (" + voyages.size() + ")</h2>");
            for (Voyage voyage : voyages) {
                out.println("<p>- " + voyage.getTrajet().getDepartStation().getName() + " → " +
                           voyage.getTrajet().getArrivalStation().getName() + " - " +
                           voyage.getPrix() + "€ - Places: " + voyage.getPlacesDisponibles() + "</p>");
            }

            List<Promotion> promotions = promotionDAO.findAll();
            out.println("<h2>Promotions (" + promotions.size() + ")</h2>");
            for (Promotion promotion : promotions) {
                out.println("<p>- " + promotion.getCode() + " (" + promotion.getDiscountPercentage() + "%) - " + promotion.getDescription() + "</p>");
            }

            List<Reservation> reservations = reservationDAO.findAll();
            out.println("<h2>Réservations (" + reservations.size() + ")</h2>");
            for (Reservation reservation : reservations) {
                out.println("<p>- " + reservation.getUser().getUsername() + " - " +
                           reservation.getVoyage().getTrajet().getDepartStation().getName() + " → " +
                           reservation.getVoyage().getTrajet().getArrivalStation().getName() + " (" + reservation.getClasse() + ")</p>");
            }

            out.println("<hr>");
            out.println("<p><a href='init-data'>Réinitialiser la base de données</a></p>");
            out.println("<p><a href='login.jsp'>Retour à la connexion</a></p>");

        } catch (Exception e) {
            out.println("<h2>Erreur</h2>");
            out.println("<p>Erreur: " + e.getMessage() + "</p>");
            out.println("<pre>");
            e.printStackTrace(out);
            out.println("</pre>");
        }

        out.println("</body></html>");
    }
}
