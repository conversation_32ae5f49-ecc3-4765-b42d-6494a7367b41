package controller;

import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import model.Reservation;
import model.User;
import util.HibernateUtil;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.logging.Logger;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.font.PDType1Font;


import dao.ReservationDAO;

/**
 * Servlet pour télécharger les billets de réservation en format PDF
 */
@WebServlet("/download-ticket")
public class DownloadTicketServlet extends HttpServlet {
    private static final Logger LOGGER = Logger.getLogger(DownloadTicketServlet.class.getName());
    private ReservationDAO reservationDAO;

    @Override
    public void init() throws ServletException {
        reservationDAO = new ReservationDAO(HibernateUtil.getSessionFactory());
    }

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        HttpSession session = request.getSession(false);
        if (session == null || session.getAttribute("user") == null) {
            LOGGER.warning("Utilisateur non connecté lors de l'accès à /download-ticket");
            response.sendRedirect(request.getContextPath() + "/login");
            return;
        }

        String reservationId = request.getParameter("reservationId");
        if (reservationId == null || reservationId.trim().isEmpty()) {
            LOGGER.warning("ID de réservation manquant lors de l'accès à /download-ticket");
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "ID de réservation manquant");
            return;
        }

        User user = (User) session.getAttribute("user");

        try {
            Reservation reservation = reservationDAO.findById(Long.parseLong(reservationId));
            if (reservation == null) {
                LOGGER.warning("Réservation non trouvée pour l'ID: " + reservationId);
                response.sendError(HttpServletResponse.SC_NOT_FOUND, "Réservation non trouvée");
                return;
            }

            // Vérifier que l'utilisateur est propriétaire de la réservation ou admin
            if (!user.getRole().equals("admin") && !reservation.getUser().getId().equals(user.getId())) {
                LOGGER.warning("Utilisateur non autorisé à télécharger la réservation ID: " + reservationId);
                response.sendError(HttpServletResponse.SC_FORBIDDEN, "Accès non autorisé");
                return;
            }

            LOGGER.info("Génération du PDF pour la réservation ID: " + reservationId);
            generatePDF(reservation, response);

        } catch (NumberFormatException e) {
            LOGGER.severe("Erreur de conversion de reservationId en Long: " + reservationId + " - " + e.getMessage());
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "ID de réservation invalide");
        } catch (Exception e) {
            LOGGER.severe("Erreur lors de la génération du PDF: " + e.getMessage());
            response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "Erreur lors de la génération du PDF");
        }
    }

    private void generatePDF(Reservation reservation, HttpServletResponse response) throws IOException {
        // Définir le type de contenu et l'en-tête pour le téléchargement
        response.setContentType("application/pdf");
        response.setHeader("Content-Disposition", "attachment; filename=billet_" + reservation.getId() + ".pdf");

        LOGGER.info("Début de la génération du PDF pour la réservation ID: " + reservation.getId());

        // Créer un document PDF avec PDFBox
        try (PDDocument document = new PDDocument()) {
            PDPage page = new PDPage();
            document.addPage(page);

            try (PDPageContentStream contentStream = new PDPageContentStream(document, page)) {
                // Configuration des polices
                PDType1Font titleFont = PDType1Font.HELVETICA_BOLD;
                PDType1Font regularFont = PDType1Font.HELVETICA;
                PDType1Font boldFont = PDType1Font.HELVETICA_BOLD;

                float margin = 50;
                float yPosition = 750;
                float lineHeight = 20;

                // Titre principal
                contentStream.beginText();
                contentStream.setFont(titleFont, 24);
                contentStream.newLineAtOffset(margin, yPosition);
                contentStream.showText("🚂 JEE TRAINING - BILLET DE VOYAGE");
                contentStream.endText();

                yPosition -= 40;

                // Ligne de séparation
                contentStream.moveTo(margin, yPosition);
                contentStream.lineTo(550, yPosition);
                contentStream.stroke();

                yPosition -= 30;

                // Informations de la réservation
                contentStream.beginText();
                contentStream.setFont(boldFont, 16);
                contentStream.newLineAtOffset(margin, yPosition);
                contentStream.showText("INFORMATIONS DE RÉSERVATION");
                contentStream.endText();

                yPosition -= 25;

                // ID de réservation
                contentStream.beginText();
                contentStream.setFont(boldFont, 12);
                contentStream.newLineAtOffset(margin, yPosition);
                contentStream.showText("Numéro de réservation: ");
                contentStream.setFont(regularFont, 12);
                contentStream.showText("#" + reservation.getId());
                contentStream.endText();

                yPosition -= lineHeight;

                // Date de réservation
                contentStream.beginText();
                contentStream.setFont(boldFont, 12);
                contentStream.newLineAtOffset(margin, yPosition);
                contentStream.showText("Date de réservation: ");
                contentStream.setFont(regularFont, 12);
                String dateReservation = reservation.getDateReservation() != null ?
                    reservation.getDateReservation().format(DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm")) : "N/A";
                contentStream.showText(dateReservation);
                contentStream.endText();

                yPosition -= lineHeight;

                // État de la réservation
                contentStream.beginText();
                contentStream.setFont(boldFont, 12);
                contentStream.newLineAtOffset(margin, yPosition);
                contentStream.showText("État: ");
                contentStream.setFont(regularFont, 12);
                contentStream.showText(reservation.getEtat() != null ? reservation.getEtat() : "N/A");
                contentStream.endText();

                yPosition -= 30;

                // Informations du voyage
                contentStream.beginText();
                contentStream.setFont(boldFont, 16);
                contentStream.newLineAtOffset(margin, yPosition);
                contentStream.showText("DÉTAILS DU VOYAGE");
                contentStream.endText();

                yPosition -= 25;

                if (reservation.getVoyage() != null) {
                    // Trajet
                    if (reservation.getVoyage().getTrajet() != null) {
                        contentStream.beginText();
                        contentStream.setFont(boldFont, 12);
                        contentStream.newLineAtOffset(margin, yPosition);
                        contentStream.showText("Trajet: ");
                        contentStream.setFont(regularFont, 12);
                        String departStation = reservation.getVoyage().getTrajet().getDepartStation() != null ?
                            reservation.getVoyage().getTrajet().getDepartStation().getName() : "N/A";
                        String arrivalStation = reservation.getVoyage().getTrajet().getArrivalStation() != null ?
                            reservation.getVoyage().getTrajet().getArrivalStation().getName() : "N/A";
                        contentStream.showText(departStation + " → " + arrivalStation);
                        contentStream.endText();

                        yPosition -= lineHeight;

                        // Villes
                        contentStream.beginText();
                        contentStream.setFont(boldFont, 12);
                        contentStream.newLineAtOffset(margin, yPosition);
                        contentStream.showText("Villes: ");
                        contentStream.setFont(regularFont, 12);
                        String departCity = reservation.getVoyage().getTrajet().getDepartStation() != null ?
                            reservation.getVoyage().getTrajet().getDepartStation().getCity() : "N/A";
                        String arrivalCity = reservation.getVoyage().getTrajet().getArrivalStation() != null ?
                            reservation.getVoyage().getTrajet().getArrivalStation().getCity() : "N/A";
                        contentStream.showText(departCity + " → " + arrivalCity);
                        contentStream.endText();

                        yPosition -= lineHeight;
                    }

                    // Heure de départ
                    contentStream.beginText();
                    contentStream.setFont(boldFont, 12);
                    contentStream.newLineAtOffset(margin, yPosition);
                    contentStream.showText("Heure de départ: ");
                    contentStream.setFont(regularFont, 12);
                    String heureDepart = reservation.getVoyage().getHeureDepart() != null ?
                        reservation.getVoyage().getHeureDepart().format(DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm")) : "N/A";
                    contentStream.showText(heureDepart);
                    contentStream.endText();

                    yPosition -= lineHeight;

                    // Heure d'arrivée
                    contentStream.beginText();
                    contentStream.setFont(boldFont, 12);
                    contentStream.newLineAtOffset(margin, yPosition);
                    contentStream.showText("Heure d'arrivée: ");
                    contentStream.setFont(regularFont, 12);
                    String heureArrivee = reservation.getVoyage().getHeureArrivee() != null ?
                        reservation.getVoyage().getHeureArrivee().format(DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm")) : "N/A";
                    contentStream.showText(heureArrivee);
                    contentStream.endText();

                    yPosition -= lineHeight;

                    // Prix
                    contentStream.beginText();
                    contentStream.setFont(boldFont, 12);
                    contentStream.newLineAtOffset(margin, yPosition);
                    contentStream.showText("Prix: ");
                    contentStream.setFont(regularFont, 12);
                    contentStream.showText(reservation.getVoyage().getPrix() + " €");
                    contentStream.endText();

                    yPosition -= 30;
                }

                // Informations du passager
                contentStream.beginText();
                contentStream.setFont(boldFont, 16);
                contentStream.newLineAtOffset(margin, yPosition);
                contentStream.showText("INFORMATIONS PASSAGER");
                contentStream.endText();

                yPosition -= 25;

                if (reservation.getUser() != null) {
                    // Nom d'utilisateur
                    contentStream.beginText();
                    contentStream.setFont(boldFont, 12);
                    contentStream.newLineAtOffset(margin, yPosition);
                    contentStream.showText("Nom: ");
                    contentStream.setFont(regularFont, 12);
                    contentStream.showText(reservation.getUser().getUsername());
                    contentStream.endText();

                    yPosition -= lineHeight;

                    // Email
                    contentStream.beginText();
                    contentStream.setFont(boldFont, 12);
                    contentStream.newLineAtOffset(margin, yPosition);
                    contentStream.showText("Email: ");
                    contentStream.setFont(regularFont, 12);
                    contentStream.showText(reservation.getUser().getEmail() != null ? reservation.getUser().getEmail() : "N/A");
                    contentStream.endText();

                    yPosition -= lineHeight;
                }

                // Classe
                contentStream.beginText();
                contentStream.setFont(boldFont, 12);
                contentStream.newLineAtOffset(margin, yPosition);
                contentStream.showText("Classe: ");
                contentStream.setFont(regularFont, 12);
                contentStream.showText(reservation.getClasse() != null ? reservation.getClasse() : "N/A");
                contentStream.endText();

                yPosition -= lineHeight;

                // Préférences
                if (reservation.getPreferences() != null && !reservation.getPreferences().trim().isEmpty()) {
                    contentStream.beginText();
                    contentStream.setFont(boldFont, 12);
                    contentStream.newLineAtOffset(margin, yPosition);
                    contentStream.showText("Préférences: ");
                    contentStream.setFont(regularFont, 12);
                    contentStream.showText(reservation.getPreferences());
                    contentStream.endText();

                    yPosition -= lineHeight;
                }

                yPosition -= 30;

                // Pied de page
                contentStream.moveTo(margin, yPosition);
                contentStream.lineTo(550, yPosition);
                contentStream.stroke();

                yPosition -= 20;

                contentStream.beginText();
                contentStream.setFont(regularFont, 10);
                contentStream.newLineAtOffset(margin, yPosition);
                contentStream.showText("Généré le " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("dd/MM/yyyy à HH:mm")));
                contentStream.endText();

                contentStream.beginText();
                contentStream.setFont(regularFont, 10);
                contentStream.newLineAtOffset(400, yPosition);
                contentStream.showText("JEE Training © 2024");
                contentStream.endText();
            }

            // Écrire le document dans la réponse
            LOGGER.info("Écriture du PDF dans la réponse pour la réservation ID: " + reservation.getId());
            document.save(response.getOutputStream());
            response.getOutputStream().flush();
            LOGGER.info("PDF généré et envoyé avec succès pour la réservation ID: " + reservation.getId());

        } catch (Exception e) {
            LOGGER.severe("Erreur lors de l'écriture du PDF: " + e.getMessage());
            throw new IOException("Erreur lors de l'écriture du PDF", e);
        }
    }
}
