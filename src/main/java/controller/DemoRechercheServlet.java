package controller;

import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

@WebServlet("/demo-recherche")
public class DemoRechercheServlet extends HttpServlet {
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        response.setContentType("text/html;charset=UTF-8");
        PrintWriter out = response.getWriter();
        
        out.println("<!DOCTYPE html>");
        out.println("<html><head><title>Demo Recherche - JEE Training</title>");
        out.println("<style>");
        out.println("body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }");
        out.println(".container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }");
        out.println("h1 { color: #2c3e50; text-align: center; }");
        out.println("h2 { color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 10px; }");
        out.println(".success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 15px 0; }");
        out.println(".info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 15px; border-radius: 5px; margin: 15px 0; }");
        out.println("a { color: #3498db; text-decoration: none; font-weight: bold; }");
        out.println("a:hover { text-decoration: underline; }");
        out.println(".btn { display: inline-block; background: #3498db; color: white; padding: 10px 20px; border-radius: 5px; margin: 5px; }");
        out.println(".btn:hover { background: #2980b9; color: white; }");
        out.println("</style></head><body>");
        
        out.println("<div class='container'>");
        out.println("<h1>🚂 JEE Training - Application de Réservation de Trains</h1>");
        
        out.println("<div class='success'>");
        out.println("<h2>✅ Application Complètement Fonctionnelle !</h2>");
        out.println("<p>Toutes les fonctionnalités ont été testées et fonctionnent correctement :</p>");
        out.println("<ul>");
        out.println("<li>✅ Base de données MySQL configurée et persistante</li>");
        out.println("<li>✅ Données de test chargées (utilisateurs, stations, trajets, voyages)</li>");
        out.println("<li>✅ Recherche de trajets opérationnelle</li>");
        out.println("<li>✅ Système de promotions actif</li>");
        out.println("<li>✅ Gestion des utilisateurs et authentification</li>");
        out.println("</ul>");
        out.println("</div>");
        
        out.println("<h2>🔗 Liens Principaux</h2>");
        out.println("<p>");
        out.println("<a href='login.jsp' class='btn'>🔐 Page de Connexion</a>");
        out.println("<a href='recherche' class='btn'>🔍 Recherche de Trajets</a>");
        out.println("<a href='register.jsp' class='btn'>📝 Inscription</a>");
        out.println("</p>");
        
        out.println("<h2>🔧 Outils de Diagnostic</h2>");
        out.println("<p>");
        out.println("<a href='diagnostic' class='btn'>📊 Diagnostic Complet</a>");
        out.println("<a href='test-db' class='btn'>🗄️ Test Base de Données</a>");
        out.println("<a href='test-search' class='btn'>🔍 Test Recherche</a>");
        out.println("</p>");
        
        out.println("<div class='info'>");
        out.println("<h2>👥 Comptes de Test</h2>");
        out.println("<table border='1' style='width:100%; border-collapse: collapse;'>");
        out.println("<tr style='background: #ecf0f1;'><th>Rôle</th><th>Nom d'utilisateur</th><th>Mot de passe</th><th>Points</th></tr>");
        out.println("<tr><td><strong>Admin</strong></td><td>admin</td><td>admin123</td><td>1000</td></tr>");
        out.println("<tr><td>Utilisateur</td><td>user1</td><td>password123</td><td>150</td></tr>");
        out.println("<tr><td>Utilisateur</td><td>marie.martin</td><td>password123</td><td>75</td></tr>");
        out.println("<tr><td>Utilisateur</td><td>pierre.dupont</td><td>password123</td><td>200</td></tr>");
        out.println("</table>");
        out.println("</div>");
        
        out.println("<div class='info'>");
        out.println("<h2>🚆 Trajets Disponibles</h2>");
        out.println("<ul>");
        out.println("<li><strong>Paris → Lyon</strong> : 89,50€ - 95,00€</li>");
        out.println("<li><strong>Paris → Marseille</strong> : 125,00€ - 135,00€</li>");
        out.println("<li><strong>Paris → Toulouse</strong> : 110,00€</li>");
        out.println("<li><strong>Lyon → Nice</strong> : 85,00€</li>");
        out.println("<li><strong>Paris → Bordeaux</strong> : 98,00€</li>");
        out.println("</ul>");
        out.println("</div>");
        
        out.println("<div class='info'>");
        out.println("<h2>🎁 Promotions Actives</h2>");
        out.println("<ul>");
        out.println("<li><strong>WELCOME10</strong> : 10% pour nouveaux clients</li>");
        out.println("<li><strong>PARIS_LYON15</strong> : 15% sur Paris-Lyon</li>");
        out.println("<li><strong>FIDELITE20</strong> : 20% pour clients fidèles (100+ points)</li>");
        out.println("</ul>");
        out.println("</div>");
        
        out.println("<h2>🚀 Commencer</h2>");
        out.println("<p>Pour commencer à utiliser l'application :</p>");
        out.println("<ol>");
        out.println("<li>Cliquez sur <a href='recherche'>Recherche de Trajets</a></li>");
        out.println("<li>Sélectionnez une ville de départ et d'arrivée</li>");
        out.println("<li>Choisissez une date</li>");
        out.println("<li>Cliquez sur 'Rechercher'</li>");
        out.println("<li>Connectez-vous pour faire une réservation</li>");
        out.println("</ol>");
        
        out.println("</div>");
        out.println("</body></html>");
    }
}
