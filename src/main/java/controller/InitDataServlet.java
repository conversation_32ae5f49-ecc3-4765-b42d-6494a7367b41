package controller;

import dao.*;
import model.*;
import util.HibernateUtil;
import org.hibernate.SessionFactory;
import org.mindrot.jbcrypt.BCrypt;

import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;

@WebServlet("/init-data")
public class InitDataServlet extends HttpServlet {

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        try {
            SessionFactory sessionFactory = HibernateUtil.getSessionFactory();

            // Initialisation des DAO
            UserDAO userDAO = new UserDAO(sessionFactory);
            StationDAO stationDAO = new StationDAO(sessionFactory);
            TrajetDAO trajetDAO = new TrajetDAO(sessionFactory);
            VoyageDAO voyageDAO = new VoyageDAO(sessionFactory);
            ReservationDAO reservationDAO = new ReservationDAO(sessionFactory);
            PromotionDAO promotionDAO = new PromotionDAO(sessionFactory);

            // Vérifier si les données existent déjà
            if (!userDAO.findAll().isEmpty()) {
                response.getWriter().println("Les données existent déjà dans la base de données.");
                return;
            }

            // 1. Créer des utilisateurs
            createUsers(userDAO);

            // 2. Créer des stations
            createStations(stationDAO);

            // 3. Créer des trajets
            createTrajets(trajetDAO, stationDAO);

            // 4. Créer des voyages
            createVoyages(voyageDAO, trajetDAO);

            // 5. Créer des promotions
            createPromotions(promotionDAO, trajetDAO);

            // 6. Créer quelques réservations
            createReservations(reservationDAO, userDAO, voyageDAO);

            response.setContentType("text/html;charset=UTF-8");
            response.getWriter().println("<h1>Base de données initialisée avec succès !</h1>");
            response.getWriter().println("<p><a href='login.jsp'>Aller à la page de connexion</a></p>");
            response.getWriter().println("<h3>Comptes de test :</h3>");
            response.getWriter().println("<ul>");
            response.getWriter().println("<li><strong>Admin :</strong> admin / admin123</li>");
            response.getWriter().println("<li><strong>Utilisateur :</strong> user1 / password123</li>");
            response.getWriter().println("<li><strong>Utilisateur :</strong> marie.martin / password123</li>");
            response.getWriter().println("</ul>");

        } catch (Exception e) {
            e.printStackTrace();
            response.getWriter().println("Erreur lors de l'initialisation : " + e.getMessage());
        }
    }

    private void createUsers(UserDAO userDAO) {
        // Administrateur
        User admin = new User("admin", BCrypt.hashpw("admin123", BCrypt.gensalt()),
                             "<EMAIL>", "admin", 1000);
        userDAO.save(admin);

        // Utilisateurs normaux
        User user1 = new User("user1", BCrypt.hashpw("password123", BCrypt.gensalt()),
                             "<EMAIL>", "user", 150);
        userDAO.save(user1);

        User user2 = new User("marie.martin", BCrypt.hashpw("password123", BCrypt.gensalt()),
                             "<EMAIL>", "user", 75);
        userDAO.save(user2);

        User user3 = new User("pierre.dupont", BCrypt.hashpw("password123", BCrypt.gensalt()),
                             "<EMAIL>", "user", 200);
        userDAO.save(user3);
    }

    private void createStations(StationDAO stationDAO) {
        stationDAO.save(new Station("Gare de Paris Nord", "Paris"));
        stationDAO.save(new Station("Gare de Lyon", "Lyon"));
        stationDAO.save(new Station("Gare de Marseille", "Marseille"));
        stationDAO.save(new Station("Gare de Toulouse", "Toulouse"));
        stationDAO.save(new Station("Gare de Nice", "Nice"));
        stationDAO.save(new Station("Gare de Bordeaux", "Bordeaux"));
        stationDAO.save(new Station("Gare de Strasbourg", "Strasbourg"));
        stationDAO.save(new Station("Gare de Lille", "Lille"));
        stationDAO.save(new Station("Gare de Nantes", "Nantes"));
        stationDAO.save(new Station("Gare de Montpellier", "Montpellier"));
    }

    private void createTrajets(TrajetDAO trajetDAO, StationDAO stationDAO) {
        var stations = stationDAO.findAll();

        // Paris -> Lyon
        trajetDAO.save(new Trajet(stations.get(0), stations.get(1)));
        // Lyon -> Marseille
        trajetDAO.save(new Trajet(stations.get(1), stations.get(2)));
        // Paris -> Marseille
        trajetDAO.save(new Trajet(stations.get(0), stations.get(2)));
        // Paris -> Toulouse
        trajetDAO.save(new Trajet(stations.get(0), stations.get(3)));
        // Lyon -> Nice
        trajetDAO.save(new Trajet(stations.get(1), stations.get(4)));
        // Paris -> Bordeaux
        trajetDAO.save(new Trajet(stations.get(0), stations.get(5)));
        // Paris -> Strasbourg
        trajetDAO.save(new Trajet(stations.get(0), stations.get(6)));
        // Paris -> Lille
        trajetDAO.save(new Trajet(stations.get(0), stations.get(7)));
    }

    private void createVoyages(VoyageDAO voyageDAO, TrajetDAO trajetDAO) {
        var trajets = trajetDAO.findAll();
        LocalDateTime now = LocalDateTime.now();

        // Voyages pour Paris -> Lyon
        voyageDAO.save(new Voyage(trajets.get(0),
            now.plusDays(1).withHour(8).withMinute(0),
            now.plusDays(1).withHour(10).withMinute(30),
            89.50, 120));
        voyageDAO.save(new Voyage(trajets.get(0),
            now.plusDays(1).withHour(14).withMinute(0),
            now.plusDays(1).withHour(16).withMinute(30),
            95.00, 100));

        // Voyages pour Lyon -> Marseille
        voyageDAO.save(new Voyage(trajets.get(1),
            now.plusDays(2).withHour(9).withMinute(15),
            now.plusDays(2).withHour(10).withMinute(45),
            45.00, 80));

        // Voyages pour Paris -> Marseille
        voyageDAO.save(new Voyage(trajets.get(2),
            now.plusDays(1).withHour(7).withMinute(30),
            now.plusDays(1).withHour(10).withMinute(45),
            125.00, 150));
        voyageDAO.save(new Voyage(trajets.get(2),
            now.plusDays(3).withHour(16).withMinute(0),
            now.plusDays(3).withHour(19).withMinute(15),
            135.00, 140));

        // Voyages pour Paris -> Toulouse
        voyageDAO.save(new Voyage(trajets.get(3),
            now.plusDays(2).withHour(11).withMinute(0),
            now.plusDays(2).withHour(15).withMinute(30),
            110.00, 90));

        // Voyages pour Lyon -> Nice
        voyageDAO.save(new Voyage(trajets.get(4),
            now.plusDays(4).withHour(13).withMinute(30),
            now.plusDays(4).withHour(18).withMinute(0),
            85.00, 70));

        // Voyages pour Paris -> Bordeaux
        voyageDAO.save(new Voyage(trajets.get(5),
            now.plusDays(3).withHour(10).withMinute(15),
            now.plusDays(3).withHour(13).withMinute(45),
            98.00, 110));
    }

    private void createPromotions(PromotionDAO promotionDAO, TrajetDAO trajetDAO) {
        var trajets = trajetDAO.findAll();
        LocalDate today = LocalDate.now();

        // Promotion générale
        promotionDAO.save(new Promotion("WELCOME10",
            "10% de réduction pour les nouveaux clients",
            10.0, today, today.plusDays(30), null, 0));

        // Promotion pour Paris -> Lyon
        promotionDAO.save(new Promotion("PARIS_LYON15",
            "15% de réduction sur Paris-Lyon",
            15.0, today, today.plusDays(15), trajets.get(0), 50));

        // Promotion fidélité
        promotionDAO.save(new Promotion("FIDELITE20",
            "20% de réduction pour les clients fidèles",
            20.0, today, today.plusDays(60), null, 100));
    }

    private void createReservations(ReservationDAO reservationDAO, UserDAO userDAO, VoyageDAO voyageDAO) {
        var users = userDAO.findAll();
        var voyages = voyageDAO.findAll();

        if (users.size() >= 2 && voyages.size() >= 3) {
            // Réservation pour user1
            reservationDAO.save(new Reservation(users.get(1), voyages.get(0), "Première", "Fenêtre"));

            // Réservation pour marie.martin
            reservationDAO.save(new Reservation(users.get(2), voyages.get(1), "Seconde", "Couloir"));

            // Réservation pour pierre.dupont
            if (users.size() >= 4) {
                reservationDAO.save(new Reservation(users.get(3), voyages.get(2), "Première", "Fenêtre"));
            }
        }
    }
}
