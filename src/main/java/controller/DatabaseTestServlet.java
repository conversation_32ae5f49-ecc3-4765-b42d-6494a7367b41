package controller;

import util.HibernateUtil;
import org.hibernate.SessionFactory;

import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.sql.Connection;
import java.sql.DriverManager;

@WebServlet("/test-db")
public class DatabaseTestServlet extends HttpServlet {

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        response.setContentType("text/html;charset=UTF-8");
        PrintWriter out = response.getWriter();

        out.println("<html><head><title>Test de Connexion Base de Données</title></head><body>");
        out.println("<h1>Test de Connexion à la Base de Données</h1>");

        // Test de connexion MySQL
        out.println("<h2>Test MySQL</h2>");
        try {
            Class.forName("com.mysql.cj.jdbc.Driver");
            Connection conn = DriverManager.getConnection(
                "**************************************", "root", "");
            out.println("<p>Connexion MySQL reussie !</p>");
            out.println("<p>Base de données : jeetraindb</p>");
            conn.close();
        } catch (Exception e) {
            out.println("<p>Connexion MySQL echouee : " + e.getMessage() + "</p>");
            out.println("<p>Vérifiez que :</p>");
            out.println("<ul>");
            out.println("<li>MySQL est installé et démarré</li>");
            out.println("<li>La base de données 'jeetraindb' existe</li>");
            out.println("<li>L'utilisateur 'root' a les permissions</li>");
            out.println("</ul>");
        }

        // Test Hibernate
        out.println("<h2>Test Hibernate</h2>");
        try {
            SessionFactory sessionFactory = HibernateUtil.getSessionFactory();
            out.println("<p>SessionFactory Hibernate creee avec succes !</p>");
        } catch (Exception e) {
            out.println("<p>Erreur Hibernate : " + e.getMessage() + "</p>");
            out.println("<pre>");
            e.printStackTrace(out);
            out.println("</pre>");
        }

        out.println("<hr>");
        out.println("<h2>Instructions pour MySQL</h2>");
        out.println("<p>Si MySQL n'est pas installé, voici les étapes :</p>");
        out.println("<ol>");
        out.println("<li>Installer MySQL Server</li>");
        out.println("<li>Créer la base de données :</li>");
        out.println("<pre>CREATE DATABASE jeetraindb;</pre>");
        out.println("<li>Redémarrer l'application</li>");
        out.println("<li>Initialiser les données avec <a href='init-data'>/init-data</a></li>");
        out.println("</ol>");

        out.println("<p><a href='diagnostic'>Diagnostic complet</a></p>");
        out.println("</body></html>");
    }
}
