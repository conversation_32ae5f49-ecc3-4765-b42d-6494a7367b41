package controller;

import java.io.IOException;
import java.util.List;
import java.util.logging.Logger;

import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

import dao.ReservationDAO;
import model.Reservation;
import model.User;
import util.HibernateUtil;

@WebServlet("/admin/reservations")
public class AdminReservationsServlet extends HttpServlet {
    private static final long serialVersionUID = 1L;
    private static final Logger LOGGER = Logger.getLogger(AdminReservationsServlet.class.getName());

    private ReservationDAO reservationDAO;

    @Override
    public void init() throws ServletException {
        super.init();
        this.reservationDAO = new ReservationDAO(HibernateUtil.getSessionFactory());
    }

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        HttpSession session = request.getSession();
        User user = (User) session.getAttribute("user");

        // Vérification de l'authentification et du rôle admin
        if (user == null) {
            response.sendRedirect(request.getContextPath() + "/login.jsp");
            return;
        }

        if (!"admin".equals(user.getRole())) {
            response.sendError(HttpServletResponse.SC_FORBIDDEN, "Accès réservé aux administrateurs");
            return;
        }

        try {
            // Récupération de toutes les réservations
            List<Reservation> allReservations = reservationDAO.findAll();

            // Statistiques
            long totalReservations = allReservations.size();
            long confirmedReservations = allReservations.stream()
                .filter(r -> "confirmée".equals(r.getEtat()) || "acheté".equals(r.getEtat()))
                .count();
            long pendingReservations = allReservations.stream()
                .filter(r -> "en attente".equals(r.getEtat()))
                .count();
            long cancelledReservations = allReservations.stream()
                .filter(r -> "annulée".equals(r.getEtat()))
                .count();

            // Calcul du chiffre d'affaires total
            double totalRevenue = allReservations.stream()
                .filter(r -> "confirmée".equals(r.getEtat()) || "acheté".equals(r.getEtat()))
                .mapToDouble(r -> r.getVoyage().getPrix())
                .sum();

            // Ajout des données à la requête
            request.setAttribute("allReservations", allReservations);
            request.setAttribute("totalReservations", totalReservations);
            request.setAttribute("confirmedReservations", confirmedReservations);
            request.setAttribute("pendingReservations", pendingReservations);
            request.setAttribute("cancelledReservations", cancelledReservations);
            request.setAttribute("totalRevenue", totalRevenue);

            LOGGER.info("Admin " + user.getUsername() + " consulte toutes les réservations (" + totalReservations + " total)");

            // Redirection vers la page JSP
            request.getRequestDispatcher("/admin/reservations.jsp").forward(request, response);

        } catch (Exception e) {
            LOGGER.severe("Erreur lors de la récupération des réservations : " + e.getMessage());
            e.printStackTrace();
            request.setAttribute("error", "Erreur lors de la récupération des réservations : " + e.getMessage());
            request.getRequestDispatcher("/admin/reservations.jsp").forward(request, response);
        }
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        HttpSession session = request.getSession();
        User user = (User) session.getAttribute("user");

        // Vérification de l'authentification et du rôle admin
        if (user == null) {
            response.sendRedirect(request.getContextPath() + "/login.jsp");
            return;
        }

        if (!"admin".equals(user.getRole())) {
            response.sendError(HttpServletResponse.SC_FORBIDDEN, "Accès réservé aux administrateurs");
            return;
        }

        String action = request.getParameter("action");
        String reservationIdStr = request.getParameter("reservationId");

        if (action == null || reservationIdStr == null) {
            session.setAttribute("error", "Paramètres manquants");
            response.sendRedirect("reservations");
            return;
        }

        try {
            Long reservationId = Long.parseLong(reservationIdStr);
            Reservation reservation = reservationDAO.findById(reservationId);

            if (reservation == null) {
                session.setAttribute("error", "Réservation introuvable");
                response.sendRedirect("reservations");
                return;
            }

            switch (action) {
                case "confirm":
                    if ("en attente".equals(reservation.getEtat())) {
                        reservation.setEtat("confirmée");
                        reservationDAO.update(reservation);
                        session.setAttribute("success", "Réservation #" + reservationId + " confirmée avec succès");
                        LOGGER.info("Admin " + user.getUsername() + " a confirmé la réservation #" + reservationId);
                    } else {
                        session.setAttribute("error", "Cette réservation ne peut pas être confirmée");
                    }
                    break;

                case "cancel":
                    if (!"annulée".equals(reservation.getEtat()) && !"utilisé".equals(reservation.getEtat())) {
                        reservation.setEtat("annulée");
                        reservationDAO.update(reservation);
                        session.setAttribute("success", "Réservation #" + reservationId + " annulée avec succès");
                        LOGGER.info("Admin " + user.getUsername() + " a annulé la réservation #" + reservationId);
                    } else {
                        session.setAttribute("error", "Cette réservation ne peut pas être annulée");
                    }
                    break;

                case "delete":
                    reservationDAO.delete(reservation);
                    session.setAttribute("success", "Réservation #" + reservationId + " supprimée avec succès");
                    LOGGER.info("Admin " + user.getUsername() + " a supprimé la réservation #" + reservationId);
                    break;

                default:
                    session.setAttribute("error", "Action non reconnue");
                    break;
            }

        } catch (NumberFormatException e) {
            session.setAttribute("error", "ID de réservation invalide");
        } catch (Exception e) {
            LOGGER.severe("Erreur lors de l'action sur la réservation : " + e.getMessage());
            session.setAttribute("error", "Erreur lors de l'opération : " + e.getMessage());
        }

        response.sendRedirect("reservations");
    }
}
