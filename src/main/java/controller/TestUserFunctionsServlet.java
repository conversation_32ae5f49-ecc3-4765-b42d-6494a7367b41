package controller;

import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import model.User;
import model.Reservation;
import model.Voyage;
import model.Trajet;
import model.Station;
import dao.UserDAO;
import dao.ReservationDAO;
import dao.VoyageDAO;
import dao.TrajetDAO;
import dao.StationDAO;
import util.HibernateUtil;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.logging.Logger;

/**
 * Servlet de test pour démontrer les fonctionnalités utilisateur
 */
@WebServlet("/test-user-functions")
public class TestUserFunctionsServlet extends HttpServlet {
    private static final Logger LOGGER = Logger.getLogger(TestUserFunctionsServlet.class.getName());

    private UserDAO userDAO;
    private ReservationDAO reservationDAO;
    private VoyageDAO voyageDAO;
    private TrajetDAO trajetDAO;
    private StationDAO stationDAO;

    @Override
    public void init() throws ServletException {
        userDAO = new UserDAO(HibernateUtil.getSessionFactory());
        reservationDAO = new ReservationDAO(HibernateUtil.getSessionFactory());
        voyageDAO = new VoyageDAO(HibernateUtil.getSessionFactory());
        trajetDAO = new TrajetDAO(HibernateUtil.getSessionFactory());
        stationDAO = new StationDAO(HibernateUtil.getSessionFactory());
    }

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        String action = request.getParameter("action");
        if (action == null) action = "menu";

        switch (action) {
            case "login-test-user":
                loginTestUser(request, response);
                break;
            case "create-test-data":
                createTestData(request, response);
                break;
            case "test-reservations":
                testReservations(request, response);
                break;
            case "test-pdf":
                testPDF(request, response);
                break;
            default:
                showMenu(request, response);
                break;
        }
    }

    private void showMenu(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        response.setContentType("text/html;charset=UTF-8");
        response.getWriter().println("""
            <!DOCTYPE html>
            <html lang="fr">
            <head>
                <meta charset="UTF-8">
                <title>🧪 Test Fonctionnalités Utilisateur - JEE Training</title>
                <link rel="stylesheet" href="/jeetraing/css/style.css">
                <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
            </head>
            <body>
                <div class="container">
                    <div class="main-content">
                        <h1><i class="fas fa-flask"></i> Test des Fonctionnalités Utilisateur</h1>

                        <div class="card">
                            <div class="card-header">
                                <h2><i class="fas fa-play"></i> Actions de Test Disponibles</h2>
                            </div>

                            <div class="row">
                                <div class="col-2">
                                    <a href="?action=login-test-user" class="btn btn-primary btn-full">
                                        <i class="fas fa-sign-in-alt"></i> Se connecter comme utilisateur test
                                    </a>
                                </div>
                                <div class="col-2">
                                    <a href="?action=create-test-data" class="btn btn-warning btn-full">
                                        <i class="fas fa-database"></i> Créer données de test
                                    </a>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-2">
                                    <a href="/jeetraing/mes-reservations" class="btn btn-success btn-full">
                                        <i class="fas fa-ticket-alt"></i> Tester Mes Réservations
                                    </a>
                                </div>
                                <div class="col-2">
                                    <a href="/jeetraing/historique" class="btn btn-info btn-full">
                                        <i class="fas fa-history"></i> Tester Historique
                                    </a>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-2">
                                    <a href="?action=test-pdf" class="btn btn-danger btn-full">
                                        <i class="fas fa-file-pdf"></i> Tester PDF
                                    </a>
                                </div>
                                <div class="col-2">
                                    <a href="/jeetraing/demo-fonctionnalites-utilisateur-finales.jsp" class="btn btn-secondary btn-full">
                                        <i class="fas fa-eye"></i> Voir Démo Complète
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h2><i class="fas fa-info-circle"></i> État Actuel</h2>
                            </div>
                            <p><strong>Session utilisateur :</strong> """ +
                            (request.getSession(false) != null && request.getSession(false).getAttribute("user") != null ?
                                "✅ Connecté comme " + ((User)request.getSession(false).getAttribute("user")).getUsername() :
                                "❌ Non connecté") + """
                            </p>
                        </div>
                    </div>
                </div>
            </body>
            </html>
            """);
    }

    private void loginTestUser(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        try {
            // Chercher ou creer un utilisateur de test
            User testUser = userDAO.findByUsername("testuser");
            if (testUser == null) {
                testUser = new User();
                testUser.setUsername("testuser");
                testUser.setPassword("password"); // En production, utiliser un hash
                testUser.setEmail("<EMAIL>");
                testUser.setRole("user");
                testUser.setBlocked(false);
                userDAO.save(testUser);
                LOGGER.info("Utilisateur de test cree : testuser");
            }

            // Connecter l'utilisateur
            HttpSession session = request.getSession(true);
            session.setAttribute("user", testUser);

            response.setContentType("text/html;charset=UTF-8");
            response.getWriter().println("""
                <!DOCTYPE html>
                <html lang="fr">
                <head>
                    <meta charset="UTF-8">
                    <title>✅ Connexion Réussie - JEE Training</title>
                    <link rel="stylesheet" href="/jeetraing/css/style.css">
                </head>
                <body>
                    <div class="container">
                        <div class="main-content">
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle"></i>
                                <strong>Connexion réussie !</strong> Vous êtes maintenant connecté comme 'testuser'.
                            </div>

                            <div class="card">
                                <h2>🎯 Testez maintenant les fonctionnalités :</h2>
                                <div class="row">
                                    <div class="col-3">
                                        <a href="/jeetraing/mes-reservations" class="btn btn-primary btn-full">
                                            <i class="fas fa-ticket-alt"></i> Mes Réservations
                                        </a>
                                    </div>
                                    <div class="col-3">
                                        <a href="/jeetraing/historique" class="btn btn-success btn-full">
                                            <i class="fas fa-history"></i> Historique
                                        </a>
                                    </div>
                                    <div class="col-3">
                                        <a href="/jeetraing/annuler-reservation" class="btn btn-warning btn-full">
                                            <i class="fas fa-times-circle"></i> Annuler
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </body>
                </html>
                """);

        } catch (Exception e) {
            LOGGER.severe("Erreur lors de la connexion de l'utilisateur test : " + e.getMessage());
            response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "Erreur lors de la connexion");
        }
    }

    private void createTestData(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        try {
            // Creer des stations de test si elles n'existent pas
            List<Station> stations = stationDAO.findAll();
            Station paris = null;
            Station lyon = null;

            for (Station s : stations) {
                if ("Paris Gare du Nord".equals(s.getName())) {
                    paris = s;
                } else if ("Lyon Part-Dieu".equals(s.getName())) {
                    lyon = s;
                }
            }

            if (paris == null) {
                paris = new Station();
                paris.setName("Paris Gare du Nord");
                paris.setCity("Paris");
                stationDAO.save(paris);
            }

            if (lyon == null) {
                lyon = new Station();
                lyon.setName("Lyon Part-Dieu");
                lyon.setCity("Lyon");
                stationDAO.save(lyon);
            }

            // Creer un trajet de test
            List<Trajet> trajets = trajetDAO.findAll();
            Trajet trajet = null;
            if (!trajets.isEmpty()) {
                trajet = trajets.get(0);
            } else {
                trajet = new Trajet();
                trajet.setDepartStation(paris);
                trajet.setArrivalStation(lyon);
                trajetDAO.save(trajet);
            }

            // Creer un voyage de test
            List<Voyage> voyages = voyageDAO.findAll();
            Voyage voyage = null;
            if (!voyages.isEmpty()) {
                voyage = voyages.get(0);
            } else {
                voyage = new Voyage();
                voyage.setTrajet(trajet);
                voyage.setHeureDepart(LocalDateTime.now().plusDays(1));
                voyage.setHeureArrivee(LocalDateTime.now().plusDays(1).plusHours(2));
                voyage.setPrix(89.50);
                voyageDAO.save(voyage);
            }

            response.setContentType("text/html;charset=UTF-8");
            response.getWriter().println("""
                <!DOCTYPE html>
                <html lang="fr">
                <head>
                    <meta charset="UTF-8">
                    <title>✅ Données Créées - JEE Training</title>
                    <link rel="stylesheet" href="/jeetraing/css/style.css">
                </head>
                <body>
                    <div class="container">
                        <div class="main-content">
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle"></i>
                                <strong>Donnees de test creees avec succes !</strong>
                            </div>

                            <div class="card">
                                <h2>📊 Donnees creees :</h2>
                                <ul>
                                    <li>✅ Stations : Paris Gare du Nord, Lyon Part-Dieu</li>
                                    <li>✅ Trajet : Paris → Lyon</li>
                                    <li>✅ Voyage : Demain a partir de maintenant</li>
                                </ul>

                                <a href="/jeetraing/test-user-functions" class="btn btn-primary">
                                    <i class="fas fa-arrow-left"></i> Retour au menu de test
                                </a>
                            </div>
                        </div>
                    </div>
                </body>
                </html>
                """);

        } catch (Exception e) {
            LOGGER.severe("Erreur lors de la creation des donnees de test : " + e.getMessage());
            response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "Erreur lors de la creation des donnees");
        }
    }

    private void testReservations(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        response.sendRedirect(request.getContextPath() + "/mes-reservations");
    }

    private void testPDF(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        // Rediriger vers le téléchargement PDF avec un ID de test
        response.sendRedirect(request.getContextPath() + "/download-ticket?reservationId=1");
    }
}
