package controller;

import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.time.LocalDate;

@WebServlet("/test-recherche-complete")
public class TestRechercheServlet extends HttpServlet {
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        response.setContentType("text/html;charset=UTF-8");
        PrintWriter out = response.getWriter();
        
        out.println("<html><head><title>Test Recherche Complete</title></head><body>");
        out.println("<h1>Test de Recherche Complete</h1>");
        
        // Simuler une recherche POST
        out.println("<h2>Test 1: Recherche Paris -> Lyon</h2>");
        out.println("<form action='recherche' method='post'>");
        out.println("<input type='hidden' name='villeDepart' value='Paris'>");
        out.println("<input type='hidden' name='villeDestination' value='Lyon'>");
        out.println("<input type='hidden' name='date' value='" + LocalDate.now().plusDays(1) + "'>");
        out.println("<button type='submit'>Rechercher Paris -> Lyon</button>");
        out.println("</form>");
        
        out.println("<h2>Test 2: Recherche Paris -> Marseille</h2>");
        out.println("<form action='recherche' method='post'>");
        out.println("<input type='hidden' name='villeDepart' value='Paris'>");
        out.println("<input type='hidden' name='villeDestination' value='Marseille'>");
        out.println("<input type='hidden' name='date' value='" + LocalDate.now().plusDays(2) + "'>");
        out.println("<button type='submit'>Rechercher Paris -> Marseille</button>");
        out.println("</form>");
        
        out.println("<h2>Test 3: Recherche Lyon -> Nice</h2>");
        out.println("<form action='recherche' method='post'>");
        out.println("<input type='hidden' name='villeDepart' value='Lyon'>");
        out.println("<input type='hidden' name='villeDestination' value='Nice'>");
        out.println("<input type='hidden' name='date' value='" + LocalDate.now().plusDays(3) + "'>");
        out.println("<button type='submit'>Rechercher Lyon -> Nice</button>");
        out.println("</form>");
        
        out.println("<hr>");
        out.println("<p><a href='recherche'>Page de recherche normale</a></p>");
        out.println("<p><a href='diagnostic'>Diagnostic</a></p>");
        
        out.println("</body></html>");
    }
}
