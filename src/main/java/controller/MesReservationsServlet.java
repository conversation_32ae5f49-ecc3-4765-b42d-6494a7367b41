package controller;

import java.io.IOException;
import java.util.List;
import java.util.logging.Logger;

import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

import model.Reservation;
import model.User;
import dao.ReservationDAO;
import util.HibernateUtil;

/**
 * Servlet pour gérer l'affichage des réservations de l'utilisateur connecté
 */
@WebServlet("/mes-reservations")
public class MesReservationsServlet extends HttpServlet {
    private static final Logger LOGGER = Logger.getLogger(MesReservationsServlet.class.getName());
    private ReservationDAO reservationDAO;

    @Override
    public void init() throws ServletException {
        reservationDAO = new ReservationDAO(HibernateUtil.getSessionFactory());
    }

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        HttpSession session = request.getSession(false);
        if (session == null || session.getAttribute("user") == null) {
            LOGGER.warning("Utilisateur non connecté lors de l'accès à /mes-reservations");
            response.sendRedirect(request.getContextPath() + "/login");
            return;
        }

        User user = (User) session.getAttribute("user");
        
        try {
            // Récupérer toutes les réservations de l'utilisateur (sauf celles utilisées)
            List<Reservation> reservations = reservationDAO.findByUserExceptEtat(user, "utilisé");
            
            request.setAttribute("reservations", reservations);
            request.getRequestDispatcher("/mes-reservations.jsp").forward(request, response);
            
        } catch (Exception e) {
            LOGGER.severe("Erreur lors de la récupération des réservations: " + e.getMessage());
            request.setAttribute("error", "Erreur lors de la récupération de vos réservations.");
            request.getRequestDispatcher("/mes-reservations.jsp").forward(request, response);
        }
    }
}
