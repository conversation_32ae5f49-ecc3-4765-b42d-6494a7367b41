package controller;

import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.Statement;

@WebServlet("/create-db")
public class CreateDatabaseServlet extends HttpServlet {
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        response.setContentType("text/html;charset=UTF-8");
        PrintWriter out = response.getWriter();
        
        out.println("<html><head><title>Creation Base de Donnees</title></head><body>");
        out.println("<h1>Creation de la Base de Donnees MySQL</h1>");
        
        try {
            // Connexion à MySQL sans spécifier de base de données
            Class.forName("com.mysql.cj.jdbc.Driver");
            Connection conn = DriverManager.getConnection(
                "***************************/", "root", "");
            
            out.println("<p>Connexion a MySQL reussie !</p>");
            
            // Création de la base de données
            Statement stmt = conn.createStatement();
            stmt.executeUpdate("CREATE DATABASE IF NOT EXISTS jeetraindb");
            out.println("<p>Base de donnees 'jeetraindb' creee avec succes !</p>");
            
            // Vérification
            stmt.executeUpdate("USE jeetraindb");
            out.println("<p>Connexion a la base 'jeetraindb' reussie !</p>");
            
            stmt.close();
            conn.close();
            
            out.println("<hr>");
            out.println("<h2>Prochaines etapes :</h2>");
            out.println("<ol>");
            out.println("<li><a href='test-db'>Tester la connexion</a></li>");
            out.println("<li><a href='init-data'>Initialiser les donnees</a></li>");
            out.println("<li><a href='login.jsp'>Aller a la page de connexion</a></li>");
            out.println("</ol>");
            
        } catch (Exception e) {
            out.println("<h2>Erreur</h2>");
            out.println("<p>Erreur lors de la creation : " + e.getMessage() + "</p>");
            out.println("<p>Verifiez que :</p>");
            out.println("<ul>");
            out.println("<li>MySQL Server est installe et demarre</li>");
            out.println("<li>L'utilisateur 'root' existe</li>");
            out.println("<li>Le mot de passe est vide ou correct</li>");
            out.println("</ul>");
            out.println("<pre>");
            e.printStackTrace(out);
            out.println("</pre>");
        }
        
        out.println("</body></html>");
    }
}
