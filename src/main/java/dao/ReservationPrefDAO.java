package dao;


import org.hibernate.SessionFactory;
import model.ReservationPref;

import org.hibernate.Session;

public class ReservationPrefDAO {
    private final SessionFactory sessionFactory;

    public ReservationPrefDAO(SessionFactory sessionFactory) {
        this.sessionFactory = sessionFactory;
    }

    public void save(ReservationPref reservationPref) {
        try (Session session = sessionFactory.openSession()) {
            session.beginTransaction();
            session.persist(reservationPref); // m�me m�thode que ReservationDAO
            session.getTransaction().commit();
        }
    }
}