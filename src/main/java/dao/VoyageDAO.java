package dao;

import java.util.List;
import java.time.LocalDate;

import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;

import model.Trajet;
import model.Voyage;

public class VoyageDAO {
    private final SessionFactory sessionFactory;

    public VoyageDAO(SessionFactory sessionFactory) {
        if (sessionFactory == null) {
            throw new IllegalArgumentException("SessionFactory cannot be null");
        }
        this.sessionFactory = sessionFactory;
    }

    public void save(Voyage voyage) {
        if (voyage == null) {
            throw new IllegalArgumentException("Voyage cannot be null");
        }
        Transaction transaction = null;
        try (Session session = sessionFactory.openSession()) {
            transaction = session.beginTransaction();
            session.persist(voyage);
            transaction.commit();
        } catch (Exception e) {
            if (transaction != null && transaction.isActive()) {
                transaction.rollback();
            }
            throw new RuntimeException("Failed to save voyage: " + e.getMessage(), e);
        }
    }

    public Voyage findById(Long id) {
        if (id == null || id <= 0) {
            throw new IllegalArgumentException("ID must be a positive non-null value");
        }
        try (Session session = sessionFactory.openSession()) {
            return session.get(Voyage.class, id);
        } catch (Exception e) {
            throw new RuntimeException("Failed to find voyage by ID: " + e.getMessage(), e);
        }
    }

    public List<Voyage> findAll() {
        try (Session session = sessionFactory.openSession()) {
            return session.createQuery("FROM Voyage", Voyage.class).list();
        } catch (Exception e) {
            throw new RuntimeException("Failed to retrieve all voyages: " + e.getMessage(), e);
        }
    }

    public void delete(Voyage voyage) {
        if (voyage == null) {
            throw new IllegalArgumentException("Voyage cannot be null");
        }
        Transaction transaction = null;
        try (Session session = sessionFactory.openSession()) {
            transaction = session.beginTransaction();
            // Reattach the object to the session if it's detached
            Voyage managedVoyage = session.get(Voyage.class, voyage.getId());
            if (managedVoyage != null) {
                session.remove(managedVoyage);
            }
            transaction.commit();
        } catch (Exception e) {
            if (transaction != null && transaction.isActive()) {
                transaction.rollback();
            }
            throw new RuntimeException("Failed to delete voyage: " + e.getMessage(), e);
        }
    }

    public void update(Voyage voyage) {
        if (voyage == null) {
            throw new IllegalArgumentException("Voyage cannot be null");
        }
        Transaction transaction = null;
        try (Session session = sessionFactory.openSession()) {
            transaction = session.beginTransaction();
            session.merge(voyage);
            transaction.commit();
        } catch (Exception e) {
            if (transaction != null && transaction.isActive()) {
                transaction.rollback();
            }
            throw new RuntimeException("Failed to update voyage: " + e.getMessage(), e);
        }
    }

    public List<Voyage> findByTrajetAndDate(Trajet trajet, LocalDate date) {
        if (trajet == null || date == null) {
            throw new IllegalArgumentException("Trajet and date cannot be null");
        }
        try (Session session = sessionFactory.openSession()) {
            return session.createQuery(
                    "FROM Voyage v WHERE v.trajet = :trajet AND DATE(v.heureDepart) = :date",
                    Voyage.class)
                    .setParameter("trajet", trajet)
                    .setParameter("date", date)
                    .list();
        } catch (Exception e) {
            throw new RuntimeException("Failed to find voyages by trajet and date: " + e.getMessage(), e);
        }
    }
}