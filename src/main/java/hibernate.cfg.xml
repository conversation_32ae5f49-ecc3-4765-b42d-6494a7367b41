<!DOCTYPE hibernate-configuration PUBLIC
	"-//Hibernate/Hibernate Configuration DTD 3.0//EN"
	"http://www.hibernate.org/dtd/hibernate-configuration-3.0.dtd">
<hibernate-configuration>
<!-- Configuration des paramètres de connexion à la base de données -->
<session-factory>
<property name="hibernate.connection.driver_class">org.h2.Driver</property>
<property name="hibernate.connection.url">jdbc:h2:mem:jeetraindb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE</property>
<property name="hibernate.connection.username">sa</property>
<property name="hibernate.connection.password"></property>
<!-- Dialecte pour H2 -->
<property name="hibernate.dialect">org.hibernate.dialect.H2Dialect</property>
<!-- Activation de la génération automatique du schéma -->
<property name="hibernate.hbm2ddl.auto">update</property>
<!-- Entités à scanner pour Hibernate -->
<mapping class="model.User"/>
<mapping class="model.Trajet"/>
<mapping class="model.Station"/>
<mapping class="model.Voyage"/>
<mapping class="model.Reservation"/>
<mapping class="model.Promotion"/>

</session-factory>
</hibernate-configuration>