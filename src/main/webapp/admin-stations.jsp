<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Espace Administrateur - Gares</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .form-group { margin-bottom: 15px; }
        label { display: inline-block; width: 100px; }
        input { padding: 5px; }
    </style>
</head>
<body>
<%@ include file="navbar.jsp" %>

    <h2>Espace Administrateur - Gestion des Gares</h2>
    <h3>Ajouter une gare</h3>
    <form action="stations" method="post">
        <input type="hidden" name="action" value="add">
        <div class="form-group">
            <label for="name">Nom :</label>
            <input type="text" id="name" name="name" required>
        </div>
        <div class="form-group">
            <label for="city">Ville :</label>
            <input type="text" id="city" name="city" required>
        </div>
        <button type="submit">Ajouter</button>
    </form>

    <h3>Liste des gares</h3>
    <table>
        <tr>
            <th>ID</th>
            <th>Nom</th>
            <th>Ville</th>
            <th>Actions</th>
        </tr>
        <c:forEach var="station" items="${stations}">
            <tr>
                <td>${station.id}</td>
                <td>${station.name}</td>
                <td>${station.city}</td>
                <td>
                    <a href="stations?action=edit&id=${station.id}">Modifier</a>
                    <a href="stations?action=delete&id=${station.id}" 
                       onclick="return confirm('Êtes-vous sûr ?')">Supprimer</a>
                </td>
            </tr>
        </c:forEach>
    </table>

    <c:if test="${not empty station}">
        <h3>Modifier une gare</h3>
        <form action="stations" method="post">
            <input type="hidden" name="action" value="update">
            <input type="hidden" name="id" value="${station.id}">
            <div class="form-group">
                <label for="name">Nom :</label>
                <input type="text" id="name" name="name" value="${station.name}" required>
            </div>
            <div class="form-group">
                <label for="city">Ville :</label>
                <input type="text" id="city" name="city" value="${station.city}" required>
            </div>
            <button type="submit">Mettre à jour</button>
        </form>
    </c:if>

    <p><a href="../admin">Retour à l'espace admin</a></p>
</body>
</html>