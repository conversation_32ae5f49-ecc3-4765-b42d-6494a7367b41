<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚉 Gestion des Gares - JEE Training</title>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <a href="${pageContext.request.contextPath}/recherche" class="logo">
                <i class="fas fa-train"></i> JEE Training
            </a>
            <div class="nav-links">
                <c:choose>
                    <c:when test="${sessionScope.user != null}">
                        <a href="${pageContext.request.contextPath}/recherche"><i class="fas fa-search"></i> Rechercher</a>
                        <c:if test="${sessionScope.user.role == 'admin'}">
                            <a href="${pageContext.request.contextPath}/admin/dashboard"><i class="fas fa-cog"></i> Admin</a>
                        </c:if>
                        <a href="${pageContext.request.contextPath}/logout"><i class="fas fa-sign-out-alt"></i> Déconnexion</a>
                    </c:when>
                    <c:otherwise>
                        <a href="${pageContext.request.contextPath}/login.jsp"><i class="fas fa-sign-in-alt"></i> Connexion</a>
                        <a href="${pageContext.request.contextPath}/register.jsp"><i class="fas fa-user-plus"></i> Inscription</a>
                    </c:otherwise>
                </c:choose>
            </div>
        </div>
    </nav>

    <!-- Contenu principal -->
    <div class="container">
        <div class="main-content">
            <h1><i class="fas fa-map-marker-alt"></i> Gestion des Gares</h1>

            <!-- Breadcrumb -->
            <div class="breadcrumb">
                <a href="${pageContext.request.contextPath}/admin/dashboard"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                <span><i class="fas fa-chevron-right"></i></span>
                <span>Gestion des Gares</span>
            </div>

            <!-- Formulaire d'ajout -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title"><i class="fas fa-plus-circle"></i> Ajouter une Gare</h2>
                </div>

                <form action="stations" method="post" class="admin-form">
                    <input type="hidden" name="action" value="add">
                    <div class="row">
                        <div class="col-2">
                            <div class="admin-form-group">
                                <label for="name"><i class="fas fa-building"></i> Nom de la gare</label>
                                <input type="text" id="name" name="name" placeholder="Ex: Gare de Paris Nord" required>
                            </div>
                        </div>
                        <div class="col-2">
                            <div class="admin-form-group">
                                <label for="city"><i class="fas fa-city"></i> Ville</label>
                                <input type="text" id="city" name="city" placeholder="Ex: Paris" required>
                            </div>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Ajouter la Gare
                    </button>
                </form>
            </div>

            <!-- Liste des gares -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title"><i class="fas fa-list"></i> Liste des Gares</h2>
                </div>

                <c:if test="${not empty stations}">
                    <table class="admin-table">
                        <thead>
                            <tr>
                                <th><i class="fas fa-hashtag"></i> ID</th>
                                <th><i class="fas fa-building"></i> Nom de la Gare</th>
                                <th><i class="fas fa-city"></i> Ville</th>
                                <th><i class="fas fa-cogs"></i> Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <c:forEach var="station" items="${stations}">
                                <tr>
                                    <td><strong>#${station.id}</strong></td>
                                    <td>
                                        <div class="station-info">
                                            <i class="fas fa-train"></i>
                                            <span>${station.name}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge badge-info">
                                            <i class="fas fa-map-marker-alt"></i> ${station.city}
                                        </span>
                                    </td>
                                    <td>
                                        <a href="stations?action=edit&id=${station.id}" class="admin-btn admin-btn-primary">
                                            <i class="fas fa-edit"></i> Modifier
                                        </a>
                                        <a href="stations?action=delete&id=${station.id}" class="admin-btn admin-btn-danger"
                                           onclick="return confirm('Êtes-vous sûr de vouloir supprimer cette gare ?')">
                                            <i class="fas fa-trash"></i> Supprimer
                                        </a>
                                    </td>
                                </tr>
                            </c:forEach>
                        </tbody>
                    </table>
                </c:if>

                <c:if test="${empty stations}">
                    <div class="empty-state">
                        <i class="fas fa-map-marker-alt"></i>
                        <h3>Aucune gare trouvée</h3>
                        <p>Commencez par ajouter votre première gare.</p>
                    </div>
                </c:if>
            </div>

            <!-- Formulaire de modification -->
            <c:if test="${not empty station}">
                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title"><i class="fas fa-edit"></i> Modifier la Gare</h2>
                    </div>

                    <form action="stations" method="post" class="admin-form">
                        <input type="hidden" name="action" value="update">
                        <input type="hidden" name="id" value="${station.id}">
                        <div class="row">
                            <div class="col-2">
                                <div class="admin-form-group">
                                    <label for="editName"><i class="fas fa-building"></i> Nom de la gare</label>
                                    <input type="text" id="editName" name="name" value="${station.name}" required>
                                </div>
                            </div>
                            <div class="col-2">
                                <div class="admin-form-group">
                                    <label for="editCity"><i class="fas fa-city"></i> Ville</label>
                                    <input type="text" id="editCity" name="city" value="${station.city}" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-4">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save"></i> Mettre à Jour
                                </button>
                                <a href="stations" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Annuler
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </c:if>

            <!-- Actions rapides -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title"><i class="fas fa-bolt"></i> Actions Rapides</h2>
                </div>

                <div class="row">
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/admin/dashboard" class="btn btn-secondary btn-full">
                            <i class="fas fa-arrow-left"></i> Retour Dashboard
                        </a>
                    </div>
                    <div class="col-3">
                        <a href="stations" class="btn btn-primary btn-full">
                            <i class="fas fa-sync"></i> Actualiser
                        </a>
                    </div>
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/admin/trajets" class="btn btn-warning btn-full">
                            <i class="fas fa-road"></i> Gérer Trajets
                        </a>
                    </div>
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/admin/voyages" class="btn btn-success btn-full">
                            <i class="fas fa-train"></i> Gérer Voyages
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>