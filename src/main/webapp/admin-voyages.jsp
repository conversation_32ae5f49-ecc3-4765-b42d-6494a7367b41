<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Espace Administrateur - Voyages</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .form-group { margin-bottom: 15px; }
        label { display: inline-block; width: 150px; }
        input, select { padding: 5px; }
    </style>
</head>
<body>
<%@ include file="navbar.jsp" %>
    <h2>Espace Administrateur - Gestion des Voyages</h2>
    <h3>Ajouter un voyage</h3>
    <form action="voyages" method="post">
        <input type="hidden" name="action" value="add">
        <div class="form-group">
            <label for="trajetId">Trajet :</label>
            <select id="trajetId" name="trajetId" required>
                <c:forEach var="trajet" items="${trajets}">
                    <option value="${trajet.id}">
                        ${trajet.departStation.name} -> ${trajet.arrivalStation.name}
                    </option>
                </c:forEach>
            </select>
        </div>
        <div class="form-group">
            <label for="heureDepart">Heure de départ :</label>
            <input type="datetime-local" id="heureDepart" name="heureDepart" required>
        </div>
        <div class="form-group">
            <label for="heureArrivee">Heure d'arrivée :</label>
            <input type="datetime-local" id="heureArrivee" name="heureArrivee" required>
        </div>
        <div class="form-group">
            <label for="prix">Prix :</label>
            <input type="number" id="prix" name="prix" step="0.01" required>
        </div>
        <div class="form-group">
            <label for="placesDisponibles">Places disponibles :</label>
            <input type="number" id="placesDisponibles" name="placesDisponibles" required>
        </div>
        <button type="submit">Ajouter</button>
    </form>

    <h3>Liste des voyages</h3>
    <table>
        <tr>
            <th>ID</th>
            <th>Trajet</th>
            <th>Heure de départ</th>
            <th>Heure d'arrivée</th>
            <th>Prix</th>
            <th>Places disponibles</th>
            <th>Actions</th>
        </tr>
        <c:forEach var="voyage" items="${voyages}">
            <tr>
                <td>${voyage.id}</td>
                <td>${voyage.trajet.departStation.name} -> ${voyage.trajet.arrivalStation.name}</td>
                <td>${voyage.heureDepartFormatted}</td>
                <td>${voyage.heureArriveeFormatted}</td>
                <td>${voyage.prix} €</td>
                <td>${voyage.placesDisponibles}</td>
                <td>
                    <a href="voyages?action=edit&id=${voyage.id}">Modifier</a>
                    <a href="voyages?action=delete&id=${voyage.id}" 
                       onclick="return confirm('Êtes-vous sûr ?')">Supprimer</a>
                </td>
            </tr>
        </c:forEach>
    </table>

    <c:if test="${not empty voyage}">
        <h3>Modifier un voyage</h3>
        <form action="voyages" method="post">
            <input type="hidden" name="action" value="update">
            <input type="hidden" name="id" value="${voyage.id}">
            <div class="form-group">
                <label for="trajetId">Trajet :</label>
                <select id="trajetId" name="trajetId" required>
                    <c:forEach var="trajet" items="${trajets}">
                        <option value="${trajet.id}" ${trajet.id == voyage.trajet.id ? 'selected' : ''}>
                            ${trajet.departStation.name} -> ${trajet.arrivalStation.name}
                        </option>
                    </c:forEach>
                </select>
            </div>
            <div class="form-group">
                <label for="heureDepart">Heure de départ :</label>
                <input type="datetime-local" id="heureDepart" name="heureDepart" value="${voyage.heureDepart}" required>
            </div>
            <div class="form-group">
                <label for="heureArrivee">Heure d'arrivée :</label>
                <input type="datetime-local" id="heureArrivee" name="heureArrivee" value="${voyage.heureArrivee}" required>
            </div>
            <div class="form-group">
                <label for="prix">Prix :</label>
                <input type="number" id="prix" name="prix" step="0.01" value="${voyage.prix}" required>
            </div>
            <div class="form-group">
                <label for="placesDisponibles">Places disponibles :</label>
                <input type="number" id="placesDisponibles" name="placesDisponibles" value="${voyage.placesDisponibles}" required>
            </div>
            <button type="submit">Mettre à jour</button>
        </form>
    </c:if>

    <p><a href="../admin">Retour à l'espace admin</a></p>
</body>
</html>