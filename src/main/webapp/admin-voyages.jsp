<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚂 Gestion des Voyages - JEE Training</title>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <a href="${pageContext.request.contextPath}/recherche" class="logo">
                <i class="fas fa-train"></i> JEE Training
            </a>
            <div class="nav-links">
                <c:choose>
                    <c:when test="${sessionScope.user != null}">
                        <a href="${pageContext.request.contextPath}/recherche"><i class="fas fa-search"></i> Rechercher</a>
                        <c:if test="${sessionScope.user.role == 'admin'}">
                            <a href="${pageContext.request.contextPath}/admin/dashboard"><i class="fas fa-cog"></i> Admin</a>
                        </c:if>
                        <a href="${pageContext.request.contextPath}/logout"><i class="fas fa-sign-out-alt"></i> Déconnexion</a>
                    </c:when>
                    <c:otherwise>
                        <a href="${pageContext.request.contextPath}/login.jsp"><i class="fas fa-sign-in-alt"></i> Connexion</a>
                        <a href="${pageContext.request.contextPath}/register.jsp"><i class="fas fa-user-plus"></i> Inscription</a>
                    </c:otherwise>
                </c:choose>
            </div>
        </div>
    </nav>

    <!-- Contenu principal -->
    <div class="container">
        <div class="main-content">
            <h1><i class="fas fa-train"></i> Gestion des Voyages</h1>

            <!-- Breadcrumb -->
            <div class="breadcrumb">
                <a href="${pageContext.request.contextPath}/admin/dashboard"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                <span><i class="fas fa-chevron-right"></i></span>
                <span>Gestion des Voyages</span>
            </div>

            <!-- Formulaire d'ajout -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title"><i class="fas fa-plus-circle"></i> Ajouter un Voyage</h2>
                </div>

                <form action="voyages" method="post" class="admin-form">
                    <input type="hidden" name="action" value="add">

                    <div class="admin-form-group">
                        <label for="trajetId"><i class="fas fa-route"></i> Trajet</label>
                        <select id="trajetId" name="trajetId" required>
                            <option value="">Sélectionnez un trajet</option>
                            <c:forEach var="trajet" items="${trajets}">
                                <option value="${trajet.id}">
                                    🚂 ${trajet.departStation.name} → ${trajet.arrivalStation.name}
                                </option>
                            </c:forEach>
                        </select>
                    </div>

                    <div class="row">
                        <div class="col-2">
                            <div class="admin-form-group">
                                <label for="heureDepart"><i class="fas fa-clock"></i> Heure de Départ</label>
                                <input type="datetime-local" id="heureDepart" name="heureDepart" required>
                            </div>
                        </div>
                        <div class="col-2">
                            <div class="admin-form-group">
                                <label for="heureArrivee"><i class="fas fa-clock"></i> Heure d'Arrivée</label>
                                <input type="datetime-local" id="heureArrivee" name="heureArrivee" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-2">
                            <div class="admin-form-group">
                                <label for="prix"><i class="fas fa-euro-sign"></i> Prix (€)</label>
                                <input type="number" id="prix" name="prix" step="0.01" min="0" placeholder="Ex: 25.50" required>
                            </div>
                        </div>
                        <div class="col-2">
                            <div class="admin-form-group">
                                <label for="placesDisponibles"><i class="fas fa-users"></i> Places Disponibles</label>
                                <input type="number" id="placesDisponibles" name="placesDisponibles" min="1" placeholder="Ex: 100" required>
                            </div>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Ajouter le Voyage
                    </button>
                </form>
            </div>

            <!-- Liste des voyages -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title"><i class="fas fa-list"></i> Liste des Voyages</h2>
                </div>

                <c:if test="${not empty voyages}">
                    <table class="admin-table">
                        <thead>
                            <tr>
                                <th><i class="fas fa-hashtag"></i> ID</th>
                                <th><i class="fas fa-route"></i> Trajet</th>
                                <th><i class="fas fa-clock"></i> Horaires</th>
                                <th><i class="fas fa-euro-sign"></i> Prix</th>
                                <th><i class="fas fa-users"></i> Places</th>
                                <th><i class="fas fa-chart-bar"></i> Statut</th>
                                <th><i class="fas fa-cogs"></i> Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <c:forEach var="voyage" items="${voyages}">
                                <tr>
                                    <td><strong>#${voyage.id}</strong></td>
                                    <td>
                                        <div class="voyage-route">
                                            <div class="route-info">
                                                <i class="fas fa-train"></i>
                                                <span>${voyage.trajet.departStation.name} → ${voyage.trajet.arrivalStation.name}</span>
                                            </div>
                                            <small>${voyage.trajet.departStation.city} - ${voyage.trajet.arrivalStation.city}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="time-schedule">
                                            <div class="departure">
                                                <i class="fas fa-play"></i> ${voyage.heureDepartFormatted}
                                            </div>
                                            <div class="arrival">
                                                <i class="fas fa-stop"></i> ${voyage.heureArriveeFormatted}
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="price-badge">
                                            <i class="fas fa-euro-sign"></i> ${voyage.prix}€
                                        </span>
                                    </td>
                                    <td>
                                        <div class="places-info">
                                            <span class="places-count">
                                                <i class="fas fa-users"></i> ${voyage.placesDisponibles}
                                            </span>
                                            <c:choose>
                                                <c:when test="${voyage.placesDisponibles > 50}">
                                                    <span class="badge badge-success">Disponible</span>
                                                </c:when>
                                                <c:when test="${voyage.placesDisponibles > 10}">
                                                    <span class="badge badge-warning">Limité</span>
                                                </c:when>
                                                <c:otherwise>
                                                    <span class="badge badge-danger">Complet</span>
                                                </c:otherwise>
                                            </c:choose>
                                        </div>
                                    </td>
                                    <td>
                                        <c:choose>
                                            <c:when test="${voyage.placesDisponibles > 0}">
                                                <span class="badge badge-success">
                                                    <i class="fas fa-check-circle"></i> Actif
                                                </span>
                                            </c:when>
                                            <c:otherwise>
                                                <span class="badge badge-danger">
                                                    <i class="fas fa-times-circle"></i> Complet
                                                </span>
                                            </c:otherwise>
                                        </c:choose>
                                    </td>
                                    <td>
                                        <a href="voyages?action=edit&id=${voyage.id}" class="admin-btn admin-btn-primary">
                                            <i class="fas fa-edit"></i> Modifier
                                        </a>
                                        <a href="voyages?action=delete&id=${voyage.id}" class="admin-btn admin-btn-danger"
                                           onclick="return confirm('Êtes-vous sûr de vouloir supprimer ce voyage ?');">
                                            <i class="fas fa-trash"></i> Supprimer
                                        </a>
                                    </td>
                                </tr>
                            </c:forEach>
                        </tbody>
                    </table>
                </c:if>

                <c:if test="${empty voyages}">
                    <div class="empty-state">
                        <i class="fas fa-train"></i>
                        <h3>Aucun voyage trouvé</h3>
                        <p>Commencez par créer votre premier voyage.</p>
                    </div>
                </c:if>
            </div>

            <!-- Formulaire de modification -->
            <c:if test="${not empty voyage}">
                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title"><i class="fas fa-edit"></i> Modifier le Voyage</h2>
                    </div>

                    <form action="voyages" method="post" class="admin-form">
                        <input type="hidden" name="action" value="update">
                        <input type="hidden" name="id" value="${voyage.id}">

                        <div class="admin-form-group">
                            <label for="editTrajetId"><i class="fas fa-route"></i> Trajet</label>
                            <select id="editTrajetId" name="trajetId" required>
                                <c:forEach var="trajet" items="${trajets}">
                                    <option value="${trajet.id}" ${trajet.id == voyage.trajet.id ? 'selected' : ''}>
                                        🚂 ${trajet.departStation.name} → ${trajet.arrivalStation.name}
                                    </option>
                                </c:forEach>
                            </select>
                        </div>

                        <div class="row">
                            <div class="col-2">
                                <div class="admin-form-group">
                                    <label for="editHeureDepart"><i class="fas fa-clock"></i> Heure de Départ</label>
                                    <input type="datetime-local" id="editHeureDepart" name="heureDepart" value="${voyage.heureDepart}" required>
                                </div>
                            </div>
                            <div class="col-2">
                                <div class="admin-form-group">
                                    <label for="editHeureArrivee"><i class="fas fa-clock"></i> Heure d'Arrivée</label>
                                    <input type="datetime-local" id="editHeureArrivee" name="heureArrivee" value="${voyage.heureArrivee}" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-2">
                                <div class="admin-form-group">
                                    <label for="editPrix"><i class="fas fa-euro-sign"></i> Prix (€)</label>
                                    <input type="number" id="editPrix" name="prix" step="0.01" min="0" value="${voyage.prix}" required>
                                </div>
                            </div>
                            <div class="col-2">
                                <div class="admin-form-group">
                                    <label for="editPlacesDisponibles"><i class="fas fa-users"></i> Places Disponibles</label>
                                    <input type="number" id="editPlacesDisponibles" name="placesDisponibles" min="0" value="${voyage.placesDisponibles}" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-4">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save"></i> Mettre à Jour
                                </button>
                                <a href="${pageContext.request.contextPath}/admin/voyages" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Annuler
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </c:if>

            <!-- Actions rapides -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title"><i class="fas fa-bolt"></i> Actions Rapides</h2>
                </div>

                <div class="row">
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/admin/dashboard" class="btn btn-secondary btn-full">
                            <i class="fas fa-arrow-left"></i> Retour Dashboard
                        </a>
                    </div>
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/admin/voyages" class="btn btn-primary btn-full">
                            <i class="fas fa-sync"></i> Actualiser
                        </a>
                    </div>
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/admin/trajets" class="btn btn-warning btn-full">
                            <i class="fas fa-road"></i> Gérer Trajets
                        </a>
                    </div>
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/admin/promotions" class="btn btn-success btn-full">
                            <i class="fas fa-tags"></i> Gérer Promotions
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>