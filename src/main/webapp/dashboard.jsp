<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🛠️ Tableau de Bord Admin - JEE Training</title>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <a href="${pageContext.request.contextPath}/recherche" class="logo">
                <i class="fas fa-train"></i> JEE Training
            </a>
            <div class="nav-links">
                <c:choose>
                    <c:when test="${sessionScope.user != null}">
                        <a href="${pageContext.request.contextPath}/recherche"><i class="fas fa-search"></i> Rechercher</a>
                        <c:if test="${sessionScope.user.role == 'admin'}">
                            <a href="${pageContext.request.contextPath}/admin/promotions"><i class="fas fa-cog"></i> Admin</a>
                        </c:if>
                        <a href="${pageContext.request.contextPath}/logout"><i class="fas fa-sign-out-alt"></i> Déconnexion</a>
                    </c:when>
                    <c:otherwise>
                        <a href="${pageContext.request.contextPath}/login.jsp"><i class="fas fa-sign-in-alt"></i> Connexion</a>
                        <a href="${pageContext.request.contextPath}/register.jsp"><i class="fas fa-user-plus"></i> Inscription</a>
                    </c:otherwise>
                </c:choose>
            </div>
        </div>
    </nav>

    <!-- Contenu principal -->
    <div class="container">
        <div class="main-content">
            <h1><i class="fas fa-tachometer-alt"></i> Tableau de Bord Administrateur</h1>

            <!-- Message de bienvenue -->
            <div class="alert alert-info">
                <i class="fas fa-user-shield"></i>
                <strong>Bienvenue, ${sessionScope.user.username} !</strong>
                <br>Vous avez accès à tous les outils d'administration de JEE Training.
            </div>

            <!-- Statistiques rapides -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title"><i class="fas fa-chart-bar"></i> Statistiques & Rapports</h2>
                </div>

                <div class="row">
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/admin/popular-trajets" class="admin-card">
                            <div class="admin-card-icon">
                                <i class="fas fa-route"></i>
                            </div>
                            <div class="admin-card-content">
                                <h3>Trajets Populaires</h3>
                                <p>Analysez les trajets les plus demandés</p>
                            </div>
                            <div class="admin-card-arrow">
                                <i class="fas fa-arrow-right"></i>
                            </div>
                        </a>
                    </div>
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/admin/revenue" class="admin-card">
                            <div class="admin-card-icon">
                                <i class="fas fa-euro-sign"></i>
                            </div>
                            <div class="admin-card-content">
                                <h3>Revenus Générés</h3>
                                <p>Consultez les revenus par période</p>
                            </div>
                            <div class="admin-card-arrow">
                                <i class="fas fa-arrow-right"></i>
                            </div>
                        </a>
                    </div>
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/admin/reservation-evolution" class="admin-card">
                            <div class="admin-card-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="admin-card-content">
                                <h3>Évolution Réservations</h3>
                                <p>Suivez l'évolution des réservations</p>
                            </div>
                            <div class="admin-card-arrow">
                                <i class="fas fa-arrow-right"></i>
                            </div>
                        </a>
                    </div>
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/admin/users" class="admin-card">
                            <div class="admin-card-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="admin-card-content">
                                <h3>Gestion Utilisateurs</h3>
                                <p>Gérez les comptes utilisateurs</p>
                            </div>
                            <div class="admin-card-arrow">
                                <i class="fas fa-arrow-right"></i>
                            </div>
                        </a>
                    </div>
                </div>

                <div class="row">
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/admin/reservations" class="admin-card">
                            <div class="admin-card-icon">
                                <i class="fas fa-ticket-alt"></i>
                            </div>
                            <div class="admin-card-content">
                                <h3>Voir Réservations</h3>
                                <p>Consultez toutes les réservations</p>
                            </div>
                            <div class="admin-card-arrow">
                                <i class="fas fa-arrow-right"></i>
                            </div>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Gestion des données -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title"><i class="fas fa-database"></i> Gestion des Données</h2>
                </div>

                <div class="row">
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/admin/stations" class="admin-card">
                            <div class="admin-card-icon">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <div class="admin-card-content">
                                <h3>Gares & Stations</h3>
                                <p>Ajoutez et modifiez les gares</p>
                            </div>
                            <div class="admin-card-arrow">
                                <i class="fas fa-arrow-right"></i>
                            </div>
                        </a>
                    </div>
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/admin/trajets" class="admin-card">
                            <div class="admin-card-icon">
                                <i class="fas fa-road"></i>
                            </div>
                            <div class="admin-card-content">
                                <h3>Trajets</h3>
                                <p>Configurez les trajets disponibles</p>
                            </div>
                            <div class="admin-card-arrow">
                                <i class="fas fa-arrow-right"></i>
                            </div>
                        </a>
                    </div>
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/admin/voyages" class="admin-card">
                            <div class="admin-card-icon">
                                <i class="fas fa-train"></i>
                            </div>
                            <div class="admin-card-content">
                                <h3>Voyages</h3>
                                <p>Planifiez les voyages et horaires</p>
                            </div>
                            <div class="admin-card-arrow">
                                <i class="fas fa-arrow-right"></i>
                            </div>
                        </a>
                    </div>
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/admin/promotions" class="admin-card">
                            <div class="admin-card-icon">
                                <i class="fas fa-tags"></i>
                            </div>
                            <div class="admin-card-content">
                                <h3>Promotions</h3>
                                <p>Créez et gérez les offres spéciales</p>
                            </div>
                            <div class="admin-card-arrow">
                                <i class="fas fa-arrow-right"></i>
                            </div>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Actions rapides -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title"><i class="fas fa-bolt"></i> Actions Rapides</h2>
                </div>

                <div class="row">
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/admin/annuler" class="btn btn-warning btn-full">
                            <i class="fas fa-times-circle"></i> Demandes d'Annulation
                        </a>
                    </div>
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/recherche" class="btn btn-primary btn-full">
                            <i class="fas fa-search"></i> Rechercher un Trajet
                        </a>
                    </div>
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/diagnostic" class="btn btn-secondary btn-full">
                            <i class="fas fa-stethoscope"></i> Diagnostic Système
                        </a>
                    </div>
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/admin/reservation-evolution" class="btn btn-info btn-full">
                            <i class="fas fa-chart-line"></i> Évolution Réservations
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>