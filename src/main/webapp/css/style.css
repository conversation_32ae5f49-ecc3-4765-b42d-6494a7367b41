/* ===== VARIABLES CSS ===== */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --accent-color: #e74c3c;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --light-gray: #ecf0f1;
    --dark-gray: #7f8c8d;
    --white: #ffffff;
    --shadow: 0 2px 10px rgba(0,0,0,0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

/* ===== RESET ET BASE ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--primary-color);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

/* ===== CONTENEUR PRINCIPAL ===== */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.main-content {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 30px;
    margin: 20px 0;
}

/* ===== NAVIGATION ===== */
.navbar {
    background: var(--white);
    box-shadow: var(--shadow);
    padding: 15px 0;
    margin-bottom: 20px;
}

.navbar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.navbar .logo {
    font-size: 24px;
    font-weight: bold;
    color: var(--primary-color);
    text-decoration: none;
}

.navbar .nav-links {
    display: flex;
    gap: 20px;
    align-items: center;
}

.navbar .nav-links a {
    color: var(--primary-color);
    text-decoration: none;
    padding: 8px 16px;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.navbar .nav-links a:hover {
    background: var(--secondary-color);
    color: var(--white);
}

/* ===== TITRES ===== */
h1, h2, h3, h4, h5, h6 {
    margin-bottom: 20px;
    color: var(--primary-color);
}

h1 {
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 30px;
    background: linear-gradient(45deg, var(--secondary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

h2 {
    font-size: 2rem;
    border-bottom: 3px solid var(--secondary-color);
    padding-bottom: 10px;
    margin-bottom: 25px;
}

/* ===== FORMULAIRES ===== */
.form-container {
    max-width: 500px;
    margin: 0 auto;
    background: var(--white);
    padding: 40px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--primary-color);
}

.form-control {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid var(--light-gray);
    border-radius: var(--border-radius);
    font-size: 16px;
    transition: var(--transition);
    background: var(--white);
}

.form-control:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.form-control:hover {
    border-color: var(--secondary-color);
}

/* ===== BOUTONS ===== */
.btn {
    display: inline-block;
    padding: 12px 24px;
    border: none;
    border-radius: var(--border-radius);
    font-size: 16px;
    font-weight: 600;
    text-decoration: none;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
    margin: 5px;
}

.btn-primary {
    background: var(--secondary-color);
    color: var(--white);
}

.btn-primary:hover {
    background: #2980b9;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.btn-success {
    background: var(--success-color);
    color: var(--white);
}

.btn-success:hover {
    background: #229954;
    transform: translateY(-2px);
}

.btn-danger {
    background: var(--accent-color);
    color: var(--white);
}

.btn-danger:hover {
    background: #c0392b;
    transform: translateY(-2px);
}

.btn-secondary {
    background: var(--dark-gray);
    color: var(--white);
}

.btn-secondary:hover {
    background: #6c7b7d;
    transform: translateY(-2px);
}

.btn-full {
    width: 100%;
}

/* ===== TABLEAUX ===== */
.table-container {
    overflow-x: auto;
    margin: 20px 0;
}

.table {
    width: 100%;
    border-collapse: collapse;
    background: var(--white);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow);
}

.table th,
.table td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid var(--light-gray);
}

.table th {
    background: var(--primary-color);
    color: var(--white);
    font-weight: 600;
    text-transform: uppercase;
    font-size: 14px;
    letter-spacing: 1px;
}

.table tr:hover {
    background: #f8f9fa;
}

.table tr:last-child td {
    border-bottom: none;
}

/* ===== ALERTES ===== */
.alert {
    padding: 15px 20px;
    border-radius: var(--border-radius);
    margin: 20px 0;
    border-left: 4px solid;
    font-weight: 500;
}

.alert-success {
    background: #d4edda;
    color: #155724;
    border-left-color: var(--success-color);
}

.alert-error {
    background: #f8d7da;
    color: #721c24;
    border-left-color: var(--accent-color);
}

.alert-info {
    background: #d1ecf1;
    color: #0c5460;
    border-left-color: var(--secondary-color);
}

.alert-warning {
    background: #fff3cd;
    color: #856404;
    border-left-color: var(--warning-color);
}

/* ===== CARTES ===== */
.card {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 25px;
    margin: 20px 0;
    transition: var(--transition);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.card-header {
    border-bottom: 2px solid var(--light-gray);
    padding-bottom: 15px;
    margin-bottom: 20px;
}

.card-title {
    margin: 0;
    color: var(--primary-color);
    font-size: 1.5rem;
}

/* ===== GRILLE ===== */
.row {
    display: flex;
    flex-wrap: wrap;
    margin: -10px;
}

.col {
    flex: 1;
    padding: 10px;
}

.col-2 {
    flex: 0 0 50%;
    padding: 10px;
}

.col-3 {
    flex: 0 0 33.333%;
    padding: 10px;
}

.col-4 {
    flex: 0 0 25%;
    padding: 10px;
}

/* ===== UTILITAIRES ===== */
.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.mt-20 {
    margin-top: 20px;
}

.mb-20 {
    margin-bottom: 20px;
}

.p-20 {
    padding: 20px;
}

.promotion {
    color: var(--success-color);
    font-weight: bold;
}

.price-original {
    text-decoration: line-through;
    color: var(--dark-gray);
}

.price-discount {
    color: var(--accent-color);
    font-weight: bold;
}

.price-current {
    font-size: 1.2rem;
    font-weight: bold;
    color: var(--primary-color);
}

/* ===== BADGES ===== */
.badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-success {
    background: var(--success-color);
    color: var(--white);
}

.badge-warning {
    background: var(--warning-color);
    color: var(--white);
}

.badge-danger {
    background: var(--accent-color);
    color: var(--white);
}

.badge-info {
    background: var(--secondary-color);
    color: var(--white);
}

/* ===== TAGS DE PROMOTION ===== */
.promotion-tag {
    display: inline-block;
    background: linear-gradient(45deg, var(--success-color), #2ecc71);
    color: var(--white);
    padding: 4px 8px;
    border-radius: 15px;
    font-size: 11px;
    margin: 2px;
    font-weight: 600;
}

.promotion-tag i {
    margin-right: 3px;
}

/* ===== TEXTE MUTED ===== */
.text-muted {
    color: var(--dark-gray);
    font-size: 0.9rem;
}

/* ===== ANIMATIONS ===== */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card {
    animation: fadeIn 0.5s ease-out;
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background: #f8f9fa;
    transform: scale(1.01);
}

/* ===== AMÉLIORATIONS FORMULAIRE ===== */
.form-control:focus {
    transform: translateY(-2px);
}

.btn:active {
    transform: translateY(0) scale(0.98);
}

/* ===== LOADER ===== */
.loader {
    border: 3px solid var(--light-gray);
    border-top: 3px solid var(--secondary-color);
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
    margin: 20px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== STYLES SPÉCIAUX POUR SÉLECTION ET CONFIRMATION ===== */

/* Route de voyage */
.travel-route {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: var(--border-radius);
    margin: 15px 0;
}

.station-info {
    text-align: center;
    flex: 1;
}

.station-info i {
    font-size: 1.5rem;
    color: var(--secondary-color);
    margin-bottom: 5px;
    display: block;
}

.route-arrow {
    margin: 0 20px;
    font-size: 1.5rem;
    color: var(--secondary-color);
}

.travel-details p {
    margin: 8px 0;
    padding: 5px 0;
    border-bottom: 1px solid var(--light-gray);
}

.travel-details p:last-child {
    border-bottom: none;
}

/* Grille de préférences */
.preferences-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 10px;
}

.preference-item {
    display: flex;
    align-items: center;
    padding: 10px;
    background: var(--light-gray);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.preference-item:hover {
    background: #ddd;
}

.preference-item input[type="checkbox"] {
    margin-right: 10px;
    transform: scale(1.2);
}

.preference-item label {
    margin: 0;
    cursor: pointer;
    font-weight: 500;
}

.preference-item label i {
    margin-right: 5px;
    color: var(--secondary-color);
}

/* Tags de préférences dans confirmation */
.preferences-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 10px;
}

.preference-tag {
    display: inline-block;
    background: linear-gradient(45deg, var(--secondary-color), #5dade2);
    color: var(--white);
    padding: 4px 10px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
}

.preference-tag i {
    margin-right: 3px;
}

/* Détails de réservation */
.reservation-details p {
    margin: 12px 0;
    padding: 8px 0;
    border-bottom: 1px solid var(--light-gray);
}

.reservation-details p:last-child {
    border-bottom: none;
}

/* Animations pour les pages de confirmation */
@keyframes successPulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(39, 174, 96, 0.7);
    }
    70% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(39, 174, 96, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(39, 174, 96, 0);
    }
}

.alert-success {
    animation: successPulse 2s ease-out;
}

/* Styles d'impression */
@media print {
    .navbar,
    .btn,
    .nav-links {
        display: none !important;
    }

    .main-content {
        box-shadow: none;
        padding: 0;
    }

    .card {
        box-shadow: none;
        border: 1px solid #ddd;
        page-break-inside: avoid;
    }

    .alert {
        border: 1px solid #ddd;
        box-shadow: none;
    }
}

/* ===== STYLES ADMIN ===== */

/* Cartes admin */
.admin-card {
    display: flex;
    align-items: center;
    padding: 20px;
    background: linear-gradient(135deg, var(--white), #f8f9fa);
    border: 2px solid var(--light-gray);
    border-radius: var(--border-radius);
    text-decoration: none;
    color: var(--text-color);
    transition: var(--transition);
    margin-bottom: 15px;
    position: relative;
    overflow: hidden;
}

.admin-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    border-color: var(--primary-color);
    text-decoration: none;
    color: var(--text-color);
}

.admin-card-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    flex-shrink: 0;
}

.admin-card-icon i {
    font-size: 24px;
    color: var(--white);
}

.admin-card-content {
    flex: 1;
}

.admin-card-content h3 {
    margin: 0 0 5px 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--text-color);
}

.admin-card-content p {
    margin: 0;
    color: var(--text-muted);
    font-size: 14px;
}

.admin-card-arrow {
    margin-left: 15px;
    opacity: 0.5;
    transition: var(--transition);
}

.admin-card:hover .admin-card-arrow {
    opacity: 1;
    transform: translateX(5px);
}

.admin-card-arrow i {
    font-size: 18px;
    color: var(--primary-color);
}

/* Tables admin */
.admin-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
    background: var(--white);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow);
}

.admin-table th {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--white);
    padding: 15px;
    text-align: left;
    font-weight: 600;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.admin-table td {
    padding: 15px;
    border-bottom: 1px solid var(--light-gray);
    vertical-align: middle;
}

.admin-table tr:hover {
    background-color: #f8f9fa;
}

.admin-table tr:last-child td {
    border-bottom: none;
}

/* Boutons admin */
.admin-btn {
    display: inline-block;
    padding: 8px 15px;
    margin: 2px;
    border: none;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: var(--transition);
    cursor: pointer;
}

.admin-btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--white);
}

.admin-btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.4);
    color: var(--white);
    text-decoration: none;
}

.admin-btn-success {
    background: linear-gradient(135deg, var(--success-color), #27ae60);
    color: var(--white);
}

.admin-btn-success:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(39, 174, 96, 0.4);
    color: var(--white);
    text-decoration: none;
}

.admin-btn-danger {
    background: linear-gradient(135deg, var(--danger-color), #c0392b);
    color: var(--white);
}

.admin-btn-danger:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.4);
    color: var(--white);
    text-decoration: none;
}

.admin-btn-warning {
    background: linear-gradient(135deg, var(--warning-color), #d68910);
    color: var(--white);
}

.admin-btn-warning:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(241, 196, 15, 0.4);
    color: var(--white);
    text-decoration: none;
}

/* Formulaires admin */
.admin-form {
    background: var(--white);
    padding: 30px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-bottom: 30px;
}

.admin-form-group {
    margin-bottom: 20px;
}

.admin-form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--text-color);
}

.admin-form-group input,
.admin-form-group select,
.admin-form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid var(--light-gray);
    border-radius: var(--border-radius);
    font-size: 14px;
    transition: var(--transition);
}

.admin-form-group input:focus,
.admin-form-group select:focus,
.admin-form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

/* Messages admin */
.admin-message {
    padding: 15px 20px;
    border-radius: var(--border-radius);
    margin-bottom: 20px;
    font-weight: 500;
}

.admin-message-success {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    color: #155724;
    border-left: 4px solid var(--success-color);
}

.admin-message-error {
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    color: #721c24;
    border-left: 4px solid var(--danger-color);
}

.admin-message-warning {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    color: #856404;
    border-left: 4px solid var(--warning-color);
}

.admin-message-info {
    background: linear-gradient(135deg, #d1ecf1, #bee5eb);
    color: #0c5460;
    border-left: 4px solid var(--info-color);
}

/* Breadcrumb */
.breadcrumb {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
    padding: 15px 20px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: var(--border-radius);
    font-size: 14px;
}

.breadcrumb a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
}

.breadcrumb a:hover {
    color: var(--secondary-color);
    text-decoration: none;
}

.breadcrumb span {
    margin: 0 10px;
    color: var(--text-muted);
}

/* Empty state */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: var(--text-muted);
}

.empty-state i {
    font-size: 64px;
    margin-bottom: 20px;
    opacity: 0.5;
}

.empty-state h3 {
    margin-bottom: 10px;
    color: var(--text-color);
}

/* User info */
.user-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.user-info i {
    color: var(--primary-color);
    font-size: 18px;
}

/* Revenue styles */
.revenue-results {
    padding: 30px;
}

.revenue-period {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 30px;
    padding: 15px;
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    border-radius: var(--border-radius);
    color: #1565c0;
}

.revenue-period i {
    font-size: 20px;
}

.revenue-total {
    text-align: center;
    margin-bottom: 30px;
}

.revenue-amount {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    margin-bottom: 10px;
}

.revenue-amount i {
    font-size: 32px;
    color: var(--warning-color);
}

.revenue-amount .amount {
    font-size: 48px;
    font-weight: 700;
    color: var(--success-color);
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

.revenue-total p {
    font-size: 18px;
    color: var(--text-muted);
    margin: 0;
}

.revenue-stats {
    display: flex;
    justify-content: center;
    gap: 20px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 15px 20px;
    background: linear-gradient(135deg, #f1f8e9, #dcedc8);
    border-radius: var(--border-radius);
    color: #33691e;
}

.stat-item i {
    font-size: 20px;
}

/* Stat cards */
.stat-card {
    display: flex;
    align-items: center;
    padding: 20px;
    background: linear-gradient(135deg, var(--white), #f8f9fa);
    border: 2px solid var(--light-gray);
    border-radius: var(--border-radius);
    transition: var(--transition);
    margin-bottom: 15px;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    border-color: var(--primary-color);
}

.stat-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    flex-shrink: 0;
}

.stat-icon i {
    font-size: 20px;
    color: var(--white);
}

.stat-content h3 {
    margin: 0 0 5px 0;
    font-size: 24px;
    font-weight: 700;
    color: var(--text-color);
}

.stat-content p {
    margin: 0;
    color: var(--text-muted);
    font-size: 14px;
}

/* Points badge */
.points-badge {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 5px 10px;
    background: linear-gradient(135deg, var(--warning-color), #f39c12);
    color: var(--white);
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
}

/* Rank badges */
.rank-badge {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.rank-gold {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #b8860b;
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

.rank-silver {
    background: linear-gradient(135deg, #c0c0c0, #e5e5e5);
    color: #696969;
    box-shadow: 0 4px 15px rgba(192, 192, 192, 0.3);
}

.rank-bronze {
    background: linear-gradient(135deg, #cd7f32, #daa520);
    color: #8b4513;
    box-shadow: 0 4px 15px rgba(205, 127, 50, 0.3);
}

.rank-badge:not(.rank-gold):not(.rank-silver):not(.rank-bronze) {
    background: linear-gradient(135deg, var(--light-gray), #f8f9fa);
    color: var(--text-muted);
}

/* Station info */
.station-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.station-info i {
    color: var(--primary-color);
    font-size: 14px;
}

.station-info small {
    color: var(--text-muted);
    font-size: 11px;
}

/* Reservation count */
.reservation-count {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 5px 10px;
    background: linear-gradient(135deg, var(--info-color), #17a2b8);
    color: var(--white);
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
}

/* Popularity bar */
.popularity-bar {
    position: relative;
    width: 100px;
    height: 20px;
    background: var(--light-gray);
    border-radius: 10px;
    overflow: hidden;
}

.popularity-fill {
    height: 100%;
    background: linear-gradient(135deg, var(--success-color), #27ae60);
    border-radius: 10px;
    transition: width 0.8s ease-out;
}

.popularity-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 10px;
    font-weight: 600;
    color: var(--white);
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    .main-content {
        padding: 20px;
    }

    .navbar .container {
        flex-direction: column;
        gap: 15px;
    }

    .navbar .nav-links {
        flex-wrap: wrap;
        justify-content: center;
    }

    .row {
        flex-direction: column;
    }

    .col,
    .col-2,
    .col-3,
    .col-4 {
        flex: 1 1 100%;
    }

    h1 {
        font-size: 2rem;
    }

    .form-container {
        padding: 20px;
    }
}
