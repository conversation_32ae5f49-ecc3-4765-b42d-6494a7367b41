<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎫 Mes Réservations - JEE Training</title>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <a href="${pageContext.request.contextPath}/recherche" class="logo">
                <i class="fas fa-train"></i> JEE Training
            </a>
            <div class="nav-links">
                <c:choose>
                    <c:when test="${sessionScope.user != null}">
                        <a href="${pageContext.request.contextPath}/recherche"><i class="fas fa-search"></i> Rechercher</a>
                        <c:if test="${sessionScope.user.role == 'admin'}">
                            <a href="${pageContext.request.contextPath}/admin/dashboard"><i class="fas fa-cog"></i> Admin</a>
                        </c:if>
                        <a href="${pageContext.request.contextPath}/logout"><i class="fas fa-sign-out-alt"></i> Déconnexion</a>
                    </c:when>
                    <c:otherwise>
                        <a href="${pageContext.request.contextPath}/login.jsp"><i class="fas fa-sign-in-alt"></i> Connexion</a>
                        <a href="${pageContext.request.contextPath}/register.jsp"><i class="fas fa-user-plus"></i> Inscription</a>
                    </c:otherwise>
                </c:choose>
            </div>
        </div>
    </nav>

    <!-- Contenu principal -->
    <div class="container">
        <div class="main-content">
            <h1><i class="fas fa-ticket-alt"></i> Mes Réservations</h1>

            <!-- Breadcrumb -->
            <div class="breadcrumb">
                <a href="${pageContext.request.contextPath}/recherche"><i class="fas fa-search"></i> Recherche</a>
                <span><i class="fas fa-chevron-right"></i></span>
                <a href="${pageContext.request.contextPath}/monCompte"><i class="fas fa-user"></i> Mon Compte</a>
                <span><i class="fas fa-chevron-right"></i></span>
                <span>Mes Réservations</span>
            </div>

            <!-- Messages -->
            <c:if test="${not empty success}">
                <div class="admin-message admin-message-success">
                    <i class="fas fa-check-circle"></i> ${success}
                </div>
            </c:if>
            <c:if test="${not empty error}">
                <div class="admin-message admin-message-error">
                    <i class="fas fa-exclamation-triangle"></i> ${error}
                </div>
            </c:if>

            <!-- Statistiques rapides -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title"><i class="fas fa-chart-pie"></i> Aperçu de vos Réservations</h2>
                </div>

                <div class="row">
                    <div class="col-4">
                        <div class="stat-card stat-primary">
                            <div class="stat-icon">
                                <i class="fas fa-ticket-alt"></i>
                            </div>
                            <div class="stat-content">
                                <h3>${reservations.size()}</h3>
                                <p>Total Réservations</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="stat-card stat-success">
                            <div class="stat-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="stat-content">
                                <h3>
                                    <c:set var="confirmees" value="0"/>
                                    <c:forEach var="reservation" items="${reservations}">
                                        <c:if test="${reservation.etat == 'confirmée'}">
                                            <c:set var="confirmees" value="${confirmees + 1}"/>
                                        </c:if>
                                    </c:forEach>
                                    ${confirmees}
                                </h3>
                                <p>Confirmées</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="stat-card stat-warning">
                            <div class="stat-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="stat-content">
                                <h3>
                                    <c:set var="enAttente" value="0"/>
                                    <c:forEach var="reservation" items="${reservations}">
                                        <c:if test="${reservation.etat == 'en attente'}">
                                            <c:set var="enAttente" value="${enAttente + 1}"/>
                                        </c:if>
                                    </c:forEach>
                                    ${enAttente}
                                </h3>
                                <p>En Attente</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Liste des réservations -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title"><i class="fas fa-list"></i> Vos Réservations Actives</h2>
                </div>

                <c:if test="${not empty reservations}">
                    <table class="admin-table">
                        <thead>
                            <tr>
                                <th><i class="fas fa-hashtag"></i> ID</th>
                                <th><i class="fas fa-route"></i> Trajet</th>
                                <th><i class="fas fa-calendar-alt"></i> Voyage</th>
                                <th><i class="fas fa-star"></i> Classe</th>
                                <th><i class="fas fa-info-circle"></i> État</th>
                                <th><i class="fas fa-cogs"></i> Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <c:forEach var="reservation" items="${reservations}">
                                <tr>
                                    <td><strong>#${reservation.id}</strong></td>
                                    <td>
                                        <div class="route-info">
                                            <div class="departure">
                                                <i class="fas fa-play"></i> ${reservation.voyage.trajet.departStation.name}
                                                <small>(${reservation.voyage.trajet.departStation.city})</small>
                                            </div>
                                            <div class="arrival">
                                                <i class="fas fa-stop"></i> ${reservation.voyage.trajet.arrivalStation.name}
                                                <small>(${reservation.voyage.trajet.arrivalStation.city})</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="voyage-details">
                                            <div class="time-info">
                                                <i class="fas fa-clock"></i> ${reservation.voyage.heureDepartFormatted}
                                            </div>
                                            <div class="price-info">
                                                <i class="fas fa-euro-sign"></i> ${reservation.voyage.prix}€
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="classe-badge classe-${reservation.classe}">
                                            <i class="fas fa-star"></i> ${reservation.classe}
                                        </span>
                                    </td>
                                    <td>
                                        <c:choose>
                                            <c:when test="${reservation.etat == 'confirmée'}">
                                                <span class="badge badge-success">
                                                    <i class="fas fa-check-circle"></i> Confirmée
                                                </span>
                                            </c:when>
                                            <c:when test="${reservation.etat == 'en attente'}">
                                                <span class="badge badge-warning">
                                                    <i class="fas fa-clock"></i> En Attente
                                                </span>
                                            </c:when>
                                            <c:when test="${reservation.etat == 'annulée'}">
                                                <span class="badge badge-danger">
                                                    <i class="fas fa-times-circle"></i> Annulée
                                                </span>
                                            </c:when>
                                            <c:otherwise>
                                                <span class="badge badge-secondary">
                                                    <i class="fas fa-question-circle"></i> ${reservation.etat}
                                                </span>
                                            </c:otherwise>
                                        </c:choose>
                                    </td>
                                    <td>
                                        <c:if test="${reservation.etat == 'confirmée'}">
                                            <a href="${pageContext.request.contextPath}/modifierReservation?id=${reservation.id}"
                                               class="admin-btn admin-btn-warning admin-btn-sm">
                                                <i class="fas fa-edit"></i> Modifier
                                            </a>
                                            <a href="${pageContext.request.contextPath}/download-ticket?reservationId=${reservation.id}"
                                               class="admin-btn admin-btn-success admin-btn-sm">
                                                <i class="fas fa-file-pdf"></i> PDF
                                            </a>
                                        </c:if>
                                        <c:if test="${reservation.etat != 'annulée'}">
                                            <a href="${pageContext.request.contextPath}/annuler-reservation?id=${reservation.id}"
                                               class="admin-btn admin-btn-danger admin-btn-sm"
                                               onclick="return confirm('Êtes-vous sûr de vouloir demander l\'annulation de cette réservation ?');">
                                                <i class="fas fa-times"></i> Annuler
                                            </a>
                                        </c:if>
                                    </td>
                                </tr>
                            </c:forEach>
                        </tbody>
                    </table>
                </c:if>

                <c:if test="${empty reservations}">
                    <div class="empty-state">
                        <i class="fas fa-ticket-alt"></i>
                        <h3>Aucune réservation trouvée</h3>
                        <p>Vous n'avez pas encore effectué de réservation.</p>
                        <a href="${pageContext.request.contextPath}/recherche" class="btn btn-primary">
                            <i class="fas fa-search"></i> Rechercher un Voyage
                        </a>
                    </div>
                </c:if>
            </div>

            <!-- Actions rapides -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title"><i class="fas fa-bolt"></i> Actions Rapides</h2>
                </div>

                <div class="row">
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/recherche" class="btn btn-primary btn-full">
                            <i class="fas fa-search"></i> Nouvelle Recherche
                        </a>
                    </div>
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/historique" class="btn btn-secondary btn-full">
                            <i class="fas fa-history"></i> Historique Voyages
                        </a>
                    </div>
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/monCompte" class="btn btn-warning btn-full">
                            <i class="fas fa-user"></i> Mon Compte
                        </a>
                    </div>
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/mes-reservations" class="btn btn-success btn-full">
                            <i class="fas fa-sync"></i> Actualiser
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
