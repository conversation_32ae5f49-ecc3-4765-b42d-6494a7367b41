<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>❌ Annuler Réservation - JEE Training</title>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <a href="${pageContext.request.contextPath}/recherche" class="logo">
                <i class="fas fa-train"></i> JEE Training
            </a>
            <div class="nav-links">
                <c:choose>
                    <c:when test="${sessionScope.user != null}">
                        <a href="${pageContext.request.contextPath}/recherche"><i class="fas fa-search"></i> Rechercher</a>
                        <c:if test="${sessionScope.user.role == 'admin'}">
                            <a href="${pageContext.request.contextPath}/admin/dashboard"><i class="fas fa-cog"></i> Admin</a>
                        </c:if>
                        <a href="${pageContext.request.contextPath}/logout"><i class="fas fa-sign-out-alt"></i> Déconnexion</a>
                    </c:when>
                    <c:otherwise>
                        <a href="${pageContext.request.contextPath}/login.jsp"><i class="fas fa-sign-in-alt"></i> Connexion</a>
                        <a href="${pageContext.request.contextPath}/register.jsp"><i class="fas fa-user-plus"></i> Inscription</a>
                    </c:otherwise>
                </c:choose>
            </div>
        </div>
    </nav>

    <!-- Contenu principal -->
    <div class="container">
        <div class="main-content">
            <h1><i class="fas fa-times-circle"></i> Demande d'Annulation</h1>
            
            <!-- Breadcrumb -->
            <div class="breadcrumb">
                <a href="${pageContext.request.contextPath}/recherche"><i class="fas fa-search"></i> Recherche</a>
                <span><i class="fas fa-chevron-right"></i></span>
                <a href="${pageContext.request.contextPath}/mes-reservations"><i class="fas fa-ticket-alt"></i> Mes Réservations</a>
                <span><i class="fas fa-chevron-right"></i></span>
                <span>Annuler Réservation</span>
            </div>

            <!-- Messages -->
            <c:if test="${not empty success}">
                <div class="admin-message admin-message-success">
                    <i class="fas fa-check-circle"></i> ${success}
                </div>
            </c:if>
            <c:if test="${not empty error}">
                <div class="admin-message admin-message-error">
                    <i class="fas fa-exclamation-triangle"></i> ${error}
                </div>
            </c:if>

            <c:choose>
                <c:when test="${not empty reservation}">
                    <!-- Détails de la réservation à annuler -->
                    <div class="card">
                        <div class="card-header">
                            <h2 class="card-title"><i class="fas fa-info-circle"></i> Détails de la Réservation</h2>
                        </div>
                        
                        <div class="reservation-details">
                            <div class="row">
                                <div class="col-2">
                                    <div class="detail-item">
                                        <label><i class="fas fa-hashtag"></i> ID Réservation</label>
                                        <span class="detail-value">#${reservation.id}</span>
                                    </div>
                                </div>
                                <div class="col-2">
                                    <div class="detail-item">
                                        <label><i class="fas fa-route"></i> Trajet</label>
                                        <span class="detail-value">
                                            ${reservation.voyage.trajet.departStation.name} → ${reservation.voyage.trajet.arrivalStation.name}
                                        </span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-2">
                                    <div class="detail-item">
                                        <label><i class="fas fa-clock"></i> Heure de Départ</label>
                                        <span class="detail-value">${reservation.voyage.heureDepartFormatted}</span>
                                    </div>
                                </div>
                                <div class="col-2">
                                    <div class="detail-item">
                                        <label><i class="fas fa-star"></i> Classe</label>
                                        <span class="classe-badge classe-${reservation.classe}">
                                            <i class="fas fa-star"></i> ${reservation.classe}
                                        </span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-2">
                                    <div class="detail-item">
                                        <label><i class="fas fa-euro-sign"></i> Prix</label>
                                        <span class="detail-value price-highlight">${reservation.voyage.prix}€</span>
                                    </div>
                                </div>
                                <div class="col-2">
                                    <div class="detail-item">
                                        <label><i class="fas fa-info-circle"></i> État</label>
                                        <span class="badge badge-warning">
                                            <i class="fas fa-clock"></i> ${reservation.etat}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Avertissement -->
                    <div class="card">
                        <div class="card-header">
                            <h2 class="card-title"><i class="fas fa-exclamation-triangle"></i> Conditions d'Annulation</h2>
                        </div>
                        
                        <div class="warning-box">
                            <div class="warning-item">
                                <i class="fas fa-info-circle"></i>
                                <span>Votre demande d'annulation sera soumise à l'approbation de l'administrateur.</span>
                            </div>
                            <div class="warning-item">
                                <i class="fas fa-clock"></i>
                                <span>Le traitement peut prendre 24 à 48 heures ouvrables.</span>
                            </div>
                            <div class="warning-item">
                                <i class="fas fa-euro-sign"></i>
                                <span>Le remboursement sera effectué selon nos conditions générales.</span>
                            </div>
                            <div class="warning-item">
                                <i class="fas fa-ban"></i>
                                <span>Une fois la demande soumise, elle ne peut plus être annulée.</span>
                            </div>
                        </div>
                    </div>

                    <!-- Formulaire de confirmation -->
                    <div class="card">
                        <div class="card-header">
                            <h2 class="card-title"><i class="fas fa-check-square"></i> Confirmation d'Annulation</h2>
                        </div>
                        
                        <form action="${pageContext.request.contextPath}/annuler-reservation" method="post" class="admin-form">
                            <input type="hidden" name="id" value="${reservation.id}">
                            
                            <div class="admin-form-group">
                                <label for="motif"><i class="fas fa-comment"></i> Motif d'annulation (optionnel)</label>
                                <textarea id="motif" name="motif" rows="4" placeholder="Veuillez indiquer la raison de votre annulation..."></textarea>
                                <small class="form-text">Ce motif aidera l'administrateur à traiter votre demande plus rapidement.</small>
                            </div>
                            
                            <div class="admin-form-group">
                                <label class="checkbox-container">
                                    <input type="checkbox" name="confirmation" value="true" required>
                                    <span class="checkmark"></span>
                                    J'ai lu et j'accepte les conditions d'annulation
                                </label>
                            </div>
                            
                            <div class="form-actions">
                                <button type="submit" class="btn btn-danger" 
                                        onclick="return confirm('Êtes-vous absolument sûr de vouloir demander l\'annulation de cette réservation ?');">
                                    <i class="fas fa-times-circle"></i> Confirmer la Demande d'Annulation
                                </button>
                                <a href="${pageContext.request.contextPath}/mes-reservations" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left"></i> Retour à Mes Réservations
                                </a>
                            </div>
                        </form>
                    </div>
                </c:when>
                <c:otherwise>
                    <!-- Sélection de réservation -->
                    <div class="card">
                        <div class="card-header">
                            <h2 class="card-title"><i class="fas fa-list"></i> Sélectionner une Réservation à Annuler</h2>
                        </div>
                        
                        <c:if test="${not empty reservations}">
                            <table class="admin-table">
                                <thead>
                                    <tr>
                                        <th><i class="fas fa-hashtag"></i> ID</th>
                                        <th><i class="fas fa-route"></i> Trajet</th>
                                        <th><i class="fas fa-clock"></i> Départ</th>
                                        <th><i class="fas fa-star"></i> Classe</th>
                                        <th><i class="fas fa-info-circle"></i> État</th>
                                        <th><i class="fas fa-cogs"></i> Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <c:forEach var="res" items="${reservations}">
                                        <c:if test="${res.etat != 'annulée' && res.etat != 'demande_annulation'}">
                                            <tr>
                                                <td><strong>#${res.id}</strong></td>
                                                <td>
                                                    <span class="trajet-badge">
                                                        <i class="fas fa-arrow-right"></i>
                                                        ${res.voyage.trajet.departStation.name} → ${res.voyage.trajet.arrivalStation.name}
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="time-info">
                                                        <i class="fas fa-clock"></i> ${res.voyage.heureDepartFormatted}
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="classe-badge classe-${res.classe}">
                                                        <i class="fas fa-star"></i> ${res.classe}
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="badge badge-success">
                                                        <i class="fas fa-check-circle"></i> ${res.etat}
                                                    </span>
                                                </td>
                                                <td>
                                                    <a href="${pageContext.request.contextPath}/annuler-reservation?id=${res.id}" 
                                                       class="admin-btn admin-btn-danger admin-btn-sm">
                                                        <i class="fas fa-times"></i> Demander Annulation
                                                    </a>
                                                </td>
                                            </tr>
                                        </c:if>
                                    </c:forEach>
                                </tbody>
                            </table>
                        </c:if>

                        <c:if test="${empty reservations}">
                            <div class="empty-state">
                                <i class="fas fa-ticket-alt"></i>
                                <h3>Aucune réservation annulable</h3>
                                <p>Vous n'avez pas de réservation pouvant être annulée.</p>
                                <a href="${pageContext.request.contextPath}/mes-reservations" class="btn btn-primary">
                                    <i class="fas fa-ticket-alt"></i> Voir Mes Réservations
                                </a>
                            </div>
                        </c:if>
                    </div>
                </c:otherwise>
            </c:choose>

            <!-- Actions rapides -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title"><i class="fas fa-bolt"></i> Actions Rapides</h2>
                </div>
                
                <div class="row">
                    <div class="col-4">
                        <a href="${pageContext.request.contextPath}/mes-reservations" class="btn btn-primary btn-full">
                            <i class="fas fa-ticket-alt"></i> Mes Réservations
                        </a>
                    </div>
                    <div class="col-4">
                        <a href="${pageContext.request.contextPath}/recherche" class="btn btn-secondary btn-full">
                            <i class="fas fa-search"></i> Nouvelle Recherche
                        </a>
                    </div>
                    <div class="col-4">
                        <a href="${pageContext.request.contextPath}/monCompte" class="btn btn-warning btn-full">
                            <i class="fas fa-user"></i> Mon Compte
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
