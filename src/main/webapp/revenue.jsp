<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Revenus générés</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>Revenus générés</h1>
        <form method="post" action="${pageContext.request.contextPath}/admin/revenue">
            <div class="mb-3">
                <label for="startDate" class="form-label">Date de début</label>
                <input type="datetime-local" class="form-control" id="startDate" name="startDate" required>
            </div>
            <div class="mb-3">
                <label for="endDate" class="form-label">Date de fin</label>
                <input type="datetime-local" class="form-control" id="endDate" name="endDate" required>
            </div>
            <button type="submit" class="btn btn-primary">Calculer</button>
        </form>
        <c:if test="${not empty totalRevenue}">
            <div class="mt-4">
                <p>Période : ${startDate} à ${endDate}</p>
                <p><strong>Revenus totaux : </strong><fmt:formatNumber value="${totalRevenue}" pattern="#,##0.00"/> €</p>
            </div>
        </c:if>
        <a href="${pageContext.request.contextPath}/admin/dashboard" class="btn btn-secondary mt-3">Retour au tableau de bord</a>
    </div>
</body>
</html>