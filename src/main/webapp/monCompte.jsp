<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Mon Compte</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; max-width: 800px; margin-left: auto; margin-right: auto; }
        h2 { color: #333; }
        h3 { color: #555; }
        .section { margin-bottom: 20px; }
        table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        a { color: #007BFF; text-decoration: none; }
        a:hover { text-decoration: underline; }
        .success { color: green; margin-bottom: 15px; }
        .error { color: red; margin-bottom: 15px; }
    </style>
</head>
<body>
<%@ include file="navbar.jsp" %>
    <h2>Mon Co<PERSON>e</h2>
    <c:if test="${not empty success}">
        <p class="success">${success}</p>
    </c:if>
    <c:if test="${not empty error}">
        <p class="error">${error}</p>
    </c:if>

    <c:if test="${sessionScope.user.role == 'admin'}">
        <div class="section">
            <h3>Administration</h3>
            <p><a href="${pageContext.request.contextPath}/admin/annuler">Gérer les demandes d'annulation</a></p>
            
            <h3>Toutes les réservations achetées</h3>
            <c:if test="${not empty allPurchasedReservations}">
                <table>
                    <tr>
                        <th>ID</th>
                        <th>Utilisateur</th>
                        <th>Gare Départ</th>
                        <th>Gare Arrivée</th>
                        <th>État</th>
                        <th>Heure Départ</th>
                        <th>Heure Arrivée</th>
                        <th>Actions</th>
                    </tr>
                    <c:forEach var="reservation" items="${allPurchasedReservations}">
                        <tr>
                            <td>${reservation.id}</td>
                            <td>${reservation.user.username}</td>
                            <td>${reservation.voyage.trajet.departStation.name}</td>
                            <td>${reservation.voyage.trajet.arrivalStation.name}</td>
                            <td>${reservation.etat}</td>
                            <td>${reservation.voyage.heureDepartFormatted}</td>
                            <td>${reservation.voyage.heureArriveeFormatted}</td>
                            <td>
                                <a href="${pageContext.request.contextPath}/modifierReservation?id=${reservation.id}">Modifier</a> |
                                <a href="${pageContext.request.contextPath}/annulerReservation?id=${reservation.id}">Annuler</a> |
                                <a href="${pageContext.request.contextPath}/telechargerBillet?id=${reservation.id}">Télécharger PDF</a>
                            </td>
                        </tr>
                    </c:forEach>
                </table>
            </c:if>
            <c:if test="${empty allPurchasedReservations}">
                <p>Aucune réservation achetée trouvée.</p>
            </c:if>

            <h3>Historique des voyages de tous les utilisateurs</h3>
            <c:if test="${not empty allUsedReservations}">
                <table>
                    <tr>
                        <th>ID</th>
                        <th>Utilisateur</th>
                        <th>Gare Départ</th>
                        <th>Gare Arrivée</th>
                        <th>Date Réservation</th>
                        <th>État</th>
                    </tr>
                    <c:forEach var="reservation" items="${allUsedReservations}">
                        <tr>
                            <td>${reservation.id}</td>
                            <td>${reservation.user.username}</td>
                            <td>${reservation.voyage.trajet.departStation.name}</td>
                            <td>${reservation.voyage.trajet.arrivalStation.name}</td>
                            <td>${reservation.dateReservation}</td>
                            <td>${reservation.etat}</td>
                        </tr>
                    </c:forEach>
                </table>
            </c:if>
            <c:if test="${empty allUsedReservations}">
                <p>Aucun voyage utilisé trouvé.</p>
            </c:if>
        </div>
    </c:if>

    <c:if test="${sessionScope.user.role != 'admin'}">
        <div class="section">
            <h3>Réservations achetées</h3>
            <c:if test="${not empty reservations}">
                <table>
                    <tr>
                        <th>ID</th>
                        <th>Gare Départ</th>
                        <th>Gare Arrivée</th>
                        <th>État</th>
                        <th>Heure Départ</th>
                        <th>Heure Arrivée</th>
                        <th>Actions</th>
                    </tr>
                    <c:forEach var="reservation" items="${reservations}">
                        <c:if test="${reservation.etat == 'acheté'}">
                            <tr>
                                <td>${reservation.id}</td>
                                <td>${reservation.voyage.trajet.departStation.name}</td>
                                <td>${reservation.voyage.trajet.arrivalStation.name}</td>
                                <td>${reservation.etat}</td>
                                <td>${reservation.voyage.heureDepartFormatted}</td>
                                <td>${reservation.voyage.heureArriveeFormatted}</td>
                                <td>
                                    <a href="${pageContext.request.contextPath}/modifierReservation?id=${reservation.id}">Modifier</a> |
                                    <a href="${pageContext.request.contextPath}/annulerReservation?id=${reservation.id}">Annuler</a> |
                                    <a href="${pageContext.request.contextPath}/telechargerBillet?id=${reservation.id}">Télécharger PDF</a>
                                </td>
                            </tr>
                        </c:if>
                    </c:forEach>
                </table>
            </c:if>
            <c:if test="${empty reservations}">
                <p>Aucune réservation trouvée.</p>
            </c:if>
        </div>

        <div class="section">
            <h3>Historique des voyages</h3>
            <p><a href="${pageContext.request.contextPath}/historique">Voir les billets utilisés</a></p>
        </div>
    </c:if>

    <p>
        <a href="${pageContext.request.contextPath}/recherche">Retour à la recherche</a> |
        <a href="${pageContext.request.contextPath}/logout">Se déconnecter</a>
    </p>
</body>
</html>