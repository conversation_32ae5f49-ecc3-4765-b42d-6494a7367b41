<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>👤 Mon Compte - JEE Training</title>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <a href="${pageContext.request.contextPath}/recherche" class="logo">
                <i class="fas fa-train"></i> JEE Training
            </a>
            <div class="nav-links">
                <c:choose>
                    <c:when test="${sessionScope.user != null}">
                        <a href="${pageContext.request.contextPath}/recherche"><i class="fas fa-search"></i> Rechercher</a>
                        <c:if test="${sessionScope.user.role == 'admin'}">
                            <a href="${pageContext.request.contextPath}/admin/dashboard"><i class="fas fa-cog"></i> Admin</a>
                        </c:if>
                        <a href="${pageContext.request.contextPath}/logout"><i class="fas fa-sign-out-alt"></i> Déconnexion</a>
                    </c:when>
                    <c:otherwise>
                        <a href="${pageContext.request.contextPath}/login.jsp"><i class="fas fa-sign-in-alt"></i> Connexion</a>
                        <a href="${pageContext.request.contextPath}/register.jsp"><i class="fas fa-user-plus"></i> Inscription</a>
                    </c:otherwise>
                </c:choose>
            </div>
        </div>
    </nav>

    <!-- Contenu principal -->
    <div class="container">
        <div class="main-content">
            <h1><i class="fas fa-user-circle"></i> Mon Compte</h1>

            <!-- Messages -->
            <c:if test="${not empty success}">
                <div class="admin-message admin-message-success">
                    <i class="fas fa-check-circle"></i> ${success}
                </div>
            </c:if>
            <c:if test="${not empty error}">
                <div class="admin-message admin-message-error">
                    <i class="fas fa-exclamation-triangle"></i> ${error}
                </div>
            </c:if>

            <!-- Redirection automatique pour les admins -->
            <c:if test="${sessionScope.user.role == 'admin'}">
                <script>
                    // Redirection automatique vers le dashboard admin
                    window.location.href = '${pageContext.request.contextPath}/admin/dashboard';
                </script>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>Redirection en cours...</strong> Vous êtes administrateur, redirection vers le dashboard admin.
                </div>
            </c:if>

            <!-- Section utilisateur normal -->
            <c:if test="${sessionScope.user.role != 'admin'}">
                <!-- Informations utilisateur -->
                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title"><i class="fas fa-user"></i> Informations du Compte</h2>
                    </div>

                    <div class="user-profile">
                        <div class="profile-info">
                            <div class="profile-avatar">
                                <i class="fas fa-user-circle"></i>
                            </div>
                            <div class="profile-details">
                                <h3>${sessionScope.user.username}</h3>
                                <p><i class="fas fa-envelope"></i> ${sessionScope.user.email}</p>
                                <p><i class="fas fa-shield-alt"></i> Utilisateur</p>
                                <c:if test="${sessionScope.user.loyaltyPoints > 0}">
                                    <p><i class="fas fa-coins"></i> ${sessionScope.user.loyaltyPoints} points de fidélité</p>
                                </c:if>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Réservations achetées -->
                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title"><i class="fas fa-ticket-alt"></i> Mes Réservations</h2>
                    </div>

                    <c:if test="${not empty reservations}">
                        <table class="admin-table">
                            <thead>
                                <tr>
                                    <th><i class="fas fa-hashtag"></i> ID</th>
                                    <th><i class="fas fa-map-marker-alt"></i> Départ</th>
                                    <th><i class="fas fa-map-marker-alt"></i> Arrivée</th>
                                    <th><i class="fas fa-info-circle"></i> État</th>
                                    <th><i class="fas fa-clock"></i> Horaires</th>
                                    <th><i class="fas fa-cogs"></i> Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <c:forEach var="reservation" items="${reservations}">
                                    <c:if test="${reservation.etat == 'acheté' || reservation.etat == 'confirmée'}">
                                        <tr>
                                            <td><strong>#${reservation.id}</strong></td>
                                            <td>
                                                <div class="station-info">
                                                    <i class="fas fa-train"></i>
                                                    <span>${reservation.voyage.trajet.departStation.name}</span>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="station-info">
                                                    <i class="fas fa-train"></i>
                                                    <span>${reservation.voyage.trajet.arrivalStation.name}</span>
                                                </div>
                                            </td>
                                            <td>
                                                <c:choose>
                                                    <c:when test="${reservation.etat == 'confirmée'}">
                                                        <span class="badge badge-success">
                                                            <i class="fas fa-check-circle"></i> Confirmée
                                                        </span>
                                                    </c:when>
                                                    <c:when test="${reservation.etat == 'acheté'}">
                                                        <span class="badge badge-primary">
                                                            <i class="fas fa-shopping-cart"></i> Acheté
                                                        </span>
                                                    </c:when>
                                                    <c:when test="${reservation.etat == 'en attente'}">
                                                        <span class="badge badge-warning">
                                                            <i class="fas fa-clock"></i> En Attente
                                                        </span>
                                                    </c:when>
                                                    <c:otherwise>
                                                        <span class="badge badge-secondary">
                                                            <i class="fas fa-info-circle"></i> ${reservation.etat}
                                                        </span>
                                                    </c:otherwise>
                                                </c:choose>
                                            </td>
                                            <td>
                                                <div class="time-info">
                                                    <div><i class="fas fa-play"></i> ${reservation.voyage.heureDepartFormatted}</div>
                                                    <div><i class="fas fa-stop"></i> ${reservation.voyage.heureArriveeFormatted}</div>
                                                </div>
                                            </td>
                                            <td>
                                                <a href="${pageContext.request.contextPath}/modifierReservation?id=${reservation.id}" class="admin-btn admin-btn-primary">
                                                    <i class="fas fa-edit"></i> Modifier
                                                </a>
                                                <a href="${pageContext.request.contextPath}/annuler-reservation?id=${reservation.id}" class="admin-btn admin-btn-danger">
                                                    <i class="fas fa-times"></i> Annuler
                                                </a>
                                                <a href="${pageContext.request.contextPath}/download-ticket?reservationId=${reservation.id}" class="admin-btn admin-btn-success">
                                                    <i class="fas fa-file-pdf"></i> PDF
                                                </a>
                                            </td>
                                        </tr>
                                    </c:if>
                                </c:forEach>
                            </tbody>
                        </table>
                    </c:if>

                    <c:if test="${empty reservations}">
                        <div class="empty-state">
                            <i class="fas fa-ticket-alt"></i>
                            <h3>Aucune réservation trouvée</h3>
                            <p>Vous n'avez pas encore de réservations.</p>
                            <a href="${pageContext.request.contextPath}/recherche" class="btn btn-primary">
                                <i class="fas fa-search"></i> Rechercher un Trajet
                            </a>
                        </div>
                    </c:if>
                </div>

                <!-- Actions rapides -->
                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title"><i class="fas fa-bolt"></i> Actions Rapides</h2>
                    </div>

                    <div class="row">
                        <div class="col-3">
                            <a href="${pageContext.request.contextPath}/recherche" class="btn btn-primary btn-full">
                                <i class="fas fa-search"></i> Nouvelle Recherche
                            </a>
                        </div>
                        <div class="col-3">
                            <a href="${pageContext.request.contextPath}/mes-reservations" class="btn btn-success btn-full">
                                <i class="fas fa-ticket-alt"></i> Mes Réservations
                            </a>
                        </div>
                        <div class="col-3">
                            <a href="${pageContext.request.contextPath}/historique" class="btn btn-secondary btn-full">
                                <i class="fas fa-history"></i> Historique
                            </a>
                        </div>
                        <div class="col-3">
                            <a href="${pageContext.request.contextPath}/logout" class="btn btn-warning btn-full">
                                <i class="fas fa-sign-out-alt"></i> Déconnexion
                            </a>
                        </div>
                    </div>
                </div>
            </c:if>
        </div>
    </div>
</body>
</html>