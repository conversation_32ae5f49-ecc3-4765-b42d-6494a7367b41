<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📝 Inscription - JEE Training</title>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <a href="${pageContext.request.contextPath}/recherche" class="logo">
                <i class="fas fa-train"></i> JEE Training
            </a>
            <div class="nav-links">
                <a href="${pageContext.request.contextPath}/recherche"><i class="fas fa-search"></i> Rechercher</a>
                <a href="${pageContext.request.contextPath}/login.jsp"><i class="fas fa-sign-in-alt"></i> Connexion</a>
            </div>
        </div>
    </nav>

    <!-- Contenu principal -->
    <div class="container">
        <div class="form-container">
            <h1><i class="fas fa-user-plus"></i> Créer un compte</h1>

            <!-- Messages d'erreur -->
            <c:if test="${not empty error}">
                <div class="alert alert-error" role="alert">
                    <i class="fas fa-exclamation-triangle"></i> ${error}
                </div>
            </c:if>

            <!-- Messages de succès -->
            <c:if test="${not empty success}">
                <div class="alert alert-success" role="alert">
                    <i class="fas fa-check-circle"></i> ${success}
                </div>
            </c:if>

            <form action="register" method="post">
                <div class="form-group">
                    <label for="username"><i class="fas fa-user"></i> Nom d'utilisateur</label>
                    <input type="text" id="username" name="username" class="form-control"
                           placeholder="Choisissez un nom d'utilisateur" required
                           aria-describedby="username-help">
                    <small id="username-help" class="text-muted">
                        <i class="fas fa-info-circle"></i>
                        Minimum 3 caractères, lettres et chiffres uniquement
                    </small>
                </div>

                <div class="form-group">
                    <label for="email"><i class="fas fa-envelope"></i> Adresse email</label>
                    <input type="email" id="email" name="email" class="form-control"
                           placeholder="<EMAIL>" required
                           pattern="^[A-Za-z0-9+_.-]+@(.+)$"
                           title="Veuillez entrer un email valide"
                           aria-describedby="email-help">
                    <small id="email-help" class="text-muted">
                        <i class="fas fa-info-circle"></i>
                        Nous ne partagerons jamais votre email
                    </small>
                </div>

                <div class="form-group">
                    <label for="password"><i class="fas fa-lock"></i> Mot de passe</label>
                    <input type="password" id="password" name="password" class="form-control"
                           placeholder="Choisissez un mot de passe sécurisé" required
                           minlength="6" aria-describedby="password-help">
                    <small id="password-help" class="text-muted">
                        <i class="fas fa-info-circle"></i>
                        Minimum 6 caractères pour votre sécurité
                    </small>
                </div>

                <button type="submit" class="btn btn-success btn-full">
                    <i class="fas fa-user-plus"></i> Créer mon compte
                </button>
            </form>

            <div class="text-center mt-20">
                <p>Vous avez déjà un compte ?</p>
                <a href="${pageContext.request.contextPath}/login.jsp" class="btn btn-secondary">
                    <i class="fas fa-sign-in-alt"></i> Se connecter
                </a>
            </div>

            <div class="text-center mt-20">
                <small class="text-muted">
                    <i class="fas fa-shield-alt"></i>
                    Vos données sont protégées et sécurisées
                </small>
            </div>
        </div>
    </div>
</body>
</html>