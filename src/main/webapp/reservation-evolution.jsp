<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Évolution des réservations</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.min.js"></script>
</head>
<body>
    <div class="container mt-5">
        <h1>Évolution des réservations</h1>
        <c:if test="${not empty errorMessage}">
            <div class="alert alert-danger mt-3">${errorMessage}</div>
        </c:if>
        <form method="post" action="${pageContext.request.contextPath}/admin/reservation-evolution">
            <div class="mb-3">
                <label for="startDate" class="form-label">Date de début</label>
                <input type="datetime-local" class="form-control" id="startDate" name="startDate" value="${startDate}" required>
            </div>
            <div class="mb-3">
                <label for="endDate" class="form-label">Date de fin</label>
                <input type="datetime-local" class="form-control" id="endDate" name="endDate" value="${endDate}" required>
            </div>
            <div class="mb-3">
                <label for="groupBy" class="form-label">Grouper par</label>
                <select class="form-control" id="groupBy" name="groupBy" disabled>
                    <option value="month" selected>Mois</option>
                </select>
            </div>
            <button type="submit" class="btn btn-primary">Afficher</button>
        </form>
        <c:if test="${empty evolutionData}">
            <div class="alert alert-info mt-3">evolutionData est vide.</div>
        </c:if>
        <c:if test="${not empty evolutionData}">
            <div class="alert alert-info mt-3">evolutionData n'est pas vide : ${evolutionData}</div>
            <div class="mt-4">
                <p>Période : ${startDate} à ${endDate}</p>
                <canvas id="evolutionChart" width="400" height="200"></canvas>
                <script>
                    try {
                        const ctx = document.getElementById('evolutionChart').getContext('2d');
                        const labels = ${labels};
                        const data = ${data};
                        console.log('Labels:', labels);
                        console.log('Data:', data);

                        new Chart(ctx, {
                            type: 'line',
                            data: {
                                labels: labels, // ["2025-04", "2025-05"]
                                datasets: [{
                                    label: 'Nombre de réservations',
                                    data: data, // [0, 4]
                                    borderColor: '#007_paramètresbff',
                                    backgroundColor: 'rgba(0, 123, 255, 0.2)',
                                    fill: true,
                                    tension: 0.1
                                }]
                            },
                            options: {
                                responsive: true,
                                plugins: {
                                    title: {
                                        display: true,
                                        text: 'Évolution des réservations par mois',
                                        font: { size: 16 }
                                    },
                                    tooltip: {
                                        callbacks: {
                                            label: function(context) {
                                                return `Réservations: ${context.raw}`;
                                            }
                                        }
                                    }
                                },
                                scales: {
                                    y: {
                                        beginAtZero: true,
                                        suggestedMax: Math.max(...data) + 1,
                                        title: { 
                                            display: true, 
                                            text: 'Nombre de réservations',
                                            font: { weight: 'bold' }
                                        }
                                    },
                                    x: {
                                        title: { 
                                            display: true, 
                                            text: 'Période',
                                            font: { weight: 'bold' }
                                        },
                                        grid: {
                                            display: false
                                        }
                                    }
                                }
                            }
                        });
                    } catch (e) {
                        console.error('Erreur lors de la création du graphique:', e);
                    }
                </script>
            </div>
        </c:if>
        <a href="${pageContext.request.contextPath}/admin/dashboard" class="btn btn-secondary mt-3">Retour au tableau de bord</a>
    </div>
</body>
</html>