<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Gestion des utilisateurs</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; max-width: 800px; margin-left: auto; margin-right: auto; }
        h2 { color: #333; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        a { color: #007BFF; text-decoration: none; }
        a:hover { text-decoration: underline; }
        .success { color: green; margin-bottom: 15px; }
        .error { color: red; margin-bottom: 15px; }
        .action-button { padding: 5px 10px; background-color: #007BFF; color: white; border: none; cursor: pointer; }
        .action-button.block { background-color: #dc3545; }
        .action-button:hover { opacity: 0.8; }
    </style>
</head>
<body>
    <%@ include file="navbar.jsp" %>

    <h2>Gestion des utilisateurs</h2>

    <c:if test="${not empty success}">
        <p class="success">${success}</p>
    </c:if>
    <c:if test="${not empty error}">
        <p class="error">${error}</p>
    </c:if>

    <c:if test="${not empty users}">
        <table>
            <tr>
                <th>ID</th>
                <th>Nom d'utilisateur</th>
                <th>Email</th>
                <th>Rôle</th>
                <th>État</th>
                <th>Actions</th>
            </tr>
            <c:forEach var="user" items="${users}">
                <tr>
                    <td>${user.id}</td>
                    <td>${user.username}</td>
                    <td>${user.email}</td>
                    <td>${user.role}</td>
                    <td>${user.blocked ? 'Bloqué' : 'Actif'}</td>
                    <td>
                        <c:choose>
                            <c:when test="${user.blocked}">
                                <form action="${pageContext.request.contextPath}/admin/users" method="post" style="display: inline;">
                                    <input type="hidden" name="id" value="${user.id}">
                                    <input type="hidden" name="action" value="unblock">
                                    <button type="submit" class="action-button">Débloquer</button>
                                </form>
                            </c:when>
                            <c:otherwise>
                                <form action="${pageContext.request.contextPath}/admin/users" method="post" style="display: inline;">
                                    <input type="hidden" name="id" value="${user.id}">
                                    <input type="hidden" name="action" value="block">
                                    <button type="submit" class="action-button block">Bloquer</button>
                                </form>
                            </c:otherwise>
                        </c:choose>
                    </td>
                </tr>
            </c:forEach>
        </table>
    </c:if>

    <c:if test="${empty users}">
        <p>Aucun utilisateur trouvé.</p>
    </c:if>

    <p><a href="${pageContext.request.contextPath}/monCompte">Retour à mon compte</a></p>
</body>
</html>