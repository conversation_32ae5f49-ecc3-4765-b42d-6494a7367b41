<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>👥 Gestion des Utilisateurs - JEE Training</title>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <a href="${pageContext.request.contextPath}/recherche" class="logo">
                <i class="fas fa-train"></i> JEE Training
            </a>
            <div class="nav-links">
                <c:choose>
                    <c:when test="${sessionScope.user != null}">
                        <a href="${pageContext.request.contextPath}/recherche"><i class="fas fa-search"></i> Rechercher</a>
                        <c:if test="${sessionScope.user.role == 'admin'}">
                            <a href="${pageContext.request.contextPath}/admin/promotions"><i class="fas fa-cog"></i> Admin</a>
                        </c:if>
                        <a href="${pageContext.request.contextPath}/logout"><i class="fas fa-sign-out-alt"></i> Déconnexion</a>
                    </c:when>
                    <c:otherwise>
                        <a href="${pageContext.request.contextPath}/login.jsp"><i class="fas fa-sign-in-alt"></i> Connexion</a>
                        <a href="${pageContext.request.contextPath}/register.jsp"><i class="fas fa-user-plus"></i> Inscription</a>
                    </c:otherwise>
                </c:choose>
            </div>
        </div>
    </nav>

    <!-- Contenu principal -->
    <div class="container">
        <div class="main-content">
            <h1><i class="fas fa-users"></i> Gestion des Utilisateurs</h1>

            <!-- Breadcrumb -->
            <div class="breadcrumb">
                <a href="${pageContext.request.contextPath}/admin/dashboard"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                <span><i class="fas fa-chevron-right"></i></span>
                <span>Gestion des Utilisateurs</span>
            </div>

            <!-- Messages -->
            <c:if test="${not empty success}">
                <div class="admin-message admin-message-success">
                    <i class="fas fa-check-circle"></i> ${success}
                </div>
            </c:if>
            <c:if test="${not empty error}">
                <div class="admin-message admin-message-error">
                    <i class="fas fa-exclamation-triangle"></i> ${error}
                </div>
            </c:if>

            <!-- Liste des utilisateurs -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title"><i class="fas fa-list"></i> Liste des Utilisateurs</h2>
                </div>

                <c:if test="${not empty users}">
                    <table class="admin-table">
                        <thead>
                            <tr>
                                <th><i class="fas fa-hashtag"></i> ID</th>
                                <th><i class="fas fa-user"></i> Nom d'utilisateur</th>
                                <th><i class="fas fa-envelope"></i> Email</th>
                                <th><i class="fas fa-shield-alt"></i> Rôle</th>
                                <th><i class="fas fa-toggle-on"></i> État</th>
                                <th><i class="fas fa-cogs"></i> Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <c:forEach var="user" items="${users}">
                                <tr>
                                    <td><strong>#${user.id}</strong></td>
                                    <td>
                                        <div class="user-info">
                                            <i class="fas fa-user-circle"></i>
                                            <span>${user.username}</span>
                                        </div>
                                    </td>
                                    <td>${user.email}</td>
                                    <td>
                                        <c:choose>
                                            <c:when test="${user.role == 'admin'}">
                                                <span class="badge badge-danger"><i class="fas fa-crown"></i> Admin</span>
                                            </c:when>
                                            <c:otherwise>
                                                <span class="badge badge-info"><i class="fas fa-user"></i> Utilisateur</span>
                                            </c:otherwise>
                                        </c:choose>
                                    </td>
                                    <td>
                                        <c:choose>
                                            <c:when test="${user.blocked}">
                                                <span class="badge badge-danger"><i class="fas fa-ban"></i> Bloqué</span>
                                            </c:when>
                                            <c:otherwise>
                                                <span class="badge badge-success"><i class="fas fa-check-circle"></i> Actif</span>
                                            </c:otherwise>
                                        </c:choose>
                                    </td>
                                    <td>
                                        <c:choose>
                                            <c:when test="${user.blocked}">
                                                <form action="${pageContext.request.contextPath}/admin/users" method="post" style="display: inline;">
                                                    <input type="hidden" name="id" value="${user.id}">
                                                    <input type="hidden" name="action" value="unblock">
                                                    <button type="submit" class="admin-btn admin-btn-success" onclick="return confirm('Êtes-vous sûr de vouloir débloquer cet utilisateur ?')">
                                                        <i class="fas fa-check"></i> Débloquer
                                                    </button>
                                                </form>
                                            </c:when>
                                            <c:otherwise>
                                                <form action="${pageContext.request.contextPath}/admin/users" method="post" style="display: inline;">
                                                    <input type="hidden" name="id" value="${user.id}">
                                                    <input type="hidden" name="action" value="block">
                                                    <button type="submit" class="admin-btn admin-btn-danger" onclick="return confirm('Êtes-vous sûr de vouloir bloquer cet utilisateur ?')">
                                                        <i class="fas fa-ban"></i> Bloquer
                                                    </button>
                                                </form>
                                            </c:otherwise>
                                        </c:choose>
                                    </td>
                                </tr>
                            </c:forEach>
                        </tbody>
                    </table>
                </c:if>

                <c:if test="${empty users}">
                    <div class="empty-state">
                        <i class="fas fa-users"></i>
                        <h3>Aucun utilisateur trouvé</h3>
                        <p>Il n'y a actuellement aucun utilisateur dans le système.</p>
                    </div>
                </c:if>
            </div>

            <!-- Actions rapides -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title"><i class="fas fa-bolt"></i> Actions Rapides</h2>
                </div>

                <div class="row">
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/admin/dashboard" class="btn btn-secondary btn-full">
                            <i class="fas fa-arrow-left"></i> Retour Dashboard
                        </a>
                    </div>
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/admin/users" class="btn btn-primary btn-full">
                            <i class="fas fa-sync"></i> Actualiser
                        </a>
                    </div>
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/register.jsp" class="btn btn-success btn-full">
                            <i class="fas fa-user-plus"></i> Nouvel Utilisateur
                        </a>
                    </div>
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/admin/annuler" class="btn btn-warning btn-full">
                            <i class="fas fa-times-circle"></i> Demandes d'Annulation
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>