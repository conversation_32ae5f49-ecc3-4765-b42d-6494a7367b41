<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<style>
    nav {
        background-color: #333;
        padding: 10px 0;
        margin-bottom: 20px;
        width: 100vw; /* Étend la navbar sur toute la largeur de la fenêtre */
        position: relative; /* Pour s'assurer qu'elle reste bien positionnée */
        left: 50%;
        right: 50%;
        margin-left: -50vw; /* Centre la navbar en tenant compte de la largeur complète */
        margin-right: -50vw;
    }
    nav ul {
        list-style-type: none;
        margin: 0;
        padding: 0;
        display: flex;
        justify-content: center;
        align-items: center; /* Centrer verticalement pour aligner le logo et les liens */
    }
    nav ul li {
        margin: 0 15px;
    }
    nav ul li a {
        color: white;
        text-decoration: none;
        font-family: Arial, sans-serif;
        font-size: 16px;
    }
    nav ul li a:hover {
        color: #007BFF;
    }
    nav ul li img {
        vertical-align: middle; /* Aligner l'image avec le texte */
    }
</style>

<nav>
    <ul>
        <!-- Ajout du logo -->
        <li>
            <a href="${pageContext.request.contextPath}/recherche">
                <img src="${pageContext.request.contextPath}/images/logo.png" alt="Logo" style="height: 30px;">
            </a>
        </li>

        <!-- Message de bienvenue avec le nom de l'utilisateur, juste après le logo -->
        <c:if test="${not empty sessionScope.user}">
            <li style="color: white; font-family: Arial, sans-serif;">
                Bienvenue, ${sessionScope.user.username} !
            </li>
        </c:if>

        <!-- Lien "Rechercher un trajet" -->
        <li><a href="${pageContext.request.contextPath}/recherche">Rechercher un trajet</a></li>

        <!-- Liens visibles pour tous les utilisateurs connectés -->
        <c:if test="${not empty sessionScope.user}">
            <li><a href="${pageContext.request.contextPath}/monCompte">Mon compte</a></li>
            
            <!-- Liens spécifiques pour les administrateurs -->
            <c:if test="${sessionScope.user.role == 'admin'}">
                <li><a href="${pageContext.request.contextPath}/admin/dashboard">Dashboard</a></li>
                <li><a href="${pageContext.request.contextPath}/admin/users">Gérer les utilisateurs</a></li>
                <li><a href="${pageContext.request.contextPath}/admin/stations">Gérer les stations</a></li>
                <li><a href="${pageContext.request.contextPath}/admin/trajets">Gérer les trajets</a></li>
                <li><a href="${pageContext.request.contextPath}/admin/voyages">Gérer les voyages</a></li>
                <li><a href="${pageContext.request.contextPath}/admin/promotions">Gérer les promotions</a></li>
                
            </c:if>

            <li><a href="${pageContext.request.contextPath}/logout">Se déconnecter</a></li>
        </c:if>

        <!-- Lien visible pour les utilisateurs non connectés -->
        <c:if test="${empty sessionScope.user}">
            <li><a href="${pageContext.request.contextPath}/login">Se connecter</a></li>
        </c:if>
    </ul>
</nav>