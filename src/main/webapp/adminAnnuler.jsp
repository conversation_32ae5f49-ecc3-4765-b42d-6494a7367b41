<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Administration - Gérer les annulations</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; max-width: 800px; margin-left: auto; margin-right: auto; }
        h2 { color: #333; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        a { color: #007BFF; text-decoration: none; }
        a:hover { text-decoration: underline; }
        .success { color: green; margin-bottom: 15px; }
        .error { color: red; margin-bottom: 15px; }
        button { padding: 5px 10px; margin-right: 5px; cursor: pointer; }
        .confirmer { background-color: #28a745; color: white; border: none; }
        .rejeter { background-color: #dc3545; color: white; border: none; }
    </style>
</head>
<body>
    <h2>Gérer les demandes d'annulation</h2>
    <c:if test="${not empty success}">
        <p class="success">${success}</p>
    </c:if>
    <c:if test="${not empty error}">
        <p class="error">${error}</p>
    </c:if>
    <c:if test="${empty reservations}">
        <p>Aucune demande d'annulation en attente.</p>
    </c:if>
    <c:if test="${not empty reservations}">
        <table>
            <tr>
                <th>ID Réservation</th>
                <th>Utilisateur</th>
                <th>Gare Départ</th>
                <th>Gare Arrivée</th>
                <th>Actions</th>
            </tr>
            <c:forEach var="reservation" items="${reservations}">
                <tr>
                    <td>${reservation.id}</td>
                    <td>${reservation.user.email}</td>
                    <td>${reservation.voyage.trajet.departStation.name} (${reservation.voyage.trajet.departStation.city})</td>
                    <td>${reservation.voyage.trajet.arrivalStation.name} (${reservation.voyage.trajet.arrivalStation.city})</td>
                    <td>
                        <form action="/jeetraing/admin/annuler" method="post" style="display:inline;">
                            <input type="hidden" name="id" value="${reservation.id}">
                            <input type="hidden" name="action" value="confirmer">
                            <button type="submit" class="confirmer">Confirmer</button>
                        </form>
                        <form action="/jeetraing/admin/annuler" method="post" style="display:inline;">
                            <input type="hidden" name="id" value="${reservation.id}">
                            <input type="hidden" name="action" value="rejeter">
                            <button type="submit" class="rejeter">Rejeter</button>
                        </form>
                    </td>
                </tr>
            </c:forEach>
        </table>
    </c:if>
    <p><a href="/jeetraing/monCompte">Retour à mon compte</a></p>
</body>
</html>