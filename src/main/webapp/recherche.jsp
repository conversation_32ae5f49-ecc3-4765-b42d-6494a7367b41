<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Recherche de Trajets</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }
        h2, h3 {
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        label {
            flex: 0 0 150px;
            margin-right: 10px;
        }
        select, input[type="date"] {
            flex: 1;
            padding: 5px;
        }
        button {
            padding: 8px 15px;
            background-color: #007BFF;
            color: white;
            border: none;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin-top: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .error {
            color: red;
            margin-bottom: 15px;
        }
        .promotion {
            color: green;
            font-weight: bold;
        }
        a {
            color: #007BFF;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
<%@ include file="navbar.jsp" %>
<p>
    <a href="logout">Se déconnecter</a>
    <c:if test="${sessionScope.user != null && sessionScope.user.admin}">
        | <a href="${pageContext.request.contextPath}/admin/promotions">Gérer les promotions (admin)</a>
    </c:if>
</p>
    <h2>Rechercher un trajet</h2>
    <c:if test="${not empty error}">
        <p class="error" aria-live="polite">${error}</p>
    </c:if>
    <form action="recherche" method="post">
        <div class="form-group">
            <label for="villeDepart">Ville de départ :</label>
            <select id="villeDepart" name="villeDepart" required>
                <option value="">Sélectionnez une ville</option>
                <c:forEach var="city" items="${cities}">
                    <option value="${city}" ${city == villeDepart ? 'selected' : ''}>${city}</option>
                </c:forEach>
            </select>
        </div>
        <div class="form-group">
            <label for="villeDestination">Ville de destination :</label>
            <select id="villeDestination" name="villeDestination" required>
                <option value="">Sélectionnez une ville</option>
                <c:forEach var="city" items="${cities}">
                    <option value="${city}" ${city == villeDestination ? 'selected' : ''}>${city}</option>
                </c:forEach>
            </select>
        </div>
        <div class="form-group">
            <label for="date">Date de départ :</label>
            <input type="date" id="date" name="date" value="${date}" required>
        </div>
        <button type="submit">Rechercher</button>
    </form>

    <c:if test="${not empty voyages}">
        <h3>Résultats de la recherche</h3>
        <table>
            <tr>
                <th>Gare de départ</th>
                <th>Gare d'arrivée</th>
                <th>Heure de départ</th>
                <th>Heure d'arrivée</th>
                <th>Durée</th>
                <th>Prix</th>
                <th>Places disponibles</th>
                <th>Promotions</th>
                <th>Action</th>
            </tr>
            <c:forEach var="voyage" items="${voyages}">
                <tr>
                    <td>${voyage.trajet.departStation.name} (${voyage.trajet.departStation.city})</td>
                    <td>${voyage.trajet.arrivalStation.name} (${voyage.trajet.arrivalStation.city})</td>
                    <td>${voyage.heureDepartFormatted}</td>
                    <td>${voyage.heureArriveeFormatted}</td>
                    <td>${voyage.duree}</td>
                    <td>
                        <c:set var="originalPrice" value="${voyage.prix}"/>
                        <c:set var="bestDiscount" value="0"/>
                        <c:forEach var="promo" items="${voyagePromotions[voyage.id]}">
                            <c:if test="${promo.discountPercentage > bestDiscount}">
                                <c:set var="bestDiscount" value="${promo.discountPercentage}"/>
                            </c:if>
                        </c:forEach>
                        <c:choose>
                            <c:when test="${bestDiscount > 0}">
                                <span style="text-decoration: line-through;">${originalPrice} €</span>
                                <span class="promotion">${originalPrice * (1 - bestDiscount / 100)} € (-${bestDiscount}%)</span>
                            </c:when>
                            <c:otherwise>
                                ${originalPrice} €
                            </c:otherwise>
                        </c:choose>
                    </td>
                    <td>${voyage.placesDisponibles}</td>
                    <td>
                        <c:if test="${not empty voyagePromotions[voyage.id]}">
                            <ul>
                                <c:forEach var="promo" items="${voyagePromotions[voyage.id]}">
                                    <li>${promo.code}: ${promo.description} (-${promo.discountPercentage}%)</li>
                                </c:forEach>
                            </ul>
                        </c:if>
                    </td>
                    <td>
                        <a href="selection?voyageId=${voyage.id}">Sélectionner</a>
                    </td>
                </tr>
            </c:forEach>
        </table>
    </c:if>
</body>
</html>