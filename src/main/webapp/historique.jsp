<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📚 Historique des Voyages - JEE Training</title>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <a href="${pageContext.request.contextPath}/recherche" class="logo">
                <i class="fas fa-train"></i> JEE Training
            </a>
            <div class="nav-links">
                <c:choose>
                    <c:when test="${sessionScope.user != null}">
                        <a href="${pageContext.request.contextPath}/recherche"><i class="fas fa-search"></i> Rechercher</a>
                        <c:if test="${sessionScope.user.role == 'admin'}">
                            <a href="${pageContext.request.contextPath}/admin/dashboard"><i class="fas fa-cog"></i> Admin</a>
                        </c:if>
                        <a href="${pageContext.request.contextPath}/logout"><i class="fas fa-sign-out-alt"></i> Déconnexion</a>
                    </c:when>
                    <c:otherwise>
                        <a href="${pageContext.request.contextPath}/login.jsp"><i class="fas fa-sign-in-alt"></i> Connexion</a>
                        <a href="${pageContext.request.contextPath}/register.jsp"><i class="fas fa-user-plus"></i> Inscription</a>
                    </c:otherwise>
                </c:choose>
            </div>
        </div>
    </nav>

    <!-- Contenu principal -->
    <div class="container">
        <div class="main-content">
            <h1><i class="fas fa-history"></i> Historique de vos Voyages</h1>

            <!-- Breadcrumb -->
            <div class="breadcrumb">
                <a href="${pageContext.request.contextPath}/recherche"><i class="fas fa-search"></i> Recherche</a>
                <span><i class="fas fa-chevron-right"></i></span>
                <a href="${pageContext.request.contextPath}/monCompte"><i class="fas fa-user"></i> Mon Compte</a>
                <span><i class="fas fa-chevron-right"></i></span>
                <span>Historique des Voyages</span>
            </div>

            <!-- Statistiques -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title"><i class="fas fa-chart-bar"></i> Vos Statistiques de Voyage</h2>
                </div>

                <div class="row">
                    <div class="col-3">
                        <div class="stat-card stat-success">
                            <div class="stat-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="stat-content">
                                <h3>${reservations.size()}</h3>
                                <p>Voyages Effectués</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="stat-card stat-info">
                            <div class="stat-icon">
                                <i class="fas fa-route"></i>
                            </div>
                            <div class="stat-content">
                                <h3>
                                    <c:set var="uniqueRoutes" value="0"/>
                                    <c:set var="seenRoutes" value=""/>
                                    <c:forEach var="reservation" items="${reservations}">
                                        <c:set var="route" value="${reservation.voyage.trajet.departStation.name}-${reservation.voyage.trajet.arrivalStation.name}"/>
                                        <c:if test="${!seenRoutes.contains(route)}">
                                            <c:set var="uniqueRoutes" value="${uniqueRoutes + 1}"/>
                                            <c:set var="seenRoutes" value="${seenRoutes}${route};"/>
                                        </c:if>
                                    </c:forEach>
                                    ${uniqueRoutes}
                                </h3>
                                <p>Trajets Différents</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="stat-card stat-warning">
                            <div class="stat-icon">
                                <i class="fas fa-euro-sign"></i>
                            </div>
                            <div class="stat-content">
                                <h3>
                                    <c:set var="totalSpent" value="0"/>
                                    <c:forEach var="reservation" items="${reservations}">
                                        <c:set var="totalSpent" value="${totalSpent + reservation.voyage.prix}"/>
                                    </c:forEach>
                                    ${totalSpent}€
                                </h3>
                                <p>Total Dépensé</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="stat-card stat-primary">
                            <div class="stat-icon">
                                <i class="fas fa-star"></i>
                            </div>
                            <div class="stat-content">
                                <h3>
                                    <c:choose>
                                        <c:when test="${reservations.size() >= 10}">🏆 VIP</c:when>
                                        <c:when test="${reservations.size() >= 5}">🥇 Gold</c:when>
                                        <c:when test="${reservations.size() >= 2}">🥈 Silver</c:when>
                                        <c:otherwise>🥉 Bronze</c:otherwise>
                                    </c:choose>
                                </h3>
                                <p>Statut Voyageur</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Liste des voyages -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title"><i class="fas fa-list"></i> Vos Voyages Effectués</h2>
                </div>

                <c:if test="${not empty reservations}">
                    <table class="admin-table">
                        <thead>
                            <tr>
                                <th><i class="fas fa-hashtag"></i> ID</th>
                                <th><i class="fas fa-route"></i> Trajet</th>
                                <th><i class="fas fa-calendar-alt"></i> Date Voyage</th>
                                <th><i class="fas fa-star"></i> Classe</th>
                                <th><i class="fas fa-euro-sign"></i> Prix</th>
                                <th><i class="fas fa-heart"></i> Préférences</th>
                            </tr>
                        </thead>
                        <tbody>
                            <c:forEach var="reservation" items="${reservations}">
                                <tr>
                                    <td><strong>#${reservation.id}</strong></td>
                                    <td>
                                        <div class="route-info">
                                            <div class="departure">
                                                <i class="fas fa-play"></i> ${reservation.voyage.trajet.departStation.name}
                                                <small>(${reservation.voyage.trajet.departStation.city})</small>
                                            </div>
                                            <div class="arrival">
                                                <i class="fas fa-stop"></i> ${reservation.voyage.trajet.arrivalStation.name}
                                                <small>(${reservation.voyage.trajet.arrivalStation.city})</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="voyage-details">
                                            <div class="time-info">
                                                <i class="fas fa-clock"></i> ${reservation.voyage.heureDepartFormatted}
                                            </div>
                                            <div class="date-info">
                                                <i class="fas fa-calendar"></i> ${reservation.dateReservation}
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="classe-badge classe-${reservation.classe}">
                                            <i class="fas fa-star"></i> ${reservation.classe}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="price-highlight">${reservation.voyage.prix}€</span>
                                    </td>
                                    <td>
                                        <c:if test="${not empty reservation.preferences}">
                                            <div class="preferences-list">
                                                <c:forEach var="pref" items="${reservation.preferences.split(', ')}">
                                                    <span class="preference-tag">
                                                        <c:choose>
                                                            <c:when test="${pref.contains('Fenêtre')}">
                                                                <i class="fas fa-window-maximize"></i>
                                                            </c:when>
                                                            <c:when test="${pref.contains('famille')}">
                                                                <i class="fas fa-users"></i>
                                                            </c:when>
                                                            <c:when test="${pref.contains('fumeur')}">
                                                                <i class="fas fa-ban"></i>
                                                            </c:when>
                                                            <c:otherwise>
                                                                <i class="fas fa-heart"></i>
                                                            </c:otherwise>
                                                        </c:choose>
                                                        ${pref}
                                                    </span>
                                                </c:forEach>
                                            </div>
                                        </c:if>
                                        <c:if test="${empty reservation.preferences}">
                                            <span class="text-muted">Aucune</span>
                                        </c:if>
                                    </td>
                                </tr>
                            </c:forEach>
                        </tbody>
                    </table>
                </c:if>

                <c:if test="${empty reservations}">
                    <div class="empty-state">
                        <i class="fas fa-history"></i>
                        <h3>Aucun voyage effectué</h3>
                        <p>Vous n'avez pas encore effectué de voyage avec JEE Training.</p>
                        <a href="${pageContext.request.contextPath}/recherche" class="btn btn-primary">
                            <i class="fas fa-search"></i> Rechercher un Voyage
                        </a>
                    </div>
                </c:if>
            </div>

            <!-- Actions rapides -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title"><i class="fas fa-bolt"></i> Actions Rapides</h2>
                </div>

                <div class="row">
                    <div class="col-4">
                        <a href="${pageContext.request.contextPath}/recherche" class="btn btn-primary btn-full">
                            <i class="fas fa-search"></i> Nouveau Voyage
                        </a>
                    </div>
                    <div class="col-4">
                        <a href="${pageContext.request.contextPath}/mes-reservations" class="btn btn-secondary btn-full">
                            <i class="fas fa-ticket-alt"></i> Mes Réservations
                        </a>
                    </div>
                    <div class="col-4">
                        <a href="${pageContext.request.contextPath}/monCompte" class="btn btn-warning btn-full">
                            <i class="fas fa-user"></i> Mon Compte
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>