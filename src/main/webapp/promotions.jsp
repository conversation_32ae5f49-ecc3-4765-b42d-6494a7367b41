<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Gestion des Promotions</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }
        h2, h3 {
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        label {
            flex: 0 0 150px;
            margin-right: 10px;
        }
        input, select, textarea {
            flex: 1;
            padding: 5px;
        }
        button {
            padding: 8px 15px;
            background-color: #007BFF;
            color: white;
            border: none;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .edit-button {
            background-color: #28a745;
        }
        .edit-button:hover {
            background-color: #218838;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin-top: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .error {
            color: red;
            margin-bottom: 15px;
        }
        .message {
            color: green;
            margin-bottom: 15px;
        }
        a {
            color: #007BFF;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
<%@ include file="navbar.jsp" %>
    <h2>Gestion des Promotions</h2>
    <p><a href="${pageContext.request.contextPath}/recherche">Retour à la recherche</a></p>
    <c:if test="${not empty error}">
        <p class="error" aria-live="polite">${error}</p>
    </c:if>
    <c:if test="${not empty message}">
        <p class="message" aria-live="polite">${message}</p>
    </c:if>

    <h3>${editPromotion != null ? 'Modifier une promotion' : 'Ajouter une promotion'}</h3>
    <form action="${pageContext.request.contextPath}/admin/promotions" method="post">
        <input type="hidden" name="action" value="${editPromotion != null ? 'update' : 'add'}">
        <c:if test="${editPromotion != null}">
            <input type="hidden" name="promotionId" value="${editPromotion.id}">
        </c:if>
        <div class="form-group">
            <label for="code">Code :</label>
            <input type="text" id="code" name="code" value="${editPromotion != null ? editPromotion.code : ''}" required>
        </div>
        <div class="form-group">
            <label for="description">Description :</label>
            <textarea id="description" name="description">${editPromotion != null ? editPromotion.description : ''}</textarea>
        </div>
        <div class="form-group">
            <label for="discountPercentage">Pourcentage de réduction (%) :</label>
            <input type="number" id="discountPercentage" name="discountPercentage" step="0.1" min="0" max="100" 
                   value="${editPromotion != null ? editPromotion.discountPercentage : ''}" required>
        </div>
        <div class="form-group">
            <label for="startDate">Date de début :</label>
            <input type="date" id="startDate" name="startDate" value="${editPromotion != null ? editPromotion.startDate : ''}" required>
        </div>
        <div class="form-group">
            <label for="endDate">Date de fin :</label>
            <input type="date" id="endDate" name="endDate" value="${editPromotion != null ? editPromotion.endDate : ''}" required>
        </div>
        <div class="form-group">
            <label for="trajetId">Trajet (optionnel) :</label>
            <select id="trajetId" name="trajetId">
                <option value="">Aucun (promotion générale)</option>
                <c:forEach var="trajet" items="${trajets}">
                    <option value="${trajet.id}" ${editPromotion != null && editPromotion.trajet != null && editPromotion.trajet.id == trajet.id ? 'selected' : ''}>
                        ${trajet.departStation.city} - ${trajet.arrivalStation.city}
                    </option>
                </c:forEach>
            </select>
        </div>
        <div class="form-group">
            <label for="minLoyaltyPoints">Points de fidélité minimum :</label>
            <input type="number" id="minLoyaltyPoints" name="minLoyaltyPoints" min="0" 
                   value="${editPromotion != null && editPromotion.minLoyaltyPoints != null ? editPromotion.minLoyaltyPoints : ''}">
        </div>
        <button type="submit">${editPromotion != null ? 'Modifier' : 'Ajouter'}</button>
        <c:if test="${editPromotion != null}">
            <button type="button" onclick="window.location.href='${pageContext.request.contextPath}/admin/promotions'">Annuler</button>
        </c:if>
    </form>

    <h3>Promotions existantes</h3>
    <c:if test="${not empty promotions}">
        <table>
            <tr>
                <th>Code</th>
                <th>Description</th>
                <th>Réduction (%)</th>
                <th>Dates</th>
                <th>Trajet</th>
                <th>Points min.</th>
                <th>Action</th>
            </tr>
            <c:forEach var="promotion" items="${promotions}">
                <tr>
                    <td>${promotion.code}</td>
                    <td>${promotion.description}</td>
                    <td>${promotion.discountPercentage}</td>
                    <td>${promotion.startDate} - ${promotion.endDate}</td>
                    <td>
                        <c:choose>
                            <c:when test="${promotion.trajet != null}">
                                ${promotion.trajet.departStation.city} - ${promotion.trajet.arrivalStation.city}
                            </c:when>
                            <c:otherwise>Générale</c:otherwise>
                        </c:choose>
                    </td>
                    <td>${promotion.minLoyaltyPoints != null ? promotion.minLoyaltyPoints : '-'}</td>
                    <td>
                        <form action="${pageContext.request.contextPath}/admin/promotions" method="post" style="display:inline;">
                            <input type="hidden" name="action" value="edit">
                            <input type="hidden" name="promotionId" value="${promotion.id}">
                            <button type="submit" class="edit-button">Modifier</button>
                        </form>
                        <form action="${pageContext.request.contextPath}/admin/promotions" method="post" style="display:inline;">
                            <input type="hidden" name="action" value="delete">
                            <input type="hidden" name="promotionId" value="${promotion.id}">
                            <button type="submit" onclick="return confirm('Supprimer cette promotion ?');">Supprimer</button>
                        </form>
                    </td>
                </tr>
            </c:forEach>
        </table>
    </c:if>
</body>
</html>