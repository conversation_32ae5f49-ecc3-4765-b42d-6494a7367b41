<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎁 Gestion des Promotions - JEE Training</title>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <a href="${pageContext.request.contextPath}/recherche" class="logo">
                <i class="fas fa-train"></i> JEE Training
            </a>
            <div class="nav-links">
                <c:choose>
                    <c:when test="${sessionScope.user != null}">
                        <a href="${pageContext.request.contextPath}/recherche"><i class="fas fa-search"></i> Rechercher</a>
                        <c:if test="${sessionScope.user.role == 'admin'}">
                            <a href="${pageContext.request.contextPath}/admin/dashboard"><i class="fas fa-cog"></i> Admin</a>
                        </c:if>
                        <a href="${pageContext.request.contextPath}/logout"><i class="fas fa-sign-out-alt"></i> Déconnexion</a>
                    </c:when>
                    <c:otherwise>
                        <a href="${pageContext.request.contextPath}/login.jsp"><i class="fas fa-sign-in-alt"></i> Connexion</a>
                        <a href="${pageContext.request.contextPath}/register.jsp"><i class="fas fa-user-plus"></i> Inscription</a>
                    </c:otherwise>
                </c:choose>
            </div>
        </div>
    </nav>

    <!-- Contenu principal -->
    <div class="container">
        <div class="main-content">
            <h1><i class="fas fa-tags"></i> Gestion des Promotions</h1>

            <!-- Breadcrumb -->
            <div class="breadcrumb">
                <a href="${pageContext.request.contextPath}/admin/dashboard"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                <span><i class="fas fa-chevron-right"></i></span>
                <span>Gestion des Promotions</span>
            </div>

            <!-- Messages -->
            <c:if test="${not empty error}">
                <div class="admin-message admin-message-error">
                    <i class="fas fa-exclamation-triangle"></i> ${error}
                </div>
            </c:if>
            <c:if test="${not empty message}">
                <div class="admin-message admin-message-success">
                    <i class="fas fa-check-circle"></i> ${message}
                </div>
            </c:if>

            <!-- Formulaire de promotion -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">
                        <i class="fas fa-${editPromotion != null ? 'edit' : 'plus-circle'}"></i>
                        ${editPromotion != null ? 'Modifier une Promotion' : 'Ajouter une Promotion'}
                    </h2>
                </div>

                <form action="${pageContext.request.contextPath}/admin/promotions" method="post" class="admin-form">
                    <input type="hidden" name="action" value="${editPromotion != null ? 'update' : 'add'}">
                    <c:if test="${editPromotion != null}">
                        <input type="hidden" name="promotionId" value="${editPromotion.id}">
                    </c:if>

                    <div class="row">
                        <div class="col-2">
                            <div class="admin-form-group">
                                <label for="code"><i class="fas fa-tag"></i> Code Promotion</label>
                                <input type="text" id="code" name="code" value="${editPromotion != null ? editPromotion.code : ''}"
                                       placeholder="Ex: WELCOME10" required>
                            </div>
                        </div>
                        <div class="col-2">
                            <div class="admin-form-group">
                                <label for="discountPercentage"><i class="fas fa-percent"></i> Réduction (%)</label>
                                <input type="number" id="discountPercentage" name="discountPercentage" step="0.1" min="0" max="100"
                                       value="${editPromotion != null ? editPromotion.discountPercentage : ''}"
                                       placeholder="Ex: 15.5" required>
                            </div>
                        </div>
                    </div>

                    <div class="admin-form-group">
                        <label for="description"><i class="fas fa-align-left"></i> Description</label>
                        <textarea id="description" name="description" rows="3"
                                  placeholder="Description de la promotion...">${editPromotion != null ? editPromotion.description : ''}</textarea>
                    </div>

                    <div class="row">
                        <div class="col-2">
                            <div class="admin-form-group">
                                <label for="startDate"><i class="fas fa-calendar-alt"></i> Date de Début</label>
                                <input type="date" id="startDate" name="startDate"
                                       value="${editPromotion != null ? editPromotion.startDate : ''}" required>
                            </div>
                        </div>
                        <div class="col-2">
                            <div class="admin-form-group">
                                <label for="endDate"><i class="fas fa-calendar-alt"></i> Date de Fin</label>
                                <input type="date" id="endDate" name="endDate"
                                       value="${editPromotion != null ? editPromotion.endDate : ''}" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-2">
                            <div class="admin-form-group">
                                <label for="trajetId"><i class="fas fa-route"></i> Trajet (Optionnel)</label>
                                <select id="trajetId" name="trajetId">
                                    <option value="">🌍 Promotion Générale</option>
                                    <c:forEach var="trajet" items="${trajets}">
                                        <option value="${trajet.id}" ${editPromotion != null && editPromotion.trajet != null && editPromotion.trajet.id == trajet.id ? 'selected' : ''}>
                                            🚂 ${trajet.departStation.city} → ${trajet.arrivalStation.city}
                                        </option>
                                    </c:forEach>
                                </select>
                            </div>
                        </div>
                        <div class="col-2">
                            <div class="admin-form-group">
                                <label for="minLoyaltyPoints"><i class="fas fa-coins"></i> Points Minimum</label>
                                <input type="number" id="minLoyaltyPoints" name="minLoyaltyPoints" min="0"
                                       value="${editPromotion != null && editPromotion.minLoyaltyPoints != null ? editPromotion.minLoyaltyPoints : ''}"
                                       placeholder="Ex: 100">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-4">
                            <button type="submit" class="btn btn-${editPromotion != null ? 'success' : 'primary'}">
                                <i class="fas fa-${editPromotion != null ? 'save' : 'plus'}"></i>
                                ${editPromotion != null ? 'Mettre à Jour' : 'Ajouter la Promotion'}
                            </button>
                            <c:if test="${editPromotion != null}">
                                <a href="${pageContext.request.contextPath}/admin/promotions" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Annuler
                                </a>
                            </c:if>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Liste des promotions -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title"><i class="fas fa-list"></i> Promotions Existantes</h2>
                </div>

                <c:if test="${not empty promotions}">
                    <table class="admin-table">
                        <thead>
                            <tr>
                                <th><i class="fas fa-tag"></i> Code</th>
                                <th><i class="fas fa-align-left"></i> Description</th>
                                <th><i class="fas fa-percent"></i> Réduction</th>
                                <th><i class="fas fa-calendar-alt"></i> Période</th>
                                <th><i class="fas fa-route"></i> Trajet</th>
                                <th><i class="fas fa-coins"></i> Points Min.</th>
                                <th><i class="fas fa-cogs"></i> Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <c:forEach var="promotion" items="${promotions}">
                                <tr>
                                    <td>
                                        <span class="promo-code">
                                            <i class="fas fa-tag"></i> ${promotion.code}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="promo-description">
                                            ${promotion.description}
                                        </div>
                                    </td>
                                    <td>
                                        <span class="discount-badge">
                                            <i class="fas fa-percent"></i> ${promotion.discountPercentage}%
                                        </span>
                                    </td>
                                    <td>
                                        <div class="date-range">
                                            <div><i class="fas fa-play"></i> ${promotion.startDate}</div>
                                            <div><i class="fas fa-stop"></i> ${promotion.endDate}</div>
                                        </div>
                                    </td>
                                    <td>
                                        <c:choose>
                                            <c:when test="${promotion.trajet != null}">
                                                <span class="badge badge-info">
                                                    <i class="fas fa-train"></i> ${promotion.trajet.departStation.city} → ${promotion.trajet.arrivalStation.city}
                                                </span>
                                            </c:when>
                                            <c:otherwise>
                                                <span class="badge badge-success">
                                                    <i class="fas fa-globe"></i> Générale
                                                </span>
                                            </c:otherwise>
                                        </c:choose>
                                    </td>
                                    <td>
                                        <c:choose>
                                            <c:when test="${promotion.minLoyaltyPoints != null}">
                                                <span class="points-badge">
                                                    <i class="fas fa-coins"></i> ${promotion.minLoyaltyPoints}
                                                </span>
                                            </c:when>
                                            <c:otherwise>
                                                <span class="badge badge-secondary">
                                                    <i class="fas fa-minus"></i> Aucun
                                                </span>
                                            </c:otherwise>
                                        </c:choose>
                                    </td>
                                    <td>
                                        <form action="${pageContext.request.contextPath}/admin/promotions" method="post" style="display:inline;">
                                            <input type="hidden" name="action" value="edit">
                                            <input type="hidden" name="promotionId" value="${promotion.id}">
                                            <button type="submit" class="admin-btn admin-btn-primary">
                                                <i class="fas fa-edit"></i> Modifier
                                            </button>
                                        </form>
                                        <form action="${pageContext.request.contextPath}/admin/promotions" method="post" style="display:inline;">
                                            <input type="hidden" name="action" value="delete">
                                            <input type="hidden" name="promotionId" value="${promotion.id}">
                                            <button type="submit" class="admin-btn admin-btn-danger"
                                                    onclick="return confirm('Êtes-vous sûr de vouloir supprimer cette promotion ?');">
                                                <i class="fas fa-trash"></i> Supprimer
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                            </c:forEach>
                        </tbody>
                    </table>
                </c:if>

                <c:if test="${empty promotions}">
                    <div class="empty-state">
                        <i class="fas fa-tags"></i>
                        <h3>Aucune promotion trouvée</h3>
                        <p>Commencez par créer votre première promotion.</p>
                    </div>
                </c:if>
            </div>

            <!-- Actions rapides -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title"><i class="fas fa-bolt"></i> Actions Rapides</h2>
                </div>

                <div class="row">
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/admin/dashboard" class="btn btn-secondary btn-full">
                            <i class="fas fa-arrow-left"></i> Retour Dashboard
                        </a>
                    </div>
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/admin/promotions" class="btn btn-primary btn-full">
                            <i class="fas fa-sync"></i> Actualiser
                        </a>
                    </div>
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/admin/trajets" class="btn btn-warning btn-full">
                            <i class="fas fa-road"></i> Gérer Trajets
                        </a>
                    </div>
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/admin/voyages" class="btn btn-success btn-full">
                            <i class="fas fa-train"></i> Gérer Voyages
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>