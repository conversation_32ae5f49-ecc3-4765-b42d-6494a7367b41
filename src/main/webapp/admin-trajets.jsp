<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Gestion des trajets</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; max-width: 800px; margin-left: auto; margin-right: auto; }
        h2 { color: #333; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .action-button { padding: 5px 10px; background-color: #007BFF; color: white; border: none; cursor: pointer; }
        .action-button.delete { background-color: #dc3545; }
        .action-button:hover { opacity: 0.8; }
        .success { color: green; margin-bottom: 15px; }
        .error { color: red; margin-bottom: 15px; }
        .form-section { margin-top: 20px; }
    </style>
</head>
<body>
    <%@ include file="navbar.jsp" %>

    <h2>Gestion des trajets</h2>

    <c:if test="${not empty success}">
        <p class="success">${success}</p>
    </c:if>
    <c:if test="${not empty error}">
        <p class="error">${error}</p>
    </c:if>

    <div class="form-section">
        <h3>Ajouter un trajet</h3>
        <form action="${pageContext.request.contextPath}/admin/trajets" method="post">
            <input type="hidden" name="action" value="add">
            <label>Gare de départ:
                <select name="departStationId" required>
                    <c:forEach var="station" items="${stations}">
                        <option value="${station.id}">${station.name}</option>
                    </c:forEach>
                </select>
            </label><br>
            <label>Gare d'arrivée:
                <select name="arrivalStationId" required>
                    <c:forEach var="station" items="${stations}">
                        <option value="${station.id}">${station.name}</option>
                    </c:forEach>
                </select>
            </label><br>
            <button type="submit" class="action-button">Ajouter</button>
        </form>
    </div>

    <c:if test="${not empty trajets}">
        <table>
            <tr>
                <th>ID</th>
                <th>Gare Départ</th>
                <th>Gare Arrivée</th>
                <th>Actions</th>
            </tr>
            <c:forEach var="trajet" items="${trajets}">
                <tr>
                    <td>${trajet.id}</td>
                    <td>${trajet.departStation.name}</td>
                    <td>${trajet.arrivalStation.name}</td>
                    <td>
                        <!-- Bouton Modifier -->
                        <form action="${pageContext.request.contextPath}/admin/trajets" method="get" style="display: inline;">
                            <input type="hidden" name="action" value="edit">
                            <input type="hidden" name="id" value="${trajet.id}">
                            <button type="submit" class="action-button">Modifier</button>
                        </form>
                        <!-- Bouton Supprimer -->
                        <form action="${pageContext.request.contextPath}/admin/trajets" method="post" style="display: inline;">
                            <input type="hidden" name="action" value="delete">
                            <input type="hidden" name="id" value="${trajet.id}">
                            <button type="submit" class="action-button delete">Supprimer</button>
                        </form>
                    </td>
                </tr>
            </c:forEach>
        </table>

        <c:if test="${not empty trajet}">
            <div class="form-section">
                <h3>Modifier un trajet</h3>
                <form action="${pageContext.request.contextPath}/admin/trajets" method="post">
                    <input type="hidden" name="action" value="update">
                    <input type="hidden" name="id" value="${trajet.id}">
                    <label>Gare de départ:
                        <select name="departStationId" required>
                            <c:forEach var="station" items="${stations}">
                                <option value="${station.id}" ${station.id == trajet.departStation.id ? 'selected' : ''}>${station.name}</option>
                            </c:forEach>
                        </select>
                    </label><br>
                    <label>Gare d'arrivée:
                        <select name="arrivalStationId" required>
                            <c:forEach var="station" items="${stations}">
                                <option value="${station.id}" ${station.id == trajet.arrivalStation.id ? 'selected' : ''}>${station.name}</option>
                            </c:forEach>
                        </select>
                    </label><br>
                    <button type="submit" class="action-button">Mettre à jour</button>
                </form>
            </div>
        </c:if>
    </c:if>

    <c:if test="${empty trajets}">
        <p>Aucun trajet trouvé.</p>
    </c:if>

    <p><a href="${pageContext.request.contextPath}/monCompte">Retour à mon compte</a></p>
</body>
</html>