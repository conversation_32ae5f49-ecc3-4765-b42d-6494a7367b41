<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Diagnostic Système - JEE Training</title>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <a href="${pageContext.request.contextPath}/recherche" class="logo">
                <i class="fas fa-train"></i> JEE Training
            </a>
            <div class="nav-links">
                <c:choose>
                    <c:when test="${sessionScope.user != null}">
                        <a href="${pageContext.request.contextPath}/recherche"><i class="fas fa-search"></i> Rechercher</a>
                        <c:if test="${sessionScope.user.role == 'admin'}">
                            <a href="${pageContext.request.contextPath}/admin/dashboard"><i class="fas fa-cog"></i> Admin</a>
                        </c:if>
                        <a href="${pageContext.request.contextPath}/logout"><i class="fas fa-sign-out-alt"></i> Déconnexion</a>
                    </c:when>
                    <c:otherwise>
                        <a href="${pageContext.request.contextPath}/login.jsp"><i class="fas fa-sign-in-alt"></i> Connexion</a>
                        <a href="${pageContext.request.contextPath}/register.jsp"><i class="fas fa-user-plus"></i> Inscription</a>
                    </c:otherwise>
                </c:choose>
            </div>
        </div>
    </nav>

    <!-- Contenu principal -->
    <div class="container">
        <div class="main-content">
            <h1><i class="fas fa-stethoscope"></i> Diagnostic Système</h1>
            
            <!-- Breadcrumb -->
            <div class="breadcrumb">
                <c:if test="${sessionScope.user.role == 'admin'}">
                    <a href="${pageContext.request.contextPath}/admin/dashboard"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                    <span><i class="fas fa-chevron-right"></i></span>
                </c:if>
                <span>Diagnostic Système</span>
            </div>

            <c:choose>
                <c:when test="${diagnosticSuccess}">
                    <!-- Statut général -->
                    <div class="card">
                        <div class="card-header">
                            <h2 class="card-title"><i class="fas fa-check-circle text-success"></i> État du Système</h2>
                        </div>
                        
                        <div class="row">
                            <div class="col-2">
                                <div class="stat-card stat-success">
                                    <div class="stat-icon">
                                        <i class="fas fa-database"></i>
                                    </div>
                                    <div class="stat-content">
                                        <h3>✅</h3>
                                        <p>Base de Données</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-2">
                                <div class="stat-card stat-success">
                                    <div class="stat-icon">
                                        <i class="fas fa-cogs"></i>
                                    </div>
                                    <div class="stat-content">
                                        <h3>✅</h3>
                                        <p>DAO Services</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="diagnostic-status">
                            <div class="status-item">
                                <i class="fas fa-check-circle text-success"></i>
                                <span>${sessionFactoryStatus}</span>
                            </div>
                            <div class="status-item">
                                <i class="fas fa-check-circle text-success"></i>
                                <span>${daoStatus}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Statistiques des données -->
                    <div class="card">
                        <div class="card-header">
                            <h2 class="card-title"><i class="fas fa-chart-bar"></i> Statistiques des Données</h2>
                        </div>
                        
                        <div class="row">
                            <div class="col-3">
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-users"></i>
                                    </div>
                                    <div class="stat-content">
                                        <h3>${users.size()}</h3>
                                        <p>Utilisateurs</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-3">
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-map-marker-alt"></i>
                                    </div>
                                    <div class="stat-content">
                                        <h3>${stations.size()}</h3>
                                        <p>Stations</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-3">
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-route"></i>
                                    </div>
                                    <div class="stat-content">
                                        <h3>${trajets.size()}</h3>
                                        <p>Trajets</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-3">
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-train"></i>
                                    </div>
                                    <div class="stat-content">
                                        <h3>${voyages.size()}</h3>
                                        <p>Voyages</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-3">
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-tags"></i>
                                    </div>
                                    <div class="stat-content">
                                        <h3>${promotions.size()}</h3>
                                        <p>Promotions</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-3">
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-ticket-alt"></i>
                                    </div>
                                    <div class="stat-content">
                                        <h3>${reservations.size()}</h3>
                                        <p>Réservations</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Détails des utilisateurs -->
                    <div class="card">
                        <div class="card-header">
                            <h2 class="card-title"><i class="fas fa-users"></i> Utilisateurs (${users.size()})</h2>
                        </div>
                        
                        <c:if test="${not empty users}">
                            <table class="admin-table">
                                <thead>
                                    <tr>
                                        <th><i class="fas fa-user"></i> Nom d'utilisateur</th>
                                        <th><i class="fas fa-shield-alt"></i> Rôle</th>
                                        <th><i class="fas fa-coins"></i> Points de Fidélité</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <c:forEach var="user" items="${users}">
                                        <tr>
                                            <td>
                                                <div class="user-info">
                                                    <i class="fas fa-user-circle"></i>
                                                    <span>${user.username}</span>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge badge-${user.role == 'admin' ? 'danger' : 'primary'}">
                                                    <i class="fas fa-${user.role == 'admin' ? 'crown' : 'user'}"></i> ${user.role}
                                                </span>
                                            </td>
                                            <td>
                                                <span class="points-badge">
                                                    <i class="fas fa-coins"></i> ${user.loyaltyPoints}
                                                </span>
                                            </td>
                                        </tr>
                                    </c:forEach>
                                </tbody>
                            </table>
                        </c:if>
                    </div>

                    <!-- Détails des stations -->
                    <div class="card">
                        <div class="card-header">
                            <h2 class="card-title"><i class="fas fa-map-marker-alt"></i> Stations (${stations.size()})</h2>
                        </div>
                        
                        <c:if test="${not empty stations}">
                            <div class="row">
                                <c:forEach var="station" items="${stations}">
                                    <div class="col-3">
                                        <div class="station-card">
                                            <i class="fas fa-train"></i>
                                            <h4>${station.name}</h4>
                                            <p>${station.city}</p>
                                        </div>
                                    </div>
                                </c:forEach>
                            </div>
                        </c:if>
                    </div>

                    <!-- Détails des trajets -->
                    <div class="card">
                        <div class="card-header">
                            <h2 class="card-title"><i class="fas fa-route"></i> Trajets (${trajets.size()})</h2>
                        </div>
                        
                        <c:if test="${not empty trajets}">
                            <div class="row">
                                <c:forEach var="trajet" items="${trajets}">
                                    <div class="col-4">
                                        <div class="trajet-card">
                                            <div class="trajet-route">
                                                <span class="departure">
                                                    <i class="fas fa-play"></i> ${trajet.departStation.name}
                                                </span>
                                                <i class="fas fa-arrow-right"></i>
                                                <span class="arrival">
                                                    <i class="fas fa-stop"></i> ${trajet.arrivalStation.name}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </c:forEach>
                            </div>
                        </c:if>
                    </div>

                    <!-- Détails des voyages -->
                    <div class="card">
                        <div class="card-header">
                            <h2 class="card-title"><i class="fas fa-train"></i> Voyages (${voyages.size()})</h2>
                        </div>
                        
                        <c:if test="${not empty voyages}">
                            <table class="admin-table">
                                <thead>
                                    <tr>
                                        <th><i class="fas fa-route"></i> Trajet</th>
                                        <th><i class="fas fa-euro-sign"></i> Prix</th>
                                        <th><i class="fas fa-users"></i> Places</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <c:forEach var="voyage" items="${voyages}">
                                        <tr>
                                            <td>
                                                <span class="trajet-badge">
                                                    <i class="fas fa-arrow-right"></i>
                                                    ${voyage.trajet.departStation.name} → ${voyage.trajet.arrivalStation.name}
                                                </span>
                                            </td>
                                            <td>
                                                <span class="price-badge">
                                                    <i class="fas fa-euro-sign"></i> ${voyage.prix}€
                                                </span>
                                            </td>
                                            <td>
                                                <span class="places-count">
                                                    <i class="fas fa-users"></i> ${voyage.placesDisponibles}
                                                </span>
                                            </td>
                                        </tr>
                                    </c:forEach>
                                </tbody>
                            </table>
                        </c:if>
                    </div>

                    <!-- Détails des promotions -->
                    <div class="card">
                        <div class="card-header">
                            <h2 class="card-title"><i class="fas fa-tags"></i> Promotions (${promotions.size()})</h2>
                        </div>
                        
                        <c:if test="${not empty promotions}">
                            <div class="row">
                                <c:forEach var="promotion" items="${promotions}">
                                    <div class="col-3">
                                        <div class="promotion-card">
                                            <div class="promo-header">
                                                <span class="promo-code">
                                                    <i class="fas fa-tag"></i> ${promotion.code}
                                                </span>
                                                <span class="discount-badge">
                                                    <i class="fas fa-percent"></i> ${promotion.discountPercentage}%
                                                </span>
                                            </div>
                                            <p class="promo-description">${promotion.description}</p>
                                        </div>
                                    </div>
                                </c:forEach>
                            </div>
                        </c:if>
                    </div>

                    <!-- Détails des réservations -->
                    <div class="card">
                        <div class="card-header">
                            <h2 class="card-title"><i class="fas fa-ticket-alt"></i> Réservations (${reservations.size()})</h2>
                        </div>
                        
                        <c:if test="${not empty reservations}">
                            <table class="admin-table">
                                <thead>
                                    <tr>
                                        <th><i class="fas fa-user"></i> Utilisateur</th>
                                        <th><i class="fas fa-route"></i> Trajet</th>
                                        <th><i class="fas fa-star"></i> Classe</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <c:forEach var="reservation" items="${reservations}">
                                        <tr>
                                            <td>
                                                <div class="user-info">
                                                    <i class="fas fa-user-circle"></i>
                                                    <span>${reservation.user.username}</span>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="trajet-badge">
                                                    <i class="fas fa-arrow-right"></i>
                                                    ${reservation.voyage.trajet.departStation.name} → ${reservation.voyage.trajet.arrivalStation.name}
                                                </span>
                                            </td>
                                            <td>
                                                <span class="classe-badge classe-${reservation.classe}">
                                                    <i class="fas fa-star"></i> ${reservation.classe}
                                                </span>
                                            </td>
                                        </tr>
                                    </c:forEach>
                                </tbody>
                            </table>
                        </c:if>
                    </div>

                </c:when>
                <c:otherwise>
                    <!-- Erreur de diagnostic -->
                    <div class="card">
                        <div class="card-header">
                            <h2 class="card-title"><i class="fas fa-exclamation-triangle text-danger"></i> Erreur de Diagnostic</h2>
                        </div>
                        
                        <div class="admin-message admin-message-error">
                            <i class="fas fa-times-circle"></i>
                            <strong>Erreur :</strong> ${errorMessage}
                        </div>
                        
                        <c:if test="${not empty stackTrace}">
                            <div class="error-details">
                                <h4><i class="fas fa-bug"></i> Détails de l'erreur :</h4>
                                <pre class="error-stack">${stackTrace}</pre>
                            </div>
                        </c:if>
                    </div>
                </c:otherwise>
            </c:choose>

            <!-- Actions rapides -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title"><i class="fas fa-bolt"></i> Actions Rapides</h2>
                </div>
                
                <div class="row">
                    <div class="col-3">
                        <c:choose>
                            <c:when test="${sessionScope.user.role == 'admin'}">
                                <a href="${pageContext.request.contextPath}/admin/dashboard" class="btn btn-secondary btn-full">
                                    <i class="fas fa-arrow-left"></i> Retour Dashboard
                                </a>
                            </c:when>
                            <c:otherwise>
                                <a href="${pageContext.request.contextPath}/recherche" class="btn btn-secondary btn-full">
                                    <i class="fas fa-arrow-left"></i> Retour Recherche
                                </a>
                            </c:otherwise>
                        </c:choose>
                    </div>
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/diagnostic" class="btn btn-primary btn-full">
                            <i class="fas fa-sync"></i> Actualiser Diagnostic
                        </a>
                    </div>
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/init-data" class="btn btn-warning btn-full" 
                           onclick="return confirm('Êtes-vous sûr de vouloir réinitialiser la base de données ?');">
                            <i class="fas fa-database"></i> Réinitialiser BDD
                        </a>
                    </div>
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/login.jsp" class="btn btn-success btn-full">
                            <i class="fas fa-sign-in-alt"></i> Page de Connexion
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
