<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔐 Connexion - JEE Training</title>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <a href="${pageContext.request.contextPath}/recherche" class="logo">
                <i class="fas fa-train"></i> JEE Training
            </a>
            <div class="nav-links">
                <a href="${pageContext.request.contextPath}/register.jsp"><i class="fas fa-user-plus"></i> Inscription</a>
            </div>
        </div>
    </nav>

    <!-- Contenu principal -->
    <div class="container">
        <div class="form-container">
            <h1><i class="fas fa-sign-in-alt"></i> Connexion</h1>

            <!-- Messages d'erreur -->
            <c:if test="${not empty error}">
                <div class="alert alert-error" role="alert">
                    <i class="fas fa-exclamation-triangle"></i> ${error}
                </div>
            </c:if>

            <!-- Messages de succès -->
            <c:if test="${not empty success}">
                <div class="alert alert-success" role="alert">
                    <i class="fas fa-check-circle"></i> ${success}
                </div>
            </c:if>

            <form action="login" method="post">
                <div class="form-group">
                    <label for="email"><i class="fas fa-envelope"></i> Adresse email</label>
                    <input type="email" id="email" name="email" class="form-control"
                           placeholder="Entrez votre adresse email" required>
                </div>

                <div class="form-group">
                    <label for="password"><i class="fas fa-lock"></i> Mot de passe</label>
                    <input type="password" id="password" name="password" class="form-control"
                           placeholder="Entrez votre mot de passe" required>
                </div>

                <button type="submit" class="btn btn-primary btn-full">
                    <i class="fas fa-sign-in-alt"></i> Se connecter
                </button>
            </form>

            <div class="text-center mt-20">
                <p>Pas encore de compte ?</p>
                <a href="${pageContext.request.contextPath}/register.jsp" class="btn btn-secondary">
                    <i class="fas fa-user-plus"></i> Créer un compte
                </a>
            </div>


        </div>
    </div>
</body>
</html>