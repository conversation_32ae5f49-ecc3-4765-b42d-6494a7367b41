<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns="https://jakarta.ee/xml/ns/jakartaee"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="https://jakarta.ee/xml/ns/jakartaee
                             https://jakarta.ee/xml/ns/jakartaee/web-app_6_0.xsd"
         version="6.0">

    <display-name>JeeTrain Application</display-name>
    <description>Application de réservation de trains</description>

    <!-- Configuration de l'encodage des caractères -->
    <request-character-encoding>UTF-8</request-character-encoding>
    <response-character-encoding>UTF-8</response-character-encoding>

    <!-- Page d'accueil par défaut -->
    <welcome-file-list>
        <welcome-file>index.jsp</welcome-file>
        <welcome-file>login.jsp</welcome-file>
    </welcome-file-list>

    <!-- Configuration des sessions -->
    <session-config>
        <session-timeout>30</session-timeout>
        <cookie-config>
            <http-only>true</http-only>
            <secure>false</secure>
        </cookie-config>
    </session-config>

    <!-- Gestion des erreurs -->
    <error-page>
        <error-code>404</error-code>
        <location>/error/404.jsp</location>
    </error-page>
    
    <error-page>
        <error-code>500</error-code>
        <location>/error/500.jsp</location>
    </error-page>

    <!-- Filtres pour l'authentification -->
    <filter>
        <filter-name>AuthenticationFilter</filter-name>
        <filter-class>filter.AuthenticationFilter</filter-class>
    </filter>
    
    <filter-mapping>
        <filter-name>AuthenticationFilter</filter-name>
        <url-pattern>/dashboard/*</url-pattern>
        <url-pattern>/admin/*</url-pattern>
        <url-pattern>/mes-reservations</url-pattern>
        <url-pattern>/recherche</url-pattern>
    </filter-mapping>

    <!-- Configuration JSTL -->
    <jsp-config>
        <jsp-property-group>
            <url-pattern>*.jsp</url-pattern>
            <page-encoding>UTF-8</page-encoding>
            <scripting-invalid>false</scripting-invalid>
            <include-prelude>/WEB-INF/jsp/common/taglibs.jsp</include-prelude>
        </jsp-property-group>
    </jsp-config>

</web-app>
