-- Script de création de la base de données JeeTrain
-- Exécuter ce script dans MySQL pour créer la base de données

-- Créer la base de données
CREATE DATABASE IF NOT EXISTS jeetraindb 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- Utiliser la base de données
USE jeetraindb;

-- Créer un utilisateur pour l'application (optionnel)
-- CREATE USER IF NOT EXISTS 'jeetrain_user'@'localhost' IDENTIFIED BY 'jeetrain_password';
-- GRANT ALL PRIVILEGES ON jeetraindb.* TO 'jeetrain_user'@'localhost';
-- FLUSH PRIVILEGES;

-- Les tables seront créées automatiquement par Hibernate avec hbm2ddl.auto=update

-- Vérifier que la base de données a été créée
SHOW DATABASES LIKE 'jeetraindb';

-- Afficher les informations de la base de données
SELECT 
    SCHEMA_NAME as 'Database Name',
    DEFAULT_CHARACTER_SET_NAME as 'Character Set',
    DEFAULT_COLLATION_NAME as 'Collation'
FROM information_schema.SCHEMATA 
WHERE SCHEMA_NAME = 'jeetraindb';
