/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: Apache Tomcat/10.1.41
 * Generated at: 2025-05-29 23:09:00 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp;

import jakarta.servlet.*;
import jakarta.servlet.http.*;
import jakarta.servlet.jsp.*;

public final class demo_002dfonctionnalites_002dutilisateur_002dfinales_jsp extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent,
                 org.apache.jasper.runtime.JspSourceImports,
                 org.apache.jasper.runtime.JspSourceDirectives {

  private static final jakarta.servlet.jsp.JspFactory _jspxFactory =
          jakarta.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  static {
    _jspx_dependants = new java.util.HashMap<java.lang.String,java.lang.Long>(2);
    _jspx_dependants.put("/WEB-INF/lib/jakarta.servlet.jsp.jstl-3.0.1.jar", Long.valueOf(1747175778000L));
    _jspx_dependants.put("jar:file:/C:/Users/<USER>/Downloads/jeetraing/apache-tomcat-10.1.41/webapps/jeetraing/WEB-INF/lib/jakarta.servlet.jsp.jstl-3.0.1.jar!/META-INF/c-1_2.tld", Long.valueOf(1664449878000L));
  }

  private static final java.util.Set<java.lang.String> _jspx_imports_packages;

  private static final java.util.Set<java.lang.String> _jspx_imports_classes;

  static {
    _jspx_imports_packages = new java.util.LinkedHashSet<>(4);
    _jspx_imports_packages.add("jakarta.servlet");
    _jspx_imports_packages.add("jakarta.servlet.http");
    _jspx_imports_packages.add("jakarta.servlet.jsp");
    _jspx_imports_classes = null;
  }

  private volatile jakarta.el.ExpressionFactory _el_expressionfactory;
  private volatile org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public java.util.Set<java.lang.String> getPackageImports() {
    return _jspx_imports_packages;
  }

  public java.util.Set<java.lang.String> getClassImports() {
    return _jspx_imports_classes;
  }

  public boolean getErrorOnELNotFound() {
    return false;
  }

  public jakarta.el.ExpressionFactory _jsp_getExpressionFactory() {
    if (_el_expressionfactory == null) {
      synchronized (this) {
        if (_el_expressionfactory == null) {
          _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
        }
      }
    }
    return _el_expressionfactory;
  }

  public org.apache.tomcat.InstanceManager _jsp_getInstanceManager() {
    if (_jsp_instancemanager == null) {
      synchronized (this) {
        if (_jsp_instancemanager == null) {
          _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
        }
      }
    }
    return _jsp_instancemanager;
  }

  public void _jspInit() {
  }

  public void _jspDestroy() {
  }

  public void _jspService(final jakarta.servlet.http.HttpServletRequest request, final jakarta.servlet.http.HttpServletResponse response)
      throws java.io.IOException, jakarta.servlet.ServletException {

    if (!jakarta.servlet.DispatcherType.ERROR.equals(request.getDispatcherType())) {
      final java.lang.String _jspx_method = request.getMethod();
      if ("OPTIONS".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        return;
      }
      if (!"GET".equals(_jspx_method) && !"POST".equals(_jspx_method) && !"HEAD".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        response.sendError(HttpServletResponse.SC_METHOD_NOT_ALLOWED, "Les JSPs ne permettent que GET, POST ou HEAD. Jasper permet aussi OPTIONS");
        return;
      }
    }

    final jakarta.servlet.jsp.PageContext pageContext;
    jakarta.servlet.http.HttpSession session = null;
    final jakarta.servlet.ServletContext application;
    final jakarta.servlet.ServletConfig config;
    jakarta.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    jakarta.servlet.jsp.JspWriter _jspx_out = null;
    jakarta.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html; charset=UTF-8");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;

      out.write("\n");
      out.write("\n");
      out.write("<!DOCTYPE html>\n");
      out.write("<html lang=\"fr\">\n");
      out.write("<head>\n");
      out.write("    <meta charset=\"UTF-8\">\n");
      out.write("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n");
      out.write("    <title>🎯 Fonctionnalités Utilisateur Complètes - JEE Training</title>\n");
      out.write("    <link rel=\"stylesheet\" href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/css/style.css\">\n");
      out.write("    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\">\n");
      out.write("</head>\n");
      out.write("<body>\n");
      out.write("    <!-- Navigation -->\n");
      out.write("    <nav class=\"navbar\">\n");
      out.write("        <div class=\"container\">\n");
      out.write("            <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/recherche\" class=\"logo\">\n");
      out.write("                <i class=\"fas fa-train\"></i> JEE Training\n");
      out.write("            </a>\n");
      out.write("            <div class=\"nav-links\">\n");
      out.write("                <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/login.jsp\"><i class=\"fas fa-sign-in-alt\"></i> Connexion</a>\n");
      out.write("                <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/register.jsp\"><i class=\"fas fa-user-plus\"></i> Inscription</a>\n");
      out.write("            </div>\n");
      out.write("        </div>\n");
      out.write("    </nav>\n");
      out.write("\n");
      out.write("    <!-- Contenu principal -->\n");
      out.write("    <div class=\"container\">\n");
      out.write("        <div class=\"main-content\">\n");
      out.write("            <h1><i class=\"fas fa-rocket\"></i> Fonctionnalités Utilisateur Complètes - JEE Training</h1>\n");
      out.write("            \n");
      out.write("            <!-- Message de succès -->\n");
      out.write("            <div class=\"alert alert-success\">\n");
      out.write("                <i class=\"fas fa-check-circle\"></i> \n");
      out.write("                <strong>🎉 TOUTES LES FONCTIONNALITÉS DEMANDÉES SONT MAINTENANT OPÉRATIONNELLES ! 🎉</strong>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Navigation utilisateur améliorée -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-compass\"></i> Navigation Utilisateur Enrichie</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"navbar-demo\">\n");
      out.write("                    <h4>🔗 Nouveaux liens dans la navbar pour utilisateurs connectés :</h4>\n");
      out.write("                    <div class=\"demo-navbar\">\n");
      out.write("                        <div class=\"demo-nav-item\">\n");
      out.write("                            <i class=\"fas fa-search\"></i> Rechercher un trajet\n");
      out.write("                        </div>\n");
      out.write("                        <div class=\"demo-nav-item\">\n");
      out.write("                            <i class=\"fas fa-user\"></i> Mon compte\n");
      out.write("                        </div>\n");
      out.write("                        <div class=\"demo-nav-item highlight\">\n");
      out.write("                            <i class=\"fas fa-history\"></i> Historique des voyages\n");
      out.write("                        </div>\n");
      out.write("                        <div class=\"demo-nav-item highlight\">\n");
      out.write("                            <i class=\"fas fa-ticket-alt\"></i> Mes réservations\n");
      out.write("                        </div>\n");
      out.write("                        <div class=\"demo-nav-item highlight\">\n");
      out.write("                            <i class=\"fas fa-times-circle\"></i> Annuler réservation\n");
      out.write("                        </div>\n");
      out.write("                        <div class=\"demo-nav-item\">\n");
      out.write("                            <i class=\"fas fa-sign-out-alt\"></i> Se déconnecter\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <small class=\"text-muted\">✨ Les éléments en surbrillance sont les nouveaux liens ajoutés</small>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Fonctionnalités principales -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-star\"></i> Fonctionnalités Principales Implémentées</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-2\">\n");
      out.write("                        <div class=\"admin-card\">\n");
      out.write("                            <div class=\"admin-card-icon\">\n");
      out.write("                                <i class=\"fas fa-ticket-alt\"></i>\n");
      out.write("                            </div>\n");
      out.write("                            <div class=\"admin-card-content\">\n");
      out.write("                                <h3>🎫 Mes Réservations</h3>\n");
      out.write("                                <p><strong>URL:</strong> <code>/mes-reservations</code></p>\n");
      out.write("                                <ul style=\"font-size: 12px;\">\n");
      out.write("                                    <li>✅ Consultation de toutes les réservations</li>\n");
      out.write("                                    <li>✅ Statistiques visuelles</li>\n");
      out.write("                                    <li>✅ Actions : Modifier, PDF, Annuler</li>\n");
      out.write("                                    <li>✅ Interface moderne et responsive</li>\n");
      out.write("                                    <li>✅ Badges colorés pour les états</li>\n");
      out.write("                                </ul>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-2\">\n");
      out.write("                        <div class=\"admin-card\">\n");
      out.write("                            <div class=\"admin-card-icon\">\n");
      out.write("                                <i class=\"fas fa-edit\"></i>\n");
      out.write("                            </div>\n");
      out.write("                            <div class=\"admin-card-content\">\n");
      out.write("                                <h3>✏️ Modifier Réservation</h3>\n");
      out.write("                                <p><strong>URL:</strong> <code>/modifierReservation</code></p>\n");
      out.write("                                <ul style=\"font-size: 12px;\">\n");
      out.write("                                    <li>✅ Modification classe de voyage</li>\n");
      out.write("                                    <li>✅ Gestion des préférences</li>\n");
      out.write("                                    <li>✅ Interface utilisateur moderne</li>\n");
      out.write("                                    <li>✅ Validation des permissions</li>\n");
      out.write("                                    <li>✅ Breadcrumb navigation</li>\n");
      out.write("                                </ul>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-2\">\n");
      out.write("                        <div class=\"admin-card\">\n");
      out.write("                            <div class=\"admin-card-icon\">\n");
      out.write("                                <i class=\"fas fa-times-circle\"></i>\n");
      out.write("                            </div>\n");
      out.write("                            <div class=\"admin-card-content\">\n");
      out.write("                                <h3>❌ Annuler Réservation</h3>\n");
      out.write("                                <p><strong>URL:</strong> <code>/annuler-reservation</code></p>\n");
      out.write("                                <ul style=\"font-size: 12px;\">\n");
      out.write("                                    <li>✅ Processus guidé d'annulation</li>\n");
      out.write("                                    <li>✅ Conditions clairement affichées</li>\n");
      out.write("                                    <li>✅ Motif d'annulation optionnel</li>\n");
      out.write("                                    <li>✅ Validation administrateur</li>\n");
      out.write("                                    <li>✅ Confirmations de sécurité</li>\n");
      out.write("                                </ul>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-2\">\n");
      out.write("                        <div class=\"admin-card\">\n");
      out.write("                            <div class=\"admin-card-icon\">\n");
      out.write("                                <i class=\"fas fa-history\"></i>\n");
      out.write("                            </div>\n");
      out.write("                            <div class=\"admin-card-content\">\n");
      out.write("                                <h3>📚 Historique Voyages</h3>\n");
      out.write("                                <p><strong>URL:</strong> <code>/historique</code></p>\n");
      out.write("                                <ul style=\"font-size: 12px;\">\n");
      out.write("                                    <li>✅ Voyages effectués (état \"utilisé\")</li>\n");
      out.write("                                    <li>✅ Statistiques personnalisées</li>\n");
      out.write("                                    <li>✅ Statut voyageur (Bronze à VIP)</li>\n");
      out.write("                                    <li>✅ Total dépensé calculé</li>\n");
      out.write("                                    <li>✅ Interface moderne avec badges</li>\n");
      out.write("                                </ul>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Téléchargement PDF corrigé -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-file-pdf\"></i> Téléchargement PDF Corrigé</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4>🔧 Problème Résolu</h4>\n");
      out.write("                            <div style=\"background: #f8d7da; padding: 15px; border-radius: 5px; margin-bottom: 10px;\">\n");
      out.write("                                <strong>❌ Avant :</strong> Erreur 404 sur <code>/download-ticket</code>\n");
      out.write("                            </div>\n");
      out.write("                            <div style=\"background: #d4edda; padding: 15px; border-radius: 5px;\">\n");
      out.write("                                <strong>✅ Après :</strong> Servlet fonctionnel avec PDF généré\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4>🛠️ Solution Technique</h4>\n");
      out.write("                            <ul style=\"font-size: 12px;\">\n");
      out.write("                                <li>✅ Nouveau servlet <code>DownloadTicketServlet</code></li>\n");
      out.write("                                <li>✅ URL correcte : <code>/download-ticket</code></li>\n");
      out.write("                                <li>✅ Génération PDF avec PDFBox</li>\n");
      out.write("                                <li>✅ Validation des permissions</li>\n");
      out.write("                                <li>✅ Gestion des erreurs complète</li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4>📄 Contenu du PDF</h4>\n");
      out.write("                            <ul style=\"font-size: 12px;\">\n");
      out.write("                                <li>✅ Informations de réservation</li>\n");
      out.write("                                <li>✅ Détails du voyage</li>\n");
      out.write("                                <li>✅ Informations passager</li>\n");
      out.write("                                <li>✅ Classe et préférences</li>\n");
      out.write("                                <li>✅ Design professionnel</li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Améliorations techniques -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-code\"></i> Améliorations Techniques Complètes</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-server\"></i> Servlets</h4>\n");
      out.write("                            <ul style=\"font-size: 12px;\">\n");
      out.write("                                <li>✅ <strong>MesReservationsServlet</strong> - Gestion réservations</li>\n");
      out.write("                                <li>✅ <strong>DownloadTicketServlet</strong> - PDF fonctionnel</li>\n");
      out.write("                                <li>✅ <strong>AnnulerReservationServlet</strong> - Demandes annulation</li>\n");
      out.write("                                <li>✅ <strong>ModifierReservationServlet</strong> - Amélioré</li>\n");
      out.write("                                <li>✅ <strong>HistoriqueServlet</strong> - Déjà existant</li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-database\"></i> Base de Données</h4>\n");
      out.write("                            <ul style=\"font-size: 12px;\">\n");
      out.write("                                <li>✅ <code>findByUserExceptEtat()</code> - Nouvelle méthode</li>\n");
      out.write("                                <li>✅ Gestion des états de réservation</li>\n");
      out.write("                                <li>✅ Validation des permissions</li>\n");
      out.write("                                <li>✅ Initialisation Hibernate complète</li>\n");
      out.write("                                <li>✅ Gestion d'erreurs robuste</li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-palette\"></i> Interface</h4>\n");
      out.write("                            <ul style=\"font-size: 12px;\">\n");
      out.write("                                <li>✅ Design cohérent avec admin</li>\n");
      out.write("                                <li>✅ Nouveaux composants CSS</li>\n");
      out.write("                                <li>✅ Responsive design</li>\n");
      out.write("                                <li>✅ Animations et transitions</li>\n");
      out.write("                                <li>✅ Breadcrumb navigation</li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- URLs de test -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-link\"></i> URLs Complètes pour Tests</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-user\"></i> Pages Utilisateur</h4>\n");
      out.write("                            <div class=\"url-list\">\n");
      out.write("                                <div class=\"url-item\">\n");
      out.write("                                    <code>/mes-reservations</code>\n");
      out.write("                                    <small>Gestion complète des réservations</small>\n");
      out.write("                                </div>\n");
      out.write("                                <div class=\"url-item\">\n");
      out.write("                                    <code>/modifierReservation?id=X</code>\n");
      out.write("                                    <small>Modification classe et préférences</small>\n");
      out.write("                                </div>\n");
      out.write("                                <div class=\"url-item\">\n");
      out.write("                                    <code>/annuler-reservation</code>\n");
      out.write("                                    <small>Demande d'annulation sécurisée</small>\n");
      out.write("                                </div>\n");
      out.write("                                <div class=\"url-item\">\n");
      out.write("                                    <code>/historique</code>\n");
      out.write("                                    <small>Voyages effectués avec stats</small>\n");
      out.write("                                </div>\n");
      out.write("                                <div class=\"url-item\">\n");
      out.write("                                    <code>/download-ticket?reservationId=X</code>\n");
      out.write("                                    <small>Téléchargement PDF fonctionnel</small>\n");
      out.write("                                </div>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-check\"></i> Pages Modifiées</h4>\n");
      out.write("                            <div class=\"url-list\">\n");
      out.write("                                <div class=\"url-item\">\n");
      out.write("                                    <code>/confirmation</code>\n");
      out.write("                                    <small>Bouton PDF au lieu d'Imprimer</small>\n");
      out.write("                                </div>\n");
      out.write("                                <div class=\"url-item\">\n");
      out.write("                                    <code>/admin/users</code>\n");
      out.write("                                    <small>Bouton débloquer orange</small>\n");
      out.write("                                </div>\n");
      out.write("                                <div class=\"url-item\">\n");
      out.write("                                    <code>/navbar.jsp</code>\n");
      out.write("                                    <small>Liens utilisateur ajoutés</small>\n");
      out.write("                                </div>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-eye\"></i> Démonstrations</h4>\n");
      out.write("                            <div class=\"url-list\">\n");
      out.write("                                <div class=\"url-item\">\n");
      out.write("                                    <code>/demo-fonctionnalites-utilisateur-finales.jsp</code>\n");
      out.write("                                    <small>Cette page de démonstration</small>\n");
      out.write("                                </div>\n");
      out.write("                                <div class=\"url-item\">\n");
      out.write("                                    <code>/demo-ameliorations-utilisateur.jsp</code>\n");
      out.write("                                    <small>Résumé des améliorations</small>\n");
      out.write("                                </div>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Workflow utilisateur complet -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-route\"></i> Workflow Utilisateur Complet</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"workflow-steps\">\n");
      out.write("                    <div class=\"workflow-step\">\n");
      out.write("                        <div class=\"step-number\">1</div>\n");
      out.write("                        <div class=\"step-content\">\n");
      out.write("                            <h4><i class=\"fas fa-sign-in-alt\"></i> Connexion</h4>\n");
      out.write("                            <p>L'utilisateur se connecte et accède à la navbar enrichie</p>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    \n");
      out.write("                    <div class=\"workflow-step\">\n");
      out.write("                        <div class=\"step-number\">2</div>\n");
      out.write("                        <div class=\"step-content\">\n");
      out.write("                            <h4><i class=\"fas fa-search\"></i> Recherche & Réservation</h4>\n");
      out.write("                            <p>Recherche de trajets et création de réservations</p>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    \n");
      out.write("                    <div class=\"workflow-step\">\n");
      out.write("                        <div class=\"step-number\">3</div>\n");
      out.write("                        <div class=\"step-content\">\n");
      out.write("                            <h4><i class=\"fas fa-ticket-alt\"></i> Gestion Réservations</h4>\n");
      out.write("                            <p>Consultation via \"Mes réservations\" avec statistiques</p>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    \n");
      out.write("                    <div class=\"workflow-step\">\n");
      out.write("                        <div class=\"step-number\">4</div>\n");
      out.write("                        <div class=\"step-content\">\n");
      out.write("                            <h4><i class=\"fas fa-edit\"></i> Modification</h4>\n");
      out.write("                            <p>Modification classe et préférences si nécessaire</p>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    \n");
      out.write("                    <div class=\"workflow-step\">\n");
      out.write("                        <div class=\"step-number\">5</div>\n");
      out.write("                        <div class=\"step-content\">\n");
      out.write("                            <h4><i class=\"fas fa-file-pdf\"></i> Téléchargement</h4>\n");
      out.write("                            <p>Téléchargement du billet en PDF</p>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    \n");
      out.write("                    <div class=\"workflow-step\">\n");
      out.write("                        <div class=\"step-number\">6</div>\n");
      out.write("                        <div class=\"step-content\">\n");
      out.write("                            <h4><i class=\"fas fa-times-circle\"></i> Annulation</h4>\n");
      out.write("                            <p>Demande d'annulation si besoin avec validation admin</p>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    \n");
      out.write("                    <div class=\"workflow-step\">\n");
      out.write("                        <div class=\"step-number\">7</div>\n");
      out.write("                        <div class=\"step-content\">\n");
      out.write("                            <h4><i class=\"fas fa-history\"></i> Historique</h4>\n");
      out.write("                            <p>Consultation de l'historique des voyages effectués</p>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Actions de test -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-rocket\"></i> Tester Toutes les Fonctionnalités</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/login.jsp\" class=\"btn btn-primary btn-full\">\n");
      out.write("                            <i class=\"fas fa-sign-in-alt\"></i> Se Connecter pour Tester\n");
      out.write("                        </a>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/mes-reservations\" class=\"btn btn-success btn-full\">\n");
      out.write("                            <i class=\"fas fa-ticket-alt\"></i> Mes Réservations\n");
      out.write("                        </a>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/historique\" class=\"btn btn-warning btn-full\">\n");
      out.write("                            <i class=\"fas fa-history\"></i> Historique Voyages\n");
      out.write("                        </a>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Message final -->\n");
      out.write("            <div class=\"text-center mt-20\">\n");
      out.write("                <div class=\"alert alert-success\">\n");
      out.write("                    <i class=\"fas fa-trophy\"></i> \n");
      out.write("                    <strong>🏆 MISSION ACCOMPLIE ! 🏆</strong>\n");
      out.write("                    <br>\n");
      out.write("                    <small>Toutes les fonctionnalités utilisateur demandées sont maintenant opérationnelles :</small>\n");
      out.write("                    <br>\n");
      out.write("                    <div style=\"margin-top: 10px;\">\n");
      out.write("                        <span class=\"badge badge-success\">✅ Navigation enrichie</span>\n");
      out.write("                        <span class=\"badge badge-success\">✅ Gestion réservations</span>\n");
      out.write("                        <span class=\"badge badge-success\">✅ Modification</span>\n");
      out.write("                        <span class=\"badge badge-success\">✅ Annulation</span>\n");
      out.write("                        <span class=\"badge badge-success\">✅ PDF fonctionnel</span>\n");
      out.write("                        <span class=\"badge badge-success\">✅ Historique</span>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("        </div>\n");
      out.write("    </div>\n");
      out.write("\n");
      out.write("    <style>\n");
      out.write("        .navbar-demo {\n");
      out.write("            padding: 20px;\n");
      out.write("            background: var(--light-bg);\n");
      out.write("            border-radius: 8px;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .demo-navbar {\n");
      out.write("            display: flex;\n");
      out.write("            flex-wrap: wrap;\n");
      out.write("            gap: 10px;\n");
      out.write("            margin: 15px 0;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .demo-nav-item {\n");
      out.write("            padding: 8px 12px;\n");
      out.write("            background: var(--white);\n");
      out.write("            border: 1px solid var(--border-color);\n");
      out.write("            border-radius: 5px;\n");
      out.write("            font-size: 14px;\n");
      out.write("            display: flex;\n");
      out.write("            align-items: center;\n");
      out.write("            gap: 5px;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .demo-nav-item.highlight {\n");
      out.write("            background: linear-gradient(135deg, var(--success-color), #27ae60);\n");
      out.write("            color: var(--white);\n");
      out.write("            border-color: var(--success-color);\n");
      out.write("            font-weight: 600;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .url-list {\n");
      out.write("            display: flex;\n");
      out.write("            flex-direction: column;\n");
      out.write("            gap: 8px;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .url-item {\n");
      out.write("            padding: 8px;\n");
      out.write("            background: var(--light-bg);\n");
      out.write("            border-radius: 5px;\n");
      out.write("            border-left: 3px solid var(--primary-color);\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .url-item code {\n");
      out.write("            display: block;\n");
      out.write("            font-weight: 600;\n");
      out.write("            color: var(--primary-color);\n");
      out.write("            margin-bottom: 2px;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .url-item small {\n");
      out.write("            color: var(--text-muted);\n");
      out.write("            font-size: 11px;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .workflow-steps {\n");
      out.write("            display: flex;\n");
      out.write("            flex-direction: column;\n");
      out.write("            gap: 15px;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .workflow-step {\n");
      out.write("            display: flex;\n");
      out.write("            align-items: flex-start;\n");
      out.write("            gap: 15px;\n");
      out.write("            padding: 15px;\n");
      out.write("            background: var(--light-bg);\n");
      out.write("            border-radius: 8px;\n");
      out.write("            border-left: 4px solid var(--primary-color);\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .step-number {\n");
      out.write("            width: 30px;\n");
      out.write("            height: 30px;\n");
      out.write("            background: var(--primary-color);\n");
      out.write("            color: var(--white);\n");
      out.write("            border-radius: 50%;\n");
      out.write("            display: flex;\n");
      out.write("            align-items: center;\n");
      out.write("            justify-content: center;\n");
      out.write("            font-weight: 600;\n");
      out.write("            flex-shrink: 0;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .step-content h4 {\n");
      out.write("            margin: 0 0 5px 0;\n");
      out.write("            color: var(--text-dark);\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .step-content p {\n");
      out.write("            margin: 0;\n");
      out.write("            color: var(--text-muted);\n");
      out.write("            font-size: 14px;\n");
      out.write("        }\n");
      out.write("    </style>\n");
      out.write("</body>\n");
      out.write("</html>\n");
    } catch (java.lang.Throwable t) {
      if (!(t instanceof jakarta.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try {
            if (response.isCommitted()) {
              out.flush();
            } else {
              out.clearBuffer();
            }
          } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }
}
