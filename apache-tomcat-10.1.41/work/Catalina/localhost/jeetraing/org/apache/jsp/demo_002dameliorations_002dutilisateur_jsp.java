/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: Apache Tomcat/10.1.41
 * Generated at: 2025-05-29 12:20:59 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp;

import jakarta.servlet.*;
import jakarta.servlet.http.*;
import jakarta.servlet.jsp.*;

public final class demo_002dameliorations_002dutilisateur_jsp extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent,
                 org.apache.jasper.runtime.JspSourceImports,
                 org.apache.jasper.runtime.JspSourceDirectives {

  private static final jakarta.servlet.jsp.JspFactory _jspxFactory =
          jakarta.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  static {
    _jspx_dependants = new java.util.HashMap<java.lang.String,java.lang.Long>(2);
    _jspx_dependants.put("/WEB-INF/lib/jakarta.servlet.jsp.jstl-3.0.1.jar", Long.valueOf(1747175778000L));
    _jspx_dependants.put("jar:file:/C:/Users/<USER>/Downloads/jeetraing/apache-tomcat-10.1.41/webapps/jeetraing/WEB-INF/lib/jakarta.servlet.jsp.jstl-3.0.1.jar!/META-INF/c-1_2.tld", Long.valueOf(1664449878000L));
  }

  private static final java.util.Set<java.lang.String> _jspx_imports_packages;

  private static final java.util.Set<java.lang.String> _jspx_imports_classes;

  static {
    _jspx_imports_packages = new java.util.LinkedHashSet<>(4);
    _jspx_imports_packages.add("jakarta.servlet");
    _jspx_imports_packages.add("jakarta.servlet.http");
    _jspx_imports_packages.add("jakarta.servlet.jsp");
    _jspx_imports_classes = null;
  }

  private volatile jakarta.el.ExpressionFactory _el_expressionfactory;
  private volatile org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public java.util.Set<java.lang.String> getPackageImports() {
    return _jspx_imports_packages;
  }

  public java.util.Set<java.lang.String> getClassImports() {
    return _jspx_imports_classes;
  }

  public boolean getErrorOnELNotFound() {
    return false;
  }

  public jakarta.el.ExpressionFactory _jsp_getExpressionFactory() {
    if (_el_expressionfactory == null) {
      synchronized (this) {
        if (_el_expressionfactory == null) {
          _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
        }
      }
    }
    return _el_expressionfactory;
  }

  public org.apache.tomcat.InstanceManager _jsp_getInstanceManager() {
    if (_jsp_instancemanager == null) {
      synchronized (this) {
        if (_jsp_instancemanager == null) {
          _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
        }
      }
    }
    return _jsp_instancemanager;
  }

  public void _jspInit() {
  }

  public void _jspDestroy() {
  }

  public void _jspService(final jakarta.servlet.http.HttpServletRequest request, final jakarta.servlet.http.HttpServletResponse response)
      throws java.io.IOException, jakarta.servlet.ServletException {

    if (!jakarta.servlet.DispatcherType.ERROR.equals(request.getDispatcherType())) {
      final java.lang.String _jspx_method = request.getMethod();
      if ("OPTIONS".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        return;
      }
      if (!"GET".equals(_jspx_method) && !"POST".equals(_jspx_method) && !"HEAD".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        response.sendError(HttpServletResponse.SC_METHOD_NOT_ALLOWED, "Les JSPs ne permettent que GET, POST ou HEAD. Jasper permet aussi OPTIONS");
        return;
      }
    }

    final jakarta.servlet.jsp.PageContext pageContext;
    jakarta.servlet.http.HttpSession session = null;
    final jakarta.servlet.ServletContext application;
    final jakarta.servlet.ServletConfig config;
    jakarta.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    jakarta.servlet.jsp.JspWriter _jspx_out = null;
    jakarta.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html; charset=UTF-8");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;

      out.write("\n");
      out.write("\n");
      out.write("<!DOCTYPE html>\n");
      out.write("<html lang=\"fr\">\n");
      out.write("<head>\n");
      out.write("    <meta charset=\"UTF-8\">\n");
      out.write("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n");
      out.write("    <title>🎯 Améliorations Interface Utilisateur - JEE Training</title>\n");
      out.write("    <link rel=\"stylesheet\" href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/css/style.css\">\n");
      out.write("    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\">\n");
      out.write("</head>\n");
      out.write("<body>\n");
      out.write("    <!-- Navigation -->\n");
      out.write("    <nav class=\"navbar\">\n");
      out.write("        <div class=\"container\">\n");
      out.write("            <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/recherche\" class=\"logo\">\n");
      out.write("                <i class=\"fas fa-train\"></i> JEE Training\n");
      out.write("            </a>\n");
      out.write("            <div class=\"nav-links\">\n");
      out.write("                <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/login.jsp\"><i class=\"fas fa-sign-in-alt\"></i> Connexion</a>\n");
      out.write("                <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/register.jsp\"><i class=\"fas fa-user-plus\"></i> Inscription</a>\n");
      out.write("            </div>\n");
      out.write("        </div>\n");
      out.write("    </nav>\n");
      out.write("\n");
      out.write("    <!-- Contenu principal -->\n");
      out.write("    <div class=\"container\">\n");
      out.write("        <div class=\"main-content\">\n");
      out.write("            <h1><i class=\"fas fa-magic\"></i> Améliorations Interface Utilisateur Complétées !</h1>\n");
      out.write("            \n");
      out.write("            <!-- Message de succès -->\n");
      out.write("            <div class=\"alert alert-success\">\n");
      out.write("                <i class=\"fas fa-check-circle\"></i> \n");
      out.write("                <strong>Mission Accomplie !</strong> Toutes les améliorations demandées ont été implémentées avec succès.\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Résumé des modifications -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-list-check\"></i> Modifications Réalisées</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-unlock text-warning\"></i> Bouton Débloquer</h4>\n");
      out.write("                            <ul>\n");
      out.write("                                <li>✅ Couleur changée en orange/warning</li>\n");
      out.write("                                <li>✅ Icône mise à jour (unlock)</li>\n");
      out.write("                                <li>✅ Style cohérent avec le design</li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-times text-danger\"></i> Bouton Accueil</h4>\n");
      out.write("                            <ul>\n");
      out.write("                                <li>✅ Supprimé de la page confirmation</li>\n");
      out.write("                                <li>✅ Layout réorganisé en 3 colonnes</li>\n");
      out.write("                                <li>✅ Interface plus épurée</li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-file-pdf text-success\"></i> Télécharger PDF</h4>\n");
      out.write("                            <ul>\n");
      out.write("                                <li>✅ Remplace le bouton \"Imprimer\"</li>\n");
      out.write("                                <li>✅ Icône PDF moderne</li>\n");
      out.write("                                <li>✅ Lien vers servlet de téléchargement</li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-nav-icon text-primary\"></i> Navigation Utilisateur</h4>\n");
      out.write("                            <ul>\n");
      out.write("                                <li>✅ Historique des voyages</li>\n");
      out.write("                                <li>✅ Mes réservations</li>\n");
      out.write("                                <li>✅ Annuler réservation</li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Nouvelles pages créées -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-plus-circle\"></i> Nouvelles Pages Créées</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-2\">\n");
      out.write("                        <div class=\"admin-card\">\n");
      out.write("                            <div class=\"admin-card-icon\">\n");
      out.write("                                <i class=\"fas fa-ticket-alt\"></i>\n");
      out.write("                            </div>\n");
      out.write("                            <div class=\"admin-card-content\">\n");
      out.write("                                <h3>🎫 Mes Réservations</h3>\n");
      out.write("                                <p>Interface moderne pour consulter et gérer ses réservations</p>\n");
      out.write("                                <ul style=\"font-size: 12px; margin-top: 10px;\">\n");
      out.write("                                    <li>Statistiques visuelles</li>\n");
      out.write("                                    <li>Tableau moderne stylisé</li>\n");
      out.write("                                    <li>Actions rapides (Modifier, PDF, Annuler)</li>\n");
      out.write("                                    <li>Badges colorés pour les états</li>\n");
      out.write("                                    <li>Navigation contextuelle</li>\n");
      out.write("                                </ul>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-2\">\n");
      out.write("                        <div class=\"admin-card\">\n");
      out.write("                            <div class=\"admin-card-icon\">\n");
      out.write("                                <i class=\"fas fa-times-circle\"></i>\n");
      out.write("                            </div>\n");
      out.write("                            <div class=\"admin-card-content\">\n");
      out.write("                                <h3>❌ Annuler Réservation</h3>\n");
      out.write("                                <p>Processus sécurisé de demande d'annulation</p>\n");
      out.write("                                <ul style=\"font-size: 12px; margin-top: 10px;\">\n");
      out.write("                                    <li>Détails de la réservation</li>\n");
      out.write("                                    <li>Conditions d'annulation</li>\n");
      out.write("                                    <li>Formulaire avec motif</li>\n");
      out.write("                                    <li>Confirmation obligatoire</li>\n");
      out.write("                                    <li>Validation administrateur</li>\n");
      out.write("                                </ul>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Fonctionnalités ajoutées -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-cogs\"></i> Fonctionnalités Ajoutées</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-history\"></i> Historique des Voyages</h4>\n");
      out.write("                            <p>Consultation des billets utilisés (voyages effectués)</p>\n");
      out.write("                            <div class=\"feature-demo\">\n");
      out.write("                                <span class=\"badge badge-success\">\n");
      out.write("                                    <i class=\"fas fa-check-circle\"></i> Voyage Effectué\n");
      out.write("                                </span>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-edit\"></i> Modifier Réservations</h4>\n");
      out.write("                            <p>Modification des billets achetés (classe, préférences)</p>\n");
      out.write("                            <div class=\"feature-demo\">\n");
      out.write("                                <span class=\"classe-badge classe-premiere\">\n");
      out.write("                                    <i class=\"fas fa-star\"></i> Première\n");
      out.write("                                </span>\n");
      out.write("                                <span class=\"classe-badge classe-business\">\n");
      out.write("                                    <i class=\"fas fa-star\"></i> Business\n");
      out.write("                                </span>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-ban\"></i> Annulation Sécurisée</h4>\n");
      out.write("                            <p>Demande d'annulation avec confirmation admin</p>\n");
      out.write("                            <div class=\"feature-demo\">\n");
      out.write("                                <span class=\"badge badge-warning\">\n");
      out.write("                                    <i class=\"fas fa-clock\"></i> En Attente Validation\n");
      out.write("                                </span>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Améliorations techniques -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-code\"></i> Améliorations Techniques</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-server\"></i> Servlets</h4>\n");
      out.write("                            <ul style=\"font-size: 14px;\">\n");
      out.write("                                <li>✅ MesReservationsServlet</li>\n");
      out.write("                                <li>✅ AnnulerReservationServlet (modifié)</li>\n");
      out.write("                                <li>✅ Gestion des sessions</li>\n");
      out.write("                                <li>✅ Validation des permissions</li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-database\"></i> DAO</h4>\n");
      out.write("                            <ul style=\"font-size: 14px;\">\n");
      out.write("                                <li>✅ findByUserExceptEtat()</li>\n");
      out.write("                                <li>✅ Méthodes de filtrage</li>\n");
      out.write("                                <li>✅ Initialisation Hibernate</li>\n");
      out.write("                                <li>✅ Gestion des erreurs</li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-palette\"></i> CSS</h4>\n");
      out.write("                            <ul style=\"font-size: 14px;\">\n");
      out.write("                                <li>✅ Nouveaux composants</li>\n");
      out.write("                                <li>✅ Variables de couleur</li>\n");
      out.write("                                <li>✅ Styles responsive</li>\n");
      out.write("                                <li>✅ Animations fluides</li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-shield-alt\"></i> Sécurité</h4>\n");
      out.write("                            <ul style=\"font-size: 14px;\">\n");
      out.write("                                <li>✅ Vérification sessions</li>\n");
      out.write("                                <li>✅ Validation permissions</li>\n");
      out.write("                                <li>✅ Confirmations utilisateur</li>\n");
      out.write("                                <li>✅ Redirections sécurisées</li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Navigation utilisateur améliorée -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-compass\"></i> Navigation Utilisateur Améliorée</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"navbar-demo\">\n");
      out.write("                    <h4>Navbar pour Utilisateurs Connectés (Non-Admin)</h4>\n");
      out.write("                    <div class=\"demo-navbar\">\n");
      out.write("                        <div class=\"demo-nav-item\">\n");
      out.write("                            <i class=\"fas fa-search\"></i> Rechercher un trajet\n");
      out.write("                        </div>\n");
      out.write("                        <div class=\"demo-nav-item\">\n");
      out.write("                            <i class=\"fas fa-user\"></i> Mon compte\n");
      out.write("                        </div>\n");
      out.write("                        <div class=\"demo-nav-item highlight\">\n");
      out.write("                            <i class=\"fas fa-history\"></i> Historique des voyages\n");
      out.write("                        </div>\n");
      out.write("                        <div class=\"demo-nav-item highlight\">\n");
      out.write("                            <i class=\"fas fa-ticket-alt\"></i> Mes réservations\n");
      out.write("                        </div>\n");
      out.write("                        <div class=\"demo-nav-item highlight\">\n");
      out.write("                            <i class=\"fas fa-times-circle\"></i> Annuler réservation\n");
      out.write("                        </div>\n");
      out.write("                        <div class=\"demo-nav-item\">\n");
      out.write("                            <i class=\"fas fa-sign-out-alt\"></i> Se déconnecter\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <small class=\"text-muted\">Les éléments en surbrillance sont les nouveaux liens ajoutés</small>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- URLs de test -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-link\"></i> URLs pour Tester les Nouvelles Fonctionnalités</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-user\"></i> Pages Utilisateur</h4>\n");
      out.write("                            <ul style=\"font-size: 12px;\">\n");
      out.write("                                <li><code>/mes-reservations</code> - Gestion réservations</li>\n");
      out.write("                                <li><code>/annuler-reservation</code> - Demande annulation</li>\n");
      out.write("                                <li><code>/historique</code> - Voyages effectués</li>\n");
      out.write("                                <li><code>/modifierReservation</code> - Modifier réservation</li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-check\"></i> Pages Modifiées</h4>\n");
      out.write("                            <ul style=\"font-size: 12px;\">\n");
      out.write("                                <li><code>/confirmation</code> - Bouton PDF ajouté</li>\n");
      out.write("                                <li><code>/admin/users</code> - Bouton débloquer orange</li>\n");
      out.write("                                <li><code>/navbar.jsp</code> - Liens utilisateur</li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-cog\"></i> Admin (Inchangé)</h4>\n");
      out.write("                            <ul style=\"font-size: 12px;\">\n");
      out.write("                                <li><code>/admin/dashboard</code> - Centre de contrôle</li>\n");
      out.write("                                <li><code>/admin/annuler</code> - Demandes d'annulation</li>\n");
      out.write("                                <li><code>/diagnostic</code> - Diagnostic système</li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-eye\"></i> Démonstrations</h4>\n");
      out.write("                            <ul style=\"font-size: 12px;\">\n");
      out.write("                                <li><code>/demo-ameliorations-utilisateur.jsp</code></li>\n");
      out.write("                                <li><code>/demo-pages-finales-completes.jsp</code></li>\n");
      out.write("                                <li><code>/demo-final.jsp</code></li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Avant/Après -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-exchange-alt\"></i> Comparaison Avant/Après</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-2\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4>❌ Avant</h4>\n");
      out.write("                            <div style=\"background: #f8d7da; padding: 15px; border-radius: 5px;\">\n");
      out.write("                                <ul style=\"margin: 0; font-size: 12px;\">\n");
      out.write("                                    <li>Bouton débloquer vert</li>\n");
      out.write("                                    <li>Bouton \"Accueil\" inutile</li>\n");
      out.write("                                    <li>Bouton \"Imprimer\" basique</li>\n");
      out.write("                                    <li>Navigation limitée</li>\n");
      out.write("                                    <li>Pas de gestion réservations</li>\n");
      out.write("                                    <li>Annulation complexe</li>\n");
      out.write("                                </ul>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-2\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4>✅ Après</h4>\n");
      out.write("                            <div style=\"background: #d4edda; padding: 15px; border-radius: 5px;\">\n");
      out.write("                                <ul style=\"margin: 0; font-size: 12px;\">\n");
      out.write("                                    <li>Bouton débloquer orange</li>\n");
      out.write("                                    <li>Interface épurée</li>\n");
      out.write("                                    <li>Téléchargement PDF moderne</li>\n");
      out.write("                                    <li>Navigation enrichie</li>\n");
      out.write("                                    <li>Gestion complète réservations</li>\n");
      out.write("                                    <li>Processus annulation guidé</li>\n");
      out.write("                                </ul>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Actions de test -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-rocket\"></i> Tester les Améliorations</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/login.jsp\" class=\"btn btn-primary btn-full\">\n");
      out.write("                            <i class=\"fas fa-sign-in-alt\"></i> Se Connecter pour Tester\n");
      out.write("                        </a>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/admin/users\" class=\"btn btn-warning btn-full\">\n");
      out.write("                            <i class=\"fas fa-unlock\"></i> Voir Bouton Débloquer\n");
      out.write("                        </a>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/confirmation\" class=\"btn btn-success btn-full\">\n");
      out.write("                            <i class=\"fas fa-file-pdf\"></i> Voir Bouton PDF\n");
      out.write("                        </a>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Message final -->\n");
      out.write("            <div class=\"text-center mt-20\">\n");
      out.write("                <div class=\"alert alert-success\">\n");
      out.write("                    <i class=\"fas fa-trophy\"></i> \n");
      out.write("                    <strong>🎉 Toutes les Améliorations Demandées ont été Implémentées avec Succès ! 🎉</strong>\n");
      out.write("                    <br>\n");
      out.write("                    <small>L'interface utilisateur de JEE Training est maintenant complète et moderne.</small>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("        </div>\n");
      out.write("    </div>\n");
      out.write("\n");
      out.write("    <style>\n");
      out.write("        .feature-demo {\n");
      out.write("            margin-top: 10px;\n");
      out.write("            display: flex;\n");
      out.write("            gap: 5px;\n");
      out.write("            flex-wrap: wrap;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .navbar-demo {\n");
      out.write("            padding: 20px;\n");
      out.write("            background: var(--light-bg);\n");
      out.write("            border-radius: 8px;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .demo-navbar {\n");
      out.write("            display: flex;\n");
      out.write("            flex-wrap: wrap;\n");
      out.write("            gap: 10px;\n");
      out.write("            margin: 15px 0;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .demo-nav-item {\n");
      out.write("            padding: 8px 12px;\n");
      out.write("            background: var(--white);\n");
      out.write("            border: 1px solid var(--border-color);\n");
      out.write("            border-radius: 5px;\n");
      out.write("            font-size: 14px;\n");
      out.write("            display: flex;\n");
      out.write("            align-items: center;\n");
      out.write("            gap: 5px;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .demo-nav-item.highlight {\n");
      out.write("            background: linear-gradient(135deg, var(--success-color), #27ae60);\n");
      out.write("            color: var(--white);\n");
      out.write("            border-color: var(--success-color);\n");
      out.write("            font-weight: 600;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .demo-nav-item i {\n");
      out.write("            font-size: 12px;\n");
      out.write("        }\n");
      out.write("    </style>\n");
      out.write("</body>\n");
      out.write("</html>\n");
    } catch (java.lang.Throwable t) {
      if (!(t instanceof jakarta.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try {
            if (response.isCommitted()) {
              out.flush();
            } else {
              out.clearBuffer();
            }
          } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }
}
