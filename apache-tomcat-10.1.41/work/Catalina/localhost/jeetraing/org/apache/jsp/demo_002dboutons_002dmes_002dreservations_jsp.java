/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: Apache Tomcat/10.1.41
 * Generated at: 2025-05-29 23:51:28 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp;

import jakarta.servlet.*;
import jakarta.servlet.http.*;
import jakarta.servlet.jsp.*;

public final class demo_002dboutons_002dmes_002dreservations_jsp extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent,
                 org.apache.jasper.runtime.JspSourceImports,
                 org.apache.jasper.runtime.JspSourceDirectives {

  private static final jakarta.servlet.jsp.JspFactory _jspxFactory =
          jakarta.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  static {
    _jspx_dependants = new java.util.HashMap<java.lang.String,java.lang.Long>(2);
    _jspx_dependants.put("/WEB-INF/lib/jakarta.servlet.jsp.jstl-3.0.1.jar", Long.valueOf(1747175778000L));
    _jspx_dependants.put("jar:file:/C:/Users/<USER>/Downloads/jeetraing/apache-tomcat-10.1.41/webapps/jeetraing/WEB-INF/lib/jakarta.servlet.jsp.jstl-3.0.1.jar!/META-INF/c-1_2.tld", Long.valueOf(1664449878000L));
  }

  private static final java.util.Set<java.lang.String> _jspx_imports_packages;

  private static final java.util.Set<java.lang.String> _jspx_imports_classes;

  static {
    _jspx_imports_packages = new java.util.LinkedHashSet<>(4);
    _jspx_imports_packages.add("jakarta.servlet");
    _jspx_imports_packages.add("jakarta.servlet.http");
    _jspx_imports_packages.add("jakarta.servlet.jsp");
    _jspx_imports_classes = null;
  }

  private volatile jakarta.el.ExpressionFactory _el_expressionfactory;
  private volatile org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public java.util.Set<java.lang.String> getPackageImports() {
    return _jspx_imports_packages;
  }

  public java.util.Set<java.lang.String> getClassImports() {
    return _jspx_imports_classes;
  }

  public boolean getErrorOnELNotFound() {
    return false;
  }

  public jakarta.el.ExpressionFactory _jsp_getExpressionFactory() {
    if (_el_expressionfactory == null) {
      synchronized (this) {
        if (_el_expressionfactory == null) {
          _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
        }
      }
    }
    return _el_expressionfactory;
  }

  public org.apache.tomcat.InstanceManager _jsp_getInstanceManager() {
    if (_jsp_instancemanager == null) {
      synchronized (this) {
        if (_jsp_instancemanager == null) {
          _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
        }
      }
    }
    return _jsp_instancemanager;
  }

  public void _jspInit() {
  }

  public void _jspDestroy() {
  }

  public void _jspService(final jakarta.servlet.http.HttpServletRequest request, final jakarta.servlet.http.HttpServletResponse response)
      throws java.io.IOException, jakarta.servlet.ServletException {

    if (!jakarta.servlet.DispatcherType.ERROR.equals(request.getDispatcherType())) {
      final java.lang.String _jspx_method = request.getMethod();
      if ("OPTIONS".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        return;
      }
      if (!"GET".equals(_jspx_method) && !"POST".equals(_jspx_method) && !"HEAD".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        response.sendError(HttpServletResponse.SC_METHOD_NOT_ALLOWED, "Les JSPs ne permettent que GET, POST ou HEAD. Jasper permet aussi OPTIONS");
        return;
      }
    }

    final jakarta.servlet.jsp.PageContext pageContext;
    jakarta.servlet.http.HttpSession session = null;
    final jakarta.servlet.ServletContext application;
    final jakarta.servlet.ServletConfig config;
    jakarta.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    jakarta.servlet.jsp.JspWriter _jspx_out = null;
    jakarta.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html; charset=UTF-8");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;

      out.write("\n");
      out.write("\n");
      out.write("<!DOCTYPE html>\n");
      out.write("<html lang=\"fr\">\n");
      out.write("<head>\n");
      out.write("    <meta charset=\"UTF-8\">\n");
      out.write("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n");
      out.write("    <title>🎯 Démonstration Boutons Mes Réservations - JEE Training</title>\n");
      out.write("    <link rel=\"stylesheet\" href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/css/style.css\">\n");
      out.write("    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\">\n");
      out.write("</head>\n");
      out.write("<body>\n");
      out.write("    <!-- Navigation -->\n");
      out.write("    <nav class=\"navbar\">\n");
      out.write("        <div class=\"container\">\n");
      out.write("            <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/recherche\" class=\"logo\">\n");
      out.write("                <i class=\"fas fa-train\"></i> JEE Training\n");
      out.write("            </a>\n");
      out.write("            <div class=\"nav-links\">\n");
      out.write("                <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/login.jsp\"><i class=\"fas fa-sign-in-alt\"></i> Connexion</a>\n");
      out.write("                <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/register.jsp\"><i class=\"fas fa-user-plus\"></i> Inscription</a>\n");
      out.write("            </div>\n");
      out.write("        </div>\n");
      out.write("    </nav>\n");
      out.write("\n");
      out.write("    <!-- Contenu principal -->\n");
      out.write("    <div class=\"container\">\n");
      out.write("        <div class=\"main-content\">\n");
      out.write("            <h1><i class=\"fas fa-palette\"></i> Démonstration des Boutons Mes Réservations</h1>\n");
      out.write("            \n");
      out.write("            <!-- Message de succès -->\n");
      out.write("            <div class=\"alert alert-success\">\n");
      out.write("                <i class=\"fas fa-check-circle\"></i> \n");
      out.write("                <strong>✅ BOUTONS MODIFIER ET ANNULER IMPLÉMENTÉS AVEC SUCCÈS !</strong>\n");
      out.write("                <br>\n");
      out.write("                <small>Bouton Modifier fonctionnel (orange) et Bouton Annuler en rouge comme demandé.</small>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Démonstration des boutons -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-mouse-pointer\"></i> Boutons d'Action dans Mes Réservations</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <div class=\"card\" style=\"text-align: center;\">\n");
      out.write("                            <h4><i class=\"fas fa-edit\"></i> Bouton Modifier</h4>\n");
      out.write("                            <a href=\"#\" class=\"admin-btn admin-btn-warning admin-btn-sm\" style=\"pointer-events: none;\">\n");
      out.write("                                <i class=\"fas fa-edit\"></i> Modifier\n");
      out.write("                            </a>\n");
      out.write("                            <p style=\"font-size: 12px; margin-top: 10px;\">\n");
      out.write("                                <strong>Couleur :</strong> Orange (Warning)<br>\n");
      out.write("                                <strong>URL :</strong> <code>/modifierReservation?id=X</code><br>\n");
      out.write("                                <strong>Fonction :</strong> Modification des réservations<br>\n");
      out.write("                                <strong>Disponible pour :</strong> États \"confirmée\" et \"acheté\"\n");
      out.write("                            </p>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <div class=\"card\" style=\"text-align: center;\">\n");
      out.write("                            <h4><i class=\"fas fa-times\"></i> Bouton Annuler</h4>\n");
      out.write("                            <a href=\"#\" class=\"admin-btn admin-btn-danger admin-btn-sm\" style=\"pointer-events: none;\">\n");
      out.write("                                <i class=\"fas fa-times\"></i> Annuler\n");
      out.write("                            </a>\n");
      out.write("                            <p style=\"font-size: 12px; margin-top: 10px;\">\n");
      out.write("                                <strong>Couleur :</strong> Rouge (Danger)<br>\n");
      out.write("                                <strong>URL :</strong> <code>/annuler-reservation?id=X</code><br>\n");
      out.write("                                <strong>Fonction :</strong> Demande d'annulation<br>\n");
      out.write("                                <strong>Disponible pour :</strong> Tous sauf \"annulée\" et \"utilisé\"\n");
      out.write("                            </p>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <div class=\"card\" style=\"text-align: center;\">\n");
      out.write("                            <h4><i class=\"fas fa-file-pdf\"></i> Bouton PDF</h4>\n");
      out.write("                            <a href=\"#\" class=\"admin-btn admin-btn-success admin-btn-sm\" style=\"pointer-events: none;\">\n");
      out.write("                                <i class=\"fas fa-file-pdf\"></i> PDF\n");
      out.write("                            </a>\n");
      out.write("                            <p style=\"font-size: 12px; margin-top: 10px;\">\n");
      out.write("                                <strong>Couleur :</strong> Vert (Success)<br>\n");
      out.write("                                <strong>URL :</strong> <code>/download-ticket?reservationId=X</code><br>\n");
      out.write("                                <strong>Fonction :</strong> Téléchargement billet<br>\n");
      out.write("                                <strong>Disponible pour :</strong> États \"confirmée\" et \"acheté\"\n");
      out.write("                            </p>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Simulation du tableau des réservations -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-table\"></i> Simulation du Tableau Mes Réservations</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <table class=\"admin-table\">\n");
      out.write("                    <thead>\n");
      out.write("                        <tr>\n");
      out.write("                            <th><i class=\"fas fa-hashtag\"></i> ID</th>\n");
      out.write("                            <th><i class=\"fas fa-route\"></i> Trajet</th>\n");
      out.write("                            <th><i class=\"fas fa-calendar\"></i> Date</th>\n");
      out.write("                            <th><i class=\"fas fa-info-circle\"></i> État</th>\n");
      out.write("                            <th><i class=\"fas fa-cogs\"></i> Actions</th>\n");
      out.write("                        </tr>\n");
      out.write("                    </thead>\n");
      out.write("                    <tbody>\n");
      out.write("                        <!-- Réservation confirmée - Tous les boutons -->\n");
      out.write("                        <tr>\n");
      out.write("                            <td><strong>#4</strong></td>\n");
      out.write("                            <td>Paris → Lyon</td>\n");
      out.write("                            <td>28/05/2025 08:00</td>\n");
      out.write("                            <td>\n");
      out.write("                                <span class=\"badge badge-success\">\n");
      out.write("                                    <i class=\"fas fa-check-circle\"></i> Confirmée\n");
      out.write("                                </span>\n");
      out.write("                            </td>\n");
      out.write("                            <td>\n");
      out.write("                                <a href=\"#\" class=\"admin-btn admin-btn-warning admin-btn-sm\" style=\"pointer-events: none;\">\n");
      out.write("                                    <i class=\"fas fa-edit\"></i> Modifier\n");
      out.write("                                </a>\n");
      out.write("                                <a href=\"#\" class=\"admin-btn admin-btn-success admin-btn-sm\" style=\"pointer-events: none;\">\n");
      out.write("                                    <i class=\"fas fa-file-pdf\"></i> PDF\n");
      out.write("                                </a>\n");
      out.write("                                <a href=\"#\" class=\"admin-btn admin-btn-danger admin-btn-sm\" style=\"pointer-events: none;\">\n");
      out.write("                                    <i class=\"fas fa-times\"></i> Annuler\n");
      out.write("                                </a>\n");
      out.write("                            </td>\n");
      out.write("                        </tr>\n");
      out.write("                        \n");
      out.write("                        <!-- Réservation achetée - Tous les boutons -->\n");
      out.write("                        <tr>\n");
      out.write("                            <td><strong>#5</strong></td>\n");
      out.write("                            <td>Paris → Lyon</td>\n");
      out.write("                            <td>28/05/2025 10:00</td>\n");
      out.write("                            <td>\n");
      out.write("                                <span class=\"badge badge-primary\">\n");
      out.write("                                    <i class=\"fas fa-shopping-cart\"></i> Acheté\n");
      out.write("                                </span>\n");
      out.write("                            </td>\n");
      out.write("                            <td>\n");
      out.write("                                <a href=\"#\" class=\"admin-btn admin-btn-warning admin-btn-sm\" style=\"pointer-events: none;\">\n");
      out.write("                                    <i class=\"fas fa-edit\"></i> Modifier\n");
      out.write("                                </a>\n");
      out.write("                                <a href=\"#\" class=\"admin-btn admin-btn-success admin-btn-sm\" style=\"pointer-events: none;\">\n");
      out.write("                                    <i class=\"fas fa-file-pdf\"></i> PDF\n");
      out.write("                                </a>\n");
      out.write("                                <a href=\"#\" class=\"admin-btn admin-btn-danger admin-btn-sm\" style=\"pointer-events: none;\">\n");
      out.write("                                    <i class=\"fas fa-times\"></i> Annuler\n");
      out.write("                                </a>\n");
      out.write("                            </td>\n");
      out.write("                        </tr>\n");
      out.write("                        \n");
      out.write("                        <!-- Réservation en attente - Seulement annuler -->\n");
      out.write("                        <tr>\n");
      out.write("                            <td><strong>#6</strong></td>\n");
      out.write("                            <td>Paris → Lyon</td>\n");
      out.write("                            <td>28/05/2025 12:00</td>\n");
      out.write("                            <td>\n");
      out.write("                                <span class=\"badge badge-warning\">\n");
      out.write("                                    <i class=\"fas fa-clock\"></i> En Attente\n");
      out.write("                                </span>\n");
      out.write("                            </td>\n");
      out.write("                            <td>\n");
      out.write("                                <a href=\"#\" class=\"admin-btn admin-btn-danger admin-btn-sm\" style=\"pointer-events: none;\">\n");
      out.write("                                    <i class=\"fas fa-times\"></i> Annuler\n");
      out.write("                                </a>\n");
      out.write("                            </td>\n");
      out.write("                        </tr>\n");
      out.write("                        \n");
      out.write("                        <!-- Réservation annulée - Aucune action -->\n");
      out.write("                        <tr>\n");
      out.write("                            <td><strong>#7</strong></td>\n");
      out.write("                            <td>Paris → Lyon</td>\n");
      out.write("                            <td>28/05/2025 14:00</td>\n");
      out.write("                            <td>\n");
      out.write("                                <span class=\"badge badge-danger\">\n");
      out.write("                                    <i class=\"fas fa-ban\"></i> Annulée\n");
      out.write("                                </span>\n");
      out.write("                            </td>\n");
      out.write("                            <td>\n");
      out.write("                                <span class=\"admin-btn admin-btn-secondary admin-btn-sm\" style=\"cursor: not-allowed;\">\n");
      out.write("                                    <i class=\"fas fa-ban\"></i> Aucune action\n");
      out.write("                                </span>\n");
      out.write("                            </td>\n");
      out.write("                        </tr>\n");
      out.write("                        \n");
      out.write("                        <!-- Réservation utilisée - Aucune action -->\n");
      out.write("                        <tr>\n");
      out.write("                            <td><strong>#8</strong></td>\n");
      out.write("                            <td>Paris → Lyon</td>\n");
      out.write("                            <td>25/05/2025 16:00</td>\n");
      out.write("                            <td>\n");
      out.write("                                <span class=\"badge badge-info\">\n");
      out.write("                                    <i class=\"fas fa-check\"></i> Utilisé\n");
      out.write("                                </span>\n");
      out.write("                            </td>\n");
      out.write("                            <td>\n");
      out.write("                                <span class=\"admin-btn admin-btn-secondary admin-btn-sm\" style=\"cursor: not-allowed;\">\n");
      out.write("                                    <i class=\"fas fa-ban\"></i> Aucune action\n");
      out.write("                                </span>\n");
      out.write("                            </td>\n");
      out.write("                        </tr>\n");
      out.write("                    </tbody>\n");
      out.write("                </table>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Logique d'affichage -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-code\"></i> Logique d'Affichage des Boutons</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-edit\"></i> Bouton Modifier</h4>\n");
      out.write("                            <ul style=\"font-size: 12px;\">\n");
      out.write("                                <li><strong>Condition :</strong> État = \"confirmée\" OU \"acheté\"</li>\n");
      out.write("                                <li><strong>Couleur :</strong> Orange (admin-btn-warning)</li>\n");
      out.write("                                <li><strong>Action :</strong> Redirection vers page modification</li>\n");
      out.write("                                <li><strong>Tooltip :</strong> \"Modifier cette réservation\"</li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-times\"></i> Bouton Annuler</h4>\n");
      out.write("                            <ul style=\"font-size: 12px;\">\n");
      out.write("                                <li><strong>Condition :</strong> État ≠ \"annulée\" ET ≠ \"utilisé\"</li>\n");
      out.write("                                <li><strong>Couleur :</strong> Rouge (admin-btn-danger)</li>\n");
      out.write("                                <li><strong>Action :</strong> Confirmation puis demande d'annulation</li>\n");
      out.write("                                <li><strong>Tooltip :</strong> \"Demander l'annulation\"</li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-file-pdf\"></i> Bouton PDF</h4>\n");
      out.write("                            <ul style=\"font-size: 12px;\">\n");
      out.write("                                <li><strong>Condition :</strong> État = \"confirmée\" OU \"acheté\"</li>\n");
      out.write("                                <li><strong>Couleur :</strong> Vert (admin-btn-success)</li>\n");
      out.write("                                <li><strong>Action :</strong> Téléchargement du billet PDF</li>\n");
      out.write("                                <li><strong>Tooltip :</strong> \"Télécharger le billet PDF\"</li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Améliorations apportées -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-star\"></i> Améliorations Apportées</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-2\">\n");
      out.write("                        <div class=\"admin-card\">\n");
      out.write("                            <div class=\"admin-card-icon\">\n");
      out.write("                                <i class=\"fas fa-palette\"></i>\n");
      out.write("                            </div>\n");
      out.write("                            <div class=\"admin-card-content\">\n");
      out.write("                                <h3>🎨 Couleurs Améliorées</h3>\n");
      out.write("                                <ul style=\"font-size: 12px;\">\n");
      out.write("                                    <li>✅ Bouton Modifier : Orange vif</li>\n");
      out.write("                                    <li>✅ Bouton Annuler : Rouge intense</li>\n");
      out.write("                                    <li>✅ Effets hover et active</li>\n");
      out.write("                                    <li>✅ Styles CSS avec !important</li>\n");
      out.write("                                </ul>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-2\">\n");
      out.write("                        <div class=\"admin-card\">\n");
      out.write("                            <div class=\"admin-card-icon\">\n");
      out.write("                                <i class=\"fas fa-cogs\"></i>\n");
      out.write("                            </div>\n");
      out.write("                            <div class=\"admin-card-content\">\n");
      out.write("                                <h3>⚙️ Logique Améliorée</h3>\n");
      out.write("                                <ul style=\"font-size: 12px;\">\n");
      out.write("                                    <li>✅ Conditions d'affichage étendues</li>\n");
      out.write("                                    <li>✅ Support états \"acheté\" et \"confirmée\"</li>\n");
      out.write("                                    <li>✅ Gestion des états non modifiables</li>\n");
      out.write("                                    <li>✅ Tooltips informatifs</li>\n");
      out.write("                                </ul>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Instructions de test -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-play-circle\"></i> Comment Tester</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"workflow-steps\">\n");
      out.write("                    <div class=\"workflow-step\">\n");
      out.write("                        <div class=\"step-number\">1</div>\n");
      out.write("                        <div class=\"step-content\">\n");
      out.write("                            <h4><i class=\"fas fa-sign-in-alt\"></i> Se Connecter</h4>\n");
      out.write("                            <p>Aller sur <code>/login.jsp</code> et se connecter avec un compte utilisateur</p>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    \n");
      out.write("                    <div class=\"workflow-step\">\n");
      out.write("                        <div class=\"step-number\">2</div>\n");
      out.write("                        <div class=\"step-content\">\n");
      out.write("                            <h4><i class=\"fas fa-ticket-alt\"></i> Accéder à Mes Réservations</h4>\n");
      out.write("                            <p>Naviguer vers <code>/mes-reservations</code> pour voir les boutons en action</p>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    \n");
      out.write("                    <div class=\"workflow-step\">\n");
      out.write("                        <div class=\"step-number\">3</div>\n");
      out.write("                        <div class=\"step-content\">\n");
      out.write("                            <h4><i class=\"fas fa-mouse-pointer\"></i> Tester les Boutons</h4>\n");
      out.write("                            <p>Cliquer sur \"Modifier\" (orange) et \"Annuler\" (rouge) pour vérifier le fonctionnement</p>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Actions de test -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-rocket\"></i> Tester Maintenant</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/login.jsp\" class=\"btn btn-primary btn-full\">\n");
      out.write("                            <i class=\"fas fa-sign-in-alt\"></i> Se Connecter\n");
      out.write("                        </a>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/mes-reservations\" class=\"btn btn-success btn-full\">\n");
      out.write("                            <i class=\"fas fa-ticket-alt\"></i> Mes Réservations\n");
      out.write("                        </a>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/corrections-correspondance-finales.jsp\" class=\"btn btn-warning btn-full\">\n");
      out.write("                            <i class=\"fas fa-eye\"></i> Voir Corrections\n");
      out.write("                        </a>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Message final -->\n");
      out.write("            <div class=\"text-center mt-20\">\n");
      out.write("                <div class=\"alert alert-success\">\n");
      out.write("                    <i class=\"fas fa-trophy\"></i> \n");
      out.write("                    <strong>🎯 BOUTONS IMPLÉMENTÉS AVEC SUCCÈS ! 🎯</strong>\n");
      out.write("                    <br>\n");
      out.write("                    <small>Bouton Modifier fonctionnel (orange) et Bouton Annuler en rouge comme demandé.</small>\n");
      out.write("                    <br>\n");
      out.write("                    <div style=\"margin-top: 10px;\">\n");
      out.write("                        <span class=\"badge badge-success\">✅ Bouton Modifier Orange</span>\n");
      out.write("                        <span class=\"badge badge-success\">✅ Bouton Annuler Rouge</span>\n");
      out.write("                        <span class=\"badge badge-success\">✅ Correspondance URLs</span>\n");
      out.write("                        <span class=\"badge badge-success\">✅ Logique Conditionnelle</span>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("        </div>\n");
      out.write("    </div>\n");
      out.write("\n");
      out.write("    <style>\n");
      out.write("        .workflow-steps {\n");
      out.write("            display: flex;\n");
      out.write("            flex-direction: column;\n");
      out.write("            gap: 15px;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .workflow-step {\n");
      out.write("            display: flex;\n");
      out.write("            align-items: flex-start;\n");
      out.write("            gap: 15px;\n");
      out.write("            padding: 15px;\n");
      out.write("            background: var(--light-bg);\n");
      out.write("            border-radius: 8px;\n");
      out.write("            border-left: 4px solid var(--success-color);\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .step-number {\n");
      out.write("            width: 30px;\n");
      out.write("            height: 30px;\n");
      out.write("            background: var(--success-color);\n");
      out.write("            color: var(--white);\n");
      out.write("            border-radius: 50%;\n");
      out.write("            display: flex;\n");
      out.write("            align-items: center;\n");
      out.write("            justify-content: center;\n");
      out.write("            font-weight: 600;\n");
      out.write("            flex-shrink: 0;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .step-content h4 {\n");
      out.write("            margin: 0 0 5px 0;\n");
      out.write("            color: var(--text-dark);\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .step-content p {\n");
      out.write("            margin: 0;\n");
      out.write("            color: var(--text-muted);\n");
      out.write("            font-size: 14px;\n");
      out.write("        }\n");
      out.write("    </style>\n");
      out.write("</body>\n");
      out.write("</html>\n");
    } catch (java.lang.Throwable t) {
      if (!(t instanceof jakarta.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try {
            if (response.isCommitted()) {
              out.flush();
            } else {
              out.clearBuffer();
            }
          } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }
}
