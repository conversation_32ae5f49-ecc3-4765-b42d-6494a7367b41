/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: Apache Tomcat/10.1.41
 * Generated at: 2025-05-29 12:02:51 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp;

import jakarta.servlet.*;
import jakarta.servlet.http.*;
import jakarta.servlet.jsp.*;

public final class demo_002dstyles_jsp extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent,
                 org.apache.jasper.runtime.JspSourceImports,
                 org.apache.jasper.runtime.JspSourceDirectives {

  private static final jakarta.servlet.jsp.JspFactory _jspxFactory =
          jakarta.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  static {
    _jspx_dependants = new java.util.HashMap<java.lang.String,java.lang.Long>(2);
    _jspx_dependants.put("/WEB-INF/lib/jakarta.servlet.jsp.jstl-3.0.1.jar", Long.valueOf(1747175778000L));
    _jspx_dependants.put("jar:file:/C:/Users/<USER>/Downloads/jeetraing/apache-tomcat-10.1.41/webapps/jeetraing/WEB-INF/lib/jakarta.servlet.jsp.jstl-3.0.1.jar!/META-INF/c-1_2.tld", Long.valueOf(1664449878000L));
  }

  private static final java.util.Set<java.lang.String> _jspx_imports_packages;

  private static final java.util.Set<java.lang.String> _jspx_imports_classes;

  static {
    _jspx_imports_packages = new java.util.LinkedHashSet<>(4);
    _jspx_imports_packages.add("jakarta.servlet");
    _jspx_imports_packages.add("jakarta.servlet.http");
    _jspx_imports_packages.add("jakarta.servlet.jsp");
    _jspx_imports_classes = null;
  }

  private volatile jakarta.el.ExpressionFactory _el_expressionfactory;
  private volatile org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public java.util.Set<java.lang.String> getPackageImports() {
    return _jspx_imports_packages;
  }

  public java.util.Set<java.lang.String> getClassImports() {
    return _jspx_imports_classes;
  }

  public boolean getErrorOnELNotFound() {
    return false;
  }

  public jakarta.el.ExpressionFactory _jsp_getExpressionFactory() {
    if (_el_expressionfactory == null) {
      synchronized (this) {
        if (_el_expressionfactory == null) {
          _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
        }
      }
    }
    return _el_expressionfactory;
  }

  public org.apache.tomcat.InstanceManager _jsp_getInstanceManager() {
    if (_jsp_instancemanager == null) {
      synchronized (this) {
        if (_jsp_instancemanager == null) {
          _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
        }
      }
    }
    return _jsp_instancemanager;
  }

  public void _jspInit() {
  }

  public void _jspDestroy() {
  }

  public void _jspService(final jakarta.servlet.http.HttpServletRequest request, final jakarta.servlet.http.HttpServletResponse response)
      throws java.io.IOException, jakarta.servlet.ServletException {

    if (!jakarta.servlet.DispatcherType.ERROR.equals(request.getDispatcherType())) {
      final java.lang.String _jspx_method = request.getMethod();
      if ("OPTIONS".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        return;
      }
      if (!"GET".equals(_jspx_method) && !"POST".equals(_jspx_method) && !"HEAD".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        response.sendError(HttpServletResponse.SC_METHOD_NOT_ALLOWED, "Les JSPs ne permettent que GET, POST ou HEAD. Jasper permet aussi OPTIONS");
        return;
      }
    }

    final jakarta.servlet.jsp.PageContext pageContext;
    jakarta.servlet.http.HttpSession session = null;
    final jakarta.servlet.ServletContext application;
    final jakarta.servlet.ServletConfig config;
    jakarta.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    jakarta.servlet.jsp.JspWriter _jspx_out = null;
    jakarta.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html; charset=UTF-8");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;

      out.write("\n");
      out.write("\n");
      out.write("<!DOCTYPE html>\n");
      out.write("<html lang=\"fr\">\n");
      out.write("<head>\n");
      out.write("    <meta charset=\"UTF-8\">\n");
      out.write("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n");
      out.write("    <title>🎨 Démonstration des Styles - JEE Training</title>\n");
      out.write("    <link rel=\"stylesheet\" href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/css/style.css\">\n");
      out.write("    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\">\n");
      out.write("</head>\n");
      out.write("<body>\n");
      out.write("    <!-- Navigation -->\n");
      out.write("    <nav class=\"navbar\">\n");
      out.write("        <div class=\"container\">\n");
      out.write("            <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/recherche\" class=\"logo\">\n");
      out.write("                <i class=\"fas fa-train\"></i> JEE Training\n");
      out.write("            </a>\n");
      out.write("            <div class=\"nav-links\">\n");
      out.write("                <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/recherche\"><i class=\"fas fa-search\"></i> Rechercher</a>\n");
      out.write("                <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/login.jsp\"><i class=\"fas fa-sign-in-alt\"></i> Connexion</a>\n");
      out.write("                <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/register.jsp\"><i class=\"fas fa-user-plus\"></i> Inscription</a>\n");
      out.write("            </div>\n");
      out.write("        </div>\n");
      out.write("    </nav>\n");
      out.write("\n");
      out.write("    <!-- Contenu principal -->\n");
      out.write("    <div class=\"container\">\n");
      out.write("        <div class=\"main-content\">\n");
      out.write("            <h1><i class=\"fas fa-palette\"></i> Démonstration des Nouveaux Styles</h1>\n");
      out.write("            \n");
      out.write("            <!-- Alerte de succès -->\n");
      out.write("            <div class=\"alert alert-success\">\n");
      out.write("                <i class=\"fas fa-check-circle\"></i> \n");
      out.write("                <strong>Félicitations !</strong> L'interface de l'application JEE Training a été complètement modernisée !\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Section des améliorations -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-star\"></i> Améliorations Apportées</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-2\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h3><i class=\"fas fa-paint-brush\"></i> Design Moderne</h3>\n");
      out.write("                            <ul>\n");
      out.write("                                <li>Palette de couleurs professionnelle</li>\n");
      out.write("                                <li>Dégradés et ombres élégants</li>\n");
      out.write("                                <li>Typographie améliorée</li>\n");
      out.write("                                <li>Icônes Font Awesome</li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-2\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h3><i class=\"fas fa-mobile-alt\"></i> Responsive Design</h3>\n");
      out.write("                            <ul>\n");
      out.write("                                <li>Adaptation mobile et tablette</li>\n");
      out.write("                                <li>Navigation responsive</li>\n");
      out.write("                                <li>Grille flexible</li>\n");
      out.write("                                <li>Formulaires adaptatifs</li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Démonstration des composants -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-puzzle-piece\"></i> Composants Stylisés</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <!-- Boutons -->\n");
      out.write("                <h3><i class=\"fas fa-mouse-pointer\"></i> Boutons</h3>\n");
      out.write("                <div class=\"mb-20\">\n");
      out.write("                    <button class=\"btn btn-primary\"><i class=\"fas fa-search\"></i> Primaire</button>\n");
      out.write("                    <button class=\"btn btn-success\"><i class=\"fas fa-check\"></i> Succès</button>\n");
      out.write("                    <button class=\"btn btn-danger\"><i class=\"fas fa-times\"></i> Danger</button>\n");
      out.write("                    <button class=\"btn btn-secondary\"><i class=\"fas fa-info\"></i> Secondaire</button>\n");
      out.write("                </div>\n");
      out.write("\n");
      out.write("                <!-- Badges -->\n");
      out.write("                <h3><i class=\"fas fa-tags\"></i> Badges</h3>\n");
      out.write("                <div class=\"mb-20\">\n");
      out.write("                    <span class=\"badge badge-success\">Disponible</span>\n");
      out.write("                    <span class=\"badge badge-warning\">Limité</span>\n");
      out.write("                    <span class=\"badge badge-danger\">Complet</span>\n");
      out.write("                    <span class=\"badge badge-info\">Information</span>\n");
      out.write("                </div>\n");
      out.write("\n");
      out.write("                <!-- Alertes -->\n");
      out.write("                <h3><i class=\"fas fa-bell\"></i> Alertes</h3>\n");
      out.write("                <div class=\"alert alert-info\">\n");
      out.write("                    <i class=\"fas fa-info-circle\"></i> \n");
      out.write("                    Ceci est une alerte d'information avec une icône.\n");
      out.write("                </div>\n");
      out.write("                <div class=\"alert alert-warning\">\n");
      out.write("                    <i class=\"fas fa-exclamation-triangle\"></i> \n");
      out.write("                    Ceci est un avertissement important.\n");
      out.write("                </div>\n");
      out.write("                <div class=\"alert alert-error\">\n");
      out.write("                    <i class=\"fas fa-times-circle\"></i> \n");
      out.write("                    Ceci est une alerte d'erreur.\n");
      out.write("                </div>\n");
      out.write("\n");
      out.write("                <!-- Promotions -->\n");
      out.write("                <h3><i class=\"fas fa-gift\"></i> Tags de Promotion</h3>\n");
      out.write("                <div class=\"mb-20\">\n");
      out.write("                    <div class=\"promotion-tag\">\n");
      out.write("                        <i class=\"fas fa-tag\"></i> WELCOME10 (-10%)\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"promotion-tag\">\n");
      out.write("                        <i class=\"fas fa-star\"></i> FIDELITE20 (-20%)\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"promotion-tag\">\n");
      out.write("                        <i class=\"fas fa-fire\"></i> SPECIAL15 (-15%)\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Tableau de démonstration -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-table\"></i> Tableau Stylisé</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"table-container\">\n");
      out.write("                    <table class=\"table\">\n");
      out.write("                        <thead>\n");
      out.write("                            <tr>\n");
      out.write("                                <th><i class=\"fas fa-route\"></i> Trajet</th>\n");
      out.write("                                <th><i class=\"fas fa-clock\"></i> Horaire</th>\n");
      out.write("                                <th><i class=\"fas fa-euro-sign\"></i> Prix</th>\n");
      out.write("                                <th><i class=\"fas fa-users\"></i> Places</th>\n");
      out.write("                                <th><i class=\"fas fa-cog\"></i> Action</th>\n");
      out.write("                            </tr>\n");
      out.write("                        </thead>\n");
      out.write("                        <tbody>\n");
      out.write("                            <tr>\n");
      out.write("                                <td><strong>Paris → Lyon</strong></td>\n");
      out.write("                                <td>08:00 → 10:30</td>\n");
      out.write("                                <td>\n");
      out.write("                                    <div class=\"price-original\">95.00 €</div>\n");
      out.write("                                    <div class=\"price-discount\">80.75 €</div>\n");
      out.write("                                    <small class=\"promotion\">-15%</small>\n");
      out.write("                                </td>\n");
      out.write("                                <td><span class=\"badge badge-success\">25 places</span></td>\n");
      out.write("                                <td><button class=\"btn btn-success\"><i class=\"fas fa-check\"></i> Sélectionner</button></td>\n");
      out.write("                            </tr>\n");
      out.write("                            <tr>\n");
      out.write("                                <td><strong>Paris → Marseille</strong></td>\n");
      out.write("                                <td>14:00 → 17:15</td>\n");
      out.write("                                <td><div class=\"price-current\">125.00 €</div></td>\n");
      out.write("                                <td><span class=\"badge badge-warning\">5 places</span></td>\n");
      out.write("                                <td><button class=\"btn btn-success\"><i class=\"fas fa-check\"></i> Sélectionner</button></td>\n");
      out.write("                            </tr>\n");
      out.write("                            <tr>\n");
      out.write("                                <td><strong>Lyon → Nice</strong></td>\n");
      out.write("                                <td>16:30 → 21:00</td>\n");
      out.write("                                <td><div class=\"price-current\">85.00 €</div></td>\n");
      out.write("                                <td><span class=\"badge badge-danger\">Complet</span></td>\n");
      out.write("                                <td><button class=\"btn btn-secondary\" disabled><i class=\"fas fa-times\"></i> Indisponible</button></td>\n");
      out.write("                            </tr>\n");
      out.write("                        </tbody>\n");
      out.write("                    </table>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Navigation vers les pages -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-link\"></i> Pages Améliorées</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/recherche\" class=\"btn btn-primary btn-full\">\n");
      out.write("                            <i class=\"fas fa-search\"></i> Page de Recherche\n");
      out.write("                        </a>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/login.jsp\" class=\"btn btn-secondary btn-full\">\n");
      out.write("                            <i class=\"fas fa-sign-in-alt\"></i> Page de Connexion\n");
      out.write("                        </a>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/register.jsp\" class=\"btn btn-success btn-full\">\n");
      out.write("                            <i class=\"fas fa-user-plus\"></i> Page d'Inscription\n");
      out.write("                        </a>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/diagnostic\" class=\"btn btn-danger btn-full\">\n");
      out.write("                            <i class=\"fas fa-cog\"></i> Diagnostic\n");
      out.write("                        </a>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Footer -->\n");
      out.write("            <div class=\"text-center mt-20\">\n");
      out.write("                <small class=\"text-muted\">\n");
      out.write("                    <i class=\"fas fa-heart\"></i> \n");
      out.write("                    Interface modernisée avec amour pour JEE Training\n");
      out.write("                </small>\n");
      out.write("            </div>\n");
      out.write("        </div>\n");
      out.write("    </div>\n");
      out.write("</body>\n");
      out.write("</html>\n");
    } catch (java.lang.Throwable t) {
      if (!(t instanceof jakarta.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try {
            if (response.isCommitted()) {
              out.flush();
            } else {
              out.clearBuffer();
            }
          } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }
}
