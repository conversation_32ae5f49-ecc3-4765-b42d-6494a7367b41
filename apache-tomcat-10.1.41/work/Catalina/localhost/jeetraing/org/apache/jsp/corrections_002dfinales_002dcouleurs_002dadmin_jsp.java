/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: Apache Tomcat/10.1.41
 * Generated at: 2025-05-30 00:15:29 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp;

import jakarta.servlet.*;
import jakarta.servlet.http.*;
import jakarta.servlet.jsp.*;

public final class corrections_002dfinales_002dcouleurs_002dadmin_jsp extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent,
                 org.apache.jasper.runtime.JspSourceImports,
                 org.apache.jasper.runtime.JspSourceDirectives {

  private static final jakarta.servlet.jsp.JspFactory _jspxFactory =
          jakarta.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  static {
    _jspx_dependants = new java.util.HashMap<java.lang.String,java.lang.Long>(2);
    _jspx_dependants.put("/WEB-INF/lib/jakarta.servlet.jsp.jstl-3.0.1.jar", Long.valueOf(1747175778000L));
    _jspx_dependants.put("jar:file:/C:/Users/<USER>/Downloads/jeetraing/apache-tomcat-10.1.41/webapps/jeetraing/WEB-INF/lib/jakarta.servlet.jsp.jstl-3.0.1.jar!/META-INF/c-1_2.tld", Long.valueOf(1664449878000L));
  }

  private static final java.util.Set<java.lang.String> _jspx_imports_packages;

  private static final java.util.Set<java.lang.String> _jspx_imports_classes;

  static {
    _jspx_imports_packages = new java.util.LinkedHashSet<>(4);
    _jspx_imports_packages.add("jakarta.servlet");
    _jspx_imports_packages.add("jakarta.servlet.http");
    _jspx_imports_packages.add("jakarta.servlet.jsp");
    _jspx_imports_classes = null;
  }

  private volatile jakarta.el.ExpressionFactory _el_expressionfactory;
  private volatile org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public java.util.Set<java.lang.String> getPackageImports() {
    return _jspx_imports_packages;
  }

  public java.util.Set<java.lang.String> getClassImports() {
    return _jspx_imports_classes;
  }

  public boolean getErrorOnELNotFound() {
    return false;
  }

  public jakarta.el.ExpressionFactory _jsp_getExpressionFactory() {
    if (_el_expressionfactory == null) {
      synchronized (this) {
        if (_el_expressionfactory == null) {
          _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
        }
      }
    }
    return _el_expressionfactory;
  }

  public org.apache.tomcat.InstanceManager _jsp_getInstanceManager() {
    if (_jsp_instancemanager == null) {
      synchronized (this) {
        if (_jsp_instancemanager == null) {
          _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
        }
      }
    }
    return _jsp_instancemanager;
  }

  public void _jspInit() {
  }

  public void _jspDestroy() {
  }

  public void _jspService(final jakarta.servlet.http.HttpServletRequest request, final jakarta.servlet.http.HttpServletResponse response)
      throws java.io.IOException, jakarta.servlet.ServletException {

    if (!jakarta.servlet.DispatcherType.ERROR.equals(request.getDispatcherType())) {
      final java.lang.String _jspx_method = request.getMethod();
      if ("OPTIONS".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        return;
      }
      if (!"GET".equals(_jspx_method) && !"POST".equals(_jspx_method) && !"HEAD".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        response.sendError(HttpServletResponse.SC_METHOD_NOT_ALLOWED, "Les JSPs ne permettent que GET, POST ou HEAD. Jasper permet aussi OPTIONS");
        return;
      }
    }

    final jakarta.servlet.jsp.PageContext pageContext;
    jakarta.servlet.http.HttpSession session = null;
    final jakarta.servlet.ServletContext application;
    final jakarta.servlet.ServletConfig config;
    jakarta.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    jakarta.servlet.jsp.JspWriter _jspx_out = null;
    jakarta.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html; charset=UTF-8");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;

      out.write("\n");
      out.write("\n");
      out.write("<!DOCTYPE html>\n");
      out.write("<html lang=\"fr\">\n");
      out.write("<head>\n");
      out.write("    <meta charset=\"UTF-8\">\n");
      out.write("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n");
      out.write("    <title>✅ Corrections Finales - Couleurs et Admin - JEE Training</title>\n");
      out.write("    <link rel=\"stylesheet\" href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/css/style.css\">\n");
      out.write("    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\">\n");
      out.write("</head>\n");
      out.write("<body>\n");
      out.write("    <!-- Navigation -->\n");
      out.write("    <nav class=\"navbar\">\n");
      out.write("        <div class=\"container\">\n");
      out.write("            <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/recherche\" class=\"logo\">\n");
      out.write("                <i class=\"fas fa-train\"></i> JEE Training\n");
      out.write("            </a>\n");
      out.write("            <div class=\"nav-links\">\n");
      out.write("                <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/login.jsp\"><i class=\"fas fa-sign-in-alt\"></i> Connexion</a>\n");
      out.write("                <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/register.jsp\"><i class=\"fas fa-user-plus\"></i> Inscription</a>\n");
      out.write("            </div>\n");
      out.write("        </div>\n");
      out.write("    </nav>\n");
      out.write("\n");
      out.write("    <!-- Contenu principal -->\n");
      out.write("    <div class=\"container\">\n");
      out.write("        <div class=\"main-content\">\n");
      out.write("            <h1><i class=\"fas fa-check-double\"></i> Corrections Finales Terminées !</h1>\n");
      out.write("            \n");
      out.write("            <!-- Message de succès -->\n");
      out.write("            <div class=\"alert alert-success\">\n");
      out.write("                <i class=\"fas fa-check-circle\"></i> \n");
      out.write("                <strong>✅ TOUTES LES CORRECTIONS DEMANDÉES ONT ÉTÉ APPLIQUÉES !</strong>\n");
      out.write("                <br>\n");
      out.write("                <small>Couleurs des places corrigées et fonctionnalité admin pour voir les réservations ajoutée.</small>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Corrections appliquées -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-wrench\"></i> Corrections Appliquées</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-2\">\n");
      out.write("                        <div class=\"admin-card\">\n");
      out.write("                            <div class=\"admin-card-icon\">\n");
      out.write("                                <i class=\"fas fa-palette\"></i>\n");
      out.write("                            </div>\n");
      out.write("                            <div class=\"admin-card-content\">\n");
      out.write("                                <h3>🎨 Couleurs des Places</h3>\n");
      out.write("                                <p><strong>Problème :</strong> Places blanches peu visibles</p>\n");
      out.write("                                <ul style=\"font-size: 12px;\">\n");
      out.write("                                    <li>✅ Badges avec dégradés colorés</li>\n");
      out.write("                                    <li>✅ Bordures et ombres ajoutées</li>\n");
      out.write("                                    <li>✅ Couleurs vives et contrastées</li>\n");
      out.write("                                    <li>✅ Plus de fond blanc</li>\n");
      out.write("                                </ul>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-2\">\n");
      out.write("                        <div class=\"admin-card\">\n");
      out.write("                            <div class=\"admin-card-icon\">\n");
      out.write("                                <i class=\"fas fa-user-shield\"></i>\n");
      out.write("                            </div>\n");
      out.write("                            <div class=\"admin-card-content\">\n");
      out.write("                                <h3>👨‍💼 Admin Voir Réservations</h3>\n");
      out.write("                                <p><strong>Demande :</strong> Admin doit voir toutes les réservations</p>\n");
      out.write("                                <ul style=\"font-size: 12px;\">\n");
      out.write("                                    <li>✅ Servlet AdminReservationsServlet créé</li>\n");
      out.write("                                    <li>✅ Page admin/reservations.jsp créée</li>\n");
      out.write("                                    <li>✅ Lien ajouté dans dashboard</li>\n");
      out.write("                                    <li>✅ Statistiques et actions admin</li>\n");
      out.write("                                </ul>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Démonstration des nouvelles couleurs -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-palette\"></i> Nouvelles Couleurs des Badges</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-6\">\n");
      out.write("                        <div class=\"card\" style=\"text-align: center;\">\n");
      out.write("                            <h4>Avant (Problématique)</h4>\n");
      out.write("                            <div style=\"background: #f8f9fa; padding: 15px; border-radius: 5px;\">\n");
      out.write("                                <span style=\"background: white; color: #666; padding: 5px 10px; border-radius: 15px; border: 1px solid #ddd;\">\n");
      out.write("                                    Badge blanc peu visible\n");
      out.write("                                </span>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-6\">\n");
      out.write("                        <div class=\"card\" style=\"text-align: center;\">\n");
      out.write("                            <h4>Après (Corrigé)</h4>\n");
      out.write("                            <div style=\"background: #f8f9fa; padding: 15px; border-radius: 5px;\">\n");
      out.write("                                <span class=\"badge badge-success\">\n");
      out.write("                                    <i class=\"fas fa-check-circle\"></i> Confirmée\n");
      out.write("                                </span>\n");
      out.write("                                <span class=\"badge badge-warning\">\n");
      out.write("                                    <i class=\"fas fa-clock\"></i> En Attente\n");
      out.write("                                </span>\n");
      out.write("                                <span class=\"badge badge-danger\">\n");
      out.write("                                    <i class=\"fas fa-ban\"></i> Annulée\n");
      out.write("                                </span>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Fonctionnalité Admin -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-user-shield\"></i> Nouvelle Fonctionnalité Admin</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-server\"></i> Servlet Créé</h4>\n");
      out.write("                            <ul style=\"font-size: 12px;\">\n");
      out.write("                                <li><strong>Fichier :</strong> <code>AdminReservationsServlet.java</code></li>\n");
      out.write("                                <li><strong>URL :</strong> <code>/admin/reservations</code></li>\n");
      out.write("                                <li><strong>Fonction :</strong> Afficher toutes les réservations</li>\n");
      out.write("                                <li><strong>Sécurité :</strong> Accès admin uniquement</li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-file\"></i> Page JSP Créée</h4>\n");
      out.write("                            <ul style=\"font-size: 12px;\">\n");
      out.write("                                <li><strong>Fichier :</strong> <code>admin/reservations.jsp</code></li>\n");
      out.write("                                <li><strong>Contenu :</strong> Tableau complet des réservations</li>\n");
      out.write("                                <li><strong>Statistiques :</strong> Total, confirmées, en attente</li>\n");
      out.write("                                <li><strong>Actions :</strong> Confirmer, annuler, supprimer</li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-link\"></i> Navigation Ajoutée</h4>\n");
      out.write("                            <ul style=\"font-size: 12px;\">\n");
      out.write("                                <li><strong>Dashboard :</strong> Lien \"Voir Réservations\"</li>\n");
      out.write("                                <li><strong>Navbar :</strong> Lien admin ajouté</li>\n");
      out.write("                                <li><strong>Accès :</strong> Depuis interface admin</li>\n");
      out.write("                                <li><strong>Retour :</strong> Navigation cohérente</li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Fonctionnalités de la page admin -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-cogs\"></i> Fonctionnalités de la Page Admin Réservations</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-chart-bar\"></i> Statistiques</h4>\n");
      out.write("                            <ul style=\"font-size: 12px;\">\n");
      out.write("                                <li>📊 Total des réservations</li>\n");
      out.write("                                <li>✅ Réservations confirmées</li>\n");
      out.write("                                <li>⏳ Réservations en attente</li>\n");
      out.write("                                <li>❌ Réservations annulées</li>\n");
      out.write("                                <li>💰 Chiffre d'affaires total</li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-table\"></i> Tableau Complet</h4>\n");
      out.write("                            <ul style=\"font-size: 12px;\">\n");
      out.write("                                <li>🆔 ID de la réservation</li>\n");
      out.write("                                <li>👤 Informations utilisateur</li>\n");
      out.write("                                <li>🚂 Détails du trajet</li>\n");
      out.write("                                <li>📅 Date et heure du voyage</li>\n");
      out.write("                                <li>🎫 Classe et prix</li>\n");
      out.write("                                <li>📊 État de la réservation</li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-tools\"></i> Actions Admin</h4>\n");
      out.write("                            <ul style=\"font-size: 12px;\">\n");
      out.write("                                <li>✅ Confirmer réservation en attente</li>\n");
      out.write("                                <li>❌ Annuler une réservation</li>\n");
      out.write("                                <li>🗑️ Supprimer définitivement</li>\n");
      out.write("                                <li>🔒 Contrôle des permissions</li>\n");
      out.write("                                <li>📝 Messages de confirmation</li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Détails techniques -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-code\"></i> Détails Techniques</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-paint-brush\"></i> Améliorations CSS</h4>\n");
      out.write("                            <ul style=\"font-size: 12px;\">\n");
      out.write("                                <li><strong>Badges :</strong> Dégradés colorés avec <code>!important</code></li>\n");
      out.write("                                <li><strong>Bordures :</strong> Ajout de bordures colorées</li>\n");
      out.write("                                <li><strong>Ombres :</strong> Box-shadow pour le relief</li>\n");
      out.write("                                <li><strong>Contraste :</strong> Couleurs vives et lisibles</li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-server\"></i> Backend Java</h4>\n");
      out.write("                            <ul style=\"font-size: 12px;\">\n");
      out.write("                                <li><strong>Servlet :</strong> AdminReservationsServlet</li>\n");
      out.write("                                <li><strong>Imports :</strong> Jakarta Servlet API</li>\n");
      out.write("                                <li><strong>DAO :</strong> Utilisation de ReservationDAO</li>\n");
      out.write("                                <li><strong>Sécurité :</strong> Vérification rôle admin</li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-database\"></i> Données</h4>\n");
      out.write("                            <ul style=\"font-size: 12px;\">\n");
      out.write("                                <li><strong>Requêtes :</strong> findAll() pour toutes les réservations</li>\n");
      out.write("                                <li><strong>Statistiques :</strong> Calculs en temps réel</li>\n");
      out.write("                                <li><strong>Actions :</strong> Update et delete des réservations</li>\n");
      out.write("                                <li><strong>Logs :</strong> Traçabilité des actions admin</li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- URLs finales -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-link\"></i> URLs Finales Opérationnelles</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-2\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-user-shield\"></i> Admin</h4>\n");
      out.write("                            <ul style=\"font-size: 12px; font-family: monospace;\">\n");
      out.write("                                <li>✅ <code>/admin/reservations</code></li>\n");
      out.write("                                <li>✅ <code>/admin/dashboard</code></li>\n");
      out.write("                                <li>✅ <code>/dashboard</code></li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-2\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-user\"></i> Utilisateur</h4>\n");
      out.write("                            <ul style=\"font-size: 12px; font-family: monospace;\">\n");
      out.write("                                <li>✅ <code>/mes-reservations</code></li>\n");
      out.write("                                <li>✅ <code>/monCompte</code></li>\n");
      out.write("                                <li>✅ <code>/demo-boutons-mes-reservations.jsp</code></li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Instructions de test -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-play-circle\"></i> Comment Tester</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"workflow-steps\">\n");
      out.write("                    <div class=\"workflow-step\">\n");
      out.write("                        <div class=\"step-number\">1</div>\n");
      out.write("                        <div class=\"step-content\">\n");
      out.write("                            <h4><i class=\"fas fa-user-shield\"></i> Test Admin</h4>\n");
      out.write("                            <p>Se connecter avec un compte admin et aller sur <code>/admin/reservations</code></p>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    \n");
      out.write("                    <div class=\"workflow-step\">\n");
      out.write("                        <div class=\"step-number\">2</div>\n");
      out.write("                        <div class=\"step-content\">\n");
      out.write("                            <h4><i class=\"fas fa-palette\"></i> Test Couleurs</h4>\n");
      out.write("                            <p>Vérifier les badges colorés dans les pages de réservations</p>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    \n");
      out.write("                    <div class=\"workflow-step\">\n");
      out.write("                        <div class=\"step-number\">3</div>\n");
      out.write("                        <div class=\"step-content\">\n");
      out.write("                            <h4><i class=\"fas fa-mouse-pointer\"></i> Test Actions</h4>\n");
      out.write("                            <p>Tester les boutons admin : Confirmer, Annuler, Supprimer</p>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Actions de test -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-rocket\"></i> Tester Maintenant</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/login.jsp\" class=\"btn btn-primary btn-full\">\n");
      out.write("                            <i class=\"fas fa-sign-in-alt\"></i> Se Connecter Admin\n");
      out.write("                        </a>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/admin/reservations\" class=\"btn btn-success btn-full\">\n");
      out.write("                            <i class=\"fas fa-ticket-alt\"></i> Voir Réservations Admin\n");
      out.write("                        </a>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/demo-boutons-mes-reservations.jsp\" class=\"btn btn-warning btn-full\">\n");
      out.write("                            <i class=\"fas fa-eye\"></i> Voir Démonstration\n");
      out.write("                        </a>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Message final -->\n");
      out.write("            <div class=\"text-center mt-20\">\n");
      out.write("                <div class=\"alert alert-success\">\n");
      out.write("                    <i class=\"fas fa-trophy\"></i> \n");
      out.write("                    <strong>🎯 CORRECTIONS FINALES TERMINÉES AVEC SUCCÈS ! 🎯</strong>\n");
      out.write("                    <br>\n");
      out.write("                    <small>Couleurs des places corrigées et fonctionnalité admin pour voir les réservations implémentée.</small>\n");
      out.write("                    <br>\n");
      out.write("                    <div style=\"margin-top: 10px;\">\n");
      out.write("                        <span class=\"badge badge-success\">✅ Couleurs Corrigées</span>\n");
      out.write("                        <span class=\"badge badge-success\">✅ Admin Réservations</span>\n");
      out.write("                        <span class=\"badge badge-success\">✅ Navigation Ajoutée</span>\n");
      out.write("                        <span class=\"badge badge-success\">✅ Fonctionnalités Complètes</span>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("        </div>\n");
      out.write("    </div>\n");
      out.write("\n");
      out.write("    <style>\n");
      out.write("        .workflow-steps {\n");
      out.write("            display: flex;\n");
      out.write("            flex-direction: column;\n");
      out.write("            gap: 15px;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .workflow-step {\n");
      out.write("            display: flex;\n");
      out.write("            align-items: flex-start;\n");
      out.write("            gap: 15px;\n");
      out.write("            padding: 15px;\n");
      out.write("            background: var(--light-bg);\n");
      out.write("            border-radius: 8px;\n");
      out.write("            border-left: 4px solid var(--success-color);\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .step-number {\n");
      out.write("            width: 30px;\n");
      out.write("            height: 30px;\n");
      out.write("            background: var(--success-color);\n");
      out.write("            color: var(--white);\n");
      out.write("            border-radius: 50%;\n");
      out.write("            display: flex;\n");
      out.write("            align-items: center;\n");
      out.write("            justify-content: center;\n");
      out.write("            font-weight: 600;\n");
      out.write("            flex-shrink: 0;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .step-content h4 {\n");
      out.write("            margin: 0 0 5px 0;\n");
      out.write("            color: var(--text-dark);\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .step-content p {\n");
      out.write("            margin: 0;\n");
      out.write("            color: var(--text-muted);\n");
      out.write("            font-size: 14px;\n");
      out.write("        }\n");
      out.write("    </style>\n");
      out.write("</body>\n");
      out.write("</html>\n");
    } catch (java.lang.Throwable t) {
      if (!(t instanceof jakarta.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try {
            if (response.isCommitted()) {
              out.flush();
            } else {
              out.clearBuffer();
            }
          } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }
}
