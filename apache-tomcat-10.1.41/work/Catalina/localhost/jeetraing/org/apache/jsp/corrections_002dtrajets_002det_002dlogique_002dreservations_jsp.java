/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: Apache Tomcat/10.1.41
 * Generated at: 2025-05-30 00:46:18 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp;

import jakarta.servlet.*;
import jakarta.servlet.http.*;
import jakarta.servlet.jsp.*;

public final class corrections_002dtrajets_002det_002dlogique_002dreservations_jsp extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent,
                 org.apache.jasper.runtime.JspSourceImports,
                 org.apache.jasper.runtime.JspSourceDirectives {

  private static final jakarta.servlet.jsp.JspFactory _jspxFactory =
          jakarta.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  static {
    _jspx_dependants = new java.util.HashMap<java.lang.String,java.lang.Long>(2);
    _jspx_dependants.put("/WEB-INF/lib/jakarta.servlet.jsp.jstl-3.0.1.jar", Long.valueOf(1747175778000L));
    _jspx_dependants.put("jar:file:/C:/Users/<USER>/Downloads/jeetraing/apache-tomcat-10.1.41/webapps/jeetraing/WEB-INF/lib/jakarta.servlet.jsp.jstl-3.0.1.jar!/META-INF/c-1_2.tld", Long.valueOf(1664449878000L));
  }

  private static final java.util.Set<java.lang.String> _jspx_imports_packages;

  private static final java.util.Set<java.lang.String> _jspx_imports_classes;

  static {
    _jspx_imports_packages = new java.util.LinkedHashSet<>(4);
    _jspx_imports_packages.add("jakarta.servlet");
    _jspx_imports_packages.add("jakarta.servlet.http");
    _jspx_imports_packages.add("jakarta.servlet.jsp");
    _jspx_imports_classes = null;
  }

  private volatile jakarta.el.ExpressionFactory _el_expressionfactory;
  private volatile org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public java.util.Set<java.lang.String> getPackageImports() {
    return _jspx_imports_packages;
  }

  public java.util.Set<java.lang.String> getClassImports() {
    return _jspx_imports_classes;
  }

  public boolean getErrorOnELNotFound() {
    return false;
  }

  public jakarta.el.ExpressionFactory _jsp_getExpressionFactory() {
    if (_el_expressionfactory == null) {
      synchronized (this) {
        if (_el_expressionfactory == null) {
          _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
        }
      }
    }
    return _el_expressionfactory;
  }

  public org.apache.tomcat.InstanceManager _jsp_getInstanceManager() {
    if (_jsp_instancemanager == null) {
      synchronized (this) {
        if (_jsp_instancemanager == null) {
          _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
        }
      }
    }
    return _jsp_instancemanager;
  }

  public void _jspInit() {
  }

  public void _jspDestroy() {
  }

  public void _jspService(final jakarta.servlet.http.HttpServletRequest request, final jakarta.servlet.http.HttpServletResponse response)
      throws java.io.IOException, jakarta.servlet.ServletException {

    if (!jakarta.servlet.DispatcherType.ERROR.equals(request.getDispatcherType())) {
      final java.lang.String _jspx_method = request.getMethod();
      if ("OPTIONS".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        return;
      }
      if (!"GET".equals(_jspx_method) && !"POST".equals(_jspx_method) && !"HEAD".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        response.sendError(HttpServletResponse.SC_METHOD_NOT_ALLOWED, "Les JSPs ne permettent que GET, POST ou HEAD. Jasper permet aussi OPTIONS");
        return;
      }
    }

    final jakarta.servlet.jsp.PageContext pageContext;
    jakarta.servlet.http.HttpSession session = null;
    final jakarta.servlet.ServletContext application;
    final jakarta.servlet.ServletConfig config;
    jakarta.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    jakarta.servlet.jsp.JspWriter _jspx_out = null;
    jakarta.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html; charset=UTF-8");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;

      out.write("\n");
      out.write("\n");
      out.write("<!DOCTYPE html>\n");
      out.write("<html lang=\"fr\">\n");
      out.write("<head>\n");
      out.write("    <meta charset=\"UTF-8\">\n");
      out.write("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n");
      out.write("    <title>🔧 Corrections Trajets et Logique Réservations - JEE Training</title>\n");
      out.write("    <link rel=\"stylesheet\" href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/css/style.css\">\n");
      out.write("    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\">\n");
      out.write("</head>\n");
      out.write("<body>\n");
      out.write("    <!-- Navigation -->\n");
      out.write("    <nav class=\"navbar\">\n");
      out.write("        <div class=\"container\">\n");
      out.write("            <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/recherche\" class=\"logo\">\n");
      out.write("                <i class=\"fas fa-train\"></i> JEE Training\n");
      out.write("            </a>\n");
      out.write("            <div class=\"nav-links\">\n");
      out.write("                <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/login.jsp\"><i class=\"fas fa-sign-in-alt\"></i> Connexion</a>\n");
      out.write("                <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/register.jsp\"><i class=\"fas fa-user-plus\"></i> Inscription</a>\n");
      out.write("            </div>\n");
      out.write("        </div>\n");
      out.write("    </nav>\n");
      out.write("\n");
      out.write("    <!-- Contenu principal -->\n");
      out.write("    <div class=\"container\">\n");
      out.write("        <div class=\"main-content\">\n");
      out.write("            <h1><i class=\"fas fa-tools\"></i> Corrections Trajets et Logique Réservations</h1>\n");
      out.write("            \n");
      out.write("            <!-- Message de succès -->\n");
      out.write("            <div class=\"alert alert-success\">\n");
      out.write("                <i class=\"fas fa-check-circle\"></i> \n");
      out.write("                <strong>✅ CORRECTIONS APPLIQUÉES AVEC SUCCÈS !</strong>\n");
      out.write("                <br>\n");
      out.write("                <small>Couleurs des trajets corrigées et logique de modification/suppression des réservations améliorée.</small>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Résumé des corrections -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-list-check\"></i> Résumé des Corrections</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-2\">\n");
      out.write("                        <div class=\"card\" style=\"background: #d4edda; border-left: 4px solid #28a745;\">\n");
      out.write("                            <h4><i class=\"fas fa-palette\"></i> Correction 1</h4>\n");
      out.write("                            <h5>🎨 Couleurs des Trajets</h5>\n");
      out.write("                            <ul style=\"font-size: 12px;\">\n");
      out.write("                                <li><strong>Problème :</strong> Trajets en blanc peu visibles</li>\n");
      out.write("                                <li><strong>Solution :</strong> Dégradé bleu avec texte blanc</li>\n");
      out.write("                                <li><strong>Fichier :</strong> <code>css/style.css</code></li>\n");
      out.write("                                <li><strong>Classe :</strong> <code>.trajet-badge</code></li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-2\">\n");
      out.write("                        <div class=\"card\" style=\"background: #d4edda; border-left: 4px solid #28a745;\">\n");
      out.write("                            <h4><i class=\"fas fa-cogs\"></i> Correction 2</h4>\n");
      out.write("                            <h5>⚙️ Logique Réservations</h5>\n");
      out.write("                            <ul style=\"font-size: 12px;\">\n");
      out.write("                                <li><strong>Problème :</strong> Billets achetés non modifiables</li>\n");
      out.write("                                <li><strong>Solution :</strong> Autoriser modification/annulation</li>\n");
      out.write("                                <li><strong>Fichier :</strong> <code>mes-reservations.jsp</code></li>\n");
      out.write("                                <li><strong>États :</strong> Confirmée + Acheté</li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Détails Correction 1 : Couleurs des Trajets -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-palette\"></i> Correction 1 : Couleurs des Trajets Admin</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-2\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4 style=\"color: #dc3545;\"><i class=\"fas fa-times\"></i> Avant</h4>\n");
      out.write("                            <div style=\"background: #f8d7da; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 11px;\">\n");
      out.write("                                <strong>CSS Ancien:</strong><br>\n");
      out.write("                                <code>background: var(--light-bg);</code><br>\n");
      out.write("                                <code>color: var(--primary-color);</code><br>\n");
      out.write("                                <code>border: 1px solid var(--border-color);</code>\n");
      out.write("                                <br><br>\n");
      out.write("                                <strong>Résultat:</strong><br>\n");
      out.write("                                <span style=\"background: white; color: #333; padding: 2px 6px; border: 1px solid #ddd; border-radius: 10px;\">\n");
      out.write("                                    → Paris → Lyon\n");
      out.write("                                </span>\n");
      out.write("                                <br><small style=\"color: #dc3545;\">❌ Peu visible sur fond blanc</small>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-2\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4 style=\"color: #28a745;\"><i class=\"fas fa-check\"></i> Après</h4>\n");
      out.write("                            <div style=\"background: #d4edda; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 11px;\">\n");
      out.write("                                <strong>CSS Nouveau:</strong><br>\n");
      out.write("                                <code>background: linear-gradient(135deg, #3498db, #2980b9);</code><br>\n");
      out.write("                                <code>color: var(--white);</code><br>\n");
      out.write("                                <code>box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);</code>\n");
      out.write("                                <br><br>\n");
      out.write("                                <strong>Résultat:</strong><br>\n");
      out.write("                                <span style=\"background: linear-gradient(135deg, #3498db, #2980b9); color: white; padding: 4px 8px; border-radius: 15px; font-weight: 600;\">\n");
      out.write("                                    → Paris → Lyon\n");
      out.write("                                </span>\n");
      out.write("                                <br><small style=\"color: #28a745;\">✅ Très visible et moderne</small>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Détails Correction 2 : Logique Réservations -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-cogs\"></i> Correction 2 : Logique Modification/Suppression Réservations</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-2\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4 style=\"color: #dc3545;\"><i class=\"fas fa-times\"></i> Problème Identifié</h4>\n");
      out.write("                            <div style=\"background: #f8d7da; padding: 15px; border-radius: 5px; font-size: 12px;\">\n");
      out.write("                                <strong>Logique Ancienne:</strong><br>\n");
      out.write("                                <ul style=\"margin: 5px 0;\">\n");
      out.write("                                    <li>❌ Billets achetés non annulables</li>\n");
      out.write("                                    <li>❌ Message d'erreur confus</li>\n");
      out.write("                                    <li>❌ États mal gérés</li>\n");
      out.write("                                </ul>\n");
      out.write("                                <br>\n");
      out.write("                                <strong>Message d'erreur:</strong><br>\n");
      out.write("                                <em>\"Cette réservation ne peut pas être annulée (état: acheté)\"</em>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-2\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4 style=\"color: #28a745;\"><i class=\"fas fa-check\"></i> Solution Appliquée</h4>\n");
      out.write("                            <div style=\"background: #d4edda; padding: 15px; border-radius: 5px; font-size: 12px;\">\n");
      out.write("                                <strong>Nouvelle Logique:</strong><br>\n");
      out.write("                                <ul style=\"margin: 5px 0;\">\n");
      out.write("                                    <li>✅ Billets achetés modifiables</li>\n");
      out.write("                                    <li>✅ Billets achetés annulables</li>\n");
      out.write("                                    <li>✅ Messages informatifs clairs</li>\n");
      out.write("                                    <li>✅ Tous les états gérés</li>\n");
      out.write("                                </ul>\n");
      out.write("                                <br>\n");
      out.write("                                <strong>Actions disponibles:</strong><br>\n");
      out.write("                                <em>Modifier, Télécharger PDF, Annuler</em>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- États des réservations -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-traffic-light\"></i> États des Réservations et Actions</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-check-circle\"></i> Confirmée</h4>\n");
      out.write("                            <div style=\"padding: 10px; background: #d4edda; border-radius: 5px;\">\n");
      out.write("                                <span class=\"badge badge-success\">\n");
      out.write("                                    <i class=\"fas fa-check-circle\"></i> Confirmée\n");
      out.write("                                </span>\n");
      out.write("                                <br><br>\n");
      out.write("                                <strong>Actions disponibles:</strong>\n");
      out.write("                                <ul style=\"font-size: 12px; margin: 5px 0;\">\n");
      out.write("                                    <li>✅ Modifier</li>\n");
      out.write("                                    <li>✅ Télécharger PDF</li>\n");
      out.write("                                    <li>✅ Annuler</li>\n");
      out.write("                                </ul>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-credit-card\"></i> Acheté</h4>\n");
      out.write("                            <div style=\"padding: 10px; background: #cce5ff; border-radius: 5px;\">\n");
      out.write("                                <span class=\"badge badge-primary\">\n");
      out.write("                                    <i class=\"fas fa-credit-card\"></i> Acheté\n");
      out.write("                                </span>\n");
      out.write("                                <br><br>\n");
      out.write("                                <strong>Actions disponibles:</strong>\n");
      out.write("                                <ul style=\"font-size: 12px; margin: 5px 0;\">\n");
      out.write("                                    <li>✅ Modifier</li>\n");
      out.write("                                    <li>✅ Télécharger PDF</li>\n");
      out.write("                                    <li>✅ Annuler</li>\n");
      out.write("                                </ul>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-clock\"></i> En Attente</h4>\n");
      out.write("                            <div style=\"padding: 10px; background: #fff3cd; border-radius: 5px;\">\n");
      out.write("                                <span class=\"badge badge-warning\">\n");
      out.write("                                    <i class=\"fas fa-clock\"></i> En Attente\n");
      out.write("                                </span>\n");
      out.write("                                <br><br>\n");
      out.write("                                <strong>Actions disponibles:</strong>\n");
      out.write("                                <ul style=\"font-size: 12px; margin: 5px 0;\">\n");
      out.write("                                    <li>⏳ Attendre confirmation admin</li>\n");
      out.write("                                    <li>❌ Pas de modification</li>\n");
      out.write("                                    <li>❌ Pas d'annulation</li>\n");
      out.write("                                </ul>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-hourglass-half\"></i> Annulation en cours</h4>\n");
      out.write("                            <div style=\"padding: 10px; background: #d1ecf1; border-radius: 5px;\">\n");
      out.write("                                <span class=\"badge badge-info\">\n");
      out.write("                                    <i class=\"fas fa-hourglass-half\"></i> Annulation en cours\n");
      out.write("                                </span>\n");
      out.write("                                <br><br>\n");
      out.write("                                <strong>Actions disponibles:</strong>\n");
      out.write("                                <ul style=\"font-size: 12px; margin: 5px 0;\">\n");
      out.write("                                    <li>⏳ Attendre décision admin</li>\n");
      out.write("                                    <li>❌ Pas de modification</li>\n");
      out.write("                                    <li>❌ Pas d'annulation</li>\n");
      out.write("                                </ul>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-times-circle\"></i> Annulée</h4>\n");
      out.write("                            <div style=\"padding: 10px; background: #f8d7da; border-radius: 5px;\">\n");
      out.write("                                <span class=\"badge badge-danger\">\n");
      out.write("                                    <i class=\"fas fa-times-circle\"></i> Annulée\n");
      out.write("                                </span>\n");
      out.write("                                <br><br>\n");
      out.write("                                <strong>Actions disponibles:</strong>\n");
      out.write("                                <ul style=\"font-size: 12px; margin: 5px 0;\">\n");
      out.write("                                    <li>❌ Aucune action</li>\n");
      out.write("                                    <li>📋 Réservation annulée</li>\n");
      out.write("                                </ul>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-check-double\"></i> Utilisé</h4>\n");
      out.write("                            <div style=\"padding: 10px; background: #e2e3e5; border-radius: 5px;\">\n");
      out.write("                                <span class=\"badge badge-dark\">\n");
      out.write("                                    <i class=\"fas fa-check-double\"></i> Utilisé\n");
      out.write("                                </span>\n");
      out.write("                                <br><br>\n");
      out.write("                                <strong>Actions disponibles:</strong>\n");
      out.write("                                <ul style=\"font-size: 12px; margin: 5px 0;\">\n");
      out.write("                                    <li>❌ Aucune action</li>\n");
      out.write("                                    <li>📋 Voyage terminé</li>\n");
      out.write("                                </ul>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Fichiers modifiés -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-file-code\"></i> Fichiers Modifiés</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-2\">\n");
      out.write("                        <div class=\"admin-card\">\n");
      out.write("                            <div class=\"admin-card-icon\">\n");
      out.write("                                <i class=\"fas fa-palette\"></i>\n");
      out.write("                            </div>\n");
      out.write("                            <div class=\"admin-card-content\">\n");
      out.write("                                <h3>🎨 CSS Styles</h3>\n");
      out.write("                                <p><strong>Fichier :</strong> <code>src/main/webapp/css/style.css</code></p>\n");
      out.write("                                <ul style=\"font-size: 12px;\">\n");
      out.write("                                    <li>✅ Classe <code>.trajet-badge</code> modifiée</li>\n");
      out.write("                                    <li>✅ Dégradé bleu ajouté</li>\n");
      out.write("                                    <li>✅ Texte blanc pour contraste</li>\n");
      out.write("                                    <li>✅ Ombre pour profondeur</li>\n");
      out.write("                                </ul>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-2\">\n");
      out.write("                        <div class=\"admin-card\">\n");
      out.write("                            <div class=\"admin-card-icon\">\n");
      out.write("                                <i class=\"fas fa-code\"></i>\n");
      out.write("                            </div>\n");
      out.write("                            <div class=\"admin-card-content\">\n");
      out.write("                                <h3>📄 JSP Réservations</h3>\n");
      out.write("                                <p><strong>Fichier :</strong> <code>src/main/webapp/mes-reservations.jsp</code></p>\n");
      out.write("                                <ul style=\"font-size: 12px;\">\n");
      out.write("                                    <li>✅ Logique d'annulation corrigée</li>\n");
      out.write("                                    <li>✅ Badge \"Acheté\" ajouté</li>\n");
      out.write("                                    <li>✅ Messages informatifs ajoutés</li>\n");
      out.write("                                    <li>✅ Tous les états gérés</li>\n");
      out.write("                                </ul>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Tests et validation -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-check-double\"></i> Tests et Validation</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"workflow-steps\">\n");
      out.write("                    <div class=\"workflow-step\">\n");
      out.write("                        <div class=\"step-number\">1</div>\n");
      out.write("                        <div class=\"step-content\">\n");
      out.write("                            <h4><i class=\"fas fa-palette\"></i> Test Couleurs Trajets</h4>\n");
      out.write("                            <p>Vérification de l'affichage des trajets dans la page admin avec les nouvelles couleurs</p>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    \n");
      out.write("                    <div class=\"workflow-step\">\n");
      out.write("                        <div class=\"step-number\">2</div>\n");
      out.write("                        <div class=\"step-content\">\n");
      out.write("                            <h4><i class=\"fas fa-cogs\"></i> Test Logique Réservations</h4>\n");
      out.write("                            <p>Validation des actions disponibles selon l'état des réservations</p>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    \n");
      out.write("                    <div class=\"workflow-step\">\n");
      out.write("                        <div class=\"step-number\">3</div>\n");
      out.write("                        <div class=\"step-content\">\n");
      out.write("                            <h4><i class=\"fas fa-compile\"></i> Compilation et Déploiement</h4>\n");
      out.write("                            <p>Build Maven réussi et déploiement sur Tomcat effectué</p>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    \n");
      out.write("                    <div class=\"workflow-step\">\n");
      out.write("                        <div class=\"step-number\">4</div>\n");
      out.write("                        <div class=\"step-content\">\n");
      out.write("                            <h4><i class=\"fas fa-check\"></i> Validation Fonctionnelle</h4>\n");
      out.write("                            <p>Tests des nouvelles fonctionnalités et vérification de l'interface</p>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- URLs de test -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-link\"></i> URLs de Test</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-user-shield\"></i> Admin</h4>\n");
      out.write("                            <ul style=\"font-size: 12px; font-family: monospace;\">\n");
      out.write("                                <li>✅ <code>/admin/trajets</code> - Couleurs corrigées</li>\n");
      out.write("                                <li>✅ <code>/admin/reservations</code> - Gestion admin</li>\n");
      out.write("                                <li>✅ <code>/admin/dashboard</code> - Tableau de bord</li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-user\"></i> Utilisateur</h4>\n");
      out.write("                            <ul style=\"font-size: 12px; font-family: monospace;\">\n");
      out.write("                                <li>✅ <code>/mes-reservations</code> - Logique corrigée</li>\n");
      out.write("                                <li>✅ <code>/monCompte</code> - Profil utilisateur</li>\n");
      out.write("                                <li>✅ <code>/recherche</code> - Recherche voyages</li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-eye\"></i> Démonstration</h4>\n");
      out.write("                            <ul style=\"font-size: 12px; font-family: monospace;\">\n");
      out.write("                                <li>✅ <code>/corrections-trajets-et-logique-reservations.jsp</code></li>\n");
      out.write("                                <li>✅ <code>/correction-erreur-admin-reservations.jsp</code></li>\n");
      out.write("                                <li>✅ <code>/corrections-finales-couleurs-admin.jsp</code></li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Actions de test -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-rocket\"></i> Tester Maintenant</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/login.jsp\" class=\"btn btn-primary btn-full\">\n");
      out.write("                            <i class=\"fas fa-sign-in-alt\"></i> Se Connecter\n");
      out.write("                        </a>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/admin/trajets\" class=\"btn btn-warning btn-full\">\n");
      out.write("                            <i class=\"fas fa-palette\"></i> Tester Couleurs Trajets\n");
      out.write("                        </a>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/mes-reservations\" class=\"btn btn-success btn-full\">\n");
      out.write("                            <i class=\"fas fa-cogs\"></i> Tester Logique Réservations\n");
      out.write("                        </a>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Message final -->\n");
      out.write("            <div class=\"text-center mt-20\">\n");
      out.write("                <div class=\"alert alert-success\">\n");
      out.write("                    <i class=\"fas fa-trophy\"></i> \n");
      out.write("                    <strong>🎯 CORRECTIONS TERMINÉES AVEC SUCCÈS ! 🎯</strong>\n");
      out.write("                    <br>\n");
      out.write("                    <small>Couleurs des trajets corrigées et logique de modification/suppression des réservations améliorée.</small>\n");
      out.write("                    <br>\n");
      out.write("                    <div style=\"margin-top: 10px;\">\n");
      out.write("                        <span class=\"badge badge-success\">✅ Trajets Colorés</span>\n");
      out.write("                        <span class=\"badge badge-success\">✅ Logique Corrigée</span>\n");
      out.write("                        <span class=\"badge badge-success\">✅ Interface Améliorée</span>\n");
      out.write("                        <span class=\"badge badge-success\">✅ Tests Validés</span>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("        </div>\n");
      out.write("    </div>\n");
      out.write("\n");
      out.write("    <style>\n");
      out.write("        .workflow-steps {\n");
      out.write("            display: flex;\n");
      out.write("            flex-direction: column;\n");
      out.write("            gap: 15px;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .workflow-step {\n");
      out.write("            display: flex;\n");
      out.write("            align-items: flex-start;\n");
      out.write("            gap: 15px;\n");
      out.write("            padding: 15px;\n");
      out.write("            background: var(--light-bg);\n");
      out.write("            border-radius: 8px;\n");
      out.write("            border-left: 4px solid var(--success-color);\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .step-number {\n");
      out.write("            width: 30px;\n");
      out.write("            height: 30px;\n");
      out.write("            background: var(--success-color);\n");
      out.write("            color: var(--white);\n");
      out.write("            border-radius: 50%;\n");
      out.write("            display: flex;\n");
      out.write("            align-items: center;\n");
      out.write("            justify-content: center;\n");
      out.write("            font-weight: 600;\n");
      out.write("            flex-shrink: 0;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .step-content h4 {\n");
      out.write("            margin: 0 0 5px 0;\n");
      out.write("            color: var(--text-dark);\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .step-content p {\n");
      out.write("            margin: 0;\n");
      out.write("            color: var(--text-muted);\n");
      out.write("            font-size: 14px;\n");
      out.write("        }\n");
      out.write("    </style>\n");
      out.write("</body>\n");
      out.write("</html>\n");
    } catch (java.lang.Throwable t) {
      if (!(t instanceof jakarta.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try {
            if (response.isCommitted()) {
              out.flush();
            } else {
              out.clearBuffer();
            }
          } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }
}
