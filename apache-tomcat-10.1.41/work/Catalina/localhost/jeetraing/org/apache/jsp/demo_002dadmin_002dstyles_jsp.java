/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: Apache Tomcat/10.1.41
 * Generated at: 2025-05-28 18:04:39 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp;

import jakarta.servlet.*;
import jakarta.servlet.http.*;
import jakarta.servlet.jsp.*;

public final class demo_002dadmin_002dstyles_jsp extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent,
                 org.apache.jasper.runtime.JspSourceImports,
                 org.apache.jasper.runtime.JspSourceDirectives {

  private static final jakarta.servlet.jsp.JspFactory _jspxFactory =
          jakarta.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  static {
    _jspx_dependants = new java.util.HashMap<java.lang.String,java.lang.Long>(2);
    _jspx_dependants.put("/WEB-INF/lib/jakarta.servlet.jsp.jstl-3.0.1.jar", Long.valueOf(1747175778000L));
    _jspx_dependants.put("jar:file:/C:/Users/<USER>/Downloads/jeetraing/apache-tomcat-10.1.41/webapps/jeetraing/WEB-INF/lib/jakarta.servlet.jsp.jstl-3.0.1.jar!/META-INF/c-1_2.tld", Long.valueOf(1664449878000L));
  }

  private static final java.util.Set<java.lang.String> _jspx_imports_packages;

  private static final java.util.Set<java.lang.String> _jspx_imports_classes;

  static {
    _jspx_imports_packages = new java.util.LinkedHashSet<>(4);
    _jspx_imports_packages.add("jakarta.servlet");
    _jspx_imports_packages.add("jakarta.servlet.http");
    _jspx_imports_packages.add("jakarta.servlet.jsp");
    _jspx_imports_classes = null;
  }

  private volatile jakarta.el.ExpressionFactory _el_expressionfactory;
  private volatile org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public java.util.Set<java.lang.String> getPackageImports() {
    return _jspx_imports_packages;
  }

  public java.util.Set<java.lang.String> getClassImports() {
    return _jspx_imports_classes;
  }

  public boolean getErrorOnELNotFound() {
    return false;
  }

  public jakarta.el.ExpressionFactory _jsp_getExpressionFactory() {
    if (_el_expressionfactory == null) {
      synchronized (this) {
        if (_el_expressionfactory == null) {
          _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
        }
      }
    }
    return _el_expressionfactory;
  }

  public org.apache.tomcat.InstanceManager _jsp_getInstanceManager() {
    if (_jsp_instancemanager == null) {
      synchronized (this) {
        if (_jsp_instancemanager == null) {
          _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
        }
      }
    }
    return _jsp_instancemanager;
  }

  public void _jspInit() {
  }

  public void _jspDestroy() {
  }

  public void _jspService(final jakarta.servlet.http.HttpServletRequest request, final jakarta.servlet.http.HttpServletResponse response)
      throws java.io.IOException, jakarta.servlet.ServletException {

    if (!jakarta.servlet.DispatcherType.ERROR.equals(request.getDispatcherType())) {
      final java.lang.String _jspx_method = request.getMethod();
      if ("OPTIONS".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        return;
      }
      if (!"GET".equals(_jspx_method) && !"POST".equals(_jspx_method) && !"HEAD".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        response.sendError(HttpServletResponse.SC_METHOD_NOT_ALLOWED, "Les JSPs ne permettent que GET, POST ou HEAD. Jasper permet aussi OPTIONS");
        return;
      }
    }

    final jakarta.servlet.jsp.PageContext pageContext;
    jakarta.servlet.http.HttpSession session = null;
    final jakarta.servlet.ServletContext application;
    final jakarta.servlet.ServletConfig config;
    jakarta.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    jakarta.servlet.jsp.JspWriter _jspx_out = null;
    jakarta.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html; charset=UTF-8");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;

      out.write("\n");
      out.write("\n");
      out.write("<!DOCTYPE html>\n");
      out.write("<html lang=\"fr\">\n");
      out.write("<head>\n");
      out.write("    <meta charset=\"UTF-8\">\n");
      out.write("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n");
      out.write("    <title>🎨 Styles Admin Améliorés - JEE Training</title>\n");
      out.write("    <link rel=\"stylesheet\" href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/css/style.css\">\n");
      out.write("    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\">\n");
      out.write("</head>\n");
      out.write("<body>\n");
      out.write("    <!-- Navigation -->\n");
      out.write("    <nav class=\"navbar\">\n");
      out.write("        <div class=\"container\">\n");
      out.write("            <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/recherche\" class=\"logo\">\n");
      out.write("                <i class=\"fas fa-train\"></i> JEE Training\n");
      out.write("            </a>\n");
      out.write("            <div class=\"nav-links\">\n");
      out.write("                <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/login.jsp\"><i class=\"fas fa-sign-in-alt\"></i> Connexion</a>\n");
      out.write("                <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/register.jsp\"><i class=\"fas fa-user-plus\"></i> Inscription</a>\n");
      out.write("            </div>\n");
      out.write("        </div>\n");
      out.write("    </nav>\n");
      out.write("\n");
      out.write("    <!-- Contenu principal -->\n");
      out.write("    <div class=\"container\">\n");
      out.write("        <div class=\"main-content\">\n");
      out.write("            <h1><i class=\"fas fa-palette\"></i> Styles Admin Améliorés</h1>\n");
      out.write("            \n");
      out.write("            <!-- Message de succès -->\n");
      out.write("            <div class=\"alert alert-success\">\n");
      out.write("                <i class=\"fas fa-check-circle\"></i> \n");
      out.write("                <strong>Transformation Réussie !</strong> Toutes les pages d'administration ont été modernisées avec un design cohérent et professionnel.\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Avant/Après -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-magic\"></i> Transformation Avant/Après</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-2\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4>❌ Avant</h4>\n");
      out.write("                            <div style=\"background: #f8d7da; padding: 15px; border-radius: 5px;\">\n");
      out.write("                                <ul style=\"margin: 0; font-size: 12px;\">\n");
      out.write("                                    <li>Bootstrap basique</li>\n");
      out.write("                                    <li>CSS inline dispersé</li>\n");
      out.write("                                    <li>Design incohérent</li>\n");
      out.write("                                    <li>Pas d'icônes</li>\n");
      out.write("                                    <li>Tables simples</li>\n");
      out.write("                                    <li>Pas de breadcrumb</li>\n");
      out.write("                                </ul>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-2\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4>✅ Après</h4>\n");
      out.write("                            <div style=\"background: #d4edda; padding: 15px; border-radius: 5px;\">\n");
      out.write("                                <ul style=\"margin: 0; font-size: 12px;\">\n");
      out.write("                                    <li>Design moderne unifié</li>\n");
      out.write("                                    <li>CSS centralisé</li>\n");
      out.write("                                    <li>Interface cohérente</li>\n");
      out.write("                                    <li>Icônes FontAwesome</li>\n");
      out.write("                                    <li>Tables stylisées</li>\n");
      out.write("                                    <li>Navigation intuitive</li>\n");
      out.write("                                </ul>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Pages améliorées -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-file-code\"></i> Pages Améliorées</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/admin/dashboard\" class=\"admin-card\">\n");
      out.write("                            <div class=\"admin-card-icon\">\n");
      out.write("                                <i class=\"fas fa-tachometer-alt\"></i>\n");
      out.write("                            </div>\n");
      out.write("                            <div class=\"admin-card-content\">\n");
      out.write("                                <h3>Dashboard Admin</h3>\n");
      out.write("                                <p>Interface moderne avec cartes interactives</p>\n");
      out.write("                            </div>\n");
      out.write("                            <div class=\"admin-card-arrow\">\n");
      out.write("                                <i class=\"fas fa-arrow-right\"></i>\n");
      out.write("                            </div>\n");
      out.write("                        </a>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/admin/users\" class=\"admin-card\">\n");
      out.write("                            <div class=\"admin-card-icon\">\n");
      out.write("                                <i class=\"fas fa-users\"></i>\n");
      out.write("                            </div>\n");
      out.write("                            <div class=\"admin-card-content\">\n");
      out.write("                                <h3>Gestion Utilisateurs</h3>\n");
      out.write("                                <p>Table moderne avec badges et actions</p>\n");
      out.write("                            </div>\n");
      out.write("                            <div class=\"admin-card-arrow\">\n");
      out.write("                                <i class=\"fas fa-arrow-right\"></i>\n");
      out.write("                            </div>\n");
      out.write("                        </a>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/admin/revenue\" class=\"admin-card\">\n");
      out.write("                            <div class=\"admin-card-icon\">\n");
      out.write("                                <i class=\"fas fa-euro-sign\"></i>\n");
      out.write("                            </div>\n");
      out.write("                            <div class=\"admin-card-content\">\n");
      out.write("                                <h3>Revenus</h3>\n");
      out.write("                                <p>Formulaires stylisés et résultats visuels</p>\n");
      out.write("                            </div>\n");
      out.write("                            <div class=\"admin-card-arrow\">\n");
      out.write("                                <i class=\"fas fa-arrow-right\"></i>\n");
      out.write("                            </div>\n");
      out.write("                        </a>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/admin/popular-trajets\" class=\"admin-card\">\n");
      out.write("                            <div class=\"admin-card-icon\">\n");
      out.write("                                <i class=\"fas fa-route\"></i>\n");
      out.write("                            </div>\n");
      out.write("                            <div class=\"admin-card-content\">\n");
      out.write("                                <h3>Trajets Populaires</h3>\n");
      out.write("                                <p>Classement avec badges et barres de progression</p>\n");
      out.write("                            </div>\n");
      out.write("                            <div class=\"admin-card-arrow\">\n");
      out.write("                                <i class=\"fas fa-arrow-right\"></i>\n");
      out.write("                            </div>\n");
      out.write("                        </a>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Nouveaux composants -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-puzzle-piece\"></i> Nouveaux Composants</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-2\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-table\"></i> Tables Admin</h4>\n");
      out.write("                            <p>Tables modernes avec en-têtes dégradés et hover effects</p>\n");
      out.write("                            <div class=\"admin-table\" style=\"font-size: 10px;\">\n");
      out.write("                                <table style=\"width: 100%;\">\n");
      out.write("                                    <thead>\n");
      out.write("                                        <tr>\n");
      out.write("                                            <th style=\"padding: 5px;\">ID</th>\n");
      out.write("                                            <th style=\"padding: 5px;\">Nom</th>\n");
      out.write("                                        </tr>\n");
      out.write("                                    </thead>\n");
      out.write("                                    <tbody>\n");
      out.write("                                        <tr>\n");
      out.write("                                            <td style=\"padding: 5px;\">#1</td>\n");
      out.write("                                            <td style=\"padding: 5px;\">Test</td>\n");
      out.write("                                        </tr>\n");
      out.write("                                    </tbody>\n");
      out.write("                                </table>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-2\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-tags\"></i> Badges</h4>\n");
      out.write("                            <p>Badges colorés pour les statuts et rôles</p>\n");
      out.write("                            <div style=\"display: flex; flex-wrap: wrap; gap: 5px;\">\n");
      out.write("                                <span class=\"badge badge-success\"><i class=\"fas fa-check\"></i> Actif</span>\n");
      out.write("                                <span class=\"badge badge-danger\"><i class=\"fas fa-ban\"></i> Bloqué</span>\n");
      out.write("                                <span class=\"badge badge-warning\"><i class=\"fas fa-crown\"></i> Admin</span>\n");
      out.write("                                <span class=\"rank-badge rank-gold\"><i class=\"fas fa-trophy\"></i> #1</span>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Améliorations techniques -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-code\"></i> Améliorations Techniques</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-paint-brush\"></i> Design System</h4>\n");
      out.write("                            <ul>\n");
      out.write("                                <li>✅ Variables CSS centralisées</li>\n");
      out.write("                                <li>✅ Couleurs cohérentes</li>\n");
      out.write("                                <li>✅ Typographie unifiée</li>\n");
      out.write("                                <li>✅ Espacements standardisés</li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-mobile-alt\"></i> Responsive</h4>\n");
      out.write("                            <ul>\n");
      out.write("                                <li>✅ Adaptation mobile</li>\n");
      out.write("                                <li>✅ Grilles flexibles</li>\n");
      out.write("                                <li>✅ Navigation adaptative</li>\n");
      out.write("                                <li>✅ Tables scrollables</li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-rocket\"></i> Performance</h4>\n");
      out.write("                            <ul>\n");
      out.write("                                <li>✅ CSS optimisé</li>\n");
      out.write("                                <li>✅ Animations fluides</li>\n");
      out.write("                                <li>✅ Chargement rapide</li>\n");
      out.write("                                <li>✅ Code maintenable</li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-universal-access\"></i> Accessibilité</h4>\n");
      out.write("                            <ul>\n");
      out.write("                                <li>✅ Contrastes respectés</li>\n");
      out.write("                                <li>✅ Navigation clavier</li>\n");
      out.write("                                <li>✅ Icônes descriptives</li>\n");
      out.write("                                <li>✅ Structure sémantique</li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Statistiques -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-chart-pie\"></i> Statistiques des Améliorations</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <div class=\"stat-card\">\n");
      out.write("                            <div class=\"stat-icon\">\n");
      out.write("                                <i class=\"fas fa-file-code\"></i>\n");
      out.write("                            </div>\n");
      out.write("                            <div class=\"stat-content\">\n");
      out.write("                                <h3>5</h3>\n");
      out.write("                                <p>Pages Modernisées</p>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <div class=\"stat-card\">\n");
      out.write("                            <div class=\"stat-icon\">\n");
      out.write("                                <i class=\"fas fa-palette\"></i>\n");
      out.write("                            </div>\n");
      out.write("                            <div class=\"stat-content\">\n");
      out.write("                                <h3>200+</h3>\n");
      out.write("                                <p>Lignes CSS Ajoutées</p>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <div class=\"stat-card\">\n");
      out.write("                            <div class=\"stat-icon\">\n");
      out.write("                                <i class=\"fas fa-puzzle-piece\"></i>\n");
      out.write("                            </div>\n");
      out.write("                            <div class=\"stat-content\">\n");
      out.write("                                <h3>15+</h3>\n");
      out.write("                                <p>Nouveaux Composants</p>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Actions rapides -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-bolt\"></i> Tester les Améliorations</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/admin/dashboard\" class=\"btn btn-primary btn-full\">\n");
      out.write("                            <i class=\"fas fa-tachometer-alt\"></i> Dashboard Admin\n");
      out.write("                        </a>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/admin/users\" class=\"btn btn-success btn-full\">\n");
      out.write("                            <i class=\"fas fa-users\"></i> Gestion Utilisateurs\n");
      out.write("                        </a>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/admin/revenue\" class=\"btn btn-warning btn-full\">\n");
      out.write("                            <i class=\"fas fa-euro-sign\"></i> Revenus\n");
      out.write("                        </a>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/admin/popular-trajets\" class=\"btn btn-info btn-full\">\n");
      out.write("                            <i class=\"fas fa-route\"></i> Trajets Populaires\n");
      out.write("                        </a>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Message final -->\n");
      out.write("            <div class=\"text-center mt-20\">\n");
      out.write("                <div class=\"alert alert-success\">\n");
      out.write("                    <i class=\"fas fa-trophy\"></i> \n");
      out.write("                    <strong>Interface d'administration complètement modernisée !</strong>\n");
      out.write("                    <br>\n");
      out.write("                    <small>Design professionnel, cohérent et intuitif pour une meilleure expérience utilisateur.</small>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("        </div>\n");
      out.write("    </div>\n");
      out.write("</body>\n");
      out.write("</html>\n");
    } catch (java.lang.Throwable t) {
      if (!(t instanceof jakarta.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try {
            if (response.isCommitted()) {
              out.flush();
            } else {
              out.clearBuffer();
            }
          } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }
}
