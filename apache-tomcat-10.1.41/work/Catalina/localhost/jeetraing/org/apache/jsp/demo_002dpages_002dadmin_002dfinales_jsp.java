/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: Apache Tomcat/10.1.41
 * Generated at: 2025-05-29 11:34:15 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp;

import jakarta.servlet.*;
import jakarta.servlet.http.*;
import jakarta.servlet.jsp.*;

public final class demo_002dpages_002dadmin_002dfinales_jsp extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent,
                 org.apache.jasper.runtime.JspSourceImports,
                 org.apache.jasper.runtime.JspSourceDirectives {

  private static final jakarta.servlet.jsp.JspFactory _jspxFactory =
          jakarta.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  static {
    _jspx_dependants = new java.util.HashMap<java.lang.String,java.lang.Long>(2);
    _jspx_dependants.put("/WEB-INF/lib/jakarta.servlet.jsp.jstl-3.0.1.jar", Long.valueOf(1747175778000L));
    _jspx_dependants.put("jar:file:/C:/Users/<USER>/Downloads/jeetraing/apache-tomcat-10.1.41/webapps/jeetraing/WEB-INF/lib/jakarta.servlet.jsp.jstl-3.0.1.jar!/META-INF/c-1_2.tld", Long.valueOf(1664449878000L));
  }

  private static final java.util.Set<java.lang.String> _jspx_imports_packages;

  private static final java.util.Set<java.lang.String> _jspx_imports_classes;

  static {
    _jspx_imports_packages = new java.util.LinkedHashSet<>(4);
    _jspx_imports_packages.add("jakarta.servlet");
    _jspx_imports_packages.add("jakarta.servlet.http");
    _jspx_imports_packages.add("jakarta.servlet.jsp");
    _jspx_imports_classes = null;
  }

  private volatile jakarta.el.ExpressionFactory _el_expressionfactory;
  private volatile org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public java.util.Set<java.lang.String> getPackageImports() {
    return _jspx_imports_packages;
  }

  public java.util.Set<java.lang.String> getClassImports() {
    return _jspx_imports_classes;
  }

  public boolean getErrorOnELNotFound() {
    return false;
  }

  public jakarta.el.ExpressionFactory _jsp_getExpressionFactory() {
    if (_el_expressionfactory == null) {
      synchronized (this) {
        if (_el_expressionfactory == null) {
          _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
        }
      }
    }
    return _el_expressionfactory;
  }

  public org.apache.tomcat.InstanceManager _jsp_getInstanceManager() {
    if (_jsp_instancemanager == null) {
      synchronized (this) {
        if (_jsp_instancemanager == null) {
          _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
        }
      }
    }
    return _jsp_instancemanager;
  }

  public void _jspInit() {
  }

  public void _jspDestroy() {
  }

  public void _jspService(final jakarta.servlet.http.HttpServletRequest request, final jakarta.servlet.http.HttpServletResponse response)
      throws java.io.IOException, jakarta.servlet.ServletException {

    if (!jakarta.servlet.DispatcherType.ERROR.equals(request.getDispatcherType())) {
      final java.lang.String _jspx_method = request.getMethod();
      if ("OPTIONS".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        return;
      }
      if (!"GET".equals(_jspx_method) && !"POST".equals(_jspx_method) && !"HEAD".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        response.sendError(HttpServletResponse.SC_METHOD_NOT_ALLOWED, "Les JSPs ne permettent que GET, POST ou HEAD. Jasper permet aussi OPTIONS");
        return;
      }
    }

    final jakarta.servlet.jsp.PageContext pageContext;
    jakarta.servlet.http.HttpSession session = null;
    final jakarta.servlet.ServletContext application;
    final jakarta.servlet.ServletConfig config;
    jakarta.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    jakarta.servlet.jsp.JspWriter _jspx_out = null;
    jakarta.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html; charset=UTF-8");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;

      out.write("\n");
      out.write("\n");
      out.write("<!DOCTYPE html>\n");
      out.write("<html lang=\"fr\">\n");
      out.write("<head>\n");
      out.write("    <meta charset=\"UTF-8\">\n");
      out.write("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n");
      out.write("    <title>🎨 Pages Admin Finales - JEE Training</title>\n");
      out.write("    <link rel=\"stylesheet\" href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/css/style.css\">\n");
      out.write("    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\">\n");
      out.write("</head>\n");
      out.write("<body>\n");
      out.write("    <!-- Navigation -->\n");
      out.write("    <nav class=\"navbar\">\n");
      out.write("        <div class=\"container\">\n");
      out.write("            <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/recherche\" class=\"logo\">\n");
      out.write("                <i class=\"fas fa-train\"></i> JEE Training\n");
      out.write("            </a>\n");
      out.write("            <div class=\"nav-links\">\n");
      out.write("                <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/login.jsp\"><i class=\"fas fa-sign-in-alt\"></i> Connexion</a>\n");
      out.write("                <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/register.jsp\"><i class=\"fas fa-user-plus\"></i> Inscription</a>\n");
      out.write("            </div>\n");
      out.write("        </div>\n");
      out.write("    </nav>\n");
      out.write("\n");
      out.write("    <!-- Contenu principal -->\n");
      out.write("    <div class=\"container\">\n");
      out.write("        <div class=\"main-content\">\n");
      out.write("            <h1><i class=\"fas fa-crown\"></i> Pages Admin Finales Modernisées</h1>\n");
      out.write("            \n");
      out.write("            <!-- Message de succès -->\n");
      out.write("            <div class=\"alert alert-success\">\n");
      out.write("                <i class=\"fas fa-check-circle\"></i> \n");
      out.write("                <strong>Modernisation Terminée !</strong> Toutes les pages d'administration ont été complètement stylisées avec un design moderne et cohérent.\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Pages modernisées -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-palette\"></i> Pages Modernisées</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/admin/promotions\" class=\"admin-card\">\n");
      out.write("                            <div class=\"admin-card-icon\">\n");
      out.write("                                <i class=\"fas fa-tags\"></i>\n");
      out.write("                            </div>\n");
      out.write("                            <div class=\"admin-card-content\">\n");
      out.write("                                <h3>🎁 Promotions</h3>\n");
      out.write("                                <p>Formulaires élégants, badges colorés, gestion complète</p>\n");
      out.write("                            </div>\n");
      out.write("                            <div class=\"admin-card-arrow\">\n");
      out.write("                                <i class=\"fas fa-arrow-right\"></i>\n");
      out.write("                            </div>\n");
      out.write("                        </a>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/admin/trajets\" class=\"admin-card\">\n");
      out.write("                            <div class=\"admin-card-icon\">\n");
      out.write("                                <i class=\"fas fa-road\"></i>\n");
      out.write("                            </div>\n");
      out.write("                            <div class=\"admin-card-content\">\n");
      out.write("                                <h3>🛤️ Trajets</h3>\n");
      out.write("                                <p>Interface moderne, sélecteurs stylisés, badges de route</p>\n");
      out.write("                            </div>\n");
      out.write("                            <div class=\"admin-card-arrow\">\n");
      out.write("                                <i class=\"fas fa-arrow-right\"></i>\n");
      out.write("                            </div>\n");
      out.write("                        </a>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/admin/voyages\" class=\"admin-card\">\n");
      out.write("                            <div class=\"admin-card-icon\">\n");
      out.write("                                <i class=\"fas fa-train\"></i>\n");
      out.write("                            </div>\n");
      out.write("                            <div class=\"admin-card-content\">\n");
      out.write("                                <h3>🚂 Voyages</h3>\n");
      out.write("                                <p>Horaires visuels, statuts colorés, gestion avancée</p>\n");
      out.write("                            </div>\n");
      out.write("                            <div class=\"admin-card-arrow\">\n");
      out.write("                                <i class=\"fas fa-arrow-right\"></i>\n");
      out.write("                            </div>\n");
      out.write("                        </a>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/admin/dashboard\" class=\"admin-card\">\n");
      out.write("                            <div class=\"admin-card-icon\">\n");
      out.write("                                <i class=\"fas fa-tachometer-alt\"></i>\n");
      out.write("                            </div>\n");
      out.write("                            <div class=\"admin-card-content\">\n");
      out.write("                                <h3>📊 Dashboard</h3>\n");
      out.write("                                <p>Centre de contrôle moderne et intuitif</p>\n");
      out.write("                            </div>\n");
      out.write("                            <div class=\"admin-card-arrow\">\n");
      out.write("                                <i class=\"fas fa-arrow-right\"></i>\n");
      out.write("                            </div>\n");
      out.write("                        </a>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Avant/Après -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-magic\"></i> Transformation Avant/Après</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-2\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4>❌ Avant</h4>\n");
      out.write("                            <div style=\"background: #f8d7da; padding: 15px; border-radius: 5px;\">\n");
      out.write("                                <ul style=\"margin: 0; font-size: 12px;\">\n");
      out.write("                                    <li>CSS inline basique</li>\n");
      out.write("                                    <li>Tables HTML simples</li>\n");
      out.write("                                    <li>Formulaires non stylisés</li>\n");
      out.write("                                    <li>Aucune icône</li>\n");
      out.write("                                    <li>Design incohérent</li>\n");
      out.write("                                    <li>Pas de navigation</li>\n");
      out.write("                                </ul>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-2\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4>✅ Après</h4>\n");
      out.write("                            <div style=\"background: #d4edda; padding: 15px; border-radius: 5px;\">\n");
      out.write("                                <ul style=\"margin: 0; font-size: 12px;\">\n");
      out.write("                                    <li>Design system unifié</li>\n");
      out.write("                                    <li>Tables modernes stylisées</li>\n");
      out.write("                                    <li>Formulaires élégants</li>\n");
      out.write("                                    <li>Icônes FontAwesome</li>\n");
      out.write("                                    <li>Interface cohérente</li>\n");
      out.write("                                    <li>Navigation contextuelle</li>\n");
      out.write("                                </ul>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Nouveaux composants -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-puzzle-piece\"></i> Nouveaux Composants</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-tags\"></i> Badges Promotions</h4>\n");
      out.write("                            <div style=\"display: flex; flex-wrap: wrap; gap: 5px; margin-top: 10px;\">\n");
      out.write("                                <span class=\"promo-code\"><i class=\"fas fa-tag\"></i> WELCOME10</span>\n");
      out.write("                                <span class=\"discount-badge\"><i class=\"fas fa-percent\"></i> 15%</span>\n");
      out.write("                                <span class=\"points-badge\"><i class=\"fas fa-coins\"></i> 100</span>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-route\"></i> Badges Trajets</h4>\n");
      out.write("                            <div style=\"margin-top: 10px;\">\n");
      out.write("                                <span class=\"trajet-badge\">\n");
      out.write("                                    <i class=\"fas fa-arrow-right\"></i> Paris → Lyon\n");
      out.write("                                </span>\n");
      out.write("                                <div class=\"station-info\" style=\"margin-top: 10px;\">\n");
      out.write("                                    <i class=\"fas fa-train\"></i>\n");
      out.write("                                    <span>Gare de Lyon</span>\n");
      out.write("                                    <small>(Paris)</small>\n");
      out.write("                                </div>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-clock\"></i> Horaires Voyages</h4>\n");
      out.write("                            <div class=\"time-schedule\" style=\"margin-top: 10px;\">\n");
      out.write("                                <div class=\"departure\">\n");
      out.write("                                    <i class=\"fas fa-play\"></i> 08:30\n");
      out.write("                                </div>\n");
      out.write("                                <div class=\"arrival\">\n");
      out.write("                                    <i class=\"fas fa-stop\"></i> 10:45\n");
      out.write("                                </div>\n");
      out.write("                            </div>\n");
      out.write("                            <span class=\"price-badge\" style=\"margin-top: 10px;\">\n");
      out.write("                                <i class=\"fas fa-euro-sign\"></i> 45€\n");
      out.write("                            </span>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-users\"></i> Statuts Places</h4>\n");
      out.write("                            <div style=\"display: flex; flex-direction: column; gap: 5px; margin-top: 10px;\">\n");
      out.write("                                <span class=\"badge badge-success\">Disponible</span>\n");
      out.write("                                <span class=\"badge badge-warning\">Limité</span>\n");
      out.write("                                <span class=\"badge badge-danger\">Complet</span>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Fonctionnalités -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-star\"></i> Fonctionnalités Ajoutées</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-tags\"></i> Promotions</h4>\n");
      out.write("                            <ul>\n");
      out.write("                                <li>✅ Formulaires modernes avec icônes</li>\n");
      out.write("                                <li>✅ Badges colorés pour codes promo</li>\n");
      out.write("                                <li>✅ Gestion des dates visuelles</li>\n");
      out.write("                                <li>✅ Sélection de trajets stylisée</li>\n");
      out.write("                                <li>✅ Points de fidélité intégrés</li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-road\"></i> Trajets</h4>\n");
      out.write("                            <ul>\n");
      out.write("                                <li>✅ Sélecteurs de gares améliorés</li>\n");
      out.write("                                <li>✅ Badges de route colorés</li>\n");
      out.write("                                <li>✅ Informations stations détaillées</li>\n");
      out.write("                                <li>✅ Actions stylisées avec confirmations</li>\n");
      out.write("                                <li>✅ Navigation contextuelle</li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-train\"></i> Voyages</h4>\n");
      out.write("                            <ul>\n");
      out.write("                                <li>✅ Horaires visuels avec icônes</li>\n");
      out.write("                                <li>✅ Statuts de places colorés</li>\n");
      out.write("                                <li>✅ Prix mis en évidence</li>\n");
      out.write("                                <li>✅ Informations de route complètes</li>\n");
      out.write("                                <li>✅ Gestion avancée des places</li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-shield-alt\"></i> Sécurité</h4>\n");
      out.write("                            <ul>\n");
      out.write("                                <li>✅ Vérification des permissions</li>\n");
      out.write("                                <li>✅ Redirection automatique</li>\n");
      out.write("                                <li>✅ Sessions protégées</li>\n");
      out.write("                                <li>✅ Confirmations de suppression</li>\n");
      out.write("                                <li>✅ Messages d'erreur stylisés</li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Statistiques -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-chart-pie\"></i> Statistiques de Modernisation</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <div class=\"stat-card\">\n");
      out.write("                            <div class=\"stat-icon\">\n");
      out.write("                                <i class=\"fas fa-file-code\"></i>\n");
      out.write("                            </div>\n");
      out.write("                            <div class=\"stat-content\">\n");
      out.write("                                <h3>8</h3>\n");
      out.write("                                <p>Pages Modernisées</p>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <div class=\"stat-card\">\n");
      out.write("                            <div class=\"stat-icon\">\n");
      out.write("                                <i class=\"fas fa-palette\"></i>\n");
      out.write("                            </div>\n");
      out.write("                            <div class=\"stat-content\">\n");
      out.write("                                <h3>500+</h3>\n");
      out.write("                                <p>Lignes CSS Ajoutées</p>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <div class=\"stat-card\">\n");
      out.write("                            <div class=\"stat-icon\">\n");
      out.write("                                <i class=\"fas fa-puzzle-piece\"></i>\n");
      out.write("                            </div>\n");
      out.write("                            <div class=\"stat-content\">\n");
      out.write("                                <h3>25+</h3>\n");
      out.write("                                <p>Nouveaux Composants</p>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <div class=\"stat-card\">\n");
      out.write("                            <div class=\"stat-icon\">\n");
      out.write("                                <i class=\"fas fa-rocket\"></i>\n");
      out.write("                            </div>\n");
      out.write("                            <div class=\"stat-content\">\n");
      out.write("                                <h3>100%</h3>\n");
      out.write("                                <p>Interface Cohérente</p>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- URLs de test -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-link\"></i> URLs pour Tester</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-tags\"></i> Promotions</h4>\n");
      out.write("                            <p><code>/admin/promotions</code></p>\n");
      out.write("                            <p>Gestion complète des promotions avec formulaires modernes</p>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-road\"></i> Trajets</h4>\n");
      out.write("                            <p><code>/admin/trajets</code></p>\n");
      out.write("                            <p>Interface moderne pour la gestion des trajets</p>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-train\"></i> Voyages</h4>\n");
      out.write("                            <p><code>/admin/voyages</code></p>\n");
      out.write("                            <p>Gestion avancée des voyages avec statuts visuels</p>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-tachometer-alt\"></i> Dashboard</h4>\n");
      out.write("                            <p><code>/admin/dashboard</code></p>\n");
      out.write("                            <p>Centre de contrôle avec accès à toutes les fonctions</p>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Actions de test -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-flask\"></i> Tester les Pages</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/admin/promotions\" class=\"btn btn-primary btn-full\">\n");
      out.write("                            <i class=\"fas fa-tags\"></i> Tester Promotions\n");
      out.write("                        </a>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/admin/trajets\" class=\"btn btn-warning btn-full\">\n");
      out.write("                            <i class=\"fas fa-road\"></i> Tester Trajets\n");
      out.write("                        </a>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/admin/voyages\" class=\"btn btn-success btn-full\">\n");
      out.write("                            <i class=\"fas fa-train\"></i> Tester Voyages\n");
      out.write("                        </a>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/admin/dashboard\" class=\"btn btn-info btn-full\">\n");
      out.write("                            <i class=\"fas fa-tachometer-alt\"></i> Dashboard Admin\n");
      out.write("                        </a>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Message final -->\n");
      out.write("            <div class=\"text-center mt-20\">\n");
      out.write("                <div class=\"alert alert-success\">\n");
      out.write("                    <i class=\"fas fa-trophy\"></i> \n");
      out.write("                    <strong>Modernisation Complète Terminée !</strong>\n");
      out.write("                    <br>\n");
      out.write("                    <small>Toutes les pages d'administration disposent maintenant d'un design moderne, cohérent et professionnel.</small>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("        </div>\n");
      out.write("    </div>\n");
      out.write("</body>\n");
      out.write("</html>\n");
    } catch (java.lang.Throwable t) {
      if (!(t instanceof jakarta.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try {
            if (response.isCommitted()) {
              out.flush();
            } else {
              out.clearBuffer();
            }
          } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }
}
