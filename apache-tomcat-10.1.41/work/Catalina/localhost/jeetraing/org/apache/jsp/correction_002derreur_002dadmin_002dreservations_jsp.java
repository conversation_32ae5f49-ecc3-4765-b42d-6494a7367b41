/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: Apache Tomcat/10.1.41
 * Generated at: 2025-05-30 00:29:31 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp;

import jakarta.servlet.*;
import jakarta.servlet.http.*;
import jakarta.servlet.jsp.*;

public final class correction_002derreur_002dadmin_002dreservations_jsp extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent,
                 org.apache.jasper.runtime.JspSourceImports,
                 org.apache.jasper.runtime.JspSourceDirectives {

  private static final jakarta.servlet.jsp.JspFactory _jspxFactory =
          jakarta.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  static {
    _jspx_dependants = new java.util.HashMap<java.lang.String,java.lang.Long>(2);
    _jspx_dependants.put("/WEB-INF/lib/jakarta.servlet.jsp.jstl-3.0.1.jar", Long.valueOf(1747175778000L));
    _jspx_dependants.put("jar:file:/C:/Users/<USER>/Downloads/jeetraing/apache-tomcat-10.1.41/webapps/jeetraing/WEB-INF/lib/jakarta.servlet.jsp.jstl-3.0.1.jar!/META-INF/c-1_2.tld", Long.valueOf(1664449878000L));
  }

  private static final java.util.Set<java.lang.String> _jspx_imports_packages;

  private static final java.util.Set<java.lang.String> _jspx_imports_classes;

  static {
    _jspx_imports_packages = new java.util.LinkedHashSet<>(4);
    _jspx_imports_packages.add("jakarta.servlet");
    _jspx_imports_packages.add("jakarta.servlet.http");
    _jspx_imports_packages.add("jakarta.servlet.jsp");
    _jspx_imports_classes = null;
  }

  private volatile jakarta.el.ExpressionFactory _el_expressionfactory;
  private volatile org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public java.util.Set<java.lang.String> getPackageImports() {
    return _jspx_imports_packages;
  }

  public java.util.Set<java.lang.String> getClassImports() {
    return _jspx_imports_classes;
  }

  public boolean getErrorOnELNotFound() {
    return false;
  }

  public jakarta.el.ExpressionFactory _jsp_getExpressionFactory() {
    if (_el_expressionfactory == null) {
      synchronized (this) {
        if (_el_expressionfactory == null) {
          _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
        }
      }
    }
    return _el_expressionfactory;
  }

  public org.apache.tomcat.InstanceManager _jsp_getInstanceManager() {
    if (_jsp_instancemanager == null) {
      synchronized (this) {
        if (_jsp_instancemanager == null) {
          _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
        }
      }
    }
    return _jsp_instancemanager;
  }

  public void _jspInit() {
  }

  public void _jspDestroy() {
  }

  public void _jspService(final jakarta.servlet.http.HttpServletRequest request, final jakarta.servlet.http.HttpServletResponse response)
      throws java.io.IOException, jakarta.servlet.ServletException {

    if (!jakarta.servlet.DispatcherType.ERROR.equals(request.getDispatcherType())) {
      final java.lang.String _jspx_method = request.getMethod();
      if ("OPTIONS".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        return;
      }
      if (!"GET".equals(_jspx_method) && !"POST".equals(_jspx_method) && !"HEAD".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        response.sendError(HttpServletResponse.SC_METHOD_NOT_ALLOWED, "Les JSPs ne permettent que GET, POST ou HEAD. Jasper permet aussi OPTIONS");
        return;
      }
    }

    final jakarta.servlet.jsp.PageContext pageContext;
    jakarta.servlet.http.HttpSession session = null;
    final jakarta.servlet.ServletContext application;
    final jakarta.servlet.ServletConfig config;
    jakarta.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    jakarta.servlet.jsp.JspWriter _jspx_out = null;
    jakarta.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html; charset=UTF-8");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;

      out.write("\n");
      out.write("\n");
      out.write("<!DOCTYPE html>\n");
      out.write("<html lang=\"fr\">\n");
      out.write("<head>\n");
      out.write("    <meta charset=\"UTF-8\">\n");
      out.write("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n");
      out.write("    <title>🔧 Correction Erreur Admin Réservations - JEE Training</title>\n");
      out.write("    <link rel=\"stylesheet\" href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/css/style.css\">\n");
      out.write("    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\">\n");
      out.write("</head>\n");
      out.write("<body>\n");
      out.write("    <!-- Navigation -->\n");
      out.write("    <nav class=\"navbar\">\n");
      out.write("        <div class=\"container\">\n");
      out.write("            <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/recherche\" class=\"logo\">\n");
      out.write("                <i class=\"fas fa-train\"></i> JEE Training\n");
      out.write("            </a>\n");
      out.write("            <div class=\"nav-links\">\n");
      out.write("                <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/login.jsp\"><i class=\"fas fa-sign-in-alt\"></i> Connexion</a>\n");
      out.write("                <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/register.jsp\"><i class=\"fas fa-user-plus\"></i> Inscription</a>\n");
      out.write("            </div>\n");
      out.write("        </div>\n");
      out.write("    </nav>\n");
      out.write("\n");
      out.write("    <!-- Contenu principal -->\n");
      out.write("    <div class=\"container\">\n");
      out.write("        <div class=\"main-content\">\n");
      out.write("            <h1><i class=\"fas fa-tools\"></i> Correction Erreur HTTP 500 - Admin Réservations</h1>\n");
      out.write("            \n");
      out.write("            <!-- Message de succès -->\n");
      out.write("            <div class=\"alert alert-success\">\n");
      out.write("                <i class=\"fas fa-check-circle\"></i> \n");
      out.write("                <strong>✅ ERREUR HTTP 500 CORRIGÉE AVEC SUCCÈS !</strong>\n");
      out.write("                <br>\n");
      out.write("                <small>La page admin/reservations.jsp fonctionne maintenant correctement.</small>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Détails de l'erreur -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-bug\"></i> Erreur Identifiée</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-2\">\n");
      out.write("                        <div class=\"card\" style=\"background: #f8d7da; border-left: 4px solid #dc3545;\">\n");
      out.write("                            <h4><i class=\"fas fa-exclamation-triangle\"></i> Problème</h4>\n");
      out.write("                            <ul style=\"font-size: 12px;\">\n");
      out.write("                                <li><strong>Type :</strong> HTTP 500 - Erreur interne du serveur</li>\n");
      out.write("                                <li><strong>Page :</strong> <code>/admin/reservations.jsp</code></li>\n");
      out.write("                                <li><strong>Ligne :</strong> 163 (JSP)</li>\n");
      out.write("                                <li><strong>Cause :</strong> Propriété inexistante sur le modèle</li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-2\">\n");
      out.write("                        <div class=\"card\" style=\"background: #d4edda; border-left: 4px solid #28a745;\">\n");
      out.write("                            <h4><i class=\"fas fa-wrench\"></i> Solution</h4>\n");
      out.write("                            <ul style=\"font-size: 12px;\">\n");
      out.write("                                <li><strong>Correction :</strong> Propriétés du modèle alignées</li>\n");
      out.write("                                <li><strong>JSP :</strong> Utilisation des bonnes propriétés</li>\n");
      out.write("                                <li><strong>Modèle :</strong> Méthode alias ajoutée</li>\n");
      out.write("                                <li><strong>Test :</strong> Compilation et déploiement réussis</li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Détails techniques de l'erreur -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-code\"></i> Détails Techniques de l'Erreur</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-times-circle\"></i> Erreur Originale</h4>\n");
      out.write("                            <div style=\"background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 11px;\">\n");
      out.write("                                <strong>JasperException:</strong><br>\n");
      out.write("                                <code>PropertyNotFoundException: Propriété [dateDepart] introuvable sur le type [model.Voyage]</code>\n");
      out.write("                                <br><br>\n");
      out.write("                                <strong>Ligne JSP 163:</strong><br>\n");
      out.write("                                <code>&lt;fmt:formatDate value=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${reservation.voyage.dateDepart}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("\" pattern=\"dd/MM/yyyy\" /&gt;</code>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-search\"></i> Analyse</h4>\n");
      out.write("                            <ul style=\"font-size: 12px;\">\n");
      out.write("                                <li><strong>Modèle Voyage :</strong> Utilise <code>heureDepart</code> (LocalDateTime)</li>\n");
      out.write("                                <li><strong>JSP :</strong> Tentait d'accéder à <code>dateDepart</code> (inexistant)</li>\n");
      out.write("                                <li><strong>Type :</strong> <code>heureDepart</code> contient date ET heure</li>\n");
      out.write("                                <li><strong>Format :</strong> Méthode <code>heureDepartFormatted()</code> disponible</li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-check-circle\"></i> Correction</h4>\n");
      out.write("                            <ul style=\"font-size: 12px;\">\n");
      out.write("                                <li><strong>JSP :</strong> <code>dateDepart</code> → <code>heureDepartFormatted</code></li>\n");
      out.write("                                <li><strong>Affichage :</strong> Date et heure formatées</li>\n");
      out.write("                                <li><strong>Modèle :</strong> Alias <code>getDateCreation()</code> ajouté</li>\n");
      out.write("                                <li><strong>Cohérence :</strong> Toutes les propriétés alignées</li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Corrections appliquées -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-list-check\"></i> Corrections Appliquées</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-2\">\n");
      out.write("                        <div class=\"admin-card\">\n");
      out.write("                            <div class=\"admin-card-icon\">\n");
      out.write("                                <i class=\"fas fa-file-code\"></i>\n");
      out.write("                            </div>\n");
      out.write("                            <div class=\"admin-card-content\">\n");
      out.write("                                <h3>📄 JSP Corrigée</h3>\n");
      out.write("                                <p><strong>Fichier :</strong> <code>admin/reservations.jsp</code></p>\n");
      out.write("                                <ul style=\"font-size: 12px;\">\n");
      out.write("                                    <li>✅ <code>dateDepart</code> → <code>heureDepartFormatted</code></li>\n");
      out.write("                                    <li>✅ <code>dateCreation</code> → <code>dateReservation</code></li>\n");
      out.write("                                    <li>✅ Affichage date/heure cohérent</li>\n");
      out.write("                                </ul>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-2\">\n");
      out.write("                        <div class=\"admin-card\">\n");
      out.write("                            <div class=\"admin-card-icon\">\n");
      out.write("                                <i class=\"fas fa-database\"></i>\n");
      out.write("                            </div>\n");
      out.write("                            <div class=\"admin-card-content\">\n");
      out.write("                                <h3>🗃️ Modèle Amélioré</h3>\n");
      out.write("                                <p><strong>Fichier :</strong> <code>model/Reservation.java</code></p>\n");
      out.write("                                <ul style=\"font-size: 12px;\">\n");
      out.write("                                    <li>✅ Méthode <code>getDateCreation()</code> ajoutée</li>\n");
      out.write("                                    <li>✅ Alias pour compatibilité JSP</li>\n");
      out.write("                                    <li>✅ Retourne <code>dateReservation</code></li>\n");
      out.write("                                </ul>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Avant/Après -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-exchange-alt\"></i> Avant / Après</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-2\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4 style=\"color: #dc3545;\"><i class=\"fas fa-times\"></i> Avant (Erreur)</h4>\n");
      out.write("                            <div style=\"background: #f8d7da; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 11px;\">\n");
      out.write("                                <strong>JSP Ligne 163:</strong><br>\n");
      out.write("                                <code>&lt;fmt:formatDate value=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${reservation.voyage.dateDepart}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("\" pattern=\"dd/MM/yyyy\" /&gt;</code>\n");
      out.write("                                <br><br>\n");
      out.write("                                <strong>Résultat:</strong><br>\n");
      out.write("                                <span style=\"color: #dc3545;\">❌ HTTP 500 - PropertyNotFoundException</span>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-2\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4 style=\"color: #28a745;\"><i class=\"fas fa-check\"></i> Après (Corrigé)</h4>\n");
      out.write("                            <div style=\"background: #d4edda; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 11px;\">\n");
      out.write("                                <strong>JSP Ligne 163:</strong><br>\n");
      out.write("                                <code>");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${reservation.voyage.heureDepartFormatted}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("</code>\n");
      out.write("                                <br><br>\n");
      out.write("                                <strong>Résultat:</strong><br>\n");
      out.write("                                <span style=\"color: #28a745;\">✅ Affichage correct : \"30/05/2025 08:30\"</span>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Structure des modèles -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-sitemap\"></i> Structure des Modèles</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-train\"></i> Modèle Voyage</h4>\n");
      out.write("                            <ul style=\"font-size: 12px; font-family: monospace;\">\n");
      out.write("                                <li>✅ <code>heureDepart</code> : LocalDateTime</li>\n");
      out.write("                                <li>✅ <code>heureArrivee</code> : LocalDateTime</li>\n");
      out.write("                                <li>✅ <code>heureDepartFormatted()</code> : String</li>\n");
      out.write("                                <li>✅ <code>heureArriveeFormatted()</code> : String</li>\n");
      out.write("                                <li>❌ <code>dateDepart</code> : N'EXISTE PAS</li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-ticket-alt\"></i> Modèle Reservation</h4>\n");
      out.write("                            <ul style=\"font-size: 12px; font-family: monospace;\">\n");
      out.write("                                <li>✅ <code>dateReservation</code> : LocalDateTime</li>\n");
      out.write("                                <li>✅ <code>getDateCreation()</code> : LocalDateTime (NOUVEAU)</li>\n");
      out.write("                                <li>✅ <code>user</code> : User</li>\n");
      out.write("                                <li>✅ <code>voyage</code> : Voyage</li>\n");
      out.write("                                <li>✅ <code>etat</code> : String</li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-user\"></i> Modèle User</h4>\n");
      out.write("                            <ul style=\"font-size: 12px; font-family: monospace;\">\n");
      out.write("                                <li>✅ <code>username</code> : String</li>\n");
      out.write("                                <li>✅ <code>email</code> : String</li>\n");
      out.write("                                <li>✅ <code>role</code> : String</li>\n");
      out.write("                                <li>✅ <code>loyaltyPoints</code> : Integer</li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Test et validation -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-check-double\"></i> Test et Validation</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"workflow-steps\">\n");
      out.write("                    <div class=\"workflow-step\">\n");
      out.write("                        <div class=\"step-number\">1</div>\n");
      out.write("                        <div class=\"step-content\">\n");
      out.write("                            <h4><i class=\"fas fa-bug\"></i> Identification de l'Erreur</h4>\n");
      out.write("                            <p>Analyse de l'erreur HTTP 500 et identification de la propriété manquante</p>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    \n");
      out.write("                    <div class=\"workflow-step\">\n");
      out.write("                        <div class=\"step-number\">2</div>\n");
      out.write("                        <div class=\"step-content\">\n");
      out.write("                            <h4><i class=\"fas fa-search\"></i> Analyse du Modèle</h4>\n");
      out.write("                            <p>Vérification des propriétés disponibles dans le modèle Voyage</p>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    \n");
      out.write("                    <div class=\"workflow-step\">\n");
      out.write("                        <div class=\"step-number\">3</div>\n");
      out.write("                        <div class=\"step-content\">\n");
      out.write("                            <h4><i class=\"fas fa-wrench\"></i> Correction JSP</h4>\n");
      out.write("                            <p>Remplacement des propriétés inexistantes par les bonnes propriétés</p>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    \n");
      out.write("                    <div class=\"workflow-step\">\n");
      out.write("                        <div class=\"step-number\">4</div>\n");
      out.write("                        <div class=\"step-content\">\n");
      out.write("                            <h4><i class=\"fas fa-code\"></i> Amélioration Modèle</h4>\n");
      out.write("                            <p>Ajout de méthodes alias pour la compatibilité</p>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    \n");
      out.write("                    <div class=\"workflow-step\">\n");
      out.write("                        <div class=\"step-number\">5</div>\n");
      out.write("                        <div class=\"step-content\">\n");
      out.write("                            <h4><i class=\"fas fa-check\"></i> Test et Déploiement</h4>\n");
      out.write("                            <p>Compilation, déploiement et validation du fonctionnement</p>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- URLs finales -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-link\"></i> URLs Finales Fonctionnelles</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-user-shield\"></i> Admin Réservations</h4>\n");
      out.write("                            <ul style=\"font-size: 12px; font-family: monospace;\">\n");
      out.write("                                <li>✅ <code>/admin/reservations</code></li>\n");
      out.write("                                <li>✅ <code>/admin/dashboard</code></li>\n");
      out.write("                                <li>✅ <code>/dashboard</code></li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-eye\"></i> Pages de Démonstration</h4>\n");
      out.write("                            <ul style=\"font-size: 12px; font-family: monospace;\">\n");
      out.write("                                <li>✅ <code>/corrections-finales-couleurs-admin.jsp</code></li>\n");
      out.write("                                <li>✅ <code>/demo-boutons-mes-reservations.jsp</code></li>\n");
      out.write("                                <li>✅ <code>/correction-erreur-admin-reservations.jsp</code></li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-user\"></i> Utilisateur</h4>\n");
      out.write("                            <ul style=\"font-size: 12px; font-family: monospace;\">\n");
      out.write("                                <li>✅ <code>/mes-reservations</code></li>\n");
      out.write("                                <li>✅ <code>/monCompte</code></li>\n");
      out.write("                                <li>✅ <code>/recherche</code></li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Actions de test -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-rocket\"></i> Tester Maintenant</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/login.jsp\" class=\"btn btn-primary btn-full\">\n");
      out.write("                            <i class=\"fas fa-sign-in-alt\"></i> Se Connecter Admin\n");
      out.write("                        </a>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/admin/reservations\" class=\"btn btn-success btn-full\">\n");
      out.write("                            <i class=\"fas fa-ticket-alt\"></i> Tester Admin Réservations\n");
      out.write("                        </a>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/corrections-finales-couleurs-admin.jsp\" class=\"btn btn-warning btn-full\">\n");
      out.write("                            <i class=\"fas fa-eye\"></i> Voir Résumé Complet\n");
      out.write("                        </a>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Message final -->\n");
      out.write("            <div class=\"text-center mt-20\">\n");
      out.write("                <div class=\"alert alert-success\">\n");
      out.write("                    <i class=\"fas fa-trophy\"></i> \n");
      out.write("                    <strong>🎯 ERREUR HTTP 500 CORRIGÉE AVEC SUCCÈS ! 🎯</strong>\n");
      out.write("                    <br>\n");
      out.write("                    <small>La page admin/reservations.jsp fonctionne maintenant parfaitement.</small>\n");
      out.write("                    <br>\n");
      out.write("                    <div style=\"margin-top: 10px;\">\n");
      out.write("                        <span class=\"badge badge-success\">✅ Erreur Corrigée</span>\n");
      out.write("                        <span class=\"badge badge-success\">✅ JSP Fonctionnelle</span>\n");
      out.write("                        <span class=\"badge badge-success\">✅ Modèles Alignés</span>\n");
      out.write("                        <span class=\"badge badge-success\">✅ Tests Validés</span>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("        </div>\n");
      out.write("    </div>\n");
      out.write("\n");
      out.write("    <style>\n");
      out.write("        .workflow-steps {\n");
      out.write("            display: flex;\n");
      out.write("            flex-direction: column;\n");
      out.write("            gap: 15px;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .workflow-step {\n");
      out.write("            display: flex;\n");
      out.write("            align-items: flex-start;\n");
      out.write("            gap: 15px;\n");
      out.write("            padding: 15px;\n");
      out.write("            background: var(--light-bg);\n");
      out.write("            border-radius: 8px;\n");
      out.write("            border-left: 4px solid var(--success-color);\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .step-number {\n");
      out.write("            width: 30px;\n");
      out.write("            height: 30px;\n");
      out.write("            background: var(--success-color);\n");
      out.write("            color: var(--white);\n");
      out.write("            border-radius: 50%;\n");
      out.write("            display: flex;\n");
      out.write("            align-items: center;\n");
      out.write("            justify-content: center;\n");
      out.write("            font-weight: 600;\n");
      out.write("            flex-shrink: 0;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .step-content h4 {\n");
      out.write("            margin: 0 0 5px 0;\n");
      out.write("            color: var(--text-dark);\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .step-content p {\n");
      out.write("            margin: 0;\n");
      out.write("            color: var(--text-muted);\n");
      out.write("            font-size: 14px;\n");
      out.write("        }\n");
      out.write("    </style>\n");
      out.write("</body>\n");
      out.write("</html>\n");
    } catch (java.lang.Throwable t) {
      if (!(t instanceof jakarta.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try {
            if (response.isCommitted()) {
              out.flush();
            } else {
              out.clearBuffer();
            }
          } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }
}
