/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: Apache Tomcat/10.1.41
 * Generated at: 2025-05-30 13:58:55 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp;

import jakarta.servlet.*;
import jakarta.servlet.http.*;
import jakarta.servlet.jsp.*;

public final class correction_002derreur_002dmes_002dreservations_jsp extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent,
                 org.apache.jasper.runtime.JspSourceImports,
                 org.apache.jasper.runtime.JspSourceDirectives {

  private static final jakarta.servlet.jsp.JspFactory _jspxFactory =
          jakarta.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  static {
    _jspx_dependants = new java.util.HashMap<java.lang.String,java.lang.Long>(2);
    _jspx_dependants.put("/WEB-INF/lib/jakarta.servlet.jsp.jstl-3.0.1.jar", Long.valueOf(1747175778000L));
    _jspx_dependants.put("jar:file:/C:/Users/<USER>/Downloads/jeetraing/apache-tomcat-10.1.41/webapps/jeetraing/WEB-INF/lib/jakarta.servlet.jsp.jstl-3.0.1.jar!/META-INF/c-1_2.tld", Long.valueOf(1664449878000L));
  }

  private static final java.util.Set<java.lang.String> _jspx_imports_packages;

  private static final java.util.Set<java.lang.String> _jspx_imports_classes;

  static {
    _jspx_imports_packages = new java.util.LinkedHashSet<>(4);
    _jspx_imports_packages.add("jakarta.servlet");
    _jspx_imports_packages.add("jakarta.servlet.http");
    _jspx_imports_packages.add("jakarta.servlet.jsp");
    _jspx_imports_classes = null;
  }

  private volatile jakarta.el.ExpressionFactory _el_expressionfactory;
  private volatile org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public java.util.Set<java.lang.String> getPackageImports() {
    return _jspx_imports_packages;
  }

  public java.util.Set<java.lang.String> getClassImports() {
    return _jspx_imports_classes;
  }

  public boolean getErrorOnELNotFound() {
    return false;
  }

  public jakarta.el.ExpressionFactory _jsp_getExpressionFactory() {
    if (_el_expressionfactory == null) {
      synchronized (this) {
        if (_el_expressionfactory == null) {
          _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
        }
      }
    }
    return _el_expressionfactory;
  }

  public org.apache.tomcat.InstanceManager _jsp_getInstanceManager() {
    if (_jsp_instancemanager == null) {
      synchronized (this) {
        if (_jsp_instancemanager == null) {
          _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
        }
      }
    }
    return _jsp_instancemanager;
  }

  public void _jspInit() {
  }

  public void _jspDestroy() {
  }

  public void _jspService(final jakarta.servlet.http.HttpServletRequest request, final jakarta.servlet.http.HttpServletResponse response)
      throws java.io.IOException, jakarta.servlet.ServletException {

    if (!jakarta.servlet.DispatcherType.ERROR.equals(request.getDispatcherType())) {
      final java.lang.String _jspx_method = request.getMethod();
      if ("OPTIONS".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        return;
      }
      if (!"GET".equals(_jspx_method) && !"POST".equals(_jspx_method) && !"HEAD".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        response.sendError(HttpServletResponse.SC_METHOD_NOT_ALLOWED, "Les JSPs ne permettent que GET, POST ou HEAD. Jasper permet aussi OPTIONS");
        return;
      }
    }

    final jakarta.servlet.jsp.PageContext pageContext;
    jakarta.servlet.http.HttpSession session = null;
    final jakarta.servlet.ServletContext application;
    final jakarta.servlet.ServletConfig config;
    jakarta.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    jakarta.servlet.jsp.JspWriter _jspx_out = null;
    jakarta.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html; charset=UTF-8");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;

      out.write("\n");
      out.write("\n");
      out.write("<!DOCTYPE html>\n");
      out.write("<html lang=\"fr\">\n");
      out.write("<head>\n");
      out.write("    <meta charset=\"UTF-8\">\n");
      out.write("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n");
      out.write("    <title>🔧 Correction Erreur HTTP 500 - Mes Réservations - JEE Training</title>\n");
      out.write("    <link rel=\"stylesheet\" href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/css/style.css\">\n");
      out.write("    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\">\n");
      out.write("</head>\n");
      out.write("<body>\n");
      out.write("    <!-- Navigation -->\n");
      out.write("    <nav class=\"navbar\">\n");
      out.write("        <div class=\"container\">\n");
      out.write("            <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/recherche\" class=\"logo\">\n");
      out.write("                <i class=\"fas fa-train\"></i> JEE Training\n");
      out.write("            </a>\n");
      out.write("            <div class=\"nav-links\">\n");
      out.write("                <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/login.jsp\"><i class=\"fas fa-sign-in-alt\"></i> Connexion</a>\n");
      out.write("                <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/register.jsp\"><i class=\"fas fa-user-plus\"></i> Inscription</a>\n");
      out.write("            </div>\n");
      out.write("        </div>\n");
      out.write("    </nav>\n");
      out.write("\n");
      out.write("    <!-- Contenu principal -->\n");
      out.write("    <div class=\"container\">\n");
      out.write("        <div class=\"main-content\">\n");
      out.write("            <h1><i class=\"fas fa-tools\"></i> Correction Erreur HTTP 500 - Mes Réservations</h1>\n");
      out.write("            \n");
      out.write("            <!-- Message de succès -->\n");
      out.write("            <div class=\"alert alert-success\">\n");
      out.write("                <i class=\"fas fa-check-circle\"></i> \n");
      out.write("                <strong>✅ ERREUR HTTP 500 CORRIGÉE AVEC SUCCÈS !</strong>\n");
      out.write("                <br>\n");
      out.write("                <small>La page /mes-reservations fonctionne maintenant correctement.</small>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Détails de l'erreur -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-bug\"></i> Erreur Identifiée et Corrigée</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-2\">\n");
      out.write("                        <div class=\"card\" style=\"background: #f8d7da; border-left: 4px solid #dc3545;\">\n");
      out.write("                            <h4><i class=\"fas fa-exclamation-triangle\"></i> Problème</h4>\n");
      out.write("                            <ul style=\"font-size: 12px;\">\n");
      out.write("                                <li><strong>Type :</strong> HTTP 500 - Erreur interne du serveur</li>\n");
      out.write("                                <li><strong>Page :</strong> <code>/mes-reservations</code></li>\n");
      out.write("                                <li><strong>Erreur :</strong> ClassNotFoundException</li>\n");
      out.write("                                <li><strong>Cause :</strong> Erreur de syntaxe JSP</li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-2\">\n");
      out.write("                        <div class=\"card\" style=\"background: #d4edda; border-left: 4px solid #28a745;\">\n");
      out.write("                            <h4><i class=\"fas fa-wrench\"></i> Solution</h4>\n");
      out.write("                            <ul style=\"font-size: 12px;\">\n");
      out.write("                                <li><strong>Correction :</strong> Apostrophes échappées</li>\n");
      out.write("                                <li><strong>Cache :</strong> Tomcat nettoyé</li>\n");
      out.write("                                <li><strong>Build :</strong> Clean + recompilation</li>\n");
      out.write("                                <li><strong>Test :</strong> Page fonctionnelle</li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Détails techniques de l'erreur -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-code\"></i> Détails Techniques de l'Erreur</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-times-circle\"></i> Erreur Originale</h4>\n");
      out.write("                            <div style=\"background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 11px;\">\n");
      out.write("                                <strong>Exception:</strong><br>\n");
      out.write("                                <code>ClassNotFoundException: org.apache.jsp.mes_002dreservations_jsp</code>\n");
      out.write("                                <br><br>\n");
      out.write("                                <strong>Cause racine:</strong><br>\n");
      out.write("                                <code>JasperException</code> - Erreur de compilation JSP\n");
      out.write("                                <br><br>\n");
      out.write("                                <strong>Ligne problématique:</strong><br>\n");
      out.write("                                <code>'en attente d\\'annulation'</code>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-search\"></i> Analyse</h4>\n");
      out.write("                            <ul style=\"font-size: 12px;\">\n");
      out.write("                                <li><strong>Problème :</strong> Apostrophe mal échappée</li>\n");
      out.write("                                <li><strong>Expression :</strong> <code>'en attente d\\'annulation'</code></li>\n");
      out.write("                                <li><strong>Conflit :</strong> Apostrophe dans \"d'annulation\"</li>\n");
      out.write("                                <li><strong>Résultat :</strong> Erreur de syntaxe JSTL</li>\n");
      out.write("                                <li><strong>Impact :</strong> JSP non compilable</li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-check-circle\"></i> Correction</h4>\n");
      out.write("                            <ul style=\"font-size: 12px;\">\n");
      out.write("                                <li><strong>Ancien :</strong> <code>'en attente d\\'annulation'</code></li>\n");
      out.write("                                <li><strong>Nouveau :</strong> <code>'en attente d&apos;annulation'</code></li>\n");
      out.write("                                <li><strong>Méthode :</strong> Entité HTML <code>&apos;</code></li>\n");
      out.write("                                <li><strong>Résultat :</strong> Syntaxe JSTL valide</li>\n");
      out.write("                                <li><strong>Test :</strong> Compilation réussie</li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Avant/Après -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-exchange-alt\"></i> Avant / Après</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-2\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4 style=\"color: #dc3545;\"><i class=\"fas fa-times\"></i> Avant (Erreur)</h4>\n");
      out.write("                            <div style=\"background: #f8d7da; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 11px;\">\n");
      out.write("                                <strong>Code JSP problématique:</strong><br>\n");
      out.write("                                <code>&lt;c:when test=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${reservation.etat == 'en attente d\\'annulation'}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("\"&gt;</code>\n");
      out.write("                                <br><br>\n");
      out.write("                                <strong>Résultat:</strong><br>\n");
      out.write("                                <span style=\"color: #dc3545;\">❌ HTTP 500 - ClassNotFoundException</span>\n");
      out.write("                                <br>\n");
      out.write("                                <span style=\"color: #dc3545;\">❌ JSP non compilable</span>\n");
      out.write("                                <br>\n");
      out.write("                                <span style=\"color: #dc3545;\">❌ Page inaccessible</span>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-2\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4 style=\"color: #28a745;\"><i class=\"fas fa-check\"></i> Après (Corrigé)</h4>\n");
      out.write("                            <div style=\"background: #d4edda; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 11px;\">\n");
      out.write("                                <strong>Code JSP corrigé:</strong><br>\n");
      out.write("                                <code>&lt;c:when test=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${reservation.etat == 'en attente d&amp;apos;annulation'}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("\"&gt;</code>\n");
      out.write("                                <br><br>\n");
      out.write("                                <strong>Résultat:</strong><br>\n");
      out.write("                                <span style=\"color: #28a745;\">✅ HTTP 200 - Page accessible</span>\n");
      out.write("                                <br>\n");
      out.write("                                <span style=\"color: #28a745;\">✅ JSP compilée avec succès</span>\n");
      out.write("                                <br>\n");
      out.write("                                <span style=\"color: #28a745;\">✅ Fonctionnalité opérationnelle</span>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Étapes de correction -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-list-check\"></i> Étapes de Correction Appliquées</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"workflow-steps\">\n");
      out.write("                    <div class=\"workflow-step\">\n");
      out.write("                        <div class=\"step-number\">1</div>\n");
      out.write("                        <div class=\"step-content\">\n");
      out.write("                            <h4><i class=\"fas fa-bug\"></i> Identification de l'Erreur</h4>\n");
      out.write("                            <p>Analyse de l'erreur ClassNotFoundException et identification de la cause racine dans la JSP</p>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    \n");
      out.write("                    <div class=\"workflow-step\">\n");
      out.write("                        <div class=\"step-number\">2</div>\n");
      out.write("                        <div class=\"step-content\">\n");
      out.write("                            <h4><i class=\"fas fa-search\"></i> Localisation du Problème</h4>\n");
      out.write("                            <p>Recherche de l'apostrophe mal échappée dans les expressions JSTL</p>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    \n");
      out.write("                    <div class=\"workflow-step\">\n");
      out.write("                        <div class=\"step-number\">3</div>\n");
      out.write("                        <div class=\"step-content\">\n");
      out.write("                            <h4><i class=\"fas fa-wrench\"></i> Correction du Code</h4>\n");
      out.write("                            <p>Remplacement de <code>\\'</code> par <code>&apos;</code> dans les expressions JSTL</p>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    \n");
      out.write("                    <div class=\"workflow-step\">\n");
      out.write("                        <div class=\"step-number\">4</div>\n");
      out.write("                        <div class=\"step-content\">\n");
      out.write("                            <h4><i class=\"fas fa-broom\"></i> Nettoyage du Cache</h4>\n");
      out.write("                            <p>Suppression du cache Tomcat et des fichiers compilés pour forcer la recompilation</p>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    \n");
      out.write("                    <div class=\"workflow-step\">\n");
      out.write("                        <div class=\"step-number\">5</div>\n");
      out.write("                        <div class=\"step-content\">\n");
      out.write("                            <h4><i class=\"fas fa-compile\"></i> Recompilation Complète</h4>\n");
      out.write("                            <p>Build Maven clean + package pour une recompilation complète</p>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    \n");
      out.write("                    <div class=\"workflow-step\">\n");
      out.write("                        <div class=\"step-number\">6</div>\n");
      out.write("                        <div class=\"step-content\">\n");
      out.write("                            <h4><i class=\"fas fa-check\"></i> Test et Validation</h4>\n");
      out.write("                            <p>Déploiement et test de la page corrigée - HTTP 200 confirmé</p>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Fichiers modifiés -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-file-code\"></i> Fichiers Modifiés</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-2\">\n");
      out.write("                        <div class=\"admin-card\">\n");
      out.write("                            <div class=\"admin-card-icon\">\n");
      out.write("                                <i class=\"fas fa-file-code\"></i>\n");
      out.write("                            </div>\n");
      out.write("                            <div class=\"admin-card-content\">\n");
      out.write("                                <h3>📄 JSP Corrigée</h3>\n");
      out.write("                                <p><strong>Fichier :</strong> <code>mes-reservations.jsp</code></p>\n");
      out.write("                                <ul style=\"font-size: 12px;\">\n");
      out.write("                                    <li>✅ Ligne 199 : Expression JSTL corrigée</li>\n");
      out.write("                                    <li>✅ Ligne 258 : Expression JSTL corrigée</li>\n");
      out.write("                                    <li>✅ Apostrophes échappées avec <code>&apos;</code></li>\n");
      out.write("                                    <li>✅ Syntaxe JSTL valide</li>\n");
      out.write("                                </ul>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-2\">\n");
      out.write("                        <div class=\"admin-card\">\n");
      out.write("                            <div class=\"admin-card-icon\">\n");
      out.write("                                <i class=\"fas fa-server\"></i>\n");
      out.write("                            </div>\n");
      out.write("                            <div class=\"admin-card-content\">\n");
      out.write("                                <h3>🗂️ Cache Nettoyé</h3>\n");
      out.write("                                <p><strong>Répertoires :</strong> Cache Tomcat</p>\n");
      out.write("                                <ul style=\"font-size: 12px;\">\n");
      out.write("                                    <li>✅ <code>work/Catalina/localhost/jeetraing/</code></li>\n");
      out.write("                                    <li>✅ <code>webapps/jeetraing.war</code></li>\n");
      out.write("                                    <li>✅ <code>webapps/jeetraing/</code></li>\n");
      out.write("                                    <li>✅ Recompilation forcée</li>\n");
      out.write("                                </ul>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Problème technique expliqué -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-graduation-cap\"></i> Explication Technique</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-question-circle\"></i> Pourquoi cette erreur ?</h4>\n");
      out.write("                            <div style=\"font-size: 12px; padding: 10px;\">\n");
      out.write("                                <p><strong>Problème d'échappement :</strong></p>\n");
      out.write("                                <ul>\n");
      out.write("                                    <li>JSTL utilise des apostrophes pour délimiter les chaînes</li>\n");
      out.write("                                    <li>L'apostrophe dans \"d'annulation\" crée un conflit</li>\n");
      out.write("                                    <li>La chaîne se termine prématurément</li>\n");
      out.write("                                    <li>Résultat : syntaxe invalide</li>\n");
      out.write("                                </ul>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-lightbulb\"></i> Solution technique</h4>\n");
      out.write("                            <div style=\"font-size: 12px; padding: 10px;\">\n");
      out.write("                                <p><strong>Entités HTML :</strong></p>\n");
      out.write("                                <ul>\n");
      out.write("                                    <li><code>&apos;</code> = apostrophe échappée</li>\n");
      out.write("                                    <li>Reconnue par le parseur JSTL</li>\n");
      out.write("                                    <li>Affichée correctement dans le HTML</li>\n");
      out.write("                                    <li>Syntaxe valide garantie</li>\n");
      out.write("                                </ul>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-shield-alt\"></i> Prévention</h4>\n");
      out.write("                            <div style=\"font-size: 12px; padding: 10px;\">\n");
      out.write("                                <p><strong>Bonnes pratiques :</strong></p>\n");
      out.write("                                <ul>\n");
      out.write("                                    <li>Toujours échapper les caractères spéciaux</li>\n");
      out.write("                                    <li>Utiliser des entités HTML</li>\n");
      out.write("                                    <li>Tester la compilation JSP</li>\n");
      out.write("                                    <li>Nettoyer le cache en cas de problème</li>\n");
      out.write("                                </ul>\n");
      out.write("                            </div>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- URLs de test -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-link\"></i> URLs Finales Fonctionnelles</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-user\"></i> Utilisateur</h4>\n");
      out.write("                            <ul style=\"font-size: 12px; font-family: monospace;\">\n");
      out.write("                                <li>✅ <code>/mes-reservations</code> - CORRIGÉ</li>\n");
      out.write("                                <li>✅ <code>/monCompte</code></li>\n");
      out.write("                                <li>✅ <code>/recherche</code></li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-user-shield\"></i> Admin</h4>\n");
      out.write("                            <ul style=\"font-size: 12px; font-family: monospace;\">\n");
      out.write("                                <li>✅ <code>/admin/trajets</code> - Couleurs corrigées</li>\n");
      out.write("                                <li>✅ <code>/admin/reservations</code></li>\n");
      out.write("                                <li>✅ <code>/admin/dashboard</code></li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-3\">\n");
      out.write("                        <div class=\"card\">\n");
      out.write("                            <h4><i class=\"fas fa-eye\"></i> Démonstration</h4>\n");
      out.write("                            <ul style=\"font-size: 12px; font-family: monospace;\">\n");
      out.write("                                <li>✅ <code>/correction-erreur-mes-reservations.jsp</code></li>\n");
      out.write("                                <li>✅ <code>/corrections-trajets-et-logique-reservations.jsp</code></li>\n");
      out.write("                                <li>✅ <code>/corrections-finales-couleurs-admin.jsp</code></li>\n");
      out.write("                            </ul>\n");
      out.write("                        </div>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Actions de test -->\n");
      out.write("            <div class=\"card\">\n");
      out.write("                <div class=\"card-header\">\n");
      out.write("                    <h2 class=\"card-title\"><i class=\"fas fa-rocket\"></i> Tester Maintenant</h2>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"row\">\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/login.jsp\" class=\"btn btn-primary btn-full\">\n");
      out.write("                            <i class=\"fas fa-sign-in-alt\"></i> Se Connecter\n");
      out.write("                        </a>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/mes-reservations\" class=\"btn btn-success btn-full\">\n");
      out.write("                            <i class=\"fas fa-ticket-alt\"></i> Tester Mes Réservations\n");
      out.write("                        </a>\n");
      out.write("                    </div>\n");
      out.write("                    <div class=\"col-4\">\n");
      out.write("                        <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("/corrections-trajets-et-logique-reservations.jsp\" class=\"btn btn-warning btn-full\">\n");
      out.write("                            <i class=\"fas fa-eye\"></i> Voir Résumé Complet\n");
      out.write("                        </a>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            <!-- Message final -->\n");
      out.write("            <div class=\"text-center mt-20\">\n");
      out.write("                <div class=\"alert alert-success\">\n");
      out.write("                    <i class=\"fas fa-trophy\"></i> \n");
      out.write("                    <strong>🎯 ERREUR HTTP 500 CORRIGÉE AVEC SUCCÈS ! 🎯</strong>\n");
      out.write("                    <br>\n");
      out.write("                    <small>La page /mes-reservations fonctionne maintenant parfaitement.</small>\n");
      out.write("                    <br>\n");
      out.write("                    <div style=\"margin-top: 10px;\">\n");
      out.write("                        <span class=\"badge badge-success\">✅ Erreur Corrigée</span>\n");
      out.write("                        <span class=\"badge badge-success\">✅ JSP Fonctionnelle</span>\n");
      out.write("                        <span class=\"badge badge-success\">✅ Syntaxe Valide</span>\n");
      out.write("                        <span class=\"badge badge-success\">✅ Tests Validés</span>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("        </div>\n");
      out.write("    </div>\n");
      out.write("\n");
      out.write("    <style>\n");
      out.write("        .workflow-steps {\n");
      out.write("            display: flex;\n");
      out.write("            flex-direction: column;\n");
      out.write("            gap: 15px;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .workflow-step {\n");
      out.write("            display: flex;\n");
      out.write("            align-items: flex-start;\n");
      out.write("            gap: 15px;\n");
      out.write("            padding: 15px;\n");
      out.write("            background: var(--light-bg);\n");
      out.write("            border-radius: 8px;\n");
      out.write("            border-left: 4px solid var(--success-color);\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .step-number {\n");
      out.write("            width: 30px;\n");
      out.write("            height: 30px;\n");
      out.write("            background: var(--success-color);\n");
      out.write("            color: var(--white);\n");
      out.write("            border-radius: 50%;\n");
      out.write("            display: flex;\n");
      out.write("            align-items: center;\n");
      out.write("            justify-content: center;\n");
      out.write("            font-weight: 600;\n");
      out.write("            flex-shrink: 0;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .step-content h4 {\n");
      out.write("            margin: 0 0 5px 0;\n");
      out.write("            color: var(--text-dark);\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .step-content p {\n");
      out.write("            margin: 0;\n");
      out.write("            color: var(--text-muted);\n");
      out.write("            font-size: 14px;\n");
      out.write("        }\n");
      out.write("    </style>\n");
      out.write("</body>\n");
      out.write("</html>\n");
    } catch (java.lang.Throwable t) {
      if (!(t instanceof jakarta.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try {
            if (response.isCommitted()) {
              out.flush();
            } else {
              out.clearBuffer();
            }
          } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }
}
