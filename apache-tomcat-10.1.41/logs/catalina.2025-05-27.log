27-May-2025 20:41:22.848 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Nom version serveur :              Apache Tomcat/10.1.41
27-May-2025 20:41:22.851 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Serveur compilé :                  May 8 2025 12:44:02 UTC
27-May-2025 20:41:22.851 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Version du serveur :               10.1.41.0
27-May-2025 20:41:22.852 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Nom de l'OS :                      Windows 11
27-May-2025 20:41:22.852 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Version OS :                       10.0
27-May-2025 20:41:22.852 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Architecture :                     amd64
27-May-2025 20:41:22.853 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Java Home :                        C:\Program Files\Java\jdk-17
27-May-2025 20:41:22.853 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Version JVM :                      17.0.12+8-LTS-286
27-May-2025 20:41:22.853 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Fournisseur de la JVM :            Oracle Corporation
27-May-2025 20:41:22.853 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE :                    C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41
27-May-2025 20:41:22.853 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME :                    C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41
27-May-2025 20:41:22.862 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Argument de la ligne de commande : -Djava.util.logging.config.file=C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\conf\logging.properties
27-May-2025 20:41:22.863 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Argument de la ligne de commande : -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
27-May-2025 20:41:22.863 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Argument de la ligne de commande : -Djdk.tls.ephemeralDHKeySize=2048
27-May-2025 20:41:22.863 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Argument de la ligne de commande : -Djava.protocol.handler.pkgs=org.apache.catalina.webresources
27-May-2025 20:41:22.863 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Argument de la ligne de commande : -Dsun.io.useCanonCaches=false
27-May-2025 20:41:22.863 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Argument de la ligne de commande : --add-opens=java.base/java.lang=ALL-UNNAMED
27-May-2025 20:41:22.864 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Argument de la ligne de commande : --add-opens=java.base/java.lang.reflect=ALL-UNNAMED
27-May-2025 20:41:22.864 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Argument de la ligne de commande : --add-opens=java.base/java.io=ALL-UNNAMED
27-May-2025 20:41:22.864 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Argument de la ligne de commande : --add-opens=java.base/java.util=ALL-UNNAMED
27-May-2025 20:41:22.864 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Argument de la ligne de commande : --add-opens=java.base/java.util.concurrent=ALL-UNNAMED
27-May-2025 20:41:22.864 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Argument de la ligne de commande : --add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED
27-May-2025 20:41:22.864 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Argument de la ligne de commande : -Dcatalina.base=C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41
27-May-2025 20:41:22.864 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Argument de la ligne de commande : -Dcatalina.home=C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41
27-May-2025 20:41:22.865 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Argument de la ligne de commande : -Djava.io.tmpdir=C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\temp
27-May-2025 20:41:22.867 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent Chargement de la librairie Apache Tomcat Native [2.0.8] en utilisant APR version [1.7.4]
27-May-2025 20:41:22.870 INFO [main] org.apache.catalina.core.AprLifecycleListener.initializeSSL OpenSSL a été initialisé avec succès [OpenSSL 3.0.14 4 Jun 2024]
27-May-2025 20:41:22.997 INFO [main] org.apache.coyote.AbstractProtocol.init Initialisation du gestionnaire de protocole ["http-nio-8080"]
27-May-2025 20:41:23.011 INFO [main] org.apache.catalina.startup.Catalina.load L'initialisation du serveur a pris [279] millisecondes
27-May-2025 20:41:23.045 INFO [main] org.apache.catalina.core.StandardService.startInternal Démarrage du service [Catalina]
27-May-2025 20:41:23.046 INFO [main] org.apache.catalina.core.StandardEngine.startInternal Démarrage du moteur de Servlets : [Apache Tomcat/10.1.41]
27-May-2025 20:41:23.057 INFO [main] org.apache.catalina.startup.HostConfig.deployWAR Déploiement de l'archive [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] de l'application web
27-May-2025 20:41:24.164 INFO [main] org.apache.jasper.servlet.TldScanner.scanJars Au moins un fichier JAR a été analysé pour trouver des TLDs mais il n'en contenait pas, le mode "debug" du journal peut être activé pour obtenir une liste complète de JAR scannés sans succès ; éviter d'analyser des JARs inutilement peut améliorer sensiblement le temps de démarrage et le temps de compilation des JSPs
27-May-2025 20:41:24.245 INFO [main] org.apache.catalina.startup.HostConfig.deployWAR Le déploiement de l'archive de l'application web [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] s'est terminé en [1 187] ms
27-May-2025 20:41:24.246 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Déploiement du répertoire d'application web [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\docs]
27-May-2025 20:41:24.265 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Le déploiement du répertoire [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\docs] de l'application web s'est terminé en [19] ms
27-May-2025 20:41:24.265 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Déploiement du répertoire d'application web [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\examples]
27-May-2025 20:41:24.406 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Le déploiement du répertoire [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\examples] de l'application web s'est terminé en [141] ms
27-May-2025 20:41:24.406 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Déploiement du répertoire d'application web [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\host-manager]
27-May-2025 20:41:24.424 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Le déploiement du répertoire [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\host-manager] de l'application web s'est terminé en [18] ms
27-May-2025 20:41:24.424 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Déploiement du répertoire d'application web [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\manager]
27-May-2025 20:41:24.438 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Le déploiement du répertoire [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\manager] de l'application web s'est terminé en [13] ms
27-May-2025 20:41:24.438 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Déploiement du répertoire d'application web [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\ROOT]
27-May-2025 20:41:24.448 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Le déploiement du répertoire [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\ROOT] de l'application web s'est terminé en [10] ms
27-May-2025 20:41:24.451 INFO [main] org.apache.coyote.AbstractProtocol.start Démarrage du gestionnaire de protocole ["http-nio-8080"]
27-May-2025 20:41:24.489 INFO [main] org.apache.catalina.startup.Catalina.start Le démarrage du serveur a pris [1478] millisecondes
