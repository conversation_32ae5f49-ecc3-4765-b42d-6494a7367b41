27-May-2025 20:41:22.848 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Nom version serveur :              Apache Tomcat/10.1.41
27-May-2025 20:41:22.851 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Serveur compilé :                  May 8 2025 12:44:02 UTC
27-May-2025 20:41:22.851 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Version du serveur :               10.1.41.0
27-May-2025 20:41:22.852 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Nom de l'OS :                      Windows 11
27-May-2025 20:41:22.852 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Version OS :                       10.0
27-May-2025 20:41:22.852 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Architecture :                     amd64
27-May-2025 20:41:22.853 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Java Home :                        C:\Program Files\Java\jdk-17
27-May-2025 20:41:22.853 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Version JVM :                      17.0.12+8-LTS-286
27-May-2025 20:41:22.853 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Fournisseur de la JVM :            Oracle Corporation
27-May-2025 20:41:22.853 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE :                    C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41
27-May-2025 20:41:22.853 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME :                    C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41
27-May-2025 20:41:22.862 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Argument de la ligne de commande : -Djava.util.logging.config.file=C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\conf\logging.properties
27-May-2025 20:41:22.863 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Argument de la ligne de commande : -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
27-May-2025 20:41:22.863 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Argument de la ligne de commande : -Djdk.tls.ephemeralDHKeySize=2048
27-May-2025 20:41:22.863 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Argument de la ligne de commande : -Djava.protocol.handler.pkgs=org.apache.catalina.webresources
27-May-2025 20:41:22.863 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Argument de la ligne de commande : -Dsun.io.useCanonCaches=false
27-May-2025 20:41:22.863 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Argument de la ligne de commande : --add-opens=java.base/java.lang=ALL-UNNAMED
27-May-2025 20:41:22.864 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Argument de la ligne de commande : --add-opens=java.base/java.lang.reflect=ALL-UNNAMED
27-May-2025 20:41:22.864 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Argument de la ligne de commande : --add-opens=java.base/java.io=ALL-UNNAMED
27-May-2025 20:41:22.864 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Argument de la ligne de commande : --add-opens=java.base/java.util=ALL-UNNAMED
27-May-2025 20:41:22.864 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Argument de la ligne de commande : --add-opens=java.base/java.util.concurrent=ALL-UNNAMED
27-May-2025 20:41:22.864 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Argument de la ligne de commande : --add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED
27-May-2025 20:41:22.864 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Argument de la ligne de commande : -Dcatalina.base=C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41
27-May-2025 20:41:22.864 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Argument de la ligne de commande : -Dcatalina.home=C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41
27-May-2025 20:41:22.865 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Argument de la ligne de commande : -Djava.io.tmpdir=C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\temp
27-May-2025 20:41:22.867 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent Chargement de la librairie Apache Tomcat Native [2.0.8] en utilisant APR version [1.7.4]
27-May-2025 20:41:22.870 INFO [main] org.apache.catalina.core.AprLifecycleListener.initializeSSL OpenSSL a été initialisé avec succès [OpenSSL 3.0.14 4 Jun 2024]
27-May-2025 20:41:22.997 INFO [main] org.apache.coyote.AbstractProtocol.init Initialisation du gestionnaire de protocole ["http-nio-8080"]
27-May-2025 20:41:23.011 INFO [main] org.apache.catalina.startup.Catalina.load L'initialisation du serveur a pris [279] millisecondes
27-May-2025 20:41:23.045 INFO [main] org.apache.catalina.core.StandardService.startInternal Démarrage du service [Catalina]
27-May-2025 20:41:23.046 INFO [main] org.apache.catalina.core.StandardEngine.startInternal Démarrage du moteur de Servlets : [Apache Tomcat/10.1.41]
27-May-2025 20:41:23.057 INFO [main] org.apache.catalina.startup.HostConfig.deployWAR Déploiement de l'archive [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] de l'application web
27-May-2025 20:41:24.164 INFO [main] org.apache.jasper.servlet.TldScanner.scanJars Au moins un fichier JAR a été analysé pour trouver des TLDs mais il n'en contenait pas, le mode "debug" du journal peut être activé pour obtenir une liste complète de JAR scannés sans succès ; éviter d'analyser des JARs inutilement peut améliorer sensiblement le temps de démarrage et le temps de compilation des JSPs
27-May-2025 20:41:24.245 INFO [main] org.apache.catalina.startup.HostConfig.deployWAR Le déploiement de l'archive de l'application web [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] s'est terminé en [1 187] ms
27-May-2025 20:41:24.246 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Déploiement du répertoire d'application web [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\docs]
27-May-2025 20:41:24.265 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Le déploiement du répertoire [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\docs] de l'application web s'est terminé en [19] ms
27-May-2025 20:41:24.265 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Déploiement du répertoire d'application web [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\examples]
27-May-2025 20:41:24.406 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Le déploiement du répertoire [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\examples] de l'application web s'est terminé en [141] ms
27-May-2025 20:41:24.406 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Déploiement du répertoire d'application web [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\host-manager]
27-May-2025 20:41:24.424 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Le déploiement du répertoire [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\host-manager] de l'application web s'est terminé en [18] ms
27-May-2025 20:41:24.424 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Déploiement du répertoire d'application web [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\manager]
27-May-2025 20:41:24.438 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Le déploiement du répertoire [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\manager] de l'application web s'est terminé en [13] ms
27-May-2025 20:41:24.438 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Déploiement du répertoire d'application web [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\ROOT]
27-May-2025 20:41:24.448 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Le déploiement du répertoire [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\ROOT] de l'application web s'est terminé en [10] ms
27-May-2025 20:41:24.451 INFO [main] org.apache.coyote.AbstractProtocol.start Démarrage du gestionnaire de protocole ["http-nio-8080"]
27-May-2025 20:41:24.489 INFO [main] org.apache.catalina.startup.Catalina.start Le démarrage du serveur a pris [1478] millisecondes
27-May-2025 20:43:36.061 INFO [http-nio-8080-exec-9] org.hibernate.Version.logVersion HHH000412: Hibernate ORM core version 6.4.4.Final
27-May-2025 20:45:54.750 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.undeploy Retrait de l'application web ayant pour chemin de contexte [/jeetraing]
27-May-2025 20:45:54.775 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.deployWAR Déploiement de l'archive [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] de l'application web
27-May-2025 20:45:55.587 INFO [Catalina-utility-1] org.apache.jasper.servlet.TldScanner.scanJars Au moins un fichier JAR a été analysé pour trouver des TLDs mais il n'en contenait pas, le mode "debug" du journal peut être activé pour obtenir une liste complète de JAR scannés sans succès ; éviter d'analyser des JARs inutilement peut améliorer sensiblement le temps de démarrage et le temps de compilation des JSPs
27-May-2025 20:45:55.601 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.deployWAR Le déploiement de l'archive de l'application web [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] s'est terminé en [826] ms
27-May-2025 20:46:22.426 INFO [http-nio-8080-exec-3] org.hibernate.Version.logVersion HHH000412: Hibernate ORM core version 6.4.4.Final
27-May-2025 20:49:05.820 INFO [Catalina-utility-2] org.apache.catalina.startup.HostConfig.undeploy Retrait de l'application web ayant pour chemin de contexte [/jeetraing]
27-May-2025 20:49:05.839 INFO [Catalina-utility-2] org.apache.catalina.startup.HostConfig.deployWAR Déploiement de l'archive [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] de l'application web
27-May-2025 20:49:06.590 INFO [Catalina-utility-2] org.apache.jasper.servlet.TldScanner.scanJars Au moins un fichier JAR a été analysé pour trouver des TLDs mais il n'en contenait pas, le mode "debug" du journal peut être activé pour obtenir une liste complète de JAR scannés sans succès ; éviter d'analyser des JARs inutilement peut améliorer sensiblement le temps de démarrage et le temps de compilation des JSPs
27-May-2025 20:49:06.604 INFO [Catalina-utility-2] org.apache.catalina.startup.HostConfig.deployWAR Le déploiement de l'archive de l'application web [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] s'est terminé en [764] ms
27-May-2025 20:49:21.521 INFO [http-nio-8080-exec-9] org.hibernate.Version.logVersion HHH000412: Hibernate ORM core version 6.4.4.Final
27-May-2025 20:49:21.788 INFO [http-nio-8080-exec-9] org.hibernate.cache.internal.RegionFactoryInitiator.initiateService HHH000026: Second-level cache disabled
27-May-2025 20:49:21.905 WARN [http-nio-8080-exec-9] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.configure HHH10001002: Using built-in connection pool (not intended for production use)
27-May-2025 20:49:21.906 INFO [http-nio-8080-exec-9] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001005: Loaded JDBC driver class: org.h2.Driver
27-May-2025 20:49:21.907 INFO [http-nio-8080-exec-9] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001012: Connecting with JDBC URL [jdbc:h2:mem:jeetraindb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE]
27-May-2025 20:49:21.907 INFO [http-nio-8080-exec-9] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001001: Connection properties: {password=****, user=sa}
27-May-2025 20:49:21.907 INFO [http-nio-8080-exec-9] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001003: Autocommit mode: false
27-May-2025 20:49:21.910 INFO [http-nio-8080-exec-9] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl$PooledConnections.<init> HHH10001115: Connection pool size: 20 (min=1)
27-May-2025 20:49:22.108 WARN [http-nio-8080-exec-9] org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.constructDialect HHH90000025: H2Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
27-May-2025 20:49:23.085 INFO [http-nio-8080-exec-9] org.hibernate.engine.transaction.jta.platform.internal.JtaPlatformInitiator.initiateService HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
27-May-2025 20:49:23.106 INFO [http-nio-8080-exec-9] org.hibernate.resource.transaction.backend.jdbc.internal.DdlTransactionIsolatorNonJtaImpl.getIsolatedConnection HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@2feb3f8a] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
27-May-2025 20:49:23.644 INFO [http-nio-8080-exec-9] dao.StationDAO.save Station saved successfully: 1
27-May-2025 20:49:23.649 INFO [http-nio-8080-exec-9] dao.StationDAO.save Station saved successfully: 2
27-May-2025 20:49:23.649 INFO [http-nio-8080-exec-9] dao.StationDAO.save Station saved successfully: 3
27-May-2025 20:49:23.650 INFO [http-nio-8080-exec-9] dao.StationDAO.save Station saved successfully: 4
27-May-2025 20:49:23.651 INFO [http-nio-8080-exec-9] dao.StationDAO.save Station saved successfully: 5
27-May-2025 20:49:23.651 INFO [http-nio-8080-exec-9] dao.StationDAO.save Station saved successfully: 6
27-May-2025 20:49:23.652 INFO [http-nio-8080-exec-9] dao.StationDAO.save Station saved successfully: 7
27-May-2025 20:49:23.652 INFO [http-nio-8080-exec-9] dao.StationDAO.save Station saved successfully: 8
27-May-2025 20:49:23.653 INFO [http-nio-8080-exec-9] dao.StationDAO.save Station saved successfully: 9
27-May-2025 20:49:23.653 INFO [http-nio-8080-exec-9] dao.StationDAO.save Station saved successfully: 10
27-May-2025 20:49:23.658 INFO [http-nio-8080-exec-9] dao.StationDAO.findAll Retrieved 10 stations
27-May-2025 20:52:26.933 INFO [http-nio-8080-exec-17] controller.LoginServlet.doPost Utilisateur connecté avec succès: <EMAIL>
27-May-2025 20:52:26.938 INFO [http-nio-8080-exec-2] dao.StationDAO.findAll Retrieved 10 stations
27-May-2025 20:52:46.929 INFO [http-nio-8080-exec-18] dao.StationDAO.findAll Retrieved 10 stations
27-May-2025 20:52:48.589 INFO [http-nio-8080-exec-20] dao.StationDAO.findAll Retrieved 10 stations
27-May-2025 20:52:49.411 INFO [http-nio-8080-exec-16] dao.StationDAO.findAll Retrieved 10 stations
27-May-2025 20:52:57.454 INFO [http-nio-8080-exec-22] dao.StationDAO.findAll Retrieved 10 stations
27-May-2025 20:53:32.450 INFO [http-nio-8080-exec-19] dao.StationDAO.findAll Retrieved 10 stations
27-May-2025 20:53:38.197 INFO [http-nio-8080-exec-16] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisé'
27-May-2025 20:53:47.528 INFO [http-nio-8080-exec-24] dao.StationDAO.findAll Retrieved 10 stations
27-May-2025 20:53:50.629 INFO [http-nio-8080-exec-20] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisé'
27-May-2025 20:53:51.605 INFO [http-nio-8080-exec-22] dao.StationDAO.findAll Retrieved 10 stations
27-May-2025 20:54:00.946 INFO [http-nio-8080-exec-24] dao.StationDAO.findAll Retrieved 10 stations
27-May-2025 21:00:37.413 INFO [Catalina-utility-2] org.apache.catalina.startup.HostConfig.undeploy Retrait de l'application web ayant pour chemin de contexte [/jeetraing]
27-May-2025 21:00:37.418 WARNING [Catalina-utility-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesJdbc L'application web [jeetraing] a enregistré un pilote JDBC [org.h2.Driver], mais ne l'a pas désenregistré avant l'arrêt de l'application.  Pour éviter une fuite de mémoire, le pilote JDBC a été désenregistré de force.
27-May-2025 21:00:37.423 WARNING [Catalina-utility-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [Hibernate Connection Pool Validation Thread] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.12/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:252)
 java.base@17.0.12/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1672)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
27-May-2025 21:00:37.441 INFO [Catalina-utility-2] org.apache.catalina.startup.HostConfig.deployWAR Déploiement de l'archive [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] de l'application web
27-May-2025 21:00:38.223 INFO [Catalina-utility-2] org.apache.jasper.servlet.TldScanner.scanJars Au moins un fichier JAR a été analysé pour trouver des TLDs mais il n'en contenait pas, le mode "debug" du journal peut être activé pour obtenir une liste complète de JAR scannés sans succès ; éviter d'analyser des JARs inutilement peut améliorer sensiblement le temps de démarrage et le temps de compilation des JSPs
27-May-2025 21:00:38.239 INFO [Catalina-utility-2] org.apache.catalina.startup.HostConfig.deployWAR Le déploiement de l'archive de l'application web [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] s'est terminé en [798] ms
27-May-2025 21:00:58.849 INFO [http-nio-8080-exec-23] org.hibernate.Version.logVersion HHH000412: Hibernate ORM core version 6.4.4.Final
27-May-2025 21:00:59.039 INFO [http-nio-8080-exec-23] org.hibernate.cache.internal.RegionFactoryInitiator.initiateService HHH000026: Second-level cache disabled
27-May-2025 21:00:59.129 WARN [http-nio-8080-exec-23] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.configure HHH10001002: Using built-in connection pool (not intended for production use)
27-May-2025 21:00:59.130 INFO [http-nio-8080-exec-23] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001005: Loaded JDBC driver class: org.h2.Driver
27-May-2025 21:00:59.130 INFO [http-nio-8080-exec-23] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001012: Connecting with JDBC URL [jdbc:h2:mem:jeetraindb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE]
27-May-2025 21:00:59.131 INFO [http-nio-8080-exec-23] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001001: Connection properties: {password=****, user=sa}
27-May-2025 21:00:59.131 INFO [http-nio-8080-exec-23] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001003: Autocommit mode: false
27-May-2025 21:00:59.133 INFO [http-nio-8080-exec-23] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl$PooledConnections.<init> HHH10001115: Connection pool size: 20 (min=1)
27-May-2025 21:00:59.307 WARN [http-nio-8080-exec-23] org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.constructDialect HHH90000025: H2Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
27-May-2025 21:01:00.162 INFO [http-nio-8080-exec-23] org.hibernate.engine.transaction.jta.platform.internal.JtaPlatformInitiator.initiateService HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
27-May-2025 21:01:00.180 INFO [http-nio-8080-exec-23] org.hibernate.resource.transaction.backend.jdbc.internal.DdlTransactionIsolatorNonJtaImpl.getIsolatedConnection HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@33268364] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
27-May-2025 21:01:00.432 INFO [http-nio-8080-exec-23] dao.StationDAO.findAll Retrieved 0 stations
27-May-2025 21:01:13.407 INFO [http-nio-8080-exec-18] dao.StationDAO.save Station saved successfully: 1
27-May-2025 21:01:13.408 INFO [http-nio-8080-exec-18] dao.StationDAO.save Station saved successfully: 2
27-May-2025 21:01:13.409 INFO [http-nio-8080-exec-18] dao.StationDAO.save Station saved successfully: 3
27-May-2025 21:01:13.409 INFO [http-nio-8080-exec-18] dao.StationDAO.save Station saved successfully: 4
27-May-2025 21:01:13.409 INFO [http-nio-8080-exec-18] dao.StationDAO.save Station saved successfully: 5
27-May-2025 21:01:13.410 INFO [http-nio-8080-exec-18] dao.StationDAO.save Station saved successfully: 6
27-May-2025 21:01:13.410 INFO [http-nio-8080-exec-18] dao.StationDAO.save Station saved successfully: 7
27-May-2025 21:01:13.411 INFO [http-nio-8080-exec-18] dao.StationDAO.save Station saved successfully: 8
27-May-2025 21:01:13.411 INFO [http-nio-8080-exec-18] dao.StationDAO.save Station saved successfully: 9
27-May-2025 21:01:13.412 INFO [http-nio-8080-exec-18] dao.StationDAO.save Station saved successfully: 10
27-May-2025 21:01:13.418 INFO [http-nio-8080-exec-18] dao.StationDAO.findAll Retrieved 10 stations
27-May-2025 21:01:23.844 INFO [http-nio-8080-exec-19] dao.StationDAO.findAll Retrieved 10 stations
27-May-2025 21:02:28.365 INFO [Catalina-utility-2] org.apache.catalina.startup.HostConfig.undeploy Retrait de l'application web ayant pour chemin de contexte [/jeetraing]
27-May-2025 21:02:28.367 WARNING [Catalina-utility-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesJdbc L'application web [jeetraing] a enregistré un pilote JDBC [org.h2.Driver], mais ne l'a pas désenregistré avant l'arrêt de l'application.  Pour éviter une fuite de mémoire, le pilote JDBC a été désenregistré de force.
27-May-2025 21:02:28.368 WARNING [Catalina-utility-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [Hibernate Connection Pool Validation Thread] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.12/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:252)
 java.base@17.0.12/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1672)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
27-May-2025 21:02:28.389 INFO [Catalina-utility-2] org.apache.catalina.startup.HostConfig.deployWAR Déploiement de l'archive [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] de l'application web
27-May-2025 21:02:29.192 INFO [Catalina-utility-2] org.apache.jasper.servlet.TldScanner.scanJars Au moins un fichier JAR a été analysé pour trouver des TLDs mais il n'en contenait pas, le mode "debug" du journal peut être activé pour obtenir une liste complète de JAR scannés sans succès ; éviter d'analyser des JARs inutilement peut améliorer sensiblement le temps de démarrage et le temps de compilation des JSPs
27-May-2025 21:02:29.206 INFO [Catalina-utility-2] org.apache.catalina.startup.HostConfig.deployWAR Le déploiement de l'archive de l'application web [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] s'est terminé en [816] ms
27-May-2025 21:05:09.175 INFO [http-nio-8080-exec-30] org.hibernate.Version.logVersion HHH000412: Hibernate ORM core version 6.4.4.Final
27-May-2025 21:05:09.410 INFO [http-nio-8080-exec-30] org.hibernate.cache.internal.RegionFactoryInitiator.initiateService HHH000026: Second-level cache disabled
27-May-2025 21:05:09.520 WARN [http-nio-8080-exec-30] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.configure HHH10001002: Using built-in connection pool (not intended for production use)
27-May-2025 21:05:09.521 INFO [http-nio-8080-exec-30] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001005: Loaded JDBC driver class: org.h2.Driver
27-May-2025 21:05:09.522 INFO [http-nio-8080-exec-30] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001012: Connecting with JDBC URL [jdbc:h2:mem:jeetraindb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE]
27-May-2025 21:05:09.522 INFO [http-nio-8080-exec-30] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001001: Connection properties: {password=****, user=sa}
27-May-2025 21:05:09.522 INFO [http-nio-8080-exec-30] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001003: Autocommit mode: false
27-May-2025 21:05:09.525 INFO [http-nio-8080-exec-30] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl$PooledConnections.<init> HHH10001115: Connection pool size: 20 (min=1)
27-May-2025 21:05:09.736 WARN [http-nio-8080-exec-30] org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.constructDialect HHH90000025: H2Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
27-May-2025 21:05:10.809 INFO [http-nio-8080-exec-30] org.hibernate.engine.transaction.jta.platform.internal.JtaPlatformInitiator.initiateService HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
27-May-2025 21:05:10.831 INFO [http-nio-8080-exec-30] org.hibernate.resource.transaction.backend.jdbc.internal.DdlTransactionIsolatorNonJtaImpl.getIsolatedConnection HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@312019b4] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
27-May-2025 21:05:11.442 INFO [http-nio-8080-exec-30] dao.StationDAO.save Station saved successfully: 1
27-May-2025 21:05:11.442 INFO [http-nio-8080-exec-30] dao.StationDAO.save Station saved successfully: 2
27-May-2025 21:05:11.443 INFO [http-nio-8080-exec-30] dao.StationDAO.save Station saved successfully: 3
27-May-2025 21:05:11.444 INFO [http-nio-8080-exec-30] dao.StationDAO.save Station saved successfully: 4
27-May-2025 21:05:11.444 INFO [http-nio-8080-exec-30] dao.StationDAO.save Station saved successfully: 5
27-May-2025 21:05:11.445 INFO [http-nio-8080-exec-30] dao.StationDAO.save Station saved successfully: 6
27-May-2025 21:05:11.446 INFO [http-nio-8080-exec-30] dao.StationDAO.save Station saved successfully: 7
27-May-2025 21:05:11.446 INFO [http-nio-8080-exec-30] dao.StationDAO.save Station saved successfully: 8
27-May-2025 21:05:11.447 INFO [http-nio-8080-exec-30] dao.StationDAO.save Station saved successfully: 9
27-May-2025 21:05:11.448 INFO [http-nio-8080-exec-30] dao.StationDAO.save Station saved successfully: 10
27-May-2025 21:05:11.455 INFO [http-nio-8080-exec-30] dao.StationDAO.findAll Retrieved 10 stations
27-May-2025 21:05:32.294 INFO [http-nio-8080-exec-34] dao.StationDAO.findAll Retrieved 10 stations
27-May-2025 21:14:10.024 INFO [Catalina-utility-2] org.apache.catalina.startup.HostConfig.undeploy Retrait de l'application web ayant pour chemin de contexte [/jeetraing]
27-May-2025 21:14:10.028 WARNING [Catalina-utility-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesJdbc L'application web [jeetraing] a enregistré un pilote JDBC [org.h2.Driver], mais ne l'a pas désenregistré avant l'arrêt de l'application.  Pour éviter une fuite de mémoire, le pilote JDBC a été désenregistré de force.
27-May-2025 21:14:10.030 WARNING [Catalina-utility-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [Hibernate Connection Pool Validation Thread] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.12/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:252)
 java.base@17.0.12/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1672)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
27-May-2025 21:14:10.054 INFO [Catalina-utility-2] org.apache.catalina.startup.HostConfig.deployWAR Déploiement de l'archive [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] de l'application web
27-May-2025 21:14:11.028 INFO [Catalina-utility-2] org.apache.jasper.servlet.TldScanner.scanJars Au moins un fichier JAR a été analysé pour trouver des TLDs mais il n'en contenait pas, le mode "debug" du journal peut être activé pour obtenir une liste complète de JAR scannés sans succès ; éviter d'analyser des JARs inutilement peut améliorer sensiblement le temps de démarrage et le temps de compilation des JSPs
27-May-2025 21:14:11.045 INFO [Catalina-utility-2] org.apache.catalina.startup.HostConfig.deployWAR Le déploiement de l'archive de l'application web [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] s'est terminé en [990] ms
27-May-2025 21:14:42.006 INFO [http-nio-8080-exec-38] org.hibernate.Version.logVersion HHH000412: Hibernate ORM core version 6.4.4.Final
27-May-2025 21:14:42.234 INFO [http-nio-8080-exec-38] org.hibernate.cache.internal.RegionFactoryInitiator.initiateService HHH000026: Second-level cache disabled
27-May-2025 21:14:42.345 WARN [http-nio-8080-exec-38] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.configure HHH10001002: Using built-in connection pool (not intended for production use)
27-May-2025 21:14:42.346 INFO [http-nio-8080-exec-38] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001005: Loaded JDBC driver class: com.mysql.cj.jdbc.Driver
27-May-2025 21:14:42.346 INFO [http-nio-8080-exec-38] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001012: Connecting with JDBC URL [**************************************]
27-May-2025 21:14:42.347 INFO [http-nio-8080-exec-38] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001001: Connection properties: {password=****, user=root}
27-May-2025 21:14:42.347 INFO [http-nio-8080-exec-38] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001003: Autocommit mode: false
27-May-2025 21:14:42.351 INFO [http-nio-8080-exec-38] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl$PooledConnections.<init> HHH10001115: Connection pool size: 20 (min=1)
27-May-2025 21:14:42.369 WARN [http-nio-8080-exec-38] org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.getJdbcEnvironmentUsingJdbcMetadata HHH000342: Could not obtain connection to query metadata
	java.lang.IllegalStateException: Cannot get a connection as the driver manager is not properly initialized
		at org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.getConnection(DriverManagerConnectionProviderImpl.java:259)
		at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess.obtainConnection(JdbcEnvironmentInitiator.java:428)
		at org.hibernate.resource.transaction.backend.jdbc.internal.JdbcIsolationDelegate.delegateWork(JdbcIsolationDelegate.java:61)
		at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.getJdbcEnvironmentUsingJdbcMetadata(JdbcEnvironmentInitiator.java:276)
		at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:107)
		at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:68)
		at org.hibernate.boot.registry.internal.StandardServiceRegistryImpl.initiateService(StandardServiceRegistryImpl.java:130)
		at org.hibernate.service.internal.AbstractServiceRegistryImpl.createService(AbstractServiceRegistryImpl.java:263)
		at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:238)
		at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:215)
		at org.hibernate.engine.jdbc.internal.JdbcServicesImpl.configure(JdbcServicesImpl.java:52)
		at org.hibernate.boot.registry.internal.StandardServiceRegistryImpl.configureService(StandardServiceRegistryImpl.java:136)
		at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:247)
		at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:215)
		at org.hibernate.engine.jdbc.connections.internal.BasicConnectionCreator.convertSqlException(BasicConnectionCreator.java:132)
		at org.hibernate.engine.jdbc.connections.internal.DriverConnectionCreator.makeConnection(DriverConnectionCreator.java:43)
		at org.hibernate.engine.jdbc.connections.internal.BasicConnectionCreator.createConnection(BasicConnectionCreator.java:61)
		at org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl$PooledConnections.addConnections(DriverManagerConnectionProviderImpl.java:499)
		at org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl$PooledConnections.<init>(DriverManagerConnectionProviderImpl.java:372)
		at org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl$PooledConnections$Builder.build(DriverManagerConnectionProviderImpl.java:550)
		at org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildPool(DriverManagerConnectionProviderImpl.java:102)
		at org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.configure(DriverManagerConnectionProviderImpl.java:82)
		at org.hibernate.boot.registry.internal.StandardServiceRegistryImpl.configureService(StandardServiceRegistryImpl.java:136)
		at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:247)
		at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:215)
		at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.buildJdbcConnectionAccess(JdbcEnvironmentInitiator.java:395)
		at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.getJdbcEnvironmentUsingJdbcMetadata(JdbcEnvironmentInitiator.java:262)
		at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:107)
		at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:68)
		at org.hibernate.boot.registry.internal.StandardServiceRegistryImpl.initiateService(StandardServiceRegistryImpl.java:130)
		at org.hibernate.service.internal.AbstractServiceRegistryImpl.createService(AbstractServiceRegistryImpl.java:263)
		at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:238)
		at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:215)
		at org.hibernate.boot.model.relational.Database.<init>(Database.java:45)
		at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.getDatabase(InFlightMetadataCollectorImpl.java:223)
		at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.<init>(InFlightMetadataCollectorImpl.java:191)
		at org.hibernate.boot.model.process.spi.MetadataBuildingProcess.complete(MetadataBuildingProcess.java:170)
		at org.hibernate.boot.model.process.spi.MetadataBuildingProcess.build(MetadataBuildingProcess.java:129)
		at org.hibernate.boot.internal.MetadataBuilderImpl.build(MetadataBuilderImpl.java:449)
		at org.hibernate.boot.internal.MetadataBuilderImpl.build(MetadataBuilderImpl.java:101)
		at org.hibernate.cfg.Configuration.buildSessionFactory(Configuration.java:910)
		at util.HibernateUtil.<clinit>(HibernateUtil.java:15)
		at controller.DatabaseTestServlet.doGet(DatabaseTestServlet.java:51)
		at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
		at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
		at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
		at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
		at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
		at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
		at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
		at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
		at org.apache.catalina.valves.AbstractAccessLogValve.invoke(AbstractAccessLogValve.java:666)
		at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
		at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
		at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
		at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
		at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
		at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1740)
		at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
		at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
		at java.base/java.lang.Thread.run(Thread.java:842)
27-May-2025 21:14:42.390 WARN [http-nio-8080-exec-38] org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.constructDialect HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
27-May-2025 21:14:42.404 WARN [http-nio-8080-exec-38] org.hibernate.engine.jdbc.spi.SqlExceptionHelper.logExceptions SQL Error: 1049, SQLState: 42000
27-May-2025 21:14:42.404 ERROR [http-nio-8080-exec-38] org.hibernate.engine.jdbc.spi.SqlExceptionHelper.logExceptions Unknown database 'jeetraindb'
27-May-2025 21:17:11.229 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.undeploy Retrait de l'application web ayant pour chemin de contexte [/jeetraing]
27-May-2025 21:17:11.232 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesJdbc L'application web [jeetraing] a enregistré un pilote JDBC [com.mysql.cj.jdbc.Driver], mais ne l'a pas désenregistré avant l'arrêt de l'application.  Pour éviter une fuite de mémoire, le pilote JDBC a été désenregistré de force.
27-May-2025 21:17:11.233 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [mysql-cj-abandoned-connection-cleanup] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/java.lang.Object.wait(Native Method)
 java.base@17.0.12/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:155)
 com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:91)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
27-May-2025 21:17:11.256 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.deployWAR Déploiement de l'archive [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] de l'application web
27-May-2025 21:17:12.037 INFO [mysql-cj-abandoned-connection-cleanup] org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading Impossible de charger [], ce chargeur de classes a déjà été arrêté
	java.lang.IllegalStateException: Impossible de charger [], ce chargeur de classes a déjà été arrêté
		at org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading(WebappClassLoaderBase.java:1400)
		at org.apache.catalina.loader.WebappClassLoaderBase.getResource(WebappClassLoaderBase.java:987)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.checkThreadContextClassLoader(AbandonedConnectionCleanupThread.java:123)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:90)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:842)
27-May-2025 21:17:12.264 INFO [Catalina-utility-1] org.apache.jasper.servlet.TldScanner.scanJars Au moins un fichier JAR a été analysé pour trouver des TLDs mais il n'en contenait pas, le mode "debug" du journal peut être activé pour obtenir une liste complète de JAR scannés sans succès ; éviter d'analyser des JARs inutilement peut améliorer sensiblement le temps de démarrage et le temps de compilation des JSPs
27-May-2025 21:17:12.283 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.deployWAR Le déploiement de l'archive de l'application web [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] s'est terminé en [1 027] ms
27-May-2025 21:17:40.500 INFO [http-nio-8080-exec-42] org.hibernate.Version.logVersion HHH000412: Hibernate ORM core version 6.4.4.Final
27-May-2025 21:17:40.720 INFO [http-nio-8080-exec-42] org.hibernate.cache.internal.RegionFactoryInitiator.initiateService HHH000026: Second-level cache disabled
27-May-2025 21:17:40.834 WARN [http-nio-8080-exec-42] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.configure HHH10001002: Using built-in connection pool (not intended for production use)
27-May-2025 21:17:40.834 INFO [http-nio-8080-exec-42] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001005: Loaded JDBC driver class: com.mysql.cj.jdbc.Driver
27-May-2025 21:17:40.835 INFO [http-nio-8080-exec-42] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001012: Connecting with JDBC URL [**************************************]
27-May-2025 21:17:40.835 INFO [http-nio-8080-exec-42] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001001: Connection properties: {password=****, user=root}
27-May-2025 21:17:40.835 INFO [http-nio-8080-exec-42] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001003: Autocommit mode: false
27-May-2025 21:17:40.838 INFO [http-nio-8080-exec-42] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl$PooledConnections.<init> HHH10001115: Connection pool size: 20 (min=1)
27-May-2025 21:17:40.885 WARN [http-nio-8080-exec-42] org.hibernate.dialect.Dialect.checkVersion HHH000511: The 5.5.0 version for [org.hibernate.dialect.MySQLDialect] is no longer supported, hence certain features may not work properly. The minimum supported version is 8.0.0. Check the community dialects project for available legacy versions.
27-May-2025 21:17:40.892 WARN [http-nio-8080-exec-42] org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.constructDialect HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
27-May-2025 21:17:41.991 INFO [http-nio-8080-exec-42] org.hibernate.engine.transaction.jta.platform.internal.JtaPlatformInitiator.initiateService HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
27-May-2025 21:17:42.017 INFO [http-nio-8080-exec-42] org.hibernate.resource.transaction.backend.jdbc.internal.DdlTransactionIsolatorNonJtaImpl.getIsolatedConnection HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@792424c4] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
27-May-2025 21:17:52.702 INFO [http-nio-8080-exec-34] dao.StationDAO.save Station saved successfully: 1
27-May-2025 21:17:52.706 INFO [http-nio-8080-exec-34] dao.StationDAO.save Station saved successfully: 2
27-May-2025 21:17:52.708 INFO [http-nio-8080-exec-34] dao.StationDAO.save Station saved successfully: 3
27-May-2025 21:17:52.711 INFO [http-nio-8080-exec-34] dao.StationDAO.save Station saved successfully: 4
27-May-2025 21:17:52.713 INFO [http-nio-8080-exec-34] dao.StationDAO.save Station saved successfully: 5
27-May-2025 21:17:52.715 INFO [http-nio-8080-exec-34] dao.StationDAO.save Station saved successfully: 6
27-May-2025 21:17:52.717 INFO [http-nio-8080-exec-34] dao.StationDAO.save Station saved successfully: 7
27-May-2025 21:17:52.718 INFO [http-nio-8080-exec-34] dao.StationDAO.save Station saved successfully: 8
27-May-2025 21:17:52.720 INFO [http-nio-8080-exec-34] dao.StationDAO.save Station saved successfully: 9
27-May-2025 21:17:52.724 INFO [http-nio-8080-exec-34] dao.StationDAO.save Station saved successfully: 10
27-May-2025 21:17:52.729 INFO [http-nio-8080-exec-34] dao.StationDAO.findAll Retrieved 10 stations
27-May-2025 21:18:06.671 INFO [http-nio-8080-exec-37] dao.StationDAO.findAll Retrieved 10 stations
27-May-2025 21:18:17.184 INFO [http-nio-8080-exec-47] dao.StationDAO.findAll Retrieved 10 stations
27-May-2025 21:20:36.663 INFO [http-nio-8080-exec-54] controller.LoginServlet.doPost Utilisateur connecté avec succès: <EMAIL>
27-May-2025 21:20:36.670 INFO [http-nio-8080-exec-55] dao.StationDAO.findAll Retrieved 10 stations
27-May-2025 21:20:46.365 INFO [http-nio-8080-exec-48] dao.StationDAO.findAll Retrieved 10 stations
27-May-2025 21:20:48.166 INFO [http-nio-8080-exec-49] dao.StationDAO.findAll Retrieved 10 stations
27-May-2025 21:21:13.358 INFO [http-nio-8080-exec-53] dao.StationDAO.findAll Retrieved 10 stations
27-May-2025 21:21:53.110 INFO [http-nio-8080-exec-49] controller.LoginServlet.doPost Utilisateur connecté avec succès: <EMAIL>
27-May-2025 21:21:53.116 INFO [http-nio-8080-exec-51] dao.StationDAO.findAll Retrieved 10 stations
27-May-2025 21:22:03.450 INFO [http-nio-8080-exec-53] dao.StationDAO.findAll Retrieved 10 stations
27-May-2025 21:22:41.254 INFO [http-nio-8080-exec-48] controller.ReservationEvolutionServlet.doGet doGet - Evolution Data: [[Ljava.lang.Object;@2d773c6c, [Ljava.lang.Object;@7df45d63]
27-May-2025 21:22:41.255 INFO [http-nio-8080-exec-48] controller.ReservationEvolutionServlet.doGet doGet - Labels: [2025-04, 2025-05]
27-May-2025 21:22:41.255 INFO [http-nio-8080-exec-48] controller.ReservationEvolutionServlet.doGet doGet - Data: [0, 3]
27-May-2025 21:22:41.256 INFO [http-nio-8080-exec-48] controller.ReservationEvolutionServlet.doGet doGet - Attributes set - evolutionData: [[Ljava.lang.Object;@2d773c6c, [Ljava.lang.Object;@7df45d63]
27-May-2025 21:23:06.997 INFO [http-nio-8080-exec-54] dao.StationDAO.findAll Retrieved 10 stations
27-May-2025 21:23:13.640 INFO [http-nio-8080-exec-56] dao.StationDAO.findAll Retrieved 10 stations
27-May-2025 21:23:25.787 INFO [http-nio-8080-exec-51] dao.StationDAO.findAll Retrieved 10 stations
27-May-2025 21:23:45.979 INFO [http-nio-8080-exec-48] dao.StationDAO.findAll Retrieved 10 stations
27-May-2025 21:24:12.373 INFO [http-nio-8080-exec-47] dao.StationDAO.findAll Retrieved 10 stations
27-May-2025 21:24:15.956 INFO [http-nio-8080-exec-53] dao.StationDAO.findAll Retrieved 10 stations
27-May-2025 21:27:22.975 INFO [Catalina-utility-2] org.apache.catalina.startup.HostConfig.undeploy Retrait de l'application web ayant pour chemin de contexte [/jeetraing]
27-May-2025 21:27:22.978 WARNING [Catalina-utility-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesJdbc L'application web [jeetraing] a enregistré un pilote JDBC [com.mysql.cj.jdbc.Driver], mais ne l'a pas désenregistré avant l'arrêt de l'application.  Pour éviter une fuite de mémoire, le pilote JDBC a été désenregistré de force.
27-May-2025 21:27:22.978 WARNING [Catalina-utility-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [mysql-cj-abandoned-connection-cleanup] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/java.lang.Object.wait(Native Method)
 java.base@17.0.12/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:155)
 com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:91)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
27-May-2025 21:27:22.979 WARNING [Catalina-utility-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [Hibernate Connection Pool Validation Thread] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.12/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:252)
 java.base@17.0.12/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1672)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
27-May-2025 21:27:23.002 INFO [Catalina-utility-2] org.apache.catalina.startup.HostConfig.deployWAR Déploiement de l'archive [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] de l'application web
27-May-2025 21:27:23.906 INFO [mysql-cj-abandoned-connection-cleanup] org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading Impossible de charger [], ce chargeur de classes a déjà été arrêté
	java.lang.IllegalStateException: Impossible de charger [], ce chargeur de classes a déjà été arrêté
		at org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading(WebappClassLoaderBase.java:1400)
		at org.apache.catalina.loader.WebappClassLoaderBase.getResource(WebappClassLoaderBase.java:987)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.checkThreadContextClassLoader(AbandonedConnectionCleanupThread.java:123)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:90)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:842)
27-May-2025 21:27:23.938 INFO [Catalina-utility-2] org.apache.jasper.servlet.TldScanner.scanJars Au moins un fichier JAR a été analysé pour trouver des TLDs mais il n'en contenait pas, le mode "debug" du journal peut être activé pour obtenir une liste complète de JAR scannés sans succès ; éviter d'analyser des JARs inutilement peut améliorer sensiblement le temps de démarrage et le temps de compilation des JSPs
27-May-2025 21:27:23.950 INFO [Catalina-utility-2] org.apache.catalina.startup.HostConfig.deployWAR Le déploiement de l'archive de l'application web [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] s'est terminé en [949] ms
27-May-2025 21:27:47.921 INFO [http-nio-8080-exec-52] org.hibernate.Version.logVersion HHH000412: Hibernate ORM core version 6.4.4.Final
27-May-2025 21:27:48.157 INFO [http-nio-8080-exec-52] org.hibernate.cache.internal.RegionFactoryInitiator.initiateService HHH000026: Second-level cache disabled
27-May-2025 21:27:48.269 WARN [http-nio-8080-exec-52] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.configure HHH10001002: Using built-in connection pool (not intended for production use)
27-May-2025 21:27:48.272 INFO [http-nio-8080-exec-52] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001005: Loaded JDBC driver class: com.mysql.cj.jdbc.Driver
27-May-2025 21:27:48.272 INFO [http-nio-8080-exec-52] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001012: Connecting with JDBC URL [**************************************]
27-May-2025 21:27:48.272 INFO [http-nio-8080-exec-52] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001001: Connection properties: {password=****, user=root}
27-May-2025 21:27:48.272 INFO [http-nio-8080-exec-52] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001003: Autocommit mode: false
27-May-2025 21:27:48.275 INFO [http-nio-8080-exec-52] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl$PooledConnections.<init> HHH10001115: Connection pool size: 20 (min=1)
27-May-2025 21:27:48.427 WARN [http-nio-8080-exec-52] org.hibernate.dialect.Dialect.checkVersion HHH000511: The 5.5.0 version for [org.hibernate.dialect.MySQLDialect] is no longer supported, hence certain features may not work properly. The minimum supported version is 8.0.0. Check the community dialects project for available legacy versions.
27-May-2025 21:27:48.430 WARN [http-nio-8080-exec-52] org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.constructDialect HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
27-May-2025 21:27:49.507 INFO [http-nio-8080-exec-52] org.hibernate.engine.transaction.jta.platform.internal.JtaPlatformInitiator.initiateService HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
27-May-2025 21:27:49.533 INFO [http-nio-8080-exec-52] org.hibernate.resource.transaction.backend.jdbc.internal.DdlTransactionIsolatorNonJtaImpl.getIsolatedConnection HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@5044597] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
27-May-2025 21:27:49.945 INFO [http-nio-8080-exec-52] dao.StationDAO.findAll Retrieved 10 stations
27-May-2025 21:28:44.049 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.undeploy Retrait de l'application web ayant pour chemin de contexte [/jeetraing]
27-May-2025 21:28:44.051 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesJdbc L'application web [jeetraing] a enregistré un pilote JDBC [com.mysql.cj.jdbc.Driver], mais ne l'a pas désenregistré avant l'arrêt de l'application.  Pour éviter une fuite de mémoire, le pilote JDBC a été désenregistré de force.
27-May-2025 21:28:44.052 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [mysql-cj-abandoned-connection-cleanup] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/java.lang.Object.wait(Native Method)
 java.base@17.0.12/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:155)
 com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:91)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
27-May-2025 21:28:44.053 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [Hibernate Connection Pool Validation Thread] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.12/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:252)
 java.base@17.0.12/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1672)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
27-May-2025 21:28:44.072 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.deployWAR Déploiement de l'archive [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] de l'application web
27-May-2025 21:28:45.069 INFO [Catalina-utility-1] org.apache.jasper.servlet.TldScanner.scanJars Au moins un fichier JAR a été analysé pour trouver des TLDs mais il n'en contenait pas, le mode "debug" du journal peut être activé pour obtenir une liste complète de JAR scannés sans succès ; éviter d'analyser des JARs inutilement peut améliorer sensiblement le temps de démarrage et le temps de compilation des JSPs
27-May-2025 21:28:45.090 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.deployWAR Le déploiement de l'archive de l'application web [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] s'est terminé en [1 017] ms
27-May-2025 21:28:48.390 INFO [mysql-cj-abandoned-connection-cleanup] org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading Impossible de charger [], ce chargeur de classes a déjà été arrêté
	java.lang.IllegalStateException: Impossible de charger [], ce chargeur de classes a déjà été arrêté
		at org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading(WebappClassLoaderBase.java:1400)
		at org.apache.catalina.loader.WebappClassLoaderBase.getResource(WebappClassLoaderBase.java:987)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.checkThreadContextClassLoader(AbandonedConnectionCleanupThread.java:123)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:90)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:842)
27-May-2025 21:29:21.749 INFO [http-nio-8080-exec-53] org.hibernate.Version.logVersion HHH000412: Hibernate ORM core version 6.4.4.Final
27-May-2025 21:29:21.971 INFO [http-nio-8080-exec-53] org.hibernate.cache.internal.RegionFactoryInitiator.initiateService HHH000026: Second-level cache disabled
27-May-2025 21:29:22.081 WARN [http-nio-8080-exec-53] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.configure HHH10001002: Using built-in connection pool (not intended for production use)
27-May-2025 21:29:22.084 INFO [http-nio-8080-exec-53] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001005: Loaded JDBC driver class: com.mysql.cj.jdbc.Driver
27-May-2025 21:29:22.085 INFO [http-nio-8080-exec-53] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001012: Connecting with JDBC URL [**************************************]
27-May-2025 21:29:22.085 INFO [http-nio-8080-exec-53] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001001: Connection properties: {password=****, user=root}
27-May-2025 21:29:22.085 INFO [http-nio-8080-exec-53] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001003: Autocommit mode: false
27-May-2025 21:29:22.088 INFO [http-nio-8080-exec-53] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl$PooledConnections.<init> HHH10001115: Connection pool size: 20 (min=1)
27-May-2025 21:29:22.240 WARN [http-nio-8080-exec-53] org.hibernate.dialect.Dialect.checkVersion HHH000511: The 5.5.0 version for [org.hibernate.dialect.MySQLDialect] is no longer supported, hence certain features may not work properly. The minimum supported version is 8.0.0. Check the community dialects project for available legacy versions.
27-May-2025 21:29:22.244 WARN [http-nio-8080-exec-53] org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.constructDialect HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
27-May-2025 21:29:23.351 INFO [http-nio-8080-exec-53] org.hibernate.engine.transaction.jta.platform.internal.JtaPlatformInitiator.initiateService HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
27-May-2025 21:29:23.382 INFO [http-nio-8080-exec-53] org.hibernate.resource.transaction.backend.jdbc.internal.DdlTransactionIsolatorNonJtaImpl.getIsolatedConnection HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@49bd2bcc] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
27-May-2025 21:29:23.844 INFO [http-nio-8080-exec-53] dao.StationDAO.findAll Retrieved 10 stations
27-May-2025 21:30:45.226 INFO [Catalina-utility-2] org.apache.catalina.startup.HostConfig.undeploy Retrait de l'application web ayant pour chemin de contexte [/jeetraing]
27-May-2025 21:30:45.228 WARNING [Catalina-utility-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesJdbc L'application web [jeetraing] a enregistré un pilote JDBC [com.mysql.cj.jdbc.Driver], mais ne l'a pas désenregistré avant l'arrêt de l'application.  Pour éviter une fuite de mémoire, le pilote JDBC a été désenregistré de force.
27-May-2025 21:30:45.229 WARNING [Catalina-utility-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [mysql-cj-abandoned-connection-cleanup] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/java.lang.Object.wait(Native Method)
 java.base@17.0.12/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:155)
 com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:91)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
27-May-2025 21:30:45.230 WARNING [Catalina-utility-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [Hibernate Connection Pool Validation Thread] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.12/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:252)
 java.base@17.0.12/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1672)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
27-May-2025 21:30:45.249 INFO [Catalina-utility-2] org.apache.catalina.startup.HostConfig.deployWAR Déploiement de l'archive [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] de l'application web
27-May-2025 21:30:46.245 INFO [Catalina-utility-2] org.apache.jasper.servlet.TldScanner.scanJars Au moins un fichier JAR a été analysé pour trouver des TLDs mais il n'en contenait pas, le mode "debug" du journal peut être activé pour obtenir une liste complète de JAR scannés sans succès ; éviter d'analyser des JARs inutilement peut améliorer sensiblement le temps de démarrage et le temps de compilation des JSPs
27-May-2025 21:30:46.265 INFO [Catalina-utility-2] org.apache.catalina.startup.HostConfig.deployWAR Le déploiement de l'archive de l'application web [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] s'est terminé en [1 016] ms
27-May-2025 21:30:47.255 INFO [mysql-cj-abandoned-connection-cleanup] org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading Impossible de charger [], ce chargeur de classes a déjà été arrêté
	java.lang.IllegalStateException: Impossible de charger [], ce chargeur de classes a déjà été arrêté
		at org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading(WebappClassLoaderBase.java:1400)
		at org.apache.catalina.loader.WebappClassLoaderBase.getResource(WebappClassLoaderBase.java:987)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.checkThreadContextClassLoader(AbandonedConnectionCleanupThread.java:123)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:90)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:842)
27-May-2025 21:31:04.107 INFO [http-nio-8080-exec-60] org.hibernate.Version.logVersion HHH000412: Hibernate ORM core version 6.4.4.Final
27-May-2025 21:31:04.316 INFO [http-nio-8080-exec-60] org.hibernate.cache.internal.RegionFactoryInitiator.initiateService HHH000026: Second-level cache disabled
27-May-2025 21:31:04.427 WARN [http-nio-8080-exec-60] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.configure HHH10001002: Using built-in connection pool (not intended for production use)
27-May-2025 21:31:04.430 INFO [http-nio-8080-exec-60] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001005: Loaded JDBC driver class: com.mysql.cj.jdbc.Driver
27-May-2025 21:31:04.430 INFO [http-nio-8080-exec-60] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001012: Connecting with JDBC URL [**************************************]
27-May-2025 21:31:04.431 INFO [http-nio-8080-exec-60] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001001: Connection properties: {password=****, user=root}
27-May-2025 21:31:04.431 INFO [http-nio-8080-exec-60] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001003: Autocommit mode: false
27-May-2025 21:31:04.434 INFO [http-nio-8080-exec-60] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl$PooledConnections.<init> HHH10001115: Connection pool size: 20 (min=1)
27-May-2025 21:31:04.576 WARN [http-nio-8080-exec-60] org.hibernate.dialect.Dialect.checkVersion HHH000511: The 5.5.0 version for [org.hibernate.dialect.MySQLDialect] is no longer supported, hence certain features may not work properly. The minimum supported version is 8.0.0. Check the community dialects project for available legacy versions.
27-May-2025 21:31:04.580 WARN [http-nio-8080-exec-60] org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.constructDialect HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
27-May-2025 21:31:05.626 INFO [http-nio-8080-exec-60] org.hibernate.engine.transaction.jta.platform.internal.JtaPlatformInitiator.initiateService HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
27-May-2025 21:31:05.652 INFO [http-nio-8080-exec-60] org.hibernate.resource.transaction.backend.jdbc.internal.DdlTransactionIsolatorNonJtaImpl.getIsolatedConnection HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@106408ee] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
27-May-2025 21:31:06.020 INFO [http-nio-8080-exec-60] dao.StationDAO.findAll Retrieved 10 stations
27-May-2025 21:31:17.118 INFO [http-nio-8080-exec-62] dao.StationDAO.findAll Retrieved 10 stations
27-May-2025 21:31:55.737 WARNING [http-nio-8080-exec-50] controller.ConfirmationServlet.doGet Aucun utilisateur connecté dans la session pour enregistrer la réservation
