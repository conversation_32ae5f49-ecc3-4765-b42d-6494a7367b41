29-May-2025 12:30:23.195 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.undeploy Retrait de l'application web ayant pour chemin de contexte [/jeetraing]
29-May-2025 12:30:23.198 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesJdbc L'application web [jeetraing] a enregistré un pilote JDBC [com.mysql.cj.jdbc.Driver], mais ne l'a pas désenregistré avant l'arrêt de l'application.  Pour éviter une fuite de mémoire, le pilote JDBC a été désenregistré de force.
29-May-2025 12:30:23.200 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [mysql-cj-abandoned-connection-cleanup] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/java.lang.Object.wait(Native Method)
 java.base@17.0.12/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:155)
 com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:91)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
29-May-2025 12:30:23.202 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [Hibernate Connection Pool Validation Thread] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.12/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:252)
 java.base@17.0.12/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1672)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
29-May-2025 12:30:23.254 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.deployWAR Déploiement de l'archive [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] de l'application web
29-May-2025 12:30:24.294 INFO [Catalina-utility-1] org.apache.jasper.servlet.TldScanner.scanJars Au moins un fichier JAR a été analysé pour trouver des TLDs mais il n'en contenait pas, le mode "debug" du journal peut être activé pour obtenir une liste complète de JAR scannés sans succès ; éviter d'analyser des JARs inutilement peut améliorer sensiblement le temps de démarrage et le temps de compilation des JSPs
29-May-2025 12:30:24.315 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.deployWAR Le déploiement de l'archive de l'application web [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] s'est terminé en [1 060] ms
29-May-2025 12:30:24.988 INFO [mysql-cj-abandoned-connection-cleanup] org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading Impossible de charger [], ce chargeur de classes a déjà été arrêté
	java.lang.IllegalStateException: Impossible de charger [], ce chargeur de classes a déjà été arrêté
		at org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading(WebappClassLoaderBase.java:1400)
		at org.apache.catalina.loader.WebappClassLoaderBase.getResource(WebappClassLoaderBase.java:987)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.checkThreadContextClassLoader(AbandonedConnectionCleanupThread.java:123)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:90)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:842)
29-May-2025 12:31:39.394 INFO [http-nio-8080-exec-170] org.hibernate.Version.logVersion HHH000412: Hibernate ORM core version 6.4.4.Final
29-May-2025 12:31:39.781 INFO [http-nio-8080-exec-170] org.hibernate.cache.internal.RegionFactoryInitiator.initiateService HHH000026: Second-level cache disabled
29-May-2025 12:31:39.979 WARN [http-nio-8080-exec-170] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.configure HHH10001002: Using built-in connection pool (not intended for production use)
29-May-2025 12:31:39.985 INFO [http-nio-8080-exec-170] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001005: Loaded JDBC driver class: com.mysql.cj.jdbc.Driver
29-May-2025 12:31:39.985 INFO [http-nio-8080-exec-170] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001012: Connecting with JDBC URL [**************************************]
29-May-2025 12:31:39.985 INFO [http-nio-8080-exec-170] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001001: Connection properties: {password=****, user=root}
29-May-2025 12:31:39.985 INFO [http-nio-8080-exec-170] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001003: Autocommit mode: false
29-May-2025 12:31:39.990 INFO [http-nio-8080-exec-170] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl$PooledConnections.<init> HHH10001115: Connection pool size: 20 (min=1)
29-May-2025 12:31:40.267 WARN [http-nio-8080-exec-170] org.hibernate.dialect.Dialect.checkVersion HHH000511: The 5.5.0 version for [org.hibernate.dialect.MySQLDialect] is no longer supported, hence certain features may not work properly. The minimum supported version is 8.0.0. Check the community dialects project for available legacy versions.
29-May-2025 12:31:40.274 WARN [http-nio-8080-exec-170] org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.constructDialect HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
29-May-2025 12:31:42.084 INFO [http-nio-8080-exec-170] org.hibernate.engine.transaction.jta.platform.internal.JtaPlatformInitiator.initiateService HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
29-May-2025 12:31:42.127 INFO [http-nio-8080-exec-170] org.hibernate.resource.transaction.backend.jdbc.internal.DdlTransactionIsolatorNonJtaImpl.getIsolatedConnection HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@2d20d7fb] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
29-May-2025 12:33:54.555 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.undeploy Retrait de l'application web ayant pour chemin de contexte [/jeetraing]
29-May-2025 12:33:54.556 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesJdbc L'application web [jeetraing] a enregistré un pilote JDBC [com.mysql.cj.jdbc.Driver], mais ne l'a pas désenregistré avant l'arrêt de l'application.  Pour éviter une fuite de mémoire, le pilote JDBC a été désenregistré de force.
29-May-2025 12:33:54.558 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [mysql-cj-abandoned-connection-cleanup] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/java.lang.Object.wait(Native Method)
 java.base@17.0.12/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:155)
 com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:91)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
29-May-2025 12:33:54.559 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [Hibernate Connection Pool Validation Thread] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.12/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:252)
 java.base@17.0.12/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1672)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
29-May-2025 12:33:54.580 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.deployWAR Déploiement de l'archive [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] de l'application web
29-May-2025 12:33:55.231 INFO [mysql-cj-abandoned-connection-cleanup] org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading Impossible de charger [], ce chargeur de classes a déjà été arrêté
	java.lang.IllegalStateException: Impossible de charger [], ce chargeur de classes a déjà été arrêté
		at org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading(WebappClassLoaderBase.java:1400)
		at org.apache.catalina.loader.WebappClassLoaderBase.getResource(WebappClassLoaderBase.java:987)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.checkThreadContextClassLoader(AbandonedConnectionCleanupThread.java:123)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:90)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:842)
29-May-2025 12:33:55.649 INFO [Catalina-utility-1] org.apache.jasper.servlet.TldScanner.scanJars Au moins un fichier JAR a été analysé pour trouver des TLDs mais il n'en contenait pas, le mode "debug" du journal peut être activé pour obtenir une liste complète de JAR scannés sans succès ; éviter d'analyser des JARs inutilement peut améliorer sensiblement le temps de démarrage et le temps de compilation des JSPs
29-May-2025 12:33:55.668 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.deployWAR Le déploiement de l'archive de l'application web [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] s'est terminé en [1 088] ms
29-May-2025 12:35:45.141 INFO [http-nio-8080-exec-173] org.hibernate.Version.logVersion HHH000412: Hibernate ORM core version 6.4.4.Final
29-May-2025 12:35:45.489 INFO [http-nio-8080-exec-173] org.hibernate.cache.internal.RegionFactoryInitiator.initiateService HHH000026: Second-level cache disabled
29-May-2025 12:35:45.668 WARN [http-nio-8080-exec-173] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.configure HHH10001002: Using built-in connection pool (not intended for production use)
29-May-2025 12:35:45.672 INFO [http-nio-8080-exec-173] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001005: Loaded JDBC driver class: com.mysql.cj.jdbc.Driver
29-May-2025 12:35:45.673 INFO [http-nio-8080-exec-173] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001012: Connecting with JDBC URL [**************************************]
29-May-2025 12:35:45.673 INFO [http-nio-8080-exec-173] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001001: Connection properties: {password=****, user=root}
29-May-2025 12:35:45.673 INFO [http-nio-8080-exec-173] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001003: Autocommit mode: false
29-May-2025 12:35:45.678 INFO [http-nio-8080-exec-173] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl$PooledConnections.<init> HHH10001115: Connection pool size: 20 (min=1)
29-May-2025 12:35:45.935 WARN [http-nio-8080-exec-173] org.hibernate.dialect.Dialect.checkVersion HHH000511: The 5.5.0 version for [org.hibernate.dialect.MySQLDialect] is no longer supported, hence certain features may not work properly. The minimum supported version is 8.0.0. Check the community dialects project for available legacy versions.
29-May-2025 12:35:45.943 WARN [http-nio-8080-exec-173] org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.constructDialect HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
29-May-2025 12:35:47.621 INFO [http-nio-8080-exec-173] org.hibernate.engine.transaction.jta.platform.internal.JtaPlatformInitiator.initiateService HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
29-May-2025 12:35:47.662 INFO [http-nio-8080-exec-173] org.hibernate.resource.transaction.backend.jdbc.internal.DdlTransactionIsolatorNonJtaImpl.getIsolatedConnection HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@18cd208d] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
29-May-2025 12:35:48.414 INFO [http-nio-8080-exec-173] controller.LoginServlet.doPost Utilisateur connectï¿½ avec succï¿½s: <EMAIL>
29-May-2025 12:35:48.421 INFO [http-nio-8080-exec-167] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
29-May-2025 12:36:15.427 INFO [http-nio-8080-exec-181] dao.StationDAO.findAll Retrieved 10 stations
29-May-2025 12:36:26.298 INFO [http-nio-8080-exec-172] dao.StationDAO.findAll Retrieved 10 stations
29-May-2025 12:36:46.694 INFO [http-nio-8080-exec-184] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
29-May-2025 12:36:56.187 INFO [http-nio-8080-exec-179] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
29-May-2025 12:37:01.395 INFO [http-nio-8080-exec-182] controller.ReservationEvolutionServlet.doGet doGet - Evolution Data: [[Ljava.lang.Object;@6ee8e37d, [Ljava.lang.Object;@5f8ef1dc]
29-May-2025 12:37:01.396 INFO [http-nio-8080-exec-182] controller.ReservationEvolutionServlet.doGet doGet - Labels: [2025-04, 2025-05]
29-May-2025 12:37:01.397 INFO [http-nio-8080-exec-182] controller.ReservationEvolutionServlet.doGet doGet - Data: [0, 6]
29-May-2025 12:37:01.397 INFO [http-nio-8080-exec-182] controller.ReservationEvolutionServlet.doGet doGet - Attributes set - evolutionData: [[Ljava.lang.Object;@6ee8e37d, [Ljava.lang.Object;@5f8ef1dc]
29-May-2025 12:37:24.207 INFO [http-nio-8080-exec-178] dao.StationDAO.findAll Retrieved 10 stations
29-May-2025 12:37:26.876 INFO [http-nio-8080-exec-180] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
29-May-2025 12:37:32.472 INFO [http-nio-8080-exec-181] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
29-May-2025 12:37:34.208 INFO [http-nio-8080-exec-185] controller.ReservationEvolutionServlet.doGet doGet - Evolution Data: [[Ljava.lang.Object;@4b284981, [Ljava.lang.Object;@6f8b35c1]
29-May-2025 12:37:34.208 INFO [http-nio-8080-exec-185] controller.ReservationEvolutionServlet.doGet doGet - Labels: [2025-04, 2025-05]
29-May-2025 12:37:34.209 INFO [http-nio-8080-exec-185] controller.ReservationEvolutionServlet.doGet doGet - Data: [0, 6]
29-May-2025 12:37:34.209 INFO [http-nio-8080-exec-185] controller.ReservationEvolutionServlet.doGet doGet - Attributes set - evolutionData: [[Ljava.lang.Object;@4b284981, [Ljava.lang.Object;@6f8b35c1]
29-May-2025 12:37:43.773 INFO [http-nio-8080-exec-186] dao.StationDAO.findAll Retrieved 10 stations
29-May-2025 12:38:06.406 INFO [http-nio-8080-exec-180] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
29-May-2025 12:38:40.517 INFO [http-nio-8080-exec-186] dao.StationDAO.findAll Retrieved 10 stations
29-May-2025 12:38:45.670 INFO [http-nio-8080-exec-187] dao.StationDAO.findAll Retrieved 10 stations
29-May-2025 12:39:26.609 INFO [http-nio-8080-exec-179] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisé'
29-May-2025 12:39:26.707 INFO [http-nio-8080-exec-185] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
29-May-2025 12:39:40.683 INFO [http-nio-8080-exec-186] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
29-May-2025 12:40:02.864 INFO [http-nio-8080-exec-184] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
29-May-2025 12:40:20.121 INFO [http-nio-8080-exec-180] dao.StationDAO.findAll Retrieved 10 stations
29-May-2025 12:49:58.936 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.undeploy Retrait de l'application web ayant pour chemin de contexte [/jeetraing]
29-May-2025 12:49:58.938 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesJdbc L'application web [jeetraing] a enregistré un pilote JDBC [com.mysql.cj.jdbc.Driver], mais ne l'a pas désenregistré avant l'arrêt de l'application.  Pour éviter une fuite de mémoire, le pilote JDBC a été désenregistré de force.
29-May-2025 12:49:58.940 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [mysql-cj-abandoned-connection-cleanup] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/java.lang.Object.wait(Native Method)
 java.base@17.0.12/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:155)
 com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:91)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
29-May-2025 12:49:58.941 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [Hibernate Connection Pool Validation Thread] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.12/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:252)
 java.base@17.0.12/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1672)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
29-May-2025 12:49:58.992 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.deployWAR Déploiement de l'archive [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] de l'application web
29-May-2025 12:49:59.981 INFO [Catalina-utility-1] org.apache.jasper.servlet.TldScanner.scanJars Au moins un fichier JAR a été analysé pour trouver des TLDs mais il n'en contenait pas, le mode "debug" du journal peut être activé pour obtenir une liste complète de JAR scannés sans succès ; éviter d'analyser des JARs inutilement peut améliorer sensiblement le temps de démarrage et le temps de compilation des JSPs
29-May-2025 12:49:59.995 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.deployWAR Le déploiement de l'archive de l'application web [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] s'est terminé en [1 003] ms
29-May-2025 12:50:02.183 INFO [mysql-cj-abandoned-connection-cleanup] org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading Impossible de charger [], ce chargeur de classes a déjà été arrêté
	java.lang.IllegalStateException: Impossible de charger [], ce chargeur de classes a déjà été arrêté
		at org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading(WebappClassLoaderBase.java:1400)
		at org.apache.catalina.loader.WebappClassLoaderBase.getResource(WebappClassLoaderBase.java:987)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.checkThreadContextClassLoader(AbandonedConnectionCleanupThread.java:123)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:90)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:842)
29-May-2025 12:50:20.767 INFO [http-nio-8080-exec-179] org.hibernate.Version.logVersion HHH000412: Hibernate ORM core version 6.4.4.Final
29-May-2025 12:50:20.979 INFO [http-nio-8080-exec-179] org.hibernate.cache.internal.RegionFactoryInitiator.initiateService HHH000026: Second-level cache disabled
29-May-2025 12:50:21.088 WARN [http-nio-8080-exec-179] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.configure HHH10001002: Using built-in connection pool (not intended for production use)
29-May-2025 12:50:21.092 INFO [http-nio-8080-exec-179] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001005: Loaded JDBC driver class: com.mysql.cj.jdbc.Driver
29-May-2025 12:50:21.092 INFO [http-nio-8080-exec-179] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001012: Connecting with JDBC URL [**************************************]
29-May-2025 12:50:21.092 INFO [http-nio-8080-exec-179] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001001: Connection properties: {password=****, user=root}
29-May-2025 12:50:21.093 INFO [http-nio-8080-exec-179] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001003: Autocommit mode: false
29-May-2025 12:50:21.095 INFO [http-nio-8080-exec-179] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl$PooledConnections.<init> HHH10001115: Connection pool size: 20 (min=1)
29-May-2025 12:50:21.244 WARN [http-nio-8080-exec-179] org.hibernate.dialect.Dialect.checkVersion HHH000511: The 5.5.0 version for [org.hibernate.dialect.MySQLDialect] is no longer supported, hence certain features may not work properly. The minimum supported version is 8.0.0. Check the community dialects project for available legacy versions.
29-May-2025 12:50:21.248 WARN [http-nio-8080-exec-179] org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.constructDialect HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
29-May-2025 12:50:22.293 INFO [http-nio-8080-exec-179] org.hibernate.engine.transaction.jta.platform.internal.JtaPlatformInitiator.initiateService HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
29-May-2025 12:50:22.319 INFO [http-nio-8080-exec-179] org.hibernate.resource.transaction.backend.jdbc.internal.DdlTransactionIsolatorNonJtaImpl.getIsolatedConnection HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@2ecd186f] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
29-May-2025 12:50:22.688 INFO [http-nio-8080-exec-179] dao.StationDAO.findAll Retrieved 10 stations
29-May-2025 12:52:20.169 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.undeploy Retrait de l'application web ayant pour chemin de contexte [/jeetraing]
29-May-2025 12:52:20.171 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesJdbc L'application web [jeetraing] a enregistré un pilote JDBC [com.mysql.cj.jdbc.Driver], mais ne l'a pas désenregistré avant l'arrêt de l'application.  Pour éviter une fuite de mémoire, le pilote JDBC a été désenregistré de force.
29-May-2025 12:52:20.172 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [mysql-cj-abandoned-connection-cleanup] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/java.lang.Object.wait(Native Method)
 java.base@17.0.12/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:155)
 com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:91)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
29-May-2025 12:52:20.173 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [Hibernate Connection Pool Validation Thread] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.12/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:252)
 java.base@17.0.12/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1672)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
29-May-2025 12:52:20.194 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.deployWAR Déploiement de l'archive [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] de l'application web
29-May-2025 12:52:21.153 INFO [Catalina-utility-1] org.apache.jasper.servlet.TldScanner.scanJars Au moins un fichier JAR a été analysé pour trouver des TLDs mais il n'en contenait pas, le mode "debug" du journal peut être activé pour obtenir une liste complète de JAR scannés sans succès ; éviter d'analyser des JARs inutilement peut améliorer sensiblement le temps de démarrage et le temps de compilation des JSPs
29-May-2025 12:52:21.168 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.deployWAR Le déploiement de l'archive de l'application web [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] s'est terminé en [974] ms
29-May-2025 12:52:21.309 INFO [mysql-cj-abandoned-connection-cleanup] org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading Impossible de charger [], ce chargeur de classes a déjà été arrêté
	java.lang.IllegalStateException: Impossible de charger [], ce chargeur de classes a déjà été arrêté
		at org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading(WebappClassLoaderBase.java:1400)
		at org.apache.catalina.loader.WebappClassLoaderBase.getResource(WebappClassLoaderBase.java:987)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.checkThreadContextClassLoader(AbandonedConnectionCleanupThread.java:123)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:90)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:842)
29-May-2025 12:53:15.388 INFO [http-nio-8080-exec-181] org.hibernate.Version.logVersion HHH000412: Hibernate ORM core version 6.4.4.Final
29-May-2025 12:53:15.597 INFO [http-nio-8080-exec-181] org.hibernate.cache.internal.RegionFactoryInitiator.initiateService HHH000026: Second-level cache disabled
29-May-2025 12:53:15.706 WARN [http-nio-8080-exec-181] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.configure HHH10001002: Using built-in connection pool (not intended for production use)
29-May-2025 12:53:15.709 INFO [http-nio-8080-exec-181] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001005: Loaded JDBC driver class: com.mysql.cj.jdbc.Driver
29-May-2025 12:53:15.709 INFO [http-nio-8080-exec-181] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001012: Connecting with JDBC URL [**************************************]
29-May-2025 12:53:15.709 INFO [http-nio-8080-exec-181] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001001: Connection properties: {password=****, user=root}
29-May-2025 12:53:15.710 INFO [http-nio-8080-exec-181] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001003: Autocommit mode: false
29-May-2025 12:53:15.713 INFO [http-nio-8080-exec-181] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl$PooledConnections.<init> HHH10001115: Connection pool size: 20 (min=1)
29-May-2025 12:53:15.869 WARN [http-nio-8080-exec-181] org.hibernate.dialect.Dialect.checkVersion HHH000511: The 5.5.0 version for [org.hibernate.dialect.MySQLDialect] is no longer supported, hence certain features may not work properly. The minimum supported version is 8.0.0. Check the community dialects project for available legacy versions.
29-May-2025 12:53:15.873 WARN [http-nio-8080-exec-181] org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.constructDialect HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
29-May-2025 12:53:16.924 INFO [http-nio-8080-exec-181] org.hibernate.engine.transaction.jta.platform.internal.JtaPlatformInitiator.initiateService HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
29-May-2025 12:53:16.950 INFO [http-nio-8080-exec-181] org.hibernate.resource.transaction.backend.jdbc.internal.DdlTransactionIsolatorNonJtaImpl.getIsolatedConnection HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@45b4cdc] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
29-May-2025 12:53:17.051 WARNING [http-nio-8080-exec-181] controller.AdminUserManagementServlet.doGet Utilisateur non connecté lors de l'accès à /admin/users
29-May-2025 12:53:28.869 INFO [http-nio-8080-exec-193] controller.LoginServlet.doPost Tentative de connexion ï¿½chouï¿½e pour l'email: <EMAIL> (mot de passe incorrect)
29-May-2025 12:53:40.264 INFO [http-nio-8080-exec-188] controller.LoginServlet.doPost Utilisateur connectï¿½ avec succï¿½s: <EMAIL>
29-May-2025 12:53:40.269 INFO [http-nio-8080-exec-185] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
29-May-2025 12:53:47.852 INFO [http-nio-8080-exec-192] controller.ReservationEvolutionServlet.doGet doGet - Evolution Data: [[Ljava.lang.Object;@c32e492, [Ljava.lang.Object;@c3683aa]
29-May-2025 12:53:47.853 INFO [http-nio-8080-exec-192] controller.ReservationEvolutionServlet.doGet doGet - Labels: [2025-04, 2025-05]
29-May-2025 12:53:47.853 INFO [http-nio-8080-exec-192] controller.ReservationEvolutionServlet.doGet doGet - Data: [0, 6]
29-May-2025 12:53:47.853 INFO [http-nio-8080-exec-192] controller.ReservationEvolutionServlet.doGet doGet - Attributes set - evolutionData: [[Ljava.lang.Object;@c32e492, [Ljava.lang.Object;@c3683aa]
29-May-2025 12:54:08.214 INFO [http-nio-8080-exec-195] controller.ReservationEvolutionServlet.doPost doPost - Evolution Data: [[Ljava.lang.Object;@4b39fbb5, [Ljava.lang.Object;@e3b7cb6]
29-May-2025 12:54:08.214 INFO [http-nio-8080-exec-195] controller.ReservationEvolutionServlet.doPost doPost - Labels: [2025-04, 2025-05]
29-May-2025 12:54:08.214 INFO [http-nio-8080-exec-195] controller.ReservationEvolutionServlet.doPost doPost - Data: [0, 6]
29-May-2025 12:54:08.215 INFO [http-nio-8080-exec-195] controller.ReservationEvolutionServlet.doPost doPost - Attributes set - evolutionData: [[Ljava.lang.Object;@4b39fbb5, [Ljava.lang.Object;@e3b7cb6]
29-May-2025 12:54:14.218 INFO [http-nio-8080-exec-193] controller.ReservationEvolutionServlet.doPost doPost - Evolution Data: [[Ljava.lang.Object;@34dbed51, [Ljava.lang.Object;@4d2d78a4]
29-May-2025 12:54:14.219 INFO [http-nio-8080-exec-193] controller.ReservationEvolutionServlet.doPost doPost - Labels: [2025-04, 2025-05]
29-May-2025 12:54:14.219 INFO [http-nio-8080-exec-193] controller.ReservationEvolutionServlet.doPost doPost - Data: [0, 6]
29-May-2025 12:54:14.219 INFO [http-nio-8080-exec-193] controller.ReservationEvolutionServlet.doPost doPost - Attributes set - evolutionData: [[Ljava.lang.Object;@34dbed51, [Ljava.lang.Object;@4d2d78a4]
29-May-2025 12:54:27.768 INFO [http-nio-8080-exec-196] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
29-May-2025 12:54:47.927 INFO [http-nio-8080-exec-199] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
29-May-2025 12:54:50.700 INFO [http-nio-8080-exec-195] dao.StationDAO.findAll Retrieved 10 stations
29-May-2025 12:55:05.241 INFO [http-nio-8080-exec-193] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
29-May-2025 12:55:10.334 INFO [http-nio-8080-exec-194] dao.StationDAO.findAll Retrieved 10 stations
29-May-2025 12:55:43.351 INFO [http-nio-8080-exec-202] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
29-May-2025 12:55:48.823 INFO [http-nio-8080-exec-195] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
29-May-2025 12:55:52.489 INFO [http-nio-8080-exec-196] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
29-May-2025 12:55:54.484 INFO [http-nio-8080-exec-194] controller.ReservationEvolutionServlet.doGet doGet - Evolution Data: [[Ljava.lang.Object;@264f066d, [Ljava.lang.Object;@3d2a0226]
29-May-2025 12:55:54.484 INFO [http-nio-8080-exec-194] controller.ReservationEvolutionServlet.doGet doGet - Labels: [2025-04, 2025-05]
29-May-2025 12:55:54.484 INFO [http-nio-8080-exec-194] controller.ReservationEvolutionServlet.doGet doGet - Data: [0, 6]
29-May-2025 12:55:54.485 INFO [http-nio-8080-exec-194] controller.ReservationEvolutionServlet.doGet doGet - Attributes set - evolutionData: [[Ljava.lang.Object;@264f066d, [Ljava.lang.Object;@3d2a0226]
29-May-2025 12:56:00.119 INFO [http-nio-8080-exec-197] dao.StationDAO.findAll Retrieved 10 stations
29-May-2025 12:56:04.679 INFO [http-nio-8080-exec-198] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
29-May-2025 12:56:07.100 INFO [http-nio-8080-exec-202] dao.StationDAO.findAll Retrieved 10 stations
29-May-2025 12:56:11.402 INFO [http-nio-8080-exec-199] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
29-May-2025 12:56:34.899 INFO [http-nio-8080-exec-196] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
29-May-2025 12:56:45.168 INFO [http-nio-8080-exec-201] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
29-May-2025 12:56:49.439 INFO [http-nio-8080-exec-202] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
29-May-2025 12:56:53.190 WARNING [http-nio-8080-exec-195] controller.AdminDashboardServlet.doGet Utilisateur non connectÃ© lors de l'accÃ¨s au dashboard admin
29-May-2025 12:57:07.060 INFO [http-nio-8080-exec-194] controller.LoginServlet.doPost Utilisateur connectï¿½ avec succï¿½s: <EMAIL>
29-May-2025 12:57:07.066 INFO [http-nio-8080-exec-197] dao.StationDAO.findAll Retrieved 10 stations
29-May-2025 12:57:22.460 INFO [http-nio-8080-exec-201] dao.StationDAO.findAll Retrieved 10 stations
29-May-2025 12:58:24.201 INFO [http-nio-8080-exec-194] dao.StationDAO.findAll Retrieved 10 stations
29-May-2025 12:58:36.385 INFO [http-nio-8080-exec-202] controller.LoginServlet.doPost Utilisateur connectï¿½ avec succï¿½s: <EMAIL>
29-May-2025 12:58:36.390 INFO [http-nio-8080-exec-199] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
29-May-2025 12:59:13.704 INFO [http-nio-8080-exec-194] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
29-May-2025 12:59:25.029 INFO [http-nio-8080-exec-201] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
29-May-2025 12:59:43.450 INFO [http-nio-8080-exec-195] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
29-May-2025 13:00:29.880 INFO [http-nio-8080-exec-201] controller.LoginServlet.doPost Utilisateur connectï¿½ avec succï¿½s: <EMAIL>
29-May-2025 13:00:29.887 INFO [http-nio-8080-exec-198] dao.StationDAO.findAll Retrieved 10 stations
29-May-2025 13:00:54.698 INFO [http-nio-8080-exec-195] dao.StationDAO.findAll Retrieved 10 stations
29-May-2025 13:02:28.850 INFO [http-nio-8080-exec-199] dao.StationDAO.findAll Retrieved 10 stations
29-May-2025 13:02:40.900 INFO [http-nio-8080-exec-195] dao.StationDAO.findAll Retrieved 10 stations
29-May-2025 13:03:07.207 INFO [http-nio-8080-exec-194] dao.StationDAO.findAll Retrieved 10 stations
29-May-2025 13:05:15.624 INFO [http-nio-8080-exec-195] dao.StationDAO.findAll Retrieved 10 stations
29-May-2025 13:10:08.887 INFO [http-nio-8080-exec-194] dao.StationDAO.findAll Retrieved 10 stations
