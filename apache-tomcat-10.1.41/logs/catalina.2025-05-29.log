29-May-2025 12:30:23.195 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.undeploy Retrait de l'application web ayant pour chemin de contexte [/jeetraing]
29-May-2025 12:30:23.198 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesJdbc L'application web [jeetraing] a enregistré un pilote JDBC [com.mysql.cj.jdbc.Driver], mais ne l'a pas désenregistré avant l'arrêt de l'application.  Pour éviter une fuite de mémoire, le pilote JDBC a été désenregistré de force.
29-May-2025 12:30:23.200 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [mysql-cj-abandoned-connection-cleanup] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/java.lang.Object.wait(Native Method)
 java.base@17.0.12/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:155)
 com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:91)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
29-May-2025 12:30:23.202 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [Hibernate Connection Pool Validation Thread] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.12/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:252)
 java.base@17.0.12/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1672)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
29-May-2025 12:30:23.254 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.deployWAR Déploiement de l'archive [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] de l'application web
29-May-2025 12:30:24.294 INFO [Catalina-utility-1] org.apache.jasper.servlet.TldScanner.scanJars Au moins un fichier JAR a été analysé pour trouver des TLDs mais il n'en contenait pas, le mode "debug" du journal peut être activé pour obtenir une liste complète de JAR scannés sans succès ; éviter d'analyser des JARs inutilement peut améliorer sensiblement le temps de démarrage et le temps de compilation des JSPs
29-May-2025 12:30:24.315 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.deployWAR Le déploiement de l'archive de l'application web [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] s'est terminé en [1 060] ms
29-May-2025 12:30:24.988 INFO [mysql-cj-abandoned-connection-cleanup] org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading Impossible de charger [], ce chargeur de classes a déjà été arrêté
	java.lang.IllegalStateException: Impossible de charger [], ce chargeur de classes a déjà été arrêté
		at org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading(WebappClassLoaderBase.java:1400)
		at org.apache.catalina.loader.WebappClassLoaderBase.getResource(WebappClassLoaderBase.java:987)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.checkThreadContextClassLoader(AbandonedConnectionCleanupThread.java:123)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:90)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:842)
29-May-2025 12:31:39.394 INFO [http-nio-8080-exec-170] org.hibernate.Version.logVersion HHH000412: Hibernate ORM core version 6.4.4.Final
29-May-2025 12:31:39.781 INFO [http-nio-8080-exec-170] org.hibernate.cache.internal.RegionFactoryInitiator.initiateService HHH000026: Second-level cache disabled
29-May-2025 12:31:39.979 WARN [http-nio-8080-exec-170] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.configure HHH10001002: Using built-in connection pool (not intended for production use)
29-May-2025 12:31:39.985 INFO [http-nio-8080-exec-170] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001005: Loaded JDBC driver class: com.mysql.cj.jdbc.Driver
29-May-2025 12:31:39.985 INFO [http-nio-8080-exec-170] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001012: Connecting with JDBC URL [**************************************]
29-May-2025 12:31:39.985 INFO [http-nio-8080-exec-170] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001001: Connection properties: {password=****, user=root}
29-May-2025 12:31:39.985 INFO [http-nio-8080-exec-170] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001003: Autocommit mode: false
29-May-2025 12:31:39.990 INFO [http-nio-8080-exec-170] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl$PooledConnections.<init> HHH10001115: Connection pool size: 20 (min=1)
29-May-2025 12:31:40.267 WARN [http-nio-8080-exec-170] org.hibernate.dialect.Dialect.checkVersion HHH000511: The 5.5.0 version for [org.hibernate.dialect.MySQLDialect] is no longer supported, hence certain features may not work properly. The minimum supported version is 8.0.0. Check the community dialects project for available legacy versions.
29-May-2025 12:31:40.274 WARN [http-nio-8080-exec-170] org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.constructDialect HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
29-May-2025 12:31:42.084 INFO [http-nio-8080-exec-170] org.hibernate.engine.transaction.jta.platform.internal.JtaPlatformInitiator.initiateService HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
29-May-2025 12:31:42.127 INFO [http-nio-8080-exec-170] org.hibernate.resource.transaction.backend.jdbc.internal.DdlTransactionIsolatorNonJtaImpl.getIsolatedConnection HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@2d20d7fb] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
29-May-2025 12:33:54.555 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.undeploy Retrait de l'application web ayant pour chemin de contexte [/jeetraing]
29-May-2025 12:33:54.556 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesJdbc L'application web [jeetraing] a enregistré un pilote JDBC [com.mysql.cj.jdbc.Driver], mais ne l'a pas désenregistré avant l'arrêt de l'application.  Pour éviter une fuite de mémoire, le pilote JDBC a été désenregistré de force.
29-May-2025 12:33:54.558 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [mysql-cj-abandoned-connection-cleanup] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/java.lang.Object.wait(Native Method)
 java.base@17.0.12/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:155)
 com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:91)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
29-May-2025 12:33:54.559 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [Hibernate Connection Pool Validation Thread] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.12/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:252)
 java.base@17.0.12/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1672)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
29-May-2025 12:33:54.580 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.deployWAR Déploiement de l'archive [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] de l'application web
29-May-2025 12:33:55.231 INFO [mysql-cj-abandoned-connection-cleanup] org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading Impossible de charger [], ce chargeur de classes a déjà été arrêté
	java.lang.IllegalStateException: Impossible de charger [], ce chargeur de classes a déjà été arrêté
		at org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading(WebappClassLoaderBase.java:1400)
		at org.apache.catalina.loader.WebappClassLoaderBase.getResource(WebappClassLoaderBase.java:987)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.checkThreadContextClassLoader(AbandonedConnectionCleanupThread.java:123)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:90)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:842)
29-May-2025 12:33:55.649 INFO [Catalina-utility-1] org.apache.jasper.servlet.TldScanner.scanJars Au moins un fichier JAR a été analysé pour trouver des TLDs mais il n'en contenait pas, le mode "debug" du journal peut être activé pour obtenir une liste complète de JAR scannés sans succès ; éviter d'analyser des JARs inutilement peut améliorer sensiblement le temps de démarrage et le temps de compilation des JSPs
29-May-2025 12:33:55.668 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.deployWAR Le déploiement de l'archive de l'application web [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] s'est terminé en [1 088] ms
29-May-2025 12:35:45.141 INFO [http-nio-8080-exec-173] org.hibernate.Version.logVersion HHH000412: Hibernate ORM core version 6.4.4.Final
29-May-2025 12:35:45.489 INFO [http-nio-8080-exec-173] org.hibernate.cache.internal.RegionFactoryInitiator.initiateService HHH000026: Second-level cache disabled
29-May-2025 12:35:45.668 WARN [http-nio-8080-exec-173] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.configure HHH10001002: Using built-in connection pool (not intended for production use)
29-May-2025 12:35:45.672 INFO [http-nio-8080-exec-173] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001005: Loaded JDBC driver class: com.mysql.cj.jdbc.Driver
29-May-2025 12:35:45.673 INFO [http-nio-8080-exec-173] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001012: Connecting with JDBC URL [**************************************]
29-May-2025 12:35:45.673 INFO [http-nio-8080-exec-173] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001001: Connection properties: {password=****, user=root}
29-May-2025 12:35:45.673 INFO [http-nio-8080-exec-173] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001003: Autocommit mode: false
29-May-2025 12:35:45.678 INFO [http-nio-8080-exec-173] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl$PooledConnections.<init> HHH10001115: Connection pool size: 20 (min=1)
29-May-2025 12:35:45.935 WARN [http-nio-8080-exec-173] org.hibernate.dialect.Dialect.checkVersion HHH000511: The 5.5.0 version for [org.hibernate.dialect.MySQLDialect] is no longer supported, hence certain features may not work properly. The minimum supported version is 8.0.0. Check the community dialects project for available legacy versions.
29-May-2025 12:35:45.943 WARN [http-nio-8080-exec-173] org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.constructDialect HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
29-May-2025 12:35:47.621 INFO [http-nio-8080-exec-173] org.hibernate.engine.transaction.jta.platform.internal.JtaPlatformInitiator.initiateService HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
29-May-2025 12:35:47.662 INFO [http-nio-8080-exec-173] org.hibernate.resource.transaction.backend.jdbc.internal.DdlTransactionIsolatorNonJtaImpl.getIsolatedConnection HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@18cd208d] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
29-May-2025 12:35:48.414 INFO [http-nio-8080-exec-173] controller.LoginServlet.doPost Utilisateur connectï¿½ avec succï¿½s: <EMAIL>
29-May-2025 12:35:48.421 INFO [http-nio-8080-exec-167] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
29-May-2025 12:36:15.427 INFO [http-nio-8080-exec-181] dao.StationDAO.findAll Retrieved 10 stations
29-May-2025 12:36:26.298 INFO [http-nio-8080-exec-172] dao.StationDAO.findAll Retrieved 10 stations
29-May-2025 12:36:46.694 INFO [http-nio-8080-exec-184] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
29-May-2025 12:36:56.187 INFO [http-nio-8080-exec-179] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
29-May-2025 12:37:01.395 INFO [http-nio-8080-exec-182] controller.ReservationEvolutionServlet.doGet doGet - Evolution Data: [[Ljava.lang.Object;@6ee8e37d, [Ljava.lang.Object;@5f8ef1dc]
29-May-2025 12:37:01.396 INFO [http-nio-8080-exec-182] controller.ReservationEvolutionServlet.doGet doGet - Labels: [2025-04, 2025-05]
29-May-2025 12:37:01.397 INFO [http-nio-8080-exec-182] controller.ReservationEvolutionServlet.doGet doGet - Data: [0, 6]
29-May-2025 12:37:01.397 INFO [http-nio-8080-exec-182] controller.ReservationEvolutionServlet.doGet doGet - Attributes set - evolutionData: [[Ljava.lang.Object;@6ee8e37d, [Ljava.lang.Object;@5f8ef1dc]
29-May-2025 12:37:24.207 INFO [http-nio-8080-exec-178] dao.StationDAO.findAll Retrieved 10 stations
29-May-2025 12:37:26.876 INFO [http-nio-8080-exec-180] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
29-May-2025 12:37:32.472 INFO [http-nio-8080-exec-181] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
29-May-2025 12:37:34.208 INFO [http-nio-8080-exec-185] controller.ReservationEvolutionServlet.doGet doGet - Evolution Data: [[Ljava.lang.Object;@4b284981, [Ljava.lang.Object;@6f8b35c1]
29-May-2025 12:37:34.208 INFO [http-nio-8080-exec-185] controller.ReservationEvolutionServlet.doGet doGet - Labels: [2025-04, 2025-05]
29-May-2025 12:37:34.209 INFO [http-nio-8080-exec-185] controller.ReservationEvolutionServlet.doGet doGet - Data: [0, 6]
29-May-2025 12:37:34.209 INFO [http-nio-8080-exec-185] controller.ReservationEvolutionServlet.doGet doGet - Attributes set - evolutionData: [[Ljava.lang.Object;@4b284981, [Ljava.lang.Object;@6f8b35c1]
29-May-2025 12:37:43.773 INFO [http-nio-8080-exec-186] dao.StationDAO.findAll Retrieved 10 stations
29-May-2025 12:38:06.406 INFO [http-nio-8080-exec-180] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
29-May-2025 12:38:40.517 INFO [http-nio-8080-exec-186] dao.StationDAO.findAll Retrieved 10 stations
29-May-2025 12:38:45.670 INFO [http-nio-8080-exec-187] dao.StationDAO.findAll Retrieved 10 stations
29-May-2025 12:39:26.609 INFO [http-nio-8080-exec-179] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisé'
29-May-2025 12:39:26.707 INFO [http-nio-8080-exec-185] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
29-May-2025 12:39:40.683 INFO [http-nio-8080-exec-186] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
29-May-2025 12:40:02.864 INFO [http-nio-8080-exec-184] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
29-May-2025 12:40:20.121 INFO [http-nio-8080-exec-180] dao.StationDAO.findAll Retrieved 10 stations
29-May-2025 12:49:58.936 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.undeploy Retrait de l'application web ayant pour chemin de contexte [/jeetraing]
29-May-2025 12:49:58.938 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesJdbc L'application web [jeetraing] a enregistré un pilote JDBC [com.mysql.cj.jdbc.Driver], mais ne l'a pas désenregistré avant l'arrêt de l'application.  Pour éviter une fuite de mémoire, le pilote JDBC a été désenregistré de force.
29-May-2025 12:49:58.940 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [mysql-cj-abandoned-connection-cleanup] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/java.lang.Object.wait(Native Method)
 java.base@17.0.12/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:155)
 com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:91)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
29-May-2025 12:49:58.941 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [Hibernate Connection Pool Validation Thread] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.12/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:252)
 java.base@17.0.12/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1672)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
29-May-2025 12:49:58.992 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.deployWAR Déploiement de l'archive [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] de l'application web
29-May-2025 12:49:59.981 INFO [Catalina-utility-1] org.apache.jasper.servlet.TldScanner.scanJars Au moins un fichier JAR a été analysé pour trouver des TLDs mais il n'en contenait pas, le mode "debug" du journal peut être activé pour obtenir une liste complète de JAR scannés sans succès ; éviter d'analyser des JARs inutilement peut améliorer sensiblement le temps de démarrage et le temps de compilation des JSPs
29-May-2025 12:49:59.995 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.deployWAR Le déploiement de l'archive de l'application web [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] s'est terminé en [1 003] ms
29-May-2025 12:50:02.183 INFO [mysql-cj-abandoned-connection-cleanup] org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading Impossible de charger [], ce chargeur de classes a déjà été arrêté
	java.lang.IllegalStateException: Impossible de charger [], ce chargeur de classes a déjà été arrêté
		at org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading(WebappClassLoaderBase.java:1400)
		at org.apache.catalina.loader.WebappClassLoaderBase.getResource(WebappClassLoaderBase.java:987)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.checkThreadContextClassLoader(AbandonedConnectionCleanupThread.java:123)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:90)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:842)
29-May-2025 12:50:20.767 INFO [http-nio-8080-exec-179] org.hibernate.Version.logVersion HHH000412: Hibernate ORM core version 6.4.4.Final
29-May-2025 12:50:20.979 INFO [http-nio-8080-exec-179] org.hibernate.cache.internal.RegionFactoryInitiator.initiateService HHH000026: Second-level cache disabled
29-May-2025 12:50:21.088 WARN [http-nio-8080-exec-179] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.configure HHH10001002: Using built-in connection pool (not intended for production use)
29-May-2025 12:50:21.092 INFO [http-nio-8080-exec-179] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001005: Loaded JDBC driver class: com.mysql.cj.jdbc.Driver
29-May-2025 12:50:21.092 INFO [http-nio-8080-exec-179] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001012: Connecting with JDBC URL [**************************************]
29-May-2025 12:50:21.092 INFO [http-nio-8080-exec-179] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001001: Connection properties: {password=****, user=root}
29-May-2025 12:50:21.093 INFO [http-nio-8080-exec-179] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001003: Autocommit mode: false
29-May-2025 12:50:21.095 INFO [http-nio-8080-exec-179] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl$PooledConnections.<init> HHH10001115: Connection pool size: 20 (min=1)
29-May-2025 12:50:21.244 WARN [http-nio-8080-exec-179] org.hibernate.dialect.Dialect.checkVersion HHH000511: The 5.5.0 version for [org.hibernate.dialect.MySQLDialect] is no longer supported, hence certain features may not work properly. The minimum supported version is 8.0.0. Check the community dialects project for available legacy versions.
29-May-2025 12:50:21.248 WARN [http-nio-8080-exec-179] org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.constructDialect HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
29-May-2025 12:50:22.293 INFO [http-nio-8080-exec-179] org.hibernate.engine.transaction.jta.platform.internal.JtaPlatformInitiator.initiateService HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
29-May-2025 12:50:22.319 INFO [http-nio-8080-exec-179] org.hibernate.resource.transaction.backend.jdbc.internal.DdlTransactionIsolatorNonJtaImpl.getIsolatedConnection HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@2ecd186f] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
29-May-2025 12:50:22.688 INFO [http-nio-8080-exec-179] dao.StationDAO.findAll Retrieved 10 stations
29-May-2025 12:52:20.169 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.undeploy Retrait de l'application web ayant pour chemin de contexte [/jeetraing]
29-May-2025 12:52:20.171 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesJdbc L'application web [jeetraing] a enregistré un pilote JDBC [com.mysql.cj.jdbc.Driver], mais ne l'a pas désenregistré avant l'arrêt de l'application.  Pour éviter une fuite de mémoire, le pilote JDBC a été désenregistré de force.
29-May-2025 12:52:20.172 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [mysql-cj-abandoned-connection-cleanup] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/java.lang.Object.wait(Native Method)
 java.base@17.0.12/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:155)
 com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:91)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
29-May-2025 12:52:20.173 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [Hibernate Connection Pool Validation Thread] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.12/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:252)
 java.base@17.0.12/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1672)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
29-May-2025 12:52:20.194 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.deployWAR Déploiement de l'archive [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] de l'application web
29-May-2025 12:52:21.153 INFO [Catalina-utility-1] org.apache.jasper.servlet.TldScanner.scanJars Au moins un fichier JAR a été analysé pour trouver des TLDs mais il n'en contenait pas, le mode "debug" du journal peut être activé pour obtenir une liste complète de JAR scannés sans succès ; éviter d'analyser des JARs inutilement peut améliorer sensiblement le temps de démarrage et le temps de compilation des JSPs
29-May-2025 12:52:21.168 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.deployWAR Le déploiement de l'archive de l'application web [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] s'est terminé en [974] ms
29-May-2025 12:52:21.309 INFO [mysql-cj-abandoned-connection-cleanup] org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading Impossible de charger [], ce chargeur de classes a déjà été arrêté
	java.lang.IllegalStateException: Impossible de charger [], ce chargeur de classes a déjà été arrêté
		at org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading(WebappClassLoaderBase.java:1400)
		at org.apache.catalina.loader.WebappClassLoaderBase.getResource(WebappClassLoaderBase.java:987)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.checkThreadContextClassLoader(AbandonedConnectionCleanupThread.java:123)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:90)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:842)
29-May-2025 12:53:15.388 INFO [http-nio-8080-exec-181] org.hibernate.Version.logVersion HHH000412: Hibernate ORM core version 6.4.4.Final
29-May-2025 12:53:15.597 INFO [http-nio-8080-exec-181] org.hibernate.cache.internal.RegionFactoryInitiator.initiateService HHH000026: Second-level cache disabled
29-May-2025 12:53:15.706 WARN [http-nio-8080-exec-181] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.configure HHH10001002: Using built-in connection pool (not intended for production use)
29-May-2025 12:53:15.709 INFO [http-nio-8080-exec-181] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001005: Loaded JDBC driver class: com.mysql.cj.jdbc.Driver
29-May-2025 12:53:15.709 INFO [http-nio-8080-exec-181] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001012: Connecting with JDBC URL [**************************************]
29-May-2025 12:53:15.709 INFO [http-nio-8080-exec-181] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001001: Connection properties: {password=****, user=root}
29-May-2025 12:53:15.710 INFO [http-nio-8080-exec-181] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001003: Autocommit mode: false
29-May-2025 12:53:15.713 INFO [http-nio-8080-exec-181] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl$PooledConnections.<init> HHH10001115: Connection pool size: 20 (min=1)
29-May-2025 12:53:15.869 WARN [http-nio-8080-exec-181] org.hibernate.dialect.Dialect.checkVersion HHH000511: The 5.5.0 version for [org.hibernate.dialect.MySQLDialect] is no longer supported, hence certain features may not work properly. The minimum supported version is 8.0.0. Check the community dialects project for available legacy versions.
29-May-2025 12:53:15.873 WARN [http-nio-8080-exec-181] org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.constructDialect HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
29-May-2025 12:53:16.924 INFO [http-nio-8080-exec-181] org.hibernate.engine.transaction.jta.platform.internal.JtaPlatformInitiator.initiateService HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
29-May-2025 12:53:16.950 INFO [http-nio-8080-exec-181] org.hibernate.resource.transaction.backend.jdbc.internal.DdlTransactionIsolatorNonJtaImpl.getIsolatedConnection HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@45b4cdc] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
29-May-2025 12:53:17.051 WARNING [http-nio-8080-exec-181] controller.AdminUserManagementServlet.doGet Utilisateur non connecté lors de l'accès à /admin/users
29-May-2025 12:53:28.869 INFO [http-nio-8080-exec-193] controller.LoginServlet.doPost Tentative de connexion ï¿½chouï¿½e pour l'email: <EMAIL> (mot de passe incorrect)
29-May-2025 12:53:40.264 INFO [http-nio-8080-exec-188] controller.LoginServlet.doPost Utilisateur connectï¿½ avec succï¿½s: <EMAIL>
29-May-2025 12:53:40.269 INFO [http-nio-8080-exec-185] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
29-May-2025 12:53:47.852 INFO [http-nio-8080-exec-192] controller.ReservationEvolutionServlet.doGet doGet - Evolution Data: [[Ljava.lang.Object;@c32e492, [Ljava.lang.Object;@c3683aa]
29-May-2025 12:53:47.853 INFO [http-nio-8080-exec-192] controller.ReservationEvolutionServlet.doGet doGet - Labels: [2025-04, 2025-05]
29-May-2025 12:53:47.853 INFO [http-nio-8080-exec-192] controller.ReservationEvolutionServlet.doGet doGet - Data: [0, 6]
29-May-2025 12:53:47.853 INFO [http-nio-8080-exec-192] controller.ReservationEvolutionServlet.doGet doGet - Attributes set - evolutionData: [[Ljava.lang.Object;@c32e492, [Ljava.lang.Object;@c3683aa]
29-May-2025 12:54:08.214 INFO [http-nio-8080-exec-195] controller.ReservationEvolutionServlet.doPost doPost - Evolution Data: [[Ljava.lang.Object;@4b39fbb5, [Ljava.lang.Object;@e3b7cb6]
29-May-2025 12:54:08.214 INFO [http-nio-8080-exec-195] controller.ReservationEvolutionServlet.doPost doPost - Labels: [2025-04, 2025-05]
29-May-2025 12:54:08.214 INFO [http-nio-8080-exec-195] controller.ReservationEvolutionServlet.doPost doPost - Data: [0, 6]
29-May-2025 12:54:08.215 INFO [http-nio-8080-exec-195] controller.ReservationEvolutionServlet.doPost doPost - Attributes set - evolutionData: [[Ljava.lang.Object;@4b39fbb5, [Ljava.lang.Object;@e3b7cb6]
29-May-2025 12:54:14.218 INFO [http-nio-8080-exec-193] controller.ReservationEvolutionServlet.doPost doPost - Evolution Data: [[Ljava.lang.Object;@34dbed51, [Ljava.lang.Object;@4d2d78a4]
29-May-2025 12:54:14.219 INFO [http-nio-8080-exec-193] controller.ReservationEvolutionServlet.doPost doPost - Labels: [2025-04, 2025-05]
29-May-2025 12:54:14.219 INFO [http-nio-8080-exec-193] controller.ReservationEvolutionServlet.doPost doPost - Data: [0, 6]
29-May-2025 12:54:14.219 INFO [http-nio-8080-exec-193] controller.ReservationEvolutionServlet.doPost doPost - Attributes set - evolutionData: [[Ljava.lang.Object;@34dbed51, [Ljava.lang.Object;@4d2d78a4]
29-May-2025 12:54:27.768 INFO [http-nio-8080-exec-196] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
29-May-2025 12:54:47.927 INFO [http-nio-8080-exec-199] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
29-May-2025 12:54:50.700 INFO [http-nio-8080-exec-195] dao.StationDAO.findAll Retrieved 10 stations
29-May-2025 12:55:05.241 INFO [http-nio-8080-exec-193] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
29-May-2025 12:55:10.334 INFO [http-nio-8080-exec-194] dao.StationDAO.findAll Retrieved 10 stations
29-May-2025 12:55:43.351 INFO [http-nio-8080-exec-202] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
29-May-2025 12:55:48.823 INFO [http-nio-8080-exec-195] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
29-May-2025 12:55:52.489 INFO [http-nio-8080-exec-196] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
29-May-2025 12:55:54.484 INFO [http-nio-8080-exec-194] controller.ReservationEvolutionServlet.doGet doGet - Evolution Data: [[Ljava.lang.Object;@264f066d, [Ljava.lang.Object;@3d2a0226]
29-May-2025 12:55:54.484 INFO [http-nio-8080-exec-194] controller.ReservationEvolutionServlet.doGet doGet - Labels: [2025-04, 2025-05]
29-May-2025 12:55:54.484 INFO [http-nio-8080-exec-194] controller.ReservationEvolutionServlet.doGet doGet - Data: [0, 6]
29-May-2025 12:55:54.485 INFO [http-nio-8080-exec-194] controller.ReservationEvolutionServlet.doGet doGet - Attributes set - evolutionData: [[Ljava.lang.Object;@264f066d, [Ljava.lang.Object;@3d2a0226]
29-May-2025 12:56:00.119 INFO [http-nio-8080-exec-197] dao.StationDAO.findAll Retrieved 10 stations
29-May-2025 12:56:04.679 INFO [http-nio-8080-exec-198] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
29-May-2025 12:56:07.100 INFO [http-nio-8080-exec-202] dao.StationDAO.findAll Retrieved 10 stations
29-May-2025 12:56:11.402 INFO [http-nio-8080-exec-199] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
29-May-2025 12:56:34.899 INFO [http-nio-8080-exec-196] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
29-May-2025 12:56:45.168 INFO [http-nio-8080-exec-201] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
29-May-2025 12:56:49.439 INFO [http-nio-8080-exec-202] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
29-May-2025 12:56:53.190 WARNING [http-nio-8080-exec-195] controller.AdminDashboardServlet.doGet Utilisateur non connectÃ© lors de l'accÃ¨s au dashboard admin
29-May-2025 12:57:07.060 INFO [http-nio-8080-exec-194] controller.LoginServlet.doPost Utilisateur connectï¿½ avec succï¿½s: <EMAIL>
29-May-2025 12:57:07.066 INFO [http-nio-8080-exec-197] dao.StationDAO.findAll Retrieved 10 stations
29-May-2025 12:57:22.460 INFO [http-nio-8080-exec-201] dao.StationDAO.findAll Retrieved 10 stations
29-May-2025 12:58:24.201 INFO [http-nio-8080-exec-194] dao.StationDAO.findAll Retrieved 10 stations
29-May-2025 12:58:36.385 INFO [http-nio-8080-exec-202] controller.LoginServlet.doPost Utilisateur connectï¿½ avec succï¿½s: <EMAIL>
29-May-2025 12:58:36.390 INFO [http-nio-8080-exec-199] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
29-May-2025 12:59:13.704 INFO [http-nio-8080-exec-194] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
29-May-2025 12:59:25.029 INFO [http-nio-8080-exec-201] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
29-May-2025 12:59:43.450 INFO [http-nio-8080-exec-195] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
29-May-2025 13:00:29.880 INFO [http-nio-8080-exec-201] controller.LoginServlet.doPost Utilisateur connectï¿½ avec succï¿½s: <EMAIL>
29-May-2025 13:00:29.887 INFO [http-nio-8080-exec-198] dao.StationDAO.findAll Retrieved 10 stations
29-May-2025 13:00:54.698 INFO [http-nio-8080-exec-195] dao.StationDAO.findAll Retrieved 10 stations
29-May-2025 13:02:28.850 INFO [http-nio-8080-exec-199] dao.StationDAO.findAll Retrieved 10 stations
29-May-2025 13:02:40.900 INFO [http-nio-8080-exec-195] dao.StationDAO.findAll Retrieved 10 stations
29-May-2025 13:03:07.207 INFO [http-nio-8080-exec-194] dao.StationDAO.findAll Retrieved 10 stations
29-May-2025 13:05:15.624 INFO [http-nio-8080-exec-195] dao.StationDAO.findAll Retrieved 10 stations
29-May-2025 13:10:08.887 INFO [http-nio-8080-exec-194] dao.StationDAO.findAll Retrieved 10 stations
29-May-2025 13:18:03.188 INFO [Catalina-utility-2] org.apache.catalina.startup.HostConfig.undeploy Retrait de l'application web ayant pour chemin de contexte [/jeetraing]
29-May-2025 13:18:03.192 WARNING [Catalina-utility-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesJdbc L'application web [jeetraing] a enregistré un pilote JDBC [com.mysql.cj.jdbc.Driver], mais ne l'a pas désenregistré avant l'arrêt de l'application.  Pour éviter une fuite de mémoire, le pilote JDBC a été désenregistré de force.
29-May-2025 13:18:03.194 WARNING [Catalina-utility-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [mysql-cj-abandoned-connection-cleanup] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/java.lang.Object.wait(Native Method)
 java.base@17.0.12/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:155)
 com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:91)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
29-May-2025 13:18:03.195 WARNING [Catalina-utility-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [Hibernate Connection Pool Validation Thread] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.12/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:252)
 java.base@17.0.12/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1672)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
29-May-2025 13:18:03.223 INFO [Catalina-utility-2] org.apache.catalina.startup.HostConfig.deployWAR Déploiement de l'archive [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] de l'application web
29-May-2025 13:18:03.455 INFO [mysql-cj-abandoned-connection-cleanup] org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading Impossible de charger [], ce chargeur de classes a déjà été arrêté
	java.lang.IllegalStateException: Impossible de charger [], ce chargeur de classes a déjà été arrêté
		at org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading(WebappClassLoaderBase.java:1400)
		at org.apache.catalina.loader.WebappClassLoaderBase.getResource(WebappClassLoaderBase.java:987)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.checkThreadContextClassLoader(AbandonedConnectionCleanupThread.java:123)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:90)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:842)
29-May-2025 13:18:04.235 INFO [Catalina-utility-2] org.apache.jasper.servlet.TldScanner.scanJars Au moins un fichier JAR a été analysé pour trouver des TLDs mais il n'en contenait pas, le mode "debug" du journal peut être activé pour obtenir une liste complète de JAR scannés sans succès ; éviter d'analyser des JARs inutilement peut améliorer sensiblement le temps de démarrage et le temps de compilation des JSPs
29-May-2025 13:18:04.254 INFO [Catalina-utility-2] org.apache.catalina.startup.HostConfig.deployWAR Le déploiement de l'archive de l'application web [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] s'est terminé en [1 031] ms
29-May-2025 13:18:26.756 INFO [http-nio-8080-exec-195] org.hibernate.Version.logVersion HHH000412: Hibernate ORM core version 6.4.4.Final
29-May-2025 13:18:26.981 INFO [http-nio-8080-exec-195] org.hibernate.cache.internal.RegionFactoryInitiator.initiateService HHH000026: Second-level cache disabled
29-May-2025 13:18:27.097 WARN [http-nio-8080-exec-195] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.configure HHH10001002: Using built-in connection pool (not intended for production use)
29-May-2025 13:18:27.100 INFO [http-nio-8080-exec-195] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001005: Loaded JDBC driver class: com.mysql.cj.jdbc.Driver
29-May-2025 13:18:27.101 INFO [http-nio-8080-exec-195] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001012: Connecting with JDBC URL [**************************************]
29-May-2025 13:18:27.101 INFO [http-nio-8080-exec-195] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001001: Connection properties: {password=****, user=root}
29-May-2025 13:18:27.101 INFO [http-nio-8080-exec-195] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001003: Autocommit mode: false
29-May-2025 13:18:27.104 INFO [http-nio-8080-exec-195] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl$PooledConnections.<init> HHH10001115: Connection pool size: 20 (min=1)
29-May-2025 13:18:27.258 WARN [http-nio-8080-exec-195] org.hibernate.dialect.Dialect.checkVersion HHH000511: The 5.5.0 version for [org.hibernate.dialect.MySQLDialect] is no longer supported, hence certain features may not work properly. The minimum supported version is 8.0.0. Check the community dialects project for available legacy versions.
29-May-2025 13:18:27.262 WARN [http-nio-8080-exec-195] org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.constructDialect HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
29-May-2025 13:18:28.342 INFO [http-nio-8080-exec-195] org.hibernate.engine.transaction.jta.platform.internal.JtaPlatformInitiator.initiateService HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
29-May-2025 13:18:28.369 INFO [http-nio-8080-exec-195] org.hibernate.resource.transaction.backend.jdbc.internal.DdlTransactionIsolatorNonJtaImpl.getIsolatedConnection HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@5da353] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
29-May-2025 13:18:28.458 WARNING [http-nio-8080-exec-195] controller.MesReservationsServlet.doGet Utilisateur non connectÃ© lors de l'accÃ¨s Ã  /mes-reservations
29-May-2025 13:20:34.448 INFO [Catalina-utility-2] org.apache.catalina.startup.HostConfig.undeploy Retrait de l'application web ayant pour chemin de contexte [/jeetraing]
29-May-2025 13:20:34.451 WARNING [Catalina-utility-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesJdbc L'application web [jeetraing] a enregistré un pilote JDBC [com.mysql.cj.jdbc.Driver], mais ne l'a pas désenregistré avant l'arrêt de l'application.  Pour éviter une fuite de mémoire, le pilote JDBC a été désenregistré de force.
29-May-2025 13:20:34.453 WARNING [Catalina-utility-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [mysql-cj-abandoned-connection-cleanup] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/java.lang.Object.wait(Native Method)
 java.base@17.0.12/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:155)
 com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:91)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
29-May-2025 13:20:34.454 WARNING [Catalina-utility-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [Hibernate Connection Pool Validation Thread] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.12/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:252)
 java.base@17.0.12/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1672)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
29-May-2025 13:20:34.481 INFO [Catalina-utility-2] org.apache.catalina.startup.HostConfig.deployWAR Déploiement de l'archive [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] de l'application web
29-May-2025 13:20:35.499 INFO [Catalina-utility-2] org.apache.jasper.servlet.TldScanner.scanJars Au moins un fichier JAR a été analysé pour trouver des TLDs mais il n'en contenait pas, le mode "debug" du journal peut être activé pour obtenir une liste complète de JAR scannés sans succès ; éviter d'analyser des JARs inutilement peut améliorer sensiblement le temps de démarrage et le temps de compilation des JSPs
29-May-2025 13:20:35.519 INFO [Catalina-utility-2] org.apache.catalina.startup.HostConfig.deployWAR Le déploiement de l'archive de l'application web [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] s'est terminé en [1 038] ms
29-May-2025 13:20:37.328 INFO [mysql-cj-abandoned-connection-cleanup] org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading Impossible de charger [], ce chargeur de classes a déjà été arrêté
	java.lang.IllegalStateException: Impossible de charger [], ce chargeur de classes a déjà été arrêté
		at org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading(WebappClassLoaderBase.java:1400)
		at org.apache.catalina.loader.WebappClassLoaderBase.getResource(WebappClassLoaderBase.java:987)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.checkThreadContextClassLoader(AbandonedConnectionCleanupThread.java:123)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:90)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:842)
29-May-2025 13:21:54.406 INFO [http-nio-8080-exec-207] org.hibernate.Version.logVersion HHH000412: Hibernate ORM core version 6.4.4.Final
29-May-2025 13:21:54.612 INFO [http-nio-8080-exec-207] org.hibernate.cache.internal.RegionFactoryInitiator.initiateService HHH000026: Second-level cache disabled
29-May-2025 13:21:54.718 WARN [http-nio-8080-exec-207] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.configure HHH10001002: Using built-in connection pool (not intended for production use)
29-May-2025 13:21:54.721 INFO [http-nio-8080-exec-207] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001005: Loaded JDBC driver class: com.mysql.cj.jdbc.Driver
29-May-2025 13:21:54.721 INFO [http-nio-8080-exec-207] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001012: Connecting with JDBC URL [**************************************]
29-May-2025 13:21:54.722 INFO [http-nio-8080-exec-207] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001001: Connection properties: {password=****, user=root}
29-May-2025 13:21:54.722 INFO [http-nio-8080-exec-207] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001003: Autocommit mode: false
29-May-2025 13:21:54.725 INFO [http-nio-8080-exec-207] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl$PooledConnections.<init> HHH10001115: Connection pool size: 20 (min=1)
29-May-2025 13:21:54.870 WARN [http-nio-8080-exec-207] org.hibernate.dialect.Dialect.checkVersion HHH000511: The 5.5.0 version for [org.hibernate.dialect.MySQLDialect] is no longer supported, hence certain features may not work properly. The minimum supported version is 8.0.0. Check the community dialects project for available legacy versions.
29-May-2025 13:21:54.873 WARN [http-nio-8080-exec-207] org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.constructDialect HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
29-May-2025 13:21:56.485 INFO [http-nio-8080-exec-207] org.hibernate.engine.transaction.jta.platform.internal.JtaPlatformInitiator.initiateService HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
29-May-2025 13:21:56.536 INFO [http-nio-8080-exec-207] org.hibernate.resource.transaction.backend.jdbc.internal.DdlTransactionIsolatorNonJtaImpl.getIsolatedConnection HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@19250a80] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
29-May-2025 13:22:06.959 INFO [http-nio-8080-exec-198] controller.LoginServlet.doPost Utilisateur connectï¿½ avec succï¿½s: <EMAIL>
29-May-2025 13:22:06.967 INFO [http-nio-8080-exec-193] dao.StationDAO.findAll Retrieved 10 stations
29-May-2025 13:22:26.541 INFO [http-nio-8080-exec-203] dao.StationDAO.findAll Retrieved 10 stations
29-May-2025 13:23:47.897 INFO [http-nio-8080-exec-211] controller.LoginServlet.doPost Utilisateur connectï¿½ avec succï¿½s: <EMAIL>
29-May-2025 13:23:47.901 INFO [http-nio-8080-exec-212] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
29-May-2025 13:24:09.216 INFO [http-nio-8080-exec-208] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
29-May-2025 13:25:18.381 INFO [http-nio-8080-exec-217] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisé'
29-May-2025 13:25:18.479 INFO [http-nio-8080-exec-209] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
29-May-2025 13:25:36.732 WARNING [http-nio-8080-exec-210] controller.AnnulerReservationServlet.doGet ID de rï¿½servation manquant lors de l'accï¿½s ï¿½ /annulerReservation
29-May-2025 13:25:36.738 INFO [http-nio-8080-exec-215] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisé'
29-May-2025 13:25:36.767 INFO [http-nio-8080-exec-212] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
29-May-2025 13:25:44.705 WARNING [http-nio-8080-exec-216] controller.AnnulerReservationServlet.doGet ID de rï¿½servation manquant lors de l'accï¿½s ï¿½ /annulerReservation
29-May-2025 13:25:44.712 INFO [http-nio-8080-exec-213] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisé'
29-May-2025 13:25:44.744 INFO [http-nio-8080-exec-214] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
29-May-2025 13:38:37.880 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.undeploy Retrait de l'application web ayant pour chemin de contexte [/jeetraing]
29-May-2025 13:38:37.882 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesJdbc L'application web [jeetraing] a enregistré un pilote JDBC [com.mysql.cj.jdbc.Driver], mais ne l'a pas désenregistré avant l'arrêt de l'application.  Pour éviter une fuite de mémoire, le pilote JDBC a été désenregistré de force.
29-May-2025 13:38:37.883 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [mysql-cj-abandoned-connection-cleanup] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/java.lang.Object.wait(Native Method)
 java.base@17.0.12/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:155)
 com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:91)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
29-May-2025 13:38:37.884 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [Hibernate Connection Pool Validation Thread] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.12/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:252)
 java.base@17.0.12/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1672)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
29-May-2025 13:38:37.907 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.deployWAR Déploiement de l'archive [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] de l'application web
29-May-2025 13:38:38.713 INFO [Catalina-utility-1] org.apache.jasper.servlet.TldScanner.scanJars Au moins un fichier JAR a été analysé pour trouver des TLDs mais il n'en contenait pas, le mode "debug" du journal peut être activé pour obtenir une liste complète de JAR scannés sans succès ; éviter d'analyser des JARs inutilement peut améliorer sensiblement le temps de démarrage et le temps de compilation des JSPs
29-May-2025 13:38:38.725 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.deployWAR Le déploiement de l'archive de l'application web [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] s'est terminé en [818] ms
29-May-2025 13:38:41.564 INFO [mysql-cj-abandoned-connection-cleanup] org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading Impossible de charger [], ce chargeur de classes a déjà été arrêté
	java.lang.IllegalStateException: Impossible de charger [], ce chargeur de classes a déjà été arrêté
		at org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading(WebappClassLoaderBase.java:1400)
		at org.apache.catalina.loader.WebappClassLoaderBase.getResource(WebappClassLoaderBase.java:987)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.checkThreadContextClassLoader(AbandonedConnectionCleanupThread.java:123)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:90)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:842)
29-May-2025 13:40:34.384 INFO [http-nio-8080-exec-209] org.hibernate.Version.logVersion HHH000412: Hibernate ORM core version 6.4.4.Final
29-May-2025 13:40:34.569 INFO [http-nio-8080-exec-209] org.hibernate.cache.internal.RegionFactoryInitiator.initiateService HHH000026: Second-level cache disabled
29-May-2025 13:40:34.658 WARN [http-nio-8080-exec-209] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.configure HHH10001002: Using built-in connection pool (not intended for production use)
29-May-2025 13:40:34.661 INFO [http-nio-8080-exec-209] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001005: Loaded JDBC driver class: com.mysql.cj.jdbc.Driver
29-May-2025 13:40:34.662 INFO [http-nio-8080-exec-209] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001012: Connecting with JDBC URL [**************************************]
29-May-2025 13:40:34.662 INFO [http-nio-8080-exec-209] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001001: Connection properties: {password=****, user=root}
29-May-2025 13:40:34.662 INFO [http-nio-8080-exec-209] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001003: Autocommit mode: false
29-May-2025 13:40:34.665 INFO [http-nio-8080-exec-209] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl$PooledConnections.<init> HHH10001115: Connection pool size: 20 (min=1)
29-May-2025 13:40:34.790 WARN [http-nio-8080-exec-209] org.hibernate.dialect.Dialect.checkVersion HHH000511: The 5.5.0 version for [org.hibernate.dialect.MySQLDialect] is no longer supported, hence certain features may not work properly. The minimum supported version is 8.0.0. Check the community dialects project for available legacy versions.
29-May-2025 13:40:34.793 WARN [http-nio-8080-exec-209] org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.constructDialect HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
29-May-2025 13:40:35.649 INFO [http-nio-8080-exec-209] org.hibernate.engine.transaction.jta.platform.internal.JtaPlatformInitiator.initiateService HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
29-May-2025 13:40:35.670 INFO [http-nio-8080-exec-209] org.hibernate.resource.transaction.backend.jdbc.internal.DdlTransactionIsolatorNonJtaImpl.getIsolatedConnection HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@2471aaf5] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
29-May-2025 13:40:35.717 WARNING [http-nio-8080-exec-209] controller.DownloadTicketServlet.doGet Utilisateur non connectÃ© lors de l'accÃ¨s Ã  /download-ticket
29-May-2025 13:49:39.599 INFO [Catalina-utility-2] org.apache.catalina.startup.HostConfig.undeploy Retrait de l'application web ayant pour chemin de contexte [/jeetraing]
29-May-2025 13:49:39.602 WARNING [Catalina-utility-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesJdbc L'application web [jeetraing] a enregistré un pilote JDBC [com.mysql.cj.jdbc.Driver], mais ne l'a pas désenregistré avant l'arrêt de l'application.  Pour éviter une fuite de mémoire, le pilote JDBC a été désenregistré de force.
29-May-2025 13:49:39.604 WARNING [Catalina-utility-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [mysql-cj-abandoned-connection-cleanup] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/java.lang.Object.wait(Native Method)
 java.base@17.0.12/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:155)
 com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:91)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
29-May-2025 13:49:39.605 WARNING [Catalina-utility-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [Hibernate Connection Pool Validation Thread] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.12/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:252)
 java.base@17.0.12/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1672)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
29-May-2025 13:49:39.642 INFO [Catalina-utility-2] org.apache.catalina.startup.HostConfig.deployWAR Déploiement de l'archive [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] de l'application web
29-May-2025 13:49:40.607 INFO [mysql-cj-abandoned-connection-cleanup] org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading Impossible de charger [], ce chargeur de classes a déjà été arrêté
	java.lang.IllegalStateException: Impossible de charger [], ce chargeur de classes a déjà été arrêté
		at org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading(WebappClassLoaderBase.java:1400)
		at org.apache.catalina.loader.WebappClassLoaderBase.getResource(WebappClassLoaderBase.java:987)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.checkThreadContextClassLoader(AbandonedConnectionCleanupThread.java:123)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:90)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:842)
29-May-2025 13:49:41.117 INFO [Catalina-utility-2] org.apache.jasper.servlet.TldScanner.scanJars Au moins un fichier JAR a été analysé pour trouver des TLDs mais il n'en contenait pas, le mode "debug" du journal peut être activé pour obtenir une liste complète de JAR scannés sans succès ; éviter d'analyser des JARs inutilement peut améliorer sensiblement le temps de démarrage et le temps de compilation des JSPs
29-May-2025 13:49:41.142 INFO [Catalina-utility-2] org.apache.catalina.startup.HostConfig.deployWAR Le déploiement de l'archive de l'application web [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] s'est terminé en [1 500] ms
29-May-2025 13:55:58.731 INFO [http-nio-8080-exec-230] org.hibernate.Version.logVersion HHH000412: Hibernate ORM core version 6.4.4.Final
29-May-2025 13:55:58.919 INFO [http-nio-8080-exec-230] org.hibernate.cache.internal.RegionFactoryInitiator.initiateService HHH000026: Second-level cache disabled
29-May-2025 13:55:59.009 WARN [http-nio-8080-exec-230] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.configure HHH10001002: Using built-in connection pool (not intended for production use)
29-May-2025 13:55:59.011 INFO [http-nio-8080-exec-230] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001005: Loaded JDBC driver class: com.mysql.cj.jdbc.Driver
29-May-2025 13:55:59.012 INFO [http-nio-8080-exec-230] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001012: Connecting with JDBC URL [**************************************]
29-May-2025 13:55:59.012 INFO [http-nio-8080-exec-230] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001001: Connection properties: {password=****, user=root}
29-May-2025 13:55:59.012 INFO [http-nio-8080-exec-230] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001003: Autocommit mode: false
29-May-2025 13:55:59.015 INFO [http-nio-8080-exec-230] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl$PooledConnections.<init> HHH10001115: Connection pool size: 20 (min=1)
29-May-2025 13:55:59.140 WARN [http-nio-8080-exec-230] org.hibernate.dialect.Dialect.checkVersion HHH000511: The 5.5.0 version for [org.hibernate.dialect.MySQLDialect] is no longer supported, hence certain features may not work properly. The minimum supported version is 8.0.0. Check the community dialects project for available legacy versions.
29-May-2025 13:55:59.143 WARN [http-nio-8080-exec-230] org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.constructDialect HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
29-May-2025 13:56:00.038 INFO [http-nio-8080-exec-230] org.hibernate.engine.transaction.jta.platform.internal.JtaPlatformInitiator.initiateService HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
29-May-2025 13:56:00.061 INFO [http-nio-8080-exec-230] org.hibernate.resource.transaction.backend.jdbc.internal.DdlTransactionIsolatorNonJtaImpl.getIsolatedConnection HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@2c3b07c0] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
29-May-2025 13:56:00.134 WARNING [http-nio-8080-exec-230] controller.ConfirmationServlet.doGet voyageId est null ou vide dans la session lors de l'accès à /confirmation
29-May-2025 13:56:00.386 INFO [http-nio-8080-exec-231] dao.StationDAO.findAll Retrieved 10 stations
29-May-2025 14:00:07.665 INFO [http-nio-8080-exec-230] controller.LoginServlet.doPost Tentative de connexion ï¿½chouï¿½e pour l'email: <EMAIL> (mot de passe incorrect)
29-May-2025 14:00:12.550 INFO [http-nio-8080-exec-231] controller.LoginServlet.doPost Utilisateur connectï¿½ avec succï¿½s: <EMAIL>
29-May-2025 14:00:12.555 INFO [http-nio-8080-exec-232] dao.StationDAO.findAll Retrieved 10 stations
29-May-2025 14:00:28.425 INFO [http-nio-8080-exec-235] dao.StationDAO.findAll Retrieved 10 stations
29-May-2025 14:01:01.901 INFO [http-nio-8080-exec-230] controller.DownloadTicketServlet.doGet GÃ©nÃ©ration du PDF pour la rÃ©servation ID: 11
29-May-2025 14:01:01.901 INFO [http-nio-8080-exec-230] controller.DownloadTicketServlet.generatePDF DÃ©but de la gÃ©nÃ©ration du PDF pour la rÃ©servation ID: 11
29-May-2025 14:01:02.170 WARNING [http-nio-8080-exec-230] org.apache.pdfbox.pdmodel.font.FileSystemFontProvider.loadDiskCache New fonts found, font cache will be re-built
29-May-2025 14:01:02.170 WARNING [http-nio-8080-exec-230] org.apache.pdfbox.pdmodel.font.FileSystemFontProvider.<init> Building on-disk font cache, this may take a while
29-May-2025 14:01:03.437 WARNING [http-nio-8080-exec-230] org.apache.pdfbox.pdmodel.font.FileSystemFontProvider.<init> Finished building on-disk font cache, found 368 fonts
29-May-2025 14:01:03.505 INFO [http-nio-8080-exec-230] controller.DownloadTicketServlet.generatePDF Ã‰criture du PDF dans la rÃ©ponse pour la rÃ©servation ID: 11
29-May-2025 14:01:03.509 INFO [http-nio-8080-exec-230] controller.DownloadTicketServlet.generatePDF PDF gÃ©nÃ©rÃ© et envoyÃ© avec succÃ¨s pour la rÃ©servation ID: 11
29-May-2025 14:01:37.184 INFO [http-nio-8080-exec-235] dao.StationDAO.findAll Retrieved 10 stations
29-May-2025 14:01:43.734 INFO [http-nio-8080-exec-236] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisé'
29-May-2025 14:02:10.673 INFO [http-nio-8080-exec-237] controller.ModifierReservationServlet.doGet Accï¿½s ï¿½ doGet - ID: 10
29-May-2025 14:02:10.676 INFO [http-nio-8080-exec-237] controller.ModifierReservationServlet.doGet Rï¿½servation rï¿½cupï¿½rï¿½e: model.Reservation@5796a1c4
29-May-2025 14:02:10.676 INFO [http-nio-8080-exec-237] controller.ModifierReservationServlet.doGet ï¿½tat de la rï¿½servation: acheté
29-May-2025 14:02:10.677 WARNING [http-nio-8080-exec-237] controller.ModifierReservationServlet.doGet Rï¿½servation non modifiable (ï¿½tat: acheté) pour l'ID: 10
29-May-2025 14:03:56.229 INFO [http-nio-8080-exec-230] controller.LoginServlet.doPost Utilisateur connectï¿½ avec succï¿½s: <EMAIL>
29-May-2025 14:03:56.233 INFO [http-nio-8080-exec-234] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
29-May-2025 14:04:37.253 INFO [http-nio-8080-exec-237] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisé'
29-May-2025 14:04:37.295 INFO [http-nio-8080-exec-233] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
29-May-2025 14:04:59.087 INFO [http-nio-8080-exec-228] controller.LoginServlet.doPost Utilisateur connectï¿½ avec succï¿½s: <EMAIL>
29-May-2025 14:04:59.092 INFO [http-nio-8080-exec-229] dao.StationDAO.findAll Retrieved 10 stations
29-May-2025 14:05:08.281 INFO [http-nio-8080-exec-237] dao.StationDAO.findAll Retrieved 10 stations
29-May-2025 14:05:48.712 INFO [http-nio-8080-exec-235] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisé'
29-May-2025 14:05:53.810 INFO [http-nio-8080-exec-236] controller.ModifierReservationServlet.doGet Accï¿½s ï¿½ doGet - ID: 4
29-May-2025 14:05:53.813 INFO [http-nio-8080-exec-236] controller.ModifierReservationServlet.doGet Rï¿½servation rï¿½cupï¿½rï¿½e: model.Reservation@40ff0d8c
29-May-2025 14:05:53.814 INFO [http-nio-8080-exec-236] controller.ModifierReservationServlet.doGet ï¿½tat de la rï¿½servation: acheté
29-May-2025 14:05:53.814 WARNING [http-nio-8080-exec-236] controller.ModifierReservationServlet.doGet Rï¿½servation non modifiable (ï¿½tat: acheté) pour l'ID: 4
29-May-2025 14:06:13.459 WARNING [http-nio-8080-exec-237] controller.AnnulerReservationServlet.doGet Rï¿½servation non annulable (ï¿½tat: acheté) pour l'ID: 4
29-May-2025 14:06:13.464 INFO [http-nio-8080-exec-233] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisé'
29-May-2025 14:06:26.685 INFO [http-nio-8080-exec-231] controller.ModifierReservationServlet.doGet Accï¿½s ï¿½ doGet - ID: 13
29-May-2025 14:06:26.688 INFO [http-nio-8080-exec-231] controller.ModifierReservationServlet.doGet Rï¿½servation rï¿½cupï¿½rï¿½e: model.Reservation@1443fe4b
29-May-2025 14:06:26.688 INFO [http-nio-8080-exec-231] controller.ModifierReservationServlet.doGet ï¿½tat de la rï¿½servation: acheté
29-May-2025 14:06:26.689 WARNING [http-nio-8080-exec-231] controller.ModifierReservationServlet.doGet Rï¿½servation non modifiable (ï¿½tat: acheté) pour l'ID: 13
29-May-2025 14:06:30.828 INFO [http-nio-8080-exec-230] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisé'
29-May-2025 14:06:33.216 INFO [http-nio-8080-exec-234] controller.ModifierReservationServlet.doGet Accï¿½s ï¿½ doGet - ID: 13
29-May-2025 14:06:33.221 INFO [http-nio-8080-exec-234] controller.ModifierReservationServlet.doGet Rï¿½servation rï¿½cupï¿½rï¿½e: model.Reservation@3d270108
29-May-2025 14:06:33.221 INFO [http-nio-8080-exec-234] controller.ModifierReservationServlet.doGet ï¿½tat de la rï¿½servation: acheté
29-May-2025 14:06:33.221 WARNING [http-nio-8080-exec-234] controller.ModifierReservationServlet.doGet Rï¿½servation non modifiable (ï¿½tat: acheté) pour l'ID: 13
29-May-2025 14:06:38.541 INFO [http-nio-8080-exec-228] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisé'
29-May-2025 14:07:26.439 INFO [http-nio-8080-exec-231] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisé'
29-May-2025 14:07:55.776 INFO [http-nio-8080-exec-234] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisé'
29-May-2025 14:08:26.754 INFO [http-nio-8080-exec-232] dao.StationDAO.findAll Retrieved 10 stations
29-May-2025 14:08:38.587 INFO [http-nio-8080-exec-230] dao.StationDAO.findAll Retrieved 10 stations
29-May-2025 14:08:41.650 INFO [http-nio-8080-exec-235] dao.StationDAO.findAll Retrieved 10 stations
29-May-2025 14:08:47.682 INFO [http-nio-8080-exec-236] dao.StationDAO.findAll Retrieved 10 stations
29-May-2025 14:09:01.864 INFO [http-nio-8080-exec-229] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisé'
29-May-2025 14:09:35.776 WARNING [http-nio-8080-exec-230] controller.AnnulerReservationServlet.doGet Rï¿½servation non annulable (ï¿½tat: acheté) pour l'ID: 5
29-May-2025 14:09:35.781 INFO [http-nio-8080-exec-234] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisé'
29-May-2025 14:10:36.898 INFO [http-nio-8080-exec-229] controller.TelechargerBilletServlet.doGet User en session: 5
29-May-2025 14:10:36.898 INFO [http-nio-8080-exec-229] controller.TelechargerBilletServlet.doGet User de la réservation: 5
29-May-2025 14:10:36.898 INFO [http-nio-8080-exec-229] controller.TelechargerBilletServlet.doGet Génération du PDF pour la réservation ID: 8
29-May-2025 14:10:36.899 INFO [http-nio-8080-exec-229] controller.TelechargerBilletServlet.generatePDF Début de la génération du PDF pour la réservation ID: 8
29-May-2025 14:10:36.900 INFO [http-nio-8080-exec-229] controller.TelechargerBilletServlet.generatePDF Écriture du PDF dans la réponse pour la réservation ID: 8
29-May-2025 14:10:36.901 INFO [http-nio-8080-exec-229] controller.TelechargerBilletServlet.generatePDF PDF généré et envoyé avec succès pour la réservation ID: 8
29-May-2025 18:15:07.896 INFO [Catalina-utility-2] org.apache.catalina.startup.HostConfig.undeploy Retrait de l'application web ayant pour chemin de contexte [/jeetraing]
29-May-2025 18:15:07.900 WARNING [Catalina-utility-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesJdbc L'application web [jeetraing] a enregistré un pilote JDBC [com.mysql.cj.jdbc.Driver], mais ne l'a pas désenregistré avant l'arrêt de l'application.  Pour éviter une fuite de mémoire, le pilote JDBC a été désenregistré de force.
29-May-2025 18:15:07.901 WARNING [Catalina-utility-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [mysql-cj-abandoned-connection-cleanup] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/java.lang.Object.wait(Native Method)
 java.base@17.0.12/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:155)
 com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:91)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
29-May-2025 18:15:07.902 WARNING [Catalina-utility-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [Hibernate Connection Pool Validation Thread] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.12/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:252)
 java.base@17.0.12/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1672)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
29-May-2025 18:15:07.964 INFO [Catalina-utility-2] org.apache.catalina.startup.HostConfig.deployWAR Déploiement de l'archive [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] de l'application web
29-May-2025 18:15:09.655 INFO [Catalina-utility-2] org.apache.jasper.servlet.TldScanner.scanJars Au moins un fichier JAR a été analysé pour trouver des TLDs mais il n'en contenait pas, le mode "debug" du journal peut être activé pour obtenir une liste complète de JAR scannés sans succès ; éviter d'analyser des JARs inutilement peut améliorer sensiblement le temps de démarrage et le temps de compilation des JSPs
29-May-2025 18:15:09.682 INFO [Catalina-utility-2] org.apache.catalina.startup.HostConfig.deployWAR Le déploiement de l'archive de l'application web [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] s'est terminé en [1 717] ms
29-May-2025 18:15:10.614 INFO [mysql-cj-abandoned-connection-cleanup] org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading Impossible de charger [], ce chargeur de classes a déjà été arrêté
	java.lang.IllegalStateException: Impossible de charger [], ce chargeur de classes a déjà été arrêté
		at org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading(WebappClassLoaderBase.java:1400)
		at org.apache.catalina.loader.WebappClassLoaderBase.getResource(WebappClassLoaderBase.java:987)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.checkThreadContextClassLoader(AbandonedConnectionCleanupThread.java:123)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:90)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:842)
29-May-2025 18:18:51.042 INFO [http-nio-8080-exec-238] org.hibernate.Version.logVersion HHH000412: Hibernate ORM core version 6.4.4.Final
29-May-2025 18:18:51.271 INFO [http-nio-8080-exec-238] org.hibernate.cache.internal.RegionFactoryInitiator.initiateService HHH000026: Second-level cache disabled
29-May-2025 18:18:51.387 WARN [http-nio-8080-exec-238] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.configure HHH10001002: Using built-in connection pool (not intended for production use)
29-May-2025 18:18:51.390 INFO [http-nio-8080-exec-238] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001005: Loaded JDBC driver class: com.mysql.cj.jdbc.Driver
29-May-2025 18:18:51.391 INFO [http-nio-8080-exec-238] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001012: Connecting with JDBC URL [**************************************]
29-May-2025 18:18:51.391 INFO [http-nio-8080-exec-238] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001001: Connection properties: {password=****, user=root}
29-May-2025 18:18:51.391 INFO [http-nio-8080-exec-238] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001003: Autocommit mode: false
29-May-2025 18:18:51.395 INFO [http-nio-8080-exec-238] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl$PooledConnections.<init> HHH10001115: Connection pool size: 20 (min=1)
29-May-2025 18:18:51.555 WARN [http-nio-8080-exec-238] org.hibernate.dialect.Dialect.checkVersion HHH000511: The 5.5.0 version for [org.hibernate.dialect.MySQLDialect] is no longer supported, hence certain features may not work properly. The minimum supported version is 8.0.0. Check the community dialects project for available legacy versions.
29-May-2025 18:18:51.560 WARN [http-nio-8080-exec-238] org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.constructDialect HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
29-May-2025 18:18:52.686 INFO [http-nio-8080-exec-238] org.hibernate.engine.transaction.jta.platform.internal.JtaPlatformInitiator.initiateService HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
29-May-2025 18:18:52.713 INFO [http-nio-8080-exec-238] org.hibernate.resource.transaction.backend.jdbc.internal.DdlTransactionIsolatorNonJtaImpl.getIsolatedConnection HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@5d29cf28] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
29-May-2025 18:18:52.777 WARNING [http-nio-8080-exec-238] controller.MonCompteServlet.doGet Utilisateur non connectï¿½ lors de l'accï¿½s ï¿½ /monCompte
29-May-2025 21:48:56.427 INFO [Catalina-utility-2] org.apache.catalina.startup.HostConfig.undeploy Retrait de l'application web ayant pour chemin de contexte [/jeetraing]
29-May-2025 21:48:56.431 WARNING [Catalina-utility-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesJdbc L'application web [jeetraing] a enregistré un pilote JDBC [com.mysql.cj.jdbc.Driver], mais ne l'a pas désenregistré avant l'arrêt de l'application.  Pour éviter une fuite de mémoire, le pilote JDBC a été désenregistré de force.
29-May-2025 21:48:56.433 WARNING [Catalina-utility-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [mysql-cj-abandoned-connection-cleanup] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/java.lang.Object.wait(Native Method)
 java.base@17.0.12/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:155)
 com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:91)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
29-May-2025 21:48:56.434 WARNING [Catalina-utility-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [Hibernate Connection Pool Validation Thread] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.12/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:252)
 java.base@17.0.12/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1672)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
29-May-2025 21:48:56.456 INFO [Catalina-utility-2] org.apache.catalina.startup.HostConfig.deployWAR Déploiement de l'archive [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] de l'application web
29-May-2025 21:48:57.333 INFO [Catalina-utility-2] org.apache.jasper.servlet.TldScanner.scanJars Au moins un fichier JAR a été analysé pour trouver des TLDs mais il n'en contenait pas, le mode "debug" du journal peut être activé pour obtenir une liste complète de JAR scannés sans succès ; éviter d'analyser des JARs inutilement peut améliorer sensiblement le temps de démarrage et le temps de compilation des JSPs
29-May-2025 21:48:57.352 INFO [Catalina-utility-2] org.apache.catalina.startup.HostConfig.deployWAR Le déploiement de l'archive de l'application web [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] s'est terminé en [895] ms
29-May-2025 21:48:58.647 INFO [mysql-cj-abandoned-connection-cleanup] org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading Impossible de charger [], ce chargeur de classes a déjà été arrêté
	java.lang.IllegalStateException: Impossible de charger [], ce chargeur de classes a déjà été arrêté
		at org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading(WebappClassLoaderBase.java:1400)
		at org.apache.catalina.loader.WebappClassLoaderBase.getResource(WebappClassLoaderBase.java:987)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.checkThreadContextClassLoader(AbandonedConnectionCleanupThread.java:123)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:90)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:842)
29-May-2025 21:55:07.853 INFO [Catalina-utility-2] org.apache.catalina.startup.HostConfig.undeploy Retrait de l'application web ayant pour chemin de contexte [/jeetraing]
29-May-2025 21:55:07.889 INFO [Catalina-utility-2] org.apache.catalina.startup.HostConfig.deployWAR Déploiement de l'archive [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] de l'application web
29-May-2025 21:55:09.213 INFO [Catalina-utility-2] org.apache.jasper.servlet.TldScanner.scanJars Au moins un fichier JAR a été analysé pour trouver des TLDs mais il n'en contenait pas, le mode "debug" du journal peut être activé pour obtenir une liste complète de JAR scannés sans succès ; éviter d'analyser des JARs inutilement peut améliorer sensiblement le temps de démarrage et le temps de compilation des JSPs
29-May-2025 21:55:09.236 INFO [Catalina-utility-2] org.apache.catalina.startup.HostConfig.deployWAR Le déploiement de l'archive de l'application web [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] s'est terminé en [1 346] ms
29-May-2025 21:58:43.316 INFO [http-nio-8080-exec-258] org.hibernate.Version.logVersion HHH000412: Hibernate ORM core version 6.4.4.Final
29-May-2025 21:58:43.635 INFO [http-nio-8080-exec-258] org.hibernate.cache.internal.RegionFactoryInitiator.initiateService HHH000026: Second-level cache disabled
29-May-2025 21:58:43.797 WARN [http-nio-8080-exec-258] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.configure HHH10001002: Using built-in connection pool (not intended for production use)
29-May-2025 21:58:43.803 INFO [http-nio-8080-exec-258] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001005: Loaded JDBC driver class: com.mysql.cj.jdbc.Driver
29-May-2025 21:58:43.803 INFO [http-nio-8080-exec-258] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001012: Connecting with JDBC URL [**************************************]
29-May-2025 21:58:43.803 INFO [http-nio-8080-exec-258] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001001: Connection properties: {password=****, user=root}
29-May-2025 21:58:43.803 INFO [http-nio-8080-exec-258] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001003: Autocommit mode: false
29-May-2025 21:58:43.807 INFO [http-nio-8080-exec-258] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl$PooledConnections.<init> HHH10001115: Connection pool size: 20 (min=1)
29-May-2025 21:58:44.049 WARN [http-nio-8080-exec-258] org.hibernate.dialect.Dialect.checkVersion HHH000511: The 5.5.0 version for [org.hibernate.dialect.MySQLDialect] is no longer supported, hence certain features may not work properly. The minimum supported version is 8.0.0. Check the community dialects project for available legacy versions.
29-May-2025 21:58:44.055 WARN [http-nio-8080-exec-258] org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.constructDialect HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
29-May-2025 21:58:45.670 INFO [http-nio-8080-exec-258] org.hibernate.engine.transaction.jta.platform.internal.JtaPlatformInitiator.initiateService HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
29-May-2025 21:58:45.707 INFO [http-nio-8080-exec-258] org.hibernate.resource.transaction.backend.jdbc.internal.DdlTransactionIsolatorNonJtaImpl.getIsolatedConnection HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@4fcda72e] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
29-May-2025 21:58:46.514 INFO [http-nio-8080-exec-252] controller.LoginServlet.doPost Utilisateur connectï¿½ avec succï¿½s: <EMAIL>
29-May-2025 21:58:46.514 INFO [http-nio-8080-exec-258] controller.LoginServlet.doPost Utilisateur connectï¿½ avec succï¿½s: <EMAIL>
29-May-2025 21:58:46.525 INFO [http-nio-8080-exec-253] dao.StationDAO.findAll Retrieved 10 stations
29-May-2025 21:58:57.918 INFO [http-nio-8080-exec-251] dao.StationDAO.findAll Retrieved 10 stations
29-May-2025 21:59:23.786 INFO [http-nio-8080-exec-260] controller.DownloadTicketServlet.doGet GÃ©nÃ©ration du PDF pour la rÃ©servation ID: 14
29-May-2025 21:59:23.786 INFO [http-nio-8080-exec-260] controller.DownloadTicketServlet.generatePDF DÃ©but de la gÃ©nÃ©ration du PDF pour la rÃ©servation ID: 14
29-May-2025 21:59:24.028 INFO [http-nio-8080-exec-260] controller.DownloadTicketServlet.generatePDF Ã‰criture du PDF dans la rÃ©ponse pour la rÃ©servation ID: 14
29-May-2025 21:59:24.032 INFO [http-nio-8080-exec-260] controller.DownloadTicketServlet.generatePDF PDF gÃ©nÃ©rÃ© et envoyÃ© avec succÃ¨s pour la rÃ©servation ID: 14
29-May-2025 21:59:28.510 INFO [http-nio-8080-exec-253] controller.DownloadTicketServlet.doGet GÃ©nÃ©ration du PDF pour la rÃ©servation ID: 14
29-May-2025 21:59:28.512 INFO [http-nio-8080-exec-253] controller.DownloadTicketServlet.generatePDF DÃ©but de la gÃ©nÃ©ration du PDF pour la rÃ©servation ID: 14
29-May-2025 21:59:28.512 INFO [http-nio-8080-exec-253] controller.DownloadTicketServlet.generatePDF Ã‰criture du PDF dans la rÃ©ponse pour la rÃ©servation ID: 14
29-May-2025 21:59:28.513 INFO [http-nio-8080-exec-253] controller.DownloadTicketServlet.generatePDF PDF gÃ©nÃ©rÃ© et envoyÃ© avec succÃ¨s pour la rÃ©servation ID: 14
29-May-2025 21:59:48.611 INFO [http-nio-8080-exec-248] dao.StationDAO.findAll Retrieved 10 stations
29-May-2025 22:00:49.105 INFO [http-nio-8080-exec-260] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
29-May-2025 22:00:52.028 INFO [http-nio-8080-exec-263] controller.ModifierReservationServlet.doGet Accï¿½s ï¿½ doGet - ID: 4
29-May-2025 22:00:52.032 INFO [http-nio-8080-exec-263] controller.ModifierReservationServlet.doGet Rï¿½servation rï¿½cupï¿½rï¿½e: model.Reservation@78461592
29-May-2025 22:00:52.032 INFO [http-nio-8080-exec-263] controller.ModifierReservationServlet.doGet ï¿½tat de la rï¿½servation: acheté
29-May-2025 22:00:52.033 WARNING [http-nio-8080-exec-263] controller.ModifierReservationServlet.doGet Rï¿½servation non modifiable (ï¿½tat: acheté) pour l'ID: 4
29-May-2025 22:01:00.581 INFO [http-nio-8080-exec-264] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
29-May-2025 22:01:03.933 WARNING [http-nio-8080-exec-265] controller.AnnulerReservationServlet.doGet Rï¿½servation non annulable (ï¿½tat: acheté) pour l'ID: 4
29-May-2025 22:01:03.939 INFO [http-nio-8080-exec-262] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
29-May-2025 22:01:06.569 WARNING [http-nio-8080-exec-259] controller.AnnulerReservationServlet.doGet Rï¿½servation non annulable (ï¿½tat: acheté) pour l'ID: 4
29-May-2025 22:01:06.575 INFO [http-nio-8080-exec-257] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
29-May-2025 22:01:14.861 INFO [http-nio-8080-exec-260] dao.StationDAO.findAll Retrieved 10 stations
