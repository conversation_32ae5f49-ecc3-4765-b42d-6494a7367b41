30-May-2025 00:09:53.380 WARNING [http-nio-8080-exec-260] controller.ModifierReservationServlet.doGet Utilisateur non connectï¿½ lors de l'accï¿½s ï¿½ /modifierReservation
30-May-2025 00:10:00.347 INFO [http-nio-8080-exec-261] controller.LoginServlet.doPost Utilisateur connectï¿½ avec succï¿½s: <EMAIL>
30-May-2025 00:10:00.355 INFO [http-nio-8080-exec-264] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:10:11.069 INFO [http-nio-8080-exec-262] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:10:33.631 INFO [http-nio-8080-exec-258] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
30-May-2025 00:11:24.758 INFO [http-nio-8080-exec-261] controller.ModifierReservationServlet.doGet Accï¿½s ï¿½ doGet - ID: 4
30-May-2025 00:11:24.760 INFO [http-nio-8080-exec-261] controller.ModifierReservationServlet.doGet Rï¿½servation rï¿½cupï¿½rï¿½e: model.Reservation@5f64616c
30-May-2025 00:11:24.761 INFO [http-nio-8080-exec-261] controller.ModifierReservationServlet.doGet ï¿½tat de la rï¿½servation: acheté
30-May-2025 00:11:24.761 WARNING [http-nio-8080-exec-261] controller.ModifierReservationServlet.doGet Rï¿½servation non modifiable (ï¿½tat: acheté) pour l'ID: 4
30-May-2025 00:11:37.299 WARNING [http-nio-8080-exec-262] controller.AnnulerReservationServlet.doGet Rï¿½servation non annulable (ï¿½tat: acheté) pour l'ID: 4
30-May-2025 00:11:37.304 INFO [http-nio-8080-exec-259] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
30-May-2025 00:11:39.528 INFO [http-nio-8080-exec-257] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
30-May-2025 00:11:43.798 INFO [http-nio-8080-exec-258] controller.ModifierReservationServlet.doGet Accï¿½s ï¿½ doGet - ID: 13
30-May-2025 00:11:43.803 INFO [http-nio-8080-exec-258] controller.ModifierReservationServlet.doGet Rï¿½servation rï¿½cupï¿½rï¿½e: model.Reservation@12f320b7
30-May-2025 00:11:43.804 INFO [http-nio-8080-exec-258] controller.ModifierReservationServlet.doGet ï¿½tat de la rï¿½servation: acheté
30-May-2025 00:11:43.804 WARNING [http-nio-8080-exec-258] controller.ModifierReservationServlet.doGet Rï¿½servation non modifiable (ï¿½tat: acheté) pour l'ID: 13
30-May-2025 00:11:55.915 INFO [http-nio-8080-exec-263] controller.ModifierReservationServlet.doGet Accï¿½s ï¿½ doGet - ID: 1
30-May-2025 00:11:55.920 INFO [http-nio-8080-exec-263] controller.ModifierReservationServlet.doGet Rï¿½servation rï¿½cupï¿½rï¿½e: model.Reservation@1945470d
30-May-2025 00:11:55.920 INFO [http-nio-8080-exec-263] controller.ModifierReservationServlet.doGet ï¿½tat de la rï¿½servation: acheté
30-May-2025 00:11:55.921 WARNING [http-nio-8080-exec-263] controller.ModifierReservationServlet.doGet Rï¿½servation non modifiable (ï¿½tat: acheté) pour l'ID: 1
30-May-2025 00:12:03.674 INFO [http-nio-8080-exec-264] controller.ModifierReservationServlet.doGet Accï¿½s ï¿½ doGet - ID: 1
30-May-2025 00:12:03.678 INFO [http-nio-8080-exec-264] controller.ModifierReservationServlet.doGet Rï¿½servation rï¿½cupï¿½rï¿½e: model.Reservation@58b81786
30-May-2025 00:12:03.679 INFO [http-nio-8080-exec-264] controller.ModifierReservationServlet.doGet ï¿½tat de la rï¿½servation: acheté
30-May-2025 00:12:03.679 WARNING [http-nio-8080-exec-264] controller.ModifierReservationServlet.doGet Rï¿½servation non modifiable (ï¿½tat: acheté) pour l'ID: 1
30-May-2025 00:12:12.577 WARNING [http-nio-8080-exec-262] controller.AnnulerReservationServlet.doGet Rï¿½servation non annulable (ï¿½tat: acheté) pour l'ID: 1
30-May-2025 00:12:12.583 INFO [http-nio-8080-exec-259] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
30-May-2025 00:13:37.473 INFO [http-nio-8080-exec-256] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
30-May-2025 00:13:44.398 INFO [http-nio-8080-exec-260] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
30-May-2025 00:13:56.856 INFO [http-nio-8080-exec-261] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
30-May-2025 00:14:06.056 INFO [http-nio-8080-exec-265] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
30-May-2025 00:14:11.429 INFO [http-nio-8080-exec-262] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
30-May-2025 00:14:22.338 INFO [http-nio-8080-exec-259] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
30-May-2025 00:14:25.572 INFO [http-nio-8080-exec-257] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
30-May-2025 00:14:25.795 INFO [http-nio-8080-exec-258] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
30-May-2025 00:14:38.703 WARNING [http-nio-8080-exec-263] controller.AnnulerReservationServlet.doGet Rï¿½servation non annulable (ï¿½tat: utilisé) pour l'ID: 5
30-May-2025 00:14:38.709 INFO [http-nio-8080-exec-261] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
30-May-2025 00:15:20.314 WARNING [http-nio-8080-exec-262] controller.AnnulerReservationServlet.doGet Rï¿½servation non annulable (ï¿½tat: acheté) pour l'ID: 8
30-May-2025 00:15:20.320 INFO [http-nio-8080-exec-259] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
30-May-2025 00:15:28.665 WARNING [http-nio-8080-exec-257] controller.AnnulerReservationServlet.doGet Rï¿½servation non annulable (ï¿½tat: acheté) pour l'ID: 6
30-May-2025 00:15:28.670 INFO [http-nio-8080-exec-258] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
30-May-2025 00:15:31.424 INFO [http-nio-8080-exec-256] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
30-May-2025 00:16:31.309 INFO [http-nio-8080-exec-259] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
30-May-2025 00:26:06.688 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.undeploy Retrait de l'application web ayant pour chemin de contexte [/jeetraing]
30-May-2025 00:26:06.690 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesJdbc L'application web [jeetraing] a enregistré un pilote JDBC [com.mysql.cj.jdbc.Driver], mais ne l'a pas désenregistré avant l'arrêt de l'application.  Pour éviter une fuite de mémoire, le pilote JDBC a été désenregistré de force.
30-May-2025 00:26:06.691 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [mysql-cj-abandoned-connection-cleanup] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/java.lang.Object.wait(Native Method)
 java.base@17.0.12/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:155)
 com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:91)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
30-May-2025 00:26:06.692 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [Hibernate Connection Pool Validation Thread] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.12/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:252)
 java.base@17.0.12/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1672)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
30-May-2025 00:26:06.739 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.deployWAR Déploiement de l'archive [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] de l'application web
30-May-2025 00:26:07.451 INFO [mysql-cj-abandoned-connection-cleanup] org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading Impossible de charger [], ce chargeur de classes a déjà été arrêté
	java.lang.IllegalStateException: Impossible de charger [], ce chargeur de classes a déjà été arrêté
		at org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading(WebappClassLoaderBase.java:1400)
		at org.apache.catalina.loader.WebappClassLoaderBase.getResource(WebappClassLoaderBase.java:987)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.checkThreadContextClassLoader(AbandonedConnectionCleanupThread.java:123)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:90)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:842)
30-May-2025 00:26:09.199 INFO [Catalina-utility-1] org.apache.jasper.servlet.TldScanner.scanJars Au moins un fichier JAR a été analysé pour trouver des TLDs mais il n'en contenait pas, le mode "debug" du journal peut être activé pour obtenir une liste complète de JAR scannés sans succès ; éviter d'analyser des JARs inutilement peut améliorer sensiblement le temps de démarrage et le temps de compilation des JSPs
30-May-2025 00:26:09.226 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.deployWAR Le déploiement de l'archive de l'application web [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] s'est terminé en [2 486] ms
30-May-2025 00:28:35.760 INFO [http-nio-8080-exec-266] org.hibernate.Version.logVersion HHH000412: Hibernate ORM core version 6.4.4.Final
30-May-2025 00:28:36.140 INFO [http-nio-8080-exec-266] org.hibernate.cache.internal.RegionFactoryInitiator.initiateService HHH000026: Second-level cache disabled
30-May-2025 00:28:36.409 WARN [http-nio-8080-exec-266] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.configure HHH10001002: Using built-in connection pool (not intended for production use)
30-May-2025 00:28:36.415 INFO [http-nio-8080-exec-266] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001005: Loaded JDBC driver class: com.mysql.cj.jdbc.Driver
30-May-2025 00:28:36.415 INFO [http-nio-8080-exec-266] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001012: Connecting with JDBC URL [**************************************]
30-May-2025 00:28:36.416 INFO [http-nio-8080-exec-266] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001001: Connection properties: {password=****, user=root}
30-May-2025 00:28:36.416 INFO [http-nio-8080-exec-266] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001003: Autocommit mode: false
30-May-2025 00:28:36.421 INFO [http-nio-8080-exec-266] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl$PooledConnections.<init> HHH10001115: Connection pool size: 20 (min=1)
30-May-2025 00:28:36.694 WARN [http-nio-8080-exec-266] org.hibernate.dialect.Dialect.checkVersion HHH000511: The 5.5.0 version for [org.hibernate.dialect.MySQLDialect] is no longer supported, hence certain features may not work properly. The minimum supported version is 8.0.0. Check the community dialects project for available legacy versions.
30-May-2025 00:28:36.701 WARN [http-nio-8080-exec-266] org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.constructDialect HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
30-May-2025 00:28:38.639 INFO [http-nio-8080-exec-266] org.hibernate.engine.transaction.jta.platform.internal.JtaPlatformInitiator.initiateService HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
30-May-2025 00:28:38.684 INFO [http-nio-8080-exec-266] org.hibernate.resource.transaction.backend.jdbc.internal.DdlTransactionIsolatorNonJtaImpl.getIsolatedConnection HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@6486503d] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
30-May-2025 00:28:38.793 WARNING [http-nio-8080-exec-266] controller.MonCompteServlet.doGet Utilisateur non connectï¿½ lors de l'accï¿½s ï¿½ /monCompte
30-May-2025 00:35:43.900 INFO [http-nio-8080-exec-269] controller.LoginServlet.doPost Utilisateur connectï¿½ avec succï¿½s: <EMAIL>
30-May-2025 00:35:43.911 INFO [http-nio-8080-exec-266] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:35:53.728 INFO [http-nio-8080-exec-267] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:35:57.099 INFO [http-nio-8080-exec-270] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:36:23.316 INFO [http-nio-8080-exec-268] controller.DownloadTicketServlet.doGet GÃ©nÃ©ration du PDF pour la rÃ©servation ID: 15
30-May-2025 00:36:23.316 INFO [http-nio-8080-exec-268] controller.DownloadTicketServlet.generatePDF DÃ©but de la gÃ©nÃ©ration du PDF pour la rÃ©servation ID: 15
30-May-2025 00:36:23.580 INFO [http-nio-8080-exec-268] controller.DownloadTicketServlet.generatePDF Ã‰criture du PDF dans la rÃ©ponse pour la rÃ©servation ID: 15
30-May-2025 00:36:23.583 INFO [http-nio-8080-exec-268] controller.DownloadTicketServlet.generatePDF PDF gÃ©nÃ©rÃ© et envoyÃ© avec succÃ¨s pour la rÃ©servation ID: 15
30-May-2025 00:36:38.594 INFO [http-nio-8080-exec-269] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
30-May-2025 00:37:02.262 WARNING [http-nio-8080-exec-270] controller.AnnulerReservationServlet.doGet Rï¿½servation non annulable (ï¿½tat: acheté) pour l'ID: 9
30-May-2025 00:37:02.268 INFO [http-nio-8080-exec-271] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
30-May-2025 00:38:28.975 INFO [http-nio-8080-exec-272] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
30-May-2025 00:39:06.475 WARNING [http-nio-8080-exec-268] controller.AnnulerReservationServlet.doGet Rï¿½servation non annulable (ï¿½tat: acheté) pour l'ID: 4
30-May-2025 00:39:06.480 INFO [http-nio-8080-exec-269] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
30-May-2025 00:41:56.453 INFO [http-nio-8080-exec-266] controller.LoginServlet.doPost Utilisateur connectï¿½ avec succï¿½s: <EMAIL>
30-May-2025 00:41:56.456 INFO [http-nio-8080-exec-267] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
30-May-2025 00:42:56.923 INFO [http-nio-8080-exec-266] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
30-May-2025 00:43:03.615 INFO [http-nio-8080-exec-270] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
30-May-2025 00:43:10.618 INFO [http-nio-8080-exec-272] controller.ReservationEvolutionServlet.doGet doGet - Evolution Data: [[Ljava.lang.Object;@2607df97, [Ljava.lang.Object;@438b6309]
30-May-2025 00:43:10.620 INFO [http-nio-8080-exec-272] controller.ReservationEvolutionServlet.doGet doGet - Labels: [2025-04, 2025-05]
30-May-2025 00:43:10.621 INFO [http-nio-8080-exec-272] controller.ReservationEvolutionServlet.doGet doGet - Data: [0, 15]
30-May-2025 00:43:10.621 INFO [http-nio-8080-exec-272] controller.ReservationEvolutionServlet.doGet doGet - Attributes set - evolutionData: [[Ljava.lang.Object;@2607df97, [Ljava.lang.Object;@438b6309]
30-May-2025 00:43:20.979 INFO [http-nio-8080-exec-275] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:43:37.522 INFO [http-nio-8080-exec-268] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:43:49.895 INFO [http-nio-8080-exec-266] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:43:57.687 INFO [http-nio-8080-exec-267] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:44:05.105 INFO [http-nio-8080-exec-271] dao.StationDAO.update Station updated successfully: 8
30-May-2025 00:44:05.111 INFO [http-nio-8080-exec-270] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:44:17.442 INFO [http-nio-8080-exec-273] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:44:23.984 INFO [http-nio-8080-exec-274] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:44:28.034 INFO [http-nio-8080-exec-275] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:44:32.840 INFO [http-nio-8080-exec-268] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:44:37.395 INFO [http-nio-8080-exec-269] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:44:43.366 INFO [http-nio-8080-exec-267] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:44:43.586 INFO [http-nio-8080-exec-270] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:44:52.496 INFO [http-nio-8080-exec-272] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:45:23.918 INFO [http-nio-8080-exec-275] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:46:10.776 INFO [Catalina-utility-2] org.apache.catalina.startup.HostConfig.undeploy Retrait de l'application web ayant pour chemin de contexte [/jeetraing]
30-May-2025 00:46:10.779 WARNING [Catalina-utility-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesJdbc L'application web [jeetraing] a enregistré un pilote JDBC [com.mysql.cj.jdbc.Driver], mais ne l'a pas désenregistré avant l'arrêt de l'application.  Pour éviter une fuite de mémoire, le pilote JDBC a été désenregistré de force.
30-May-2025 00:46:10.781 WARNING [Catalina-utility-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [mysql-cj-abandoned-connection-cleanup] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/java.lang.Object.wait(Native Method)
 java.base@17.0.12/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:155)
 com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:91)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
30-May-2025 00:46:10.782 WARNING [Catalina-utility-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [Hibernate Connection Pool Validation Thread] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.12/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:252)
 java.base@17.0.12/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1672)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
30-May-2025 00:46:10.841 INFO [Catalina-utility-2] org.apache.catalina.startup.HostConfig.deployWAR Déploiement de l'archive [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] de l'application web
30-May-2025 00:46:11.896 INFO [Catalina-utility-2] org.apache.jasper.servlet.TldScanner.scanJars Au moins un fichier JAR a été analysé pour trouver des TLDs mais il n'en contenait pas, le mode "debug" du journal peut être activé pour obtenir une liste complète de JAR scannés sans succès ; éviter d'analyser des JARs inutilement peut améliorer sensiblement le temps de démarrage et le temps de compilation des JSPs
30-May-2025 00:46:11.916 INFO [Catalina-utility-2] org.apache.catalina.startup.HostConfig.deployWAR Le déploiement de l'archive de l'application web [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] s'est terminé en [1 075] ms
30-May-2025 00:46:13.138 WARNING [http-nio-8080-exec-275] controller.AdminDashboardServlet.doGet Utilisateur non connectÃ© lors de l'accÃ¨s au dashboard admin
30-May-2025 00:46:13.161 INFO [mysql-cj-abandoned-connection-cleanup] org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading Impossible de charger [], ce chargeur de classes a déjà été arrêté
	java.lang.IllegalStateException: Impossible de charger [], ce chargeur de classes a déjà été arrêté
		at org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading(WebappClassLoaderBase.java:1400)
		at org.apache.catalina.loader.WebappClassLoaderBase.getResource(WebappClassLoaderBase.java:987)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.checkThreadContextClassLoader(AbandonedConnectionCleanupThread.java:123)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:90)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:842)
30-May-2025 00:46:13.258 INFO [http-nio-8080-exec-274] org.hibernate.Version.logVersion HHH000412: Hibernate ORM core version 6.4.4.Final
30-May-2025 00:46:13.621 INFO [http-nio-8080-exec-274] org.hibernate.cache.internal.RegionFactoryInitiator.initiateService HHH000026: Second-level cache disabled
30-May-2025 00:46:13.809 WARN [http-nio-8080-exec-274] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.configure HHH10001002: Using built-in connection pool (not intended for production use)
30-May-2025 00:46:13.813 INFO [http-nio-8080-exec-274] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001005: Loaded JDBC driver class: com.mysql.cj.jdbc.Driver
30-May-2025 00:46:13.813 INFO [http-nio-8080-exec-274] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001012: Connecting with JDBC URL [**************************************]
30-May-2025 00:46:13.814 INFO [http-nio-8080-exec-274] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001001: Connection properties: {password=****, user=root}
30-May-2025 00:46:13.814 INFO [http-nio-8080-exec-274] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001003: Autocommit mode: false
30-May-2025 00:46:13.820 INFO [http-nio-8080-exec-274] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl$PooledConnections.<init> HHH10001115: Connection pool size: 20 (min=1)
30-May-2025 00:46:14.073 WARN [http-nio-8080-exec-274] org.hibernate.dialect.Dialect.checkVersion HHH000511: The 5.5.0 version for [org.hibernate.dialect.MySQLDialect] is no longer supported, hence certain features may not work properly. The minimum supported version is 8.0.0. Check the community dialects project for available legacy versions.
30-May-2025 00:46:14.079 WARN [http-nio-8080-exec-274] org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.constructDialect HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
30-May-2025 00:46:15.824 INFO [http-nio-8080-exec-274] org.hibernate.engine.transaction.jta.platform.internal.JtaPlatformInitiator.initiateService HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
30-May-2025 00:46:15.867 INFO [http-nio-8080-exec-274] org.hibernate.resource.transaction.backend.jdbc.internal.DdlTransactionIsolatorNonJtaImpl.getIsolatedConnection HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@64f8b4bb] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
30-May-2025 00:47:29.422 WARNING [http-nio-8080-exec-276] controller.MesReservationsServlet.doGet Utilisateur non connectÃ© lors de l'accÃ¨s Ã  /mes-reservations
30-May-2025 00:50:02.225 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.undeploy Retrait de l'application web ayant pour chemin de contexte [/jeetraing]
30-May-2025 00:50:02.227 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesJdbc L'application web [jeetraing] a enregistré un pilote JDBC [com.mysql.cj.jdbc.Driver], mais ne l'a pas désenregistré avant l'arrêt de l'application.  Pour éviter une fuite de mémoire, le pilote JDBC a été désenregistré de force.
30-May-2025 00:50:02.229 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [mysql-cj-abandoned-connection-cleanup] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/java.lang.Object.wait(Native Method)
 java.base@17.0.12/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:155)
 com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:91)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
30-May-2025 00:50:02.230 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [Hibernate Connection Pool Validation Thread] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.12/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:252)
 java.base@17.0.12/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1672)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
30-May-2025 00:50:02.257 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.deployWAR Déploiement de l'archive [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] de l'application web
30-May-2025 00:50:03.365 INFO [Catalina-utility-1] org.apache.jasper.servlet.TldScanner.scanJars Au moins un fichier JAR a été analysé pour trouver des TLDs mais il n'en contenait pas, le mode "debug" du journal peut être activé pour obtenir une liste complète de JAR scannés sans succès ; éviter d'analyser des JARs inutilement peut améliorer sensiblement le temps de démarrage et le temps de compilation des JSPs
30-May-2025 00:50:03.387 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.deployWAR Le déploiement de l'archive de l'application web [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] s'est terminé en [1 131] ms
30-May-2025 00:50:04.150 INFO [mysql-cj-abandoned-connection-cleanup] org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading Impossible de charger [], ce chargeur de classes a déjà été arrêté
	java.lang.IllegalStateException: Impossible de charger [], ce chargeur de classes a déjà été arrêté
		at org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading(WebappClassLoaderBase.java:1400)
		at org.apache.catalina.loader.WebappClassLoaderBase.getResource(WebappClassLoaderBase.java:987)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.checkThreadContextClassLoader(AbandonedConnectionCleanupThread.java:123)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:90)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:842)
30-May-2025 00:53:17.898 INFO [http-nio-8080-exec-283] org.hibernate.Version.logVersion HHH000412: Hibernate ORM core version 6.4.4.Final
30-May-2025 00:53:18.238 INFO [http-nio-8080-exec-283] org.hibernate.cache.internal.RegionFactoryInitiator.initiateService HHH000026: Second-level cache disabled
30-May-2025 00:53:18.413 WARN [http-nio-8080-exec-283] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.configure HHH10001002: Using built-in connection pool (not intended for production use)
30-May-2025 00:53:18.418 INFO [http-nio-8080-exec-283] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001005: Loaded JDBC driver class: com.mysql.cj.jdbc.Driver
30-May-2025 00:53:18.418 INFO [http-nio-8080-exec-283] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001012: Connecting with JDBC URL [**************************************]
30-May-2025 00:53:18.418 INFO [http-nio-8080-exec-283] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001001: Connection properties: {password=****, user=root}
30-May-2025 00:53:18.419 INFO [http-nio-8080-exec-283] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001003: Autocommit mode: false
30-May-2025 00:53:18.423 INFO [http-nio-8080-exec-283] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl$PooledConnections.<init> HHH10001115: Connection pool size: 20 (min=1)
30-May-2025 00:53:18.650 WARN [http-nio-8080-exec-283] org.hibernate.dialect.Dialect.checkVersion HHH000511: The 5.5.0 version for [org.hibernate.dialect.MySQLDialect] is no longer supported, hence certain features may not work properly. The minimum supported version is 8.0.0. Check the community dialects project for available legacy versions.
30-May-2025 00:53:18.656 WARN [http-nio-8080-exec-283] org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.constructDialect HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
30-May-2025 00:53:20.351 INFO [http-nio-8080-exec-283] org.hibernate.engine.transaction.jta.platform.internal.JtaPlatformInitiator.initiateService HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
30-May-2025 00:53:20.393 INFO [http-nio-8080-exec-283] org.hibernate.resource.transaction.backend.jdbc.internal.DdlTransactionIsolatorNonJtaImpl.getIsolatedConnection HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@dca8d2a] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
30-May-2025 00:53:21.128 INFO [http-nio-8080-exec-283] controller.LoginServlet.doPost Utilisateur connectï¿½ avec succï¿½s: <EMAIL>
30-May-2025 00:53:21.140 INFO [http-nio-8080-exec-284] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:53:35.070 INFO [http-nio-8080-exec-286] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:53:46.247 INFO [http-nio-8080-exec-279] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:54:21.619 INFO [http-nio-8080-exec-285] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
30-May-2025 00:54:33.918 INFO [http-nio-8080-exec-289] controller.ModifierReservationServlet.doGet Accï¿½s ï¿½ doGet - ID: 4
30-May-2025 00:54:33.925 INFO [http-nio-8080-exec-289] controller.ModifierReservationServlet.doGet Rï¿½servation rï¿½cupï¿½rï¿½e: model.Reservation@7bb7838f
30-May-2025 00:54:33.925 INFO [http-nio-8080-exec-289] controller.ModifierReservationServlet.doGet ï¿½tat de la rï¿½servation: acheté
30-May-2025 00:54:33.926 WARNING [http-nio-8080-exec-289] controller.ModifierReservationServlet.doGet Rï¿½servation non modifiable (ï¿½tat: acheté) pour l'ID: 4
30-May-2025 00:54:56.099 INFO [http-nio-8080-exec-287] controller.ModifierReservationServlet.doGet Accï¿½s ï¿½ doGet - ID: 7
30-May-2025 00:54:56.104 INFO [http-nio-8080-exec-287] controller.ModifierReservationServlet.doGet Rï¿½servation rï¿½cupï¿½rï¿½e: model.Reservation@683605c8
30-May-2025 00:54:56.105 INFO [http-nio-8080-exec-287] controller.ModifierReservationServlet.doGet ï¿½tat de la rï¿½servation: acheté
30-May-2025 00:54:56.105 WARNING [http-nio-8080-exec-287] controller.ModifierReservationServlet.doGet Rï¿½servation non modifiable (ï¿½tat: acheté) pour l'ID: 7
30-May-2025 00:55:23.997 WARNING [http-nio-8080-exec-283] controller.AnnulerReservationServlet.doGet Rï¿½servation non annulable (ï¿½tat: acheté) pour l'ID: 15
30-May-2025 00:55:24.003 INFO [http-nio-8080-exec-284] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
30-May-2025 00:58:26.746 INFO [http-nio-8080-exec-287] controller.LoginServlet.doPost Utilisateur connectï¿½ avec succï¿½s: <EMAIL>
30-May-2025 00:58:26.750 INFO [http-nio-8080-exec-283] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
30-May-2025 00:58:45.745 INFO [http-nio-8080-exec-288] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:59:10.094 INFO [http-nio-8080-exec-290] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:59:10.233 INFO [http-nio-8080-exec-292] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:59:10.454 INFO [http-nio-8080-exec-291] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:59:10.801 INFO [http-nio-8080-exec-287] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:59:11.033 INFO [http-nio-8080-exec-283] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:59:11.877 INFO [http-nio-8080-exec-284] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:59:12.090 INFO [http-nio-8080-exec-285] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:59:16.856 INFO [http-nio-8080-exec-288] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
30-May-2025 00:59:24.233 INFO [http-nio-8080-exec-289] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
30-May-2025 00:59:27.744 INFO [http-nio-8080-exec-290] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:59:33.379 INFO [http-nio-8080-exec-291] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
30-May-2025 00:59:41.495 INFO [http-nio-8080-exec-283] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
30-May-2025 00:59:44.278 INFO [http-nio-8080-exec-284] controller.ReservationEvolutionServlet.doGet doGet - Evolution Data: [[Ljava.lang.Object;@257c8939, [Ljava.lang.Object;@72babd5]
30-May-2025 00:59:44.278 INFO [http-nio-8080-exec-284] controller.ReservationEvolutionServlet.doGet doGet - Labels: [2025-04, 2025-05]
30-May-2025 00:59:44.278 INFO [http-nio-8080-exec-284] controller.ReservationEvolutionServlet.doGet doGet - Data: [0, 16]
30-May-2025 00:59:44.279 INFO [http-nio-8080-exec-284] controller.ReservationEvolutionServlet.doGet doGet - Attributes set - evolutionData: [[Ljava.lang.Object;@257c8939, [Ljava.lang.Object;@72babd5]
30-May-2025 01:10:35.028 INFO [Catalina-utility-2] org.apache.catalina.startup.HostConfig.undeploy Retrait de l'application web ayant pour chemin de contexte [/jeetraing]
30-May-2025 01:10:35.032 WARNING [Catalina-utility-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesJdbc L'application web [jeetraing] a enregistré un pilote JDBC [com.mysql.cj.jdbc.Driver], mais ne l'a pas désenregistré avant l'arrêt de l'application.  Pour éviter une fuite de mémoire, le pilote JDBC a été désenregistré de force.
30-May-2025 01:10:35.033 WARNING [Catalina-utility-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [mysql-cj-abandoned-connection-cleanup] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/java.lang.Object.wait(Native Method)
 java.base@17.0.12/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:155)
 com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:91)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
30-May-2025 01:10:35.035 WARNING [Catalina-utility-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [Hibernate Connection Pool Validation Thread] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.12/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:252)
 java.base@17.0.12/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1672)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
30-May-2025 01:10:35.124 INFO [Catalina-utility-2] org.apache.catalina.startup.HostConfig.deployWAR Déploiement de l'archive [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] de l'application web
30-May-2025 01:10:35.231 INFO [mysql-cj-abandoned-connection-cleanup] org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading Impossible de charger [], ce chargeur de classes a déjà été arrêté
	java.lang.IllegalStateException: Impossible de charger [], ce chargeur de classes a déjà été arrêté
		at org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading(WebappClassLoaderBase.java:1400)
		at org.apache.catalina.loader.WebappClassLoaderBase.getResource(WebappClassLoaderBase.java:987)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.checkThreadContextClassLoader(AbandonedConnectionCleanupThread.java:123)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:90)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:842)
30-May-2025 01:10:36.195 INFO [Catalina-utility-2] org.apache.jasper.servlet.TldScanner.scanJars Au moins un fichier JAR a été analysé pour trouver des TLDs mais il n'en contenait pas, le mode "debug" du journal peut être activé pour obtenir une liste complète de JAR scannés sans succès ; éviter d'analyser des JARs inutilement peut améliorer sensiblement le temps de démarrage et le temps de compilation des JSPs
30-May-2025 01:10:36.215 INFO [Catalina-utility-2] org.apache.catalina.startup.HostConfig.deployWAR Le déploiement de l'archive de l'application web [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] s'est terminé en [1 091] ms
30-May-2025 01:11:37.830 INFO [http-nio-8080-exec-290] org.hibernate.Version.logVersion HHH000412: Hibernate ORM core version 6.4.4.Final
30-May-2025 01:11:38.065 INFO [http-nio-8080-exec-290] org.hibernate.cache.internal.RegionFactoryInitiator.initiateService HHH000026: Second-level cache disabled
30-May-2025 01:11:38.180 WARN [http-nio-8080-exec-290] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.configure HHH10001002: Using built-in connection pool (not intended for production use)
30-May-2025 01:11:38.183 INFO [http-nio-8080-exec-290] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001005: Loaded JDBC driver class: com.mysql.cj.jdbc.Driver
30-May-2025 01:11:38.183 INFO [http-nio-8080-exec-290] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001012: Connecting with JDBC URL [**************************************]
30-May-2025 01:11:38.184 INFO [http-nio-8080-exec-290] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001001: Connection properties: {password=****, user=root}
30-May-2025 01:11:38.184 INFO [http-nio-8080-exec-290] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001003: Autocommit mode: false
30-May-2025 01:11:38.187 INFO [http-nio-8080-exec-290] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl$PooledConnections.<init> HHH10001115: Connection pool size: 20 (min=1)
30-May-2025 01:11:38.357 WARN [http-nio-8080-exec-290] org.hibernate.dialect.Dialect.checkVersion HHH000511: The 5.5.0 version for [org.hibernate.dialect.MySQLDialect] is no longer supported, hence certain features may not work properly. The minimum supported version is 8.0.0. Check the community dialects project for available legacy versions.
30-May-2025 01:11:38.361 WARN [http-nio-8080-exec-290] org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.constructDialect HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
30-May-2025 01:11:39.477 INFO [http-nio-8080-exec-290] org.hibernate.engine.transaction.jta.platform.internal.JtaPlatformInitiator.initiateService HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
30-May-2025 01:11:39.505 INFO [http-nio-8080-exec-290] org.hibernate.resource.transaction.backend.jdbc.internal.DdlTransactionIsolatorNonJtaImpl.getIsolatedConnection HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@1813d094] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
30-May-2025 01:14:46.548 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.undeploy Retrait de l'application web ayant pour chemin de contexte [/jeetraing]
30-May-2025 01:14:46.551 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesJdbc L'application web [jeetraing] a enregistré un pilote JDBC [com.mysql.cj.jdbc.Driver], mais ne l'a pas désenregistré avant l'arrêt de l'application.  Pour éviter une fuite de mémoire, le pilote JDBC a été désenregistré de force.
30-May-2025 01:14:46.553 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [mysql-cj-abandoned-connection-cleanup] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/java.lang.Object.wait(Native Method)
 java.base@17.0.12/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:155)
 com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:91)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
30-May-2025 01:14:46.554 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [Hibernate Connection Pool Validation Thread] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.12/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:252)
 java.base@17.0.12/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1672)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
30-May-2025 01:14:46.612 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.deployWAR Déploiement de l'archive [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] de l'application web
30-May-2025 01:14:47.584 INFO [Catalina-utility-1] org.apache.jasper.servlet.TldScanner.scanJars Au moins un fichier JAR a été analysé pour trouver des TLDs mais il n'en contenait pas, le mode "debug" du journal peut être activé pour obtenir une liste complète de JAR scannés sans succès ; éviter d'analyser des JARs inutilement peut améliorer sensiblement le temps de démarrage et le temps de compilation des JSPs
30-May-2025 01:14:47.598 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.deployWAR Le déploiement de l'archive de l'application web [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] s'est terminé en [986] ms
30-May-2025 01:14:48.498 INFO [mysql-cj-abandoned-connection-cleanup] org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading Impossible de charger [], ce chargeur de classes a déjà été arrêté
	java.lang.IllegalStateException: Impossible de charger [], ce chargeur de classes a déjà été arrêté
		at org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading(WebappClassLoaderBase.java:1400)
		at org.apache.catalina.loader.WebappClassLoaderBase.getResource(WebappClassLoaderBase.java:987)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.checkThreadContextClassLoader(AbandonedConnectionCleanupThread.java:123)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:90)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:842)
30-May-2025 01:16:56.760 INFO [http-nio-8080-exec-300] org.hibernate.Version.logVersion HHH000412: Hibernate ORM core version 6.4.4.Final
30-May-2025 01:16:57.106 INFO [http-nio-8080-exec-300] org.hibernate.cache.internal.RegionFactoryInitiator.initiateService HHH000026: Second-level cache disabled
30-May-2025 01:16:57.279 WARN [http-nio-8080-exec-300] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.configure HHH10001002: Using built-in connection pool (not intended for production use)
30-May-2025 01:16:57.284 INFO [http-nio-8080-exec-300] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001005: Loaded JDBC driver class: com.mysql.cj.jdbc.Driver
30-May-2025 01:16:57.285 INFO [http-nio-8080-exec-300] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001012: Connecting with JDBC URL [**************************************]
30-May-2025 01:16:57.286 INFO [http-nio-8080-exec-300] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001001: Connection properties: {password=****, user=root}
30-May-2025 01:16:57.286 INFO [http-nio-8080-exec-300] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001003: Autocommit mode: false
30-May-2025 01:16:57.291 INFO [http-nio-8080-exec-300] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl$PooledConnections.<init> HHH10001115: Connection pool size: 20 (min=1)
30-May-2025 01:16:57.536 WARN [http-nio-8080-exec-300] org.hibernate.dialect.Dialect.checkVersion HHH000511: The 5.5.0 version for [org.hibernate.dialect.MySQLDialect] is no longer supported, hence certain features may not work properly. The minimum supported version is 8.0.0. Check the community dialects project for available legacy versions.
30-May-2025 01:16:57.544 WARN [http-nio-8080-exec-300] org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.constructDialect HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
30-May-2025 01:16:59.262 INFO [http-nio-8080-exec-300] org.hibernate.engine.transaction.jta.platform.internal.JtaPlatformInitiator.initiateService HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
30-May-2025 01:16:59.304 INFO [http-nio-8080-exec-300] org.hibernate.resource.transaction.backend.jdbc.internal.DdlTransactionIsolatorNonJtaImpl.getIsolatedConnection HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@57117f79] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
30-May-2025 01:17:00.149 INFO [http-nio-8080-exec-301] controller.LoginServlet.doPost Utilisateur connectï¿½ avec succï¿½s: <EMAIL>
30-May-2025 01:17:00.150 INFO [http-nio-8080-exec-300] controller.LoginServlet.doPost Utilisateur connectï¿½ avec succï¿½s: <EMAIL>
30-May-2025 01:17:00.153 INFO [http-nio-8080-exec-302] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
30-May-2025 01:17:04.636 INFO [http-nio-8080-exec-294] controller.AdminReservationsServlet.doGet Admin admin consulte toutes les rÃ©servations (16 total)
30-May-2025 01:17:04.994 SEVERE [http-nio-8080-exec-294] controller.AdminReservationsServlet.doGet Erreur lors de la rÃ©cupÃ©ration des rÃ©servations : Une exception s'est produite lors du traitement de [/admin/reservations.jsp] à la ligne [163]

160:                                         <td>
161:                                             <div class="date-info">
162:                                                 <i class="fas fa-calendar"></i>
163:                                                 <fmt:formatDate value="${reservation.voyage.dateDepart}" pattern="dd/MM/yyyy" />
164:                                                 <br>
165:                                                 <small>
166:                                                     <i class="fas fa-clock"></i>


Stacktrace:
30-May-2025 01:17:28.359 INFO [http-nio-8080-exec-297] controller.AdminReservationsServlet.doGet Admin admin consulte toutes les rÃ©servations (16 total)
30-May-2025 01:17:28.369 SEVERE [http-nio-8080-exec-297] controller.AdminReservationsServlet.doGet Erreur lors de la rÃ©cupÃ©ration des rÃ©servations : Une exception s'est produite lors du traitement de [/admin/reservations.jsp] à la ligne [163]

160:                                         <td>
161:                                             <div class="date-info">
162:                                                 <i class="fas fa-calendar"></i>
163:                                                 <fmt:formatDate value="${reservation.voyage.dateDepart}" pattern="dd/MM/yyyy" />
164:                                                 <br>
165:                                                 <small>
166:                                                     <i class="fas fa-clock"></i>


Stacktrace:
30-May-2025 01:17:34.067 INFO [http-nio-8080-exec-291] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 01:17:57.042 INFO [http-nio-8080-exec-300] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
30-May-2025 01:18:11.045 INFO [http-nio-8080-exec-305] controller.AdminReservationsServlet.doGet Admin admin consulte toutes les rÃ©servations (16 total)
30-May-2025 01:18:11.049 SEVERE [http-nio-8080-exec-305] controller.AdminReservationsServlet.doGet Erreur lors de la rÃ©cupÃ©ration des rÃ©servations : Une exception s'est produite lors du traitement de [/admin/reservations.jsp] à la ligne [163]

160:                                         <td>
161:                                             <div class="date-info">
162:                                                 <i class="fas fa-calendar"></i>
163:                                                 <fmt:formatDate value="${reservation.voyage.dateDepart}" pattern="dd/MM/yyyy" />
164:                                                 <br>
165:                                                 <small>
166:                                                     <i class="fas fa-clock"></i>


Stacktrace:
30-May-2025 01:18:51.713 INFO [http-nio-8080-exec-307] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
30-May-2025 01:19:02.228 INFO [http-nio-8080-exec-309] controller.ReservationEvolutionServlet.doGet doGet - Evolution Data: [[Ljava.lang.Object;@7bc04eb0, [Ljava.lang.Object;@56dd74c8]
30-May-2025 01:19:02.230 INFO [http-nio-8080-exec-309] controller.ReservationEvolutionServlet.doGet doGet - Labels: [2025-04, 2025-05]
30-May-2025 01:19:02.230 INFO [http-nio-8080-exec-309] controller.ReservationEvolutionServlet.doGet doGet - Data: [0, 16]
30-May-2025 01:19:02.230 INFO [http-nio-8080-exec-309] controller.ReservationEvolutionServlet.doGet doGet - Attributes set - evolutionData: [[Ljava.lang.Object;@7bc04eb0, [Ljava.lang.Object;@56dd74c8]
30-May-2025 01:19:12.099 INFO [http-nio-8080-exec-301] controller.ReservationEvolutionServlet.doGet doGet - Evolution Data: [[Ljava.lang.Object;@25f85d0f, [Ljava.lang.Object;@13b48cd7]
30-May-2025 01:19:12.115 INFO [http-nio-8080-exec-301] controller.ReservationEvolutionServlet.doGet doGet - Labels: [2025-04, 2025-05]
30-May-2025 01:19:12.115 INFO [http-nio-8080-exec-301] controller.ReservationEvolutionServlet.doGet doGet - Data: [0, 16]
30-May-2025 01:19:12.116 INFO [http-nio-8080-exec-301] controller.ReservationEvolutionServlet.doGet doGet - Attributes set - evolutionData: [[Ljava.lang.Object;@25f85d0f, [Ljava.lang.Object;@13b48cd7]
30-May-2025 01:19:18.906 INFO [http-nio-8080-exec-300] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
30-May-2025 01:19:22.122 INFO [http-nio-8080-exec-302] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 01:22:13.633 INFO [http-nio-8080-exec-306] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 01:22:13.863 INFO [http-nio-8080-exec-308] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 01:22:18.927 INFO [http-nio-8080-exec-309] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 01:22:19.143 INFO [http-nio-8080-exec-303] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 01:22:26.015 INFO [http-nio-8080-exec-300] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 01:23:09.873 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.undeploy Retrait de l'application web ayant pour chemin de contexte [/jeetraing]
30-May-2025 01:23:09.875 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesJdbc L'application web [jeetraing] a enregistré un pilote JDBC [com.mysql.cj.jdbc.Driver], mais ne l'a pas désenregistré avant l'arrêt de l'application.  Pour éviter une fuite de mémoire, le pilote JDBC a été désenregistré de force.
30-May-2025 01:23:09.877 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [mysql-cj-abandoned-connection-cleanup] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/java.lang.Object.wait(Native Method)
 java.base@17.0.12/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:155)
 com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:91)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
30-May-2025 01:23:09.878 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [Hibernate Connection Pool Validation Thread] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.12/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:252)
 java.base@17.0.12/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1672)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
30-May-2025 01:23:09.905 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.deployWAR Déploiement de l'archive [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] de l'application web
30-May-2025 01:23:10.894 INFO [Catalina-utility-1] org.apache.jasper.servlet.TldScanner.scanJars Au moins un fichier JAR a été analysé pour trouver des TLDs mais il n'en contenait pas, le mode "debug" du journal peut être activé pour obtenir une liste complète de JAR scannés sans succès ; éviter d'analyser des JARs inutilement peut améliorer sensiblement le temps de démarrage et le temps de compilation des JSPs
30-May-2025 01:23:10.914 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.deployWAR Le déploiement de l'archive de l'application web [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] s'est terminé en [1 009] ms
30-May-2025 01:23:12.976 INFO [mysql-cj-abandoned-connection-cleanup] org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading Impossible de charger [], ce chargeur de classes a déjà été arrêté
	java.lang.IllegalStateException: Impossible de charger [], ce chargeur de classes a déjà été arrêté
		at org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading(WebappClassLoaderBase.java:1400)
		at org.apache.catalina.loader.WebappClassLoaderBase.getResource(WebappClassLoaderBase.java:987)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.checkThreadContextClassLoader(AbandonedConnectionCleanupThread.java:123)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:90)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:842)
30-May-2025 01:23:57.075 INFO [http-nio-8080-exec-302] org.hibernate.Version.logVersion HHH000412: Hibernate ORM core version 6.4.4.Final
30-May-2025 01:23:57.297 INFO [http-nio-8080-exec-302] org.hibernate.cache.internal.RegionFactoryInitiator.initiateService HHH000026: Second-level cache disabled
30-May-2025 01:23:57.409 WARN [http-nio-8080-exec-302] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.configure HHH10001002: Using built-in connection pool (not intended for production use)
30-May-2025 01:23:57.413 INFO [http-nio-8080-exec-302] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001005: Loaded JDBC driver class: com.mysql.cj.jdbc.Driver
30-May-2025 01:23:57.413 INFO [http-nio-8080-exec-302] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001012: Connecting with JDBC URL [**************************************]
30-May-2025 01:23:57.413 INFO [http-nio-8080-exec-302] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001001: Connection properties: {password=****, user=root}
30-May-2025 01:23:57.413 INFO [http-nio-8080-exec-302] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001003: Autocommit mode: false
30-May-2025 01:23:57.416 INFO [http-nio-8080-exec-302] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl$PooledConnections.<init> HHH10001115: Connection pool size: 20 (min=1)
30-May-2025 01:23:57.576 WARN [http-nio-8080-exec-302] org.hibernate.dialect.Dialect.checkVersion HHH000511: The 5.5.0 version for [org.hibernate.dialect.MySQLDialect] is no longer supported, hence certain features may not work properly. The minimum supported version is 8.0.0. Check the community dialects project for available legacy versions.
30-May-2025 01:23:57.580 WARN [http-nio-8080-exec-302] org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.constructDialect HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
30-May-2025 01:23:58.670 INFO [http-nio-8080-exec-302] org.hibernate.engine.transaction.jta.platform.internal.JtaPlatformInitiator.initiateService HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
30-May-2025 01:23:58.697 INFO [http-nio-8080-exec-302] org.hibernate.resource.transaction.backend.jdbc.internal.DdlTransactionIsolatorNonJtaImpl.getIsolatedConnection HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@6ac915df] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
30-May-2025 01:24:10.946 INFO [http-nio-8080-exec-310] controller.LoginServlet.doPost Utilisateur connectï¿½ avec succï¿½s: <EMAIL>
30-May-2025 01:24:10.956 INFO [http-nio-8080-exec-304] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 01:24:37.063 INFO [http-nio-8080-exec-306] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 01:24:47.590 INFO [http-nio-8080-exec-309] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 01:24:55.450 INFO [http-nio-8080-exec-300] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 01:25:05.700 INFO [http-nio-8080-exec-307] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 01:25:25.405 INFO [http-nio-8080-exec-313] controller.ModifierReservationServlet.doGet Accï¿½s ï¿½ doGet - ID: 12
30-May-2025 01:25:25.412 INFO [http-nio-8080-exec-313] controller.ModifierReservationServlet.doGet Rï¿½servation rï¿½cupï¿½rï¿½e: model.Reservation@746c7736
30-May-2025 01:25:25.413 INFO [http-nio-8080-exec-313] controller.ModifierReservationServlet.doGet ï¿½tat de la rï¿½servation: acheté
30-May-2025 01:25:25.413 WARNING [http-nio-8080-exec-313] controller.ModifierReservationServlet.doGet Rï¿½servation non modifiable (ï¿½tat: acheté) pour l'ID: 12
30-May-2025 01:25:31.972 WARNING [http-nio-8080-exec-315] controller.AnnulerReservationServlet.doGet Rï¿½servation non annulable (ï¿½tat: acheté) pour l'ID: 15
30-May-2025 01:25:32.026 INFO [http-nio-8080-exec-316] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
30-May-2025 01:25:49.486 INFO [http-nio-8080-exec-318] controller.ModifierReservationServlet.doGet Accï¿½s ï¿½ doGet - ID: 7
30-May-2025 01:25:49.490 INFO [http-nio-8080-exec-318] controller.ModifierReservationServlet.doGet Rï¿½servation rï¿½cupï¿½rï¿½e: model.Reservation@1f55d69
30-May-2025 01:25:49.491 INFO [http-nio-8080-exec-318] controller.ModifierReservationServlet.doGet ï¿½tat de la rï¿½servation: acheté
30-May-2025 01:25:49.491 WARNING [http-nio-8080-exec-318] controller.ModifierReservationServlet.doGet Rï¿½servation non modifiable (ï¿½tat: acheté) pour l'ID: 7
30-May-2025 01:27:11.244 INFO [Catalina-utility-2] org.apache.catalina.startup.HostConfig.undeploy Retrait de l'application web ayant pour chemin de contexte [/jeetraing]
30-May-2025 01:27:11.246 WARNING [Catalina-utility-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesJdbc L'application web [jeetraing] a enregistré un pilote JDBC [com.mysql.cj.jdbc.Driver], mais ne l'a pas désenregistré avant l'arrêt de l'application.  Pour éviter une fuite de mémoire, le pilote JDBC a été désenregistré de force.
30-May-2025 01:27:11.247 WARNING [Catalina-utility-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [mysql-cj-abandoned-connection-cleanup] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/java.lang.Object.wait(Native Method)
 java.base@17.0.12/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:155)
 com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:91)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
30-May-2025 01:27:11.248 WARNING [Catalina-utility-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [Hibernate Connection Pool Validation Thread] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.12/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:252)
 java.base@17.0.12/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1672)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
30-May-2025 01:27:11.301 INFO [Catalina-utility-2] org.apache.catalina.startup.HostConfig.deployWAR Déploiement de l'archive [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] de l'application web
30-May-2025 01:27:12.288 INFO [Catalina-utility-2] org.apache.jasper.servlet.TldScanner.scanJars Au moins un fichier JAR a été analysé pour trouver des TLDs mais il n'en contenait pas, le mode "debug" du journal peut être activé pour obtenir une liste complète de JAR scannés sans succès ; éviter d'analyser des JARs inutilement peut améliorer sensiblement le temps de démarrage et le temps de compilation des JSPs
30-May-2025 01:27:12.305 INFO [Catalina-utility-2] org.apache.catalina.startup.HostConfig.deployWAR Le déploiement de l'archive de l'application web [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] s'est terminé en [1 004] ms
30-May-2025 01:27:12.718 INFO [mysql-cj-abandoned-connection-cleanup] org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading Impossible de charger [], ce chargeur de classes a déjà été arrêté
	java.lang.IllegalStateException: Impossible de charger [], ce chargeur de classes a déjà été arrêté
		at org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading(WebappClassLoaderBase.java:1400)
		at org.apache.catalina.loader.WebappClassLoaderBase.getResource(WebappClassLoaderBase.java:987)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.checkThreadContextClassLoader(AbandonedConnectionCleanupThread.java:123)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:90)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:842)
30-May-2025 01:31:52.248 INFO [http-nio-8080-exec-320] org.hibernate.Version.logVersion HHH000412: Hibernate ORM core version 6.4.4.Final
30-May-2025 01:31:52.474 INFO [http-nio-8080-exec-320] org.hibernate.cache.internal.RegionFactoryInitiator.initiateService HHH000026: Second-level cache disabled
30-May-2025 01:31:52.592 WARN [http-nio-8080-exec-320] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.configure HHH10001002: Using built-in connection pool (not intended for production use)
30-May-2025 01:31:52.595 INFO [http-nio-8080-exec-320] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001005: Loaded JDBC driver class: com.mysql.cj.jdbc.Driver
30-May-2025 01:31:52.595 INFO [http-nio-8080-exec-320] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001012: Connecting with JDBC URL [**************************************]
30-May-2025 01:31:52.596 INFO [http-nio-8080-exec-320] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001001: Connection properties: {password=****, user=root}
30-May-2025 01:31:52.596 INFO [http-nio-8080-exec-320] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001003: Autocommit mode: false
30-May-2025 01:31:52.600 INFO [http-nio-8080-exec-320] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl$PooledConnections.<init> HHH10001115: Connection pool size: 20 (min=1)
30-May-2025 01:31:52.769 WARN [http-nio-8080-exec-320] org.hibernate.dialect.Dialect.checkVersion HHH000511: The 5.5.0 version for [org.hibernate.dialect.MySQLDialect] is no longer supported, hence certain features may not work properly. The minimum supported version is 8.0.0. Check the community dialects project for available legacy versions.
30-May-2025 01:31:52.774 WARN [http-nio-8080-exec-320] org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.constructDialect HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
30-May-2025 01:31:53.956 INFO [http-nio-8080-exec-320] org.hibernate.engine.transaction.jta.platform.internal.JtaPlatformInitiator.initiateService HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
30-May-2025 01:31:53.983 INFO [http-nio-8080-exec-320] org.hibernate.resource.transaction.backend.jdbc.internal.DdlTransactionIsolatorNonJtaImpl.getIsolatedConnection HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@3f50edeb] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
30-May-2025 01:32:10.741 INFO [http-nio-8080-exec-318] controller.LoginServlet.doPost Utilisateur connectï¿½ avec succï¿½s: <EMAIL>
30-May-2025 01:32:10.745 INFO [http-nio-8080-exec-311] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
30-May-2025 01:32:14.053 INFO [http-nio-8080-exec-321] controller.AdminReservationsServlet.doGet Admin admin consulte toutes les rÃ©servations (16 total)
30-May-2025 01:32:14.247 SEVERE [http-nio-8080-exec-321] controller.AdminReservationsServlet.doGet Erreur lors de la rÃ©cupÃ©ration des rÃ©servations : Une exception s'est produite lors du traitement de [/admin/reservations.jsp] à la ligne [223]

220:                                         </td>
221:                                         <td>
222:                                             <div class="date-info">
223:                                                 <fmt:formatDate value="${reservation.dateReservation}" pattern="dd/MM/yyyy" />
224:                                                 <br>
225:                                                 <small>
226:                                                     <fmt:formatDate value="${reservation.dateReservation}" pattern="HH:mm" />


Stacktrace:
30-May-2025 01:33:46.447 INFO [http-nio-8080-exec-322] controller.AdminReservationsServlet.doGet Admin admin consulte toutes les rÃ©servations (16 total)
30-May-2025 01:33:46.452 SEVERE [http-nio-8080-exec-322] controller.AdminReservationsServlet.doGet Erreur lors de la rÃ©cupÃ©ration des rÃ©servations : Une exception s'est produite lors du traitement de [/admin/reservations.jsp] à la ligne [223]

220:                                         </td>
221:                                         <td>
222:                                             <div class="date-info">
223:                                                 <fmt:formatDate value="${reservation.dateReservation}" pattern="dd/MM/yyyy" />
224:                                                 <br>
225:                                                 <small>
226:                                                     <fmt:formatDate value="${reservation.dateReservation}" pattern="HH:mm" />


Stacktrace:
30-May-2025 01:33:57.026 INFO [http-nio-8080-exec-326] controller.AdminReservationsServlet.doGet Admin admin consulte toutes les rÃ©servations (16 total)
30-May-2025 01:33:57.032 SEVERE [http-nio-8080-exec-326] controller.AdminReservationsServlet.doGet Erreur lors de la rÃ©cupÃ©ration des rÃ©servations : Une exception s'est produite lors du traitement de [/admin/reservations.jsp] à la ligne [223]

220:                                         </td>
221:                                         <td>
222:                                             <div class="date-info">
223:                                                 <fmt:formatDate value="${reservation.dateReservation}" pattern="dd/MM/yyyy" />
224:                                                 <br>
225:                                                 <small>
226:                                                     <fmt:formatDate value="${reservation.dateReservation}" pattern="HH:mm" />


Stacktrace:
30-May-2025 01:33:59.523 INFO [http-nio-8080-exec-324] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
30-May-2025 01:34:12.109 INFO [http-nio-8080-exec-320] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
30-May-2025 01:34:13.958 INFO [http-nio-8080-exec-328] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
30-May-2025 01:40:44.758 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.undeploy Retrait de l'application web ayant pour chemin de contexte [/jeetraing]
30-May-2025 01:40:44.760 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesJdbc L'application web [jeetraing] a enregistré un pilote JDBC [com.mysql.cj.jdbc.Driver], mais ne l'a pas désenregistré avant l'arrêt de l'application.  Pour éviter une fuite de mémoire, le pilote JDBC a été désenregistré de force.
30-May-2025 01:40:44.761 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [mysql-cj-abandoned-connection-cleanup] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/java.lang.Object.wait(Native Method)
 java.base@17.0.12/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:155)
 com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:91)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
30-May-2025 01:40:44.762 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [Hibernate Connection Pool Validation Thread] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.12/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:252)
 java.base@17.0.12/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1672)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
30-May-2025 01:40:44.818 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.deployWAR Déploiement de l'archive [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] de l'application web
30-May-2025 01:40:45.777 INFO [Catalina-utility-1] org.apache.jasper.servlet.TldScanner.scanJars Au moins un fichier JAR a été analysé pour trouver des TLDs mais il n'en contenait pas, le mode "debug" du journal peut être activé pour obtenir une liste complète de JAR scannés sans succès ; éviter d'analyser des JARs inutilement peut améliorer sensiblement le temps de démarrage et le temps de compilation des JSPs
30-May-2025 01:40:45.791 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.deployWAR Le déploiement de l'archive de l'application web [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] s'est terminé en [973] ms
30-May-2025 01:40:48.449 INFO [mysql-cj-abandoned-connection-cleanup] org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading Impossible de charger [], ce chargeur de classes a déjà été arrêté
	java.lang.IllegalStateException: Impossible de charger [], ce chargeur de classes a déjà été arrêté
		at org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading(WebappClassLoaderBase.java:1400)
		at org.apache.catalina.loader.WebappClassLoaderBase.getResource(WebappClassLoaderBase.java:987)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.checkThreadContextClassLoader(AbandonedConnectionCleanupThread.java:123)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:90)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:842)
30-May-2025 01:44:16.027 INFO [Catalina-utility-2] org.apache.catalina.startup.HostConfig.undeploy Retrait de l'application web ayant pour chemin de contexte [/jeetraing]
30-May-2025 01:44:16.052 INFO [Catalina-utility-2] org.apache.catalina.startup.HostConfig.deployWAR Déploiement de l'archive [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] de l'application web
30-May-2025 01:44:17.064 INFO [Catalina-utility-2] org.apache.jasper.servlet.TldScanner.scanJars Au moins un fichier JAR a été analysé pour trouver des TLDs mais il n'en contenait pas, le mode "debug" du journal peut être activé pour obtenir une liste complète de JAR scannés sans succès ; éviter d'analyser des JARs inutilement peut améliorer sensiblement le temps de démarrage et le temps de compilation des JSPs
30-May-2025 01:44:17.083 INFO [Catalina-utility-2] org.apache.catalina.startup.HostConfig.deployWAR Le déploiement de l'archive de l'application web [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] s'est terminé en [1 031] ms
30-May-2025 01:46:52.611 INFO [http-nio-8080-exec-322] org.hibernate.Version.logVersion HHH000412: Hibernate ORM core version 6.4.4.Final
30-May-2025 01:46:52.980 INFO [http-nio-8080-exec-322] org.hibernate.cache.internal.RegionFactoryInitiator.initiateService HHH000026: Second-level cache disabled
30-May-2025 01:46:53.173 WARN [http-nio-8080-exec-322] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.configure HHH10001002: Using built-in connection pool (not intended for production use)
30-May-2025 01:46:53.179 INFO [http-nio-8080-exec-322] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001005: Loaded JDBC driver class: com.mysql.cj.jdbc.Driver
30-May-2025 01:46:53.179 INFO [http-nio-8080-exec-322] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001012: Connecting with JDBC URL [**************************************]
30-May-2025 01:46:53.179 INFO [http-nio-8080-exec-322] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001001: Connection properties: {password=****, user=root}
30-May-2025 01:46:53.180 INFO [http-nio-8080-exec-322] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001003: Autocommit mode: false
30-May-2025 01:46:53.184 INFO [http-nio-8080-exec-322] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl$PooledConnections.<init> HHH10001115: Connection pool size: 20 (min=1)
30-May-2025 01:46:53.434 WARN [http-nio-8080-exec-322] org.hibernate.dialect.Dialect.checkVersion HHH000511: The 5.5.0 version for [org.hibernate.dialect.MySQLDialect] is no longer supported, hence certain features may not work properly. The minimum supported version is 8.0.0. Check the community dialects project for available legacy versions.
30-May-2025 01:46:53.441 WARN [http-nio-8080-exec-322] org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.constructDialect HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
30-May-2025 01:46:55.256 INFO [http-nio-8080-exec-322] org.hibernate.engine.transaction.jta.platform.internal.JtaPlatformInitiator.initiateService HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
30-May-2025 01:46:55.298 INFO [http-nio-8080-exec-322] org.hibernate.resource.transaction.backend.jdbc.internal.DdlTransactionIsolatorNonJtaImpl.getIsolatedConnection HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@1b88f54c] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
30-May-2025 01:46:56.204 INFO [http-nio-8080-exec-330] controller.LoginServlet.doPost Utilisateur connectï¿½ avec succï¿½s: <EMAIL>
30-May-2025 01:46:56.204 INFO [http-nio-8080-exec-322] controller.LoginServlet.doPost Utilisateur connectï¿½ avec succï¿½s: <EMAIL>
30-May-2025 01:46:56.216 INFO [http-nio-8080-exec-326] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 01:47:55.356 INFO [http-nio-8080-exec-333] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 01:48:25.938 INFO [http-nio-8080-exec-337] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 01:49:02.942 INFO [http-nio-8080-exec-333] controller.ModifierReservationServlet.doGet Accï¿½s ï¿½ doGet - ID: null
30-May-2025 01:49:02.943 WARNING [http-nio-8080-exec-333] controller.ModifierReservationServlet.doGet ID de rï¿½servation manquant lors de l'accï¿½s ï¿½ /modifierReservation
30-May-2025 01:49:03.084 INFO [http-nio-8080-exec-341] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
30-May-2025 01:49:11.342 SEVERE [http-nio-8080-exec-335] controller.MesReservationsServlet.doGet Erreur lors de la rÃ©cupÃ©ration des rÃ©servations: /mes-reservations.jsp (ligne : [199], colonne : [44]) [${reservation.etat == 'en attente d'annulation'}] contient d'incorrecte(s) expression(s) : [jakarta.el.ELException: Impossible de traiter l'expression [${reservation.etat == 'en attente d'annulation'}]]
30-May-2025 01:49:23.712 INFO [http-nio-8080-exec-337] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
30-May-2025 01:49:26.257 INFO [http-nio-8080-exec-338] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
30-May-2025 01:49:26.403 INFO [http-nio-8080-exec-334] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
30-May-2025 01:49:26.571 INFO [http-nio-8080-exec-339] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
30-May-2025 01:49:26.724 INFO [http-nio-8080-exec-340] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
30-May-2025 01:49:26.880 INFO [http-nio-8080-exec-342] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
30-May-2025 01:49:27.022 INFO [http-nio-8080-exec-333] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
30-May-2025 01:49:27.207 INFO [http-nio-8080-exec-341] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
30-May-2025 01:49:29.792 SEVERE [http-nio-8080-exec-335] controller.MesReservationsServlet.doGet Erreur lors de la rÃ©cupÃ©ration des rÃ©servations: /mes-reservations.jsp (ligne : [199], colonne : [44]) [${reservation.etat == 'en attente d'annulation'}] contient d'incorrecte(s) expression(s) : [jakarta.el.ELException: Impossible de traiter l'expression [${reservation.etat == 'en attente d'annulation'}]]
30-May-2025 01:49:55.557 INFO [http-nio-8080-exec-334] controller.LoginServlet.doPost Utilisateur connectï¿½ avec succï¿½s: <EMAIL>
30-May-2025 01:49:55.563 INFO [http-nio-8080-exec-339] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
30-May-2025 01:50:01.059 INFO [http-nio-8080-exec-333] controller.AdminReservationsServlet.doGet Admin admin consulte toutes les rÃ©servations (16 total)
30-May-2025 01:50:01.328 SEVERE [http-nio-8080-exec-333] controller.AdminReservationsServlet.doGet Erreur lors de la rÃ©cupÃ©ration des rÃ©servations : Une exception s'est produite lors du traitement de [/admin/reservations.jsp] à la ligne [223]

220:                                         </td>
221:                                         <td>
222:                                             <div class="date-info">
223:                                                 <fmt:formatDate value="${reservation.dateReservation}" pattern="dd/MM/yyyy" />
224:                                                 <br>
225:                                                 <small>
226:                                                     <fmt:formatDate value="${reservation.dateReservation}" pattern="HH:mm" />


Stacktrace:
30-May-2025 01:50:08.196 INFO [http-nio-8080-exec-341] controller.AdminReservationsServlet.doGet Admin admin consulte toutes les rÃ©servations (16 total)
30-May-2025 01:50:08.210 SEVERE [http-nio-8080-exec-341] controller.AdminReservationsServlet.doGet Erreur lors de la rÃ©cupÃ©ration des rÃ©servations : Une exception s'est produite lors du traitement de [/admin/reservations.jsp] à la ligne [223]

220:                                         </td>
221:                                         <td>
222:                                             <div class="date-info">
223:                                                 <fmt:formatDate value="${reservation.dateReservation}" pattern="dd/MM/yyyy" />
224:                                                 <br>
225:                                                 <small>
226:                                                     <fmt:formatDate value="${reservation.dateReservation}" pattern="HH:mm" />


Stacktrace:
30-May-2025 01:50:45.002 INFO [http-nio-8080-exec-337] controller.LoginServlet.doPost Utilisateur connectï¿½ avec succï¿½s: <EMAIL>
30-May-2025 01:50:45.006 INFO [http-nio-8080-exec-338] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
30-May-2025 01:50:49.688 INFO [http-nio-8080-exec-334] controller.AdminReservationsServlet.doGet Admin admin consulte toutes les rÃ©servations (16 total)
30-May-2025 01:50:49.699 SEVERE [http-nio-8080-exec-334] controller.AdminReservationsServlet.doGet Erreur lors de la rÃ©cupÃ©ration des rÃ©servations : Une exception s'est produite lors du traitement de [/admin/reservations.jsp] à la ligne [223]

220:                                         </td>
221:                                         <td>
222:                                             <div class="date-info">
223:                                                 <fmt:formatDate value="${reservation.dateReservation}" pattern="dd/MM/yyyy" />
224:                                                 <br>
225:                                                 <small>
226:                                                     <fmt:formatDate value="${reservation.dateReservation}" pattern="HH:mm" />


Stacktrace:
30-May-2025 01:51:07.648 INFO [http-nio-8080-exec-340] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 14:46:56.151 INFO [http-nio-8080-exec-336] controller.LoginServlet.doPost Utilisateur connectï¿½ avec succï¿½s: <EMAIL>
30-May-2025 14:46:56.157 INFO [http-nio-8080-exec-337] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 14:47:19.374 INFO [http-nio-8080-exec-339] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 14:47:40.697 INFO [http-nio-8080-exec-333] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 14:47:50.846 INFO [http-nio-8080-exec-341] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 14:48:08.688 INFO [http-nio-8080-exec-337] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 14:48:20.761 INFO [http-nio-8080-exec-338] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 14:48:46.647 INFO [http-nio-8080-exec-333] controller.DownloadTicketServlet.doGet GÃ©nÃ©ration du PDF pour la rÃ©servation ID: 17
30-May-2025 14:48:46.647 INFO [http-nio-8080-exec-333] controller.DownloadTicketServlet.generatePDF DÃ©but de la gÃ©nÃ©ration du PDF pour la rÃ©servation ID: 17
30-May-2025 14:48:46.908 INFO [http-nio-8080-exec-333] controller.DownloadTicketServlet.generatePDF Ã‰criture du PDF dans la rÃ©ponse pour la rÃ©servation ID: 17
30-May-2025 14:48:46.911 INFO [http-nio-8080-exec-333] controller.DownloadTicketServlet.generatePDF PDF gÃ©nÃ©rÃ© et envoyÃ© avec succÃ¨s pour la rÃ©servation ID: 17
30-May-2025 14:48:56.630 SEVERE [http-nio-8080-exec-341] controller.MesReservationsServlet.doGet Erreur lors de la rÃ©cupÃ©ration des rÃ©servations: /mes-reservations.jsp (ligne : [199], colonne : [44]) [${reservation.etat == 'en attente d'annulation'}] contient d'incorrecte(s) expression(s) : [jakarta.el.ELException: Impossible de traiter l'expression [${reservation.etat == 'en attente d'annulation'}]]
30-May-2025 14:49:06.541 INFO [http-nio-8080-exec-336] controller.MonCompteServlet.doGet Found 1 reservations to update to 'utilisï¿½'
30-May-2025 14:49:06.548 INFO [http-nio-8080-exec-336] controller.MonCompteServlet.doGet Updated reservation ID 17 to 'utilisï¿½'
30-May-2025 14:49:12.128 SEVERE [http-nio-8080-exec-337] controller.MesReservationsServlet.doGet Erreur lors de la rÃ©cupÃ©ration des rÃ©servations: /mes-reservations.jsp (ligne : [199], colonne : [44]) [${reservation.etat == 'en attente d'annulation'}] contient d'incorrecte(s) expression(s) : [jakarta.el.ELException: Impossible de traiter l'expression [${reservation.etat == 'en attente d'annulation'}]]
30-May-2025 14:49:20.330 SEVERE [http-nio-8080-exec-338] controller.MesReservationsServlet.doGet Erreur lors de la rÃ©cupÃ©ration des rÃ©servations: /mes-reservations.jsp (ligne : [199], colonne : [44]) [${reservation.etat == 'en attente d'annulation'}] contient d'incorrecte(s) expression(s) : [jakarta.el.ELException: Impossible de traiter l'expression [${reservation.etat == 'en attente d'annulation'}]]
30-May-2025 14:49:38.197 INFO [http-nio-8080-exec-339] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 14:50:03.366 INFO [http-nio-8080-exec-336] controller.LoginServlet.doPost Utilisateur connectï¿½ avec succï¿½s: <EMAIL>
30-May-2025 14:50:03.372 INFO [http-nio-8080-exec-337] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
30-May-2025 14:50:07.488 INFO [http-nio-8080-exec-338] controller.AdminReservationsServlet.doGet Admin admin consulte toutes les rÃ©servations (17 total)
30-May-2025 14:50:07.494 SEVERE [http-nio-8080-exec-338] controller.AdminReservationsServlet.doGet Erreur lors de la rÃ©cupÃ©ration des rÃ©servations : Une exception s'est produite lors du traitement de [/admin/reservations.jsp] à la ligne [223]

220:                                         </td>
221:                                         <td>
222:                                             <div class="date-info">
223:                                                 <fmt:formatDate value="${reservation.dateReservation}" pattern="dd/MM/yyyy" />
224:                                                 <br>
225:                                                 <small>
226:                                                     <fmt:formatDate value="${reservation.dateReservation}" pattern="HH:mm" />


Stacktrace:
30-May-2025 14:50:30.333 INFO [http-nio-8080-exec-339] controller.AdminReservationsServlet.doGet Admin admin consulte toutes les rÃ©servations (17 total)
30-May-2025 14:50:30.340 SEVERE [http-nio-8080-exec-339] controller.AdminReservationsServlet.doGet Erreur lors de la rÃ©cupÃ©ration des rÃ©servations : Une exception s'est produite lors du traitement de [/admin/reservations.jsp] à la ligne [223]

220:                                         </td>
221:                                         <td>
222:                                             <div class="date-info">
223:                                                 <fmt:formatDate value="${reservation.dateReservation}" pattern="dd/MM/yyyy" />
224:                                                 <br>
225:                                                 <small>
226:                                                     <fmt:formatDate value="${reservation.dateReservation}" pattern="HH:mm" />


Stacktrace:
30-May-2025 14:51:05.008 INFO [http-nio-8080-exec-336] controller.LoginServlet.doPost Utilisateur connectï¿½ avec succï¿½s: <EMAIL>
30-May-2025 14:51:05.014 INFO [http-nio-8080-exec-337] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 14:51:20.319 SEVERE [http-nio-8080-exec-338] controller.MesReservationsServlet.doGet Erreur lors de la rÃ©cupÃ©ration des rÃ©servations: /mes-reservations.jsp (ligne : [199], colonne : [44]) [${reservation.etat == 'en attente d'annulation'}] contient d'incorrecte(s) expression(s) : [jakarta.el.ELException: Impossible de traiter l'expression [${reservation.etat == 'en attente d'annulation'}]]
30-May-2025 14:54:21.352 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.undeploy Retrait de l'application web ayant pour chemin de contexte [/jeetraing]
30-May-2025 14:54:21.354 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesJdbc L'application web [jeetraing] a enregistré un pilote JDBC [com.mysql.cj.jdbc.Driver], mais ne l'a pas désenregistré avant l'arrêt de l'application.  Pour éviter une fuite de mémoire, le pilote JDBC a été désenregistré de force.
30-May-2025 14:54:21.355 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [mysql-cj-abandoned-connection-cleanup] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/java.lang.Object.wait(Native Method)
 java.base@17.0.12/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:155)
 com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:91)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
30-May-2025 14:54:21.356 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [Hibernate Connection Pool Validation Thread] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.12/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:252)
 java.base@17.0.12/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1672)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
30-May-2025 14:54:22.212 INFO [mysql-cj-abandoned-connection-cleanup] org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading Impossible de charger [], ce chargeur de classes a déjà été arrêté
	java.lang.IllegalStateException: Impossible de charger [], ce chargeur de classes a déjà été arrêté
		at org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading(WebappClassLoaderBase.java:1400)
		at org.apache.catalina.loader.WebappClassLoaderBase.getResource(WebappClassLoaderBase.java:987)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.checkThreadContextClassLoader(AbandonedConnectionCleanupThread.java:123)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:90)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:842)
30-May-2025 14:55:21.436 INFO [Catalina-utility-2] org.apache.catalina.startup.HostConfig.deployWAR Déploiement de l'archive [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] de l'application web
30-May-2025 14:55:22.206 INFO [Catalina-utility-2] org.apache.jasper.servlet.TldScanner.scanJars Au moins un fichier JAR a été analysé pour trouver des TLDs mais il n'en contenait pas, le mode "debug" du journal peut être activé pour obtenir une liste complète de JAR scannés sans succès ; éviter d'analyser des JARs inutilement peut améliorer sensiblement le temps de démarrage et le temps de compilation des JSPs
30-May-2025 14:55:22.216 INFO [Catalina-utility-2] org.apache.catalina.startup.HostConfig.deployWAR Le déploiement de l'archive de l'application web [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] s'est terminé en [780] ms
30-May-2025 14:56:10.618 INFO [http-nio-8080-exec-335] org.hibernate.Version.logVersion HHH000412: Hibernate ORM core version 6.4.4.Final
30-May-2025 14:56:10.953 INFO [http-nio-8080-exec-335] org.hibernate.cache.internal.RegionFactoryInitiator.initiateService HHH000026: Second-level cache disabled
30-May-2025 14:56:11.123 WARN [http-nio-8080-exec-335] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.configure HHH10001002: Using built-in connection pool (not intended for production use)
30-May-2025 14:56:11.128 INFO [http-nio-8080-exec-335] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001005: Loaded JDBC driver class: com.mysql.cj.jdbc.Driver
30-May-2025 14:56:11.128 INFO [http-nio-8080-exec-335] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001012: Connecting with JDBC URL [**************************************]
30-May-2025 14:56:11.129 INFO [http-nio-8080-exec-335] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001001: Connection properties: {password=****, user=root}
30-May-2025 14:56:11.129 INFO [http-nio-8080-exec-335] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001003: Autocommit mode: false
30-May-2025 14:56:11.133 INFO [http-nio-8080-exec-335] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl$PooledConnections.<init> HHH10001115: Connection pool size: 20 (min=1)
30-May-2025 14:56:11.378 WARN [http-nio-8080-exec-335] org.hibernate.dialect.Dialect.checkVersion HHH000511: The 5.5.0 version for [org.hibernate.dialect.MySQLDialect] is no longer supported, hence certain features may not work properly. The minimum supported version is 8.0.0. Check the community dialects project for available legacy versions.
30-May-2025 14:56:11.383 WARN [http-nio-8080-exec-335] org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.constructDialect HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
30-May-2025 14:56:13.181 INFO [http-nio-8080-exec-335] org.hibernate.engine.transaction.jta.platform.internal.JtaPlatformInitiator.initiateService HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
30-May-2025 14:56:13.219 INFO [http-nio-8080-exec-335] org.hibernate.resource.transaction.backend.jdbc.internal.DdlTransactionIsolatorNonJtaImpl.getIsolatedConnection HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@3203f416] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
30-May-2025 14:56:13.298 WARNING [http-nio-8080-exec-335] controller.MesReservationsServlet.doGet Utilisateur non connectÃ© lors de l'accÃ¨s Ã  /mes-reservations
30-May-2025 14:58:23.459 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.undeploy Retrait de l'application web ayant pour chemin de contexte [/jeetraing]
30-May-2025 14:58:23.462 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesJdbc L'application web [jeetraing] a enregistré un pilote JDBC [com.mysql.cj.jdbc.Driver], mais ne l'a pas désenregistré avant l'arrêt de l'application.  Pour éviter une fuite de mémoire, le pilote JDBC a été désenregistré de force.
30-May-2025 14:58:23.464 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [mysql-cj-abandoned-connection-cleanup] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/java.lang.Object.wait(Native Method)
 java.base@17.0.12/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:155)
 com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:91)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
30-May-2025 14:58:23.465 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [Hibernate Connection Pool Validation Thread] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.12/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:252)
 java.base@17.0.12/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1672)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
30-May-2025 14:58:23.486 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.deployWAR Déploiement de l'archive [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] de l'application web
30-May-2025 14:58:24.237 INFO [Catalina-utility-1] org.apache.jasper.servlet.TldScanner.scanJars Au moins un fichier JAR a été analysé pour trouver des TLDs mais il n'en contenait pas, le mode "debug" du journal peut être activé pour obtenir une liste complète de JAR scannés sans succès ; éviter d'analyser des JARs inutilement peut améliorer sensiblement le temps de démarrage et le temps de compilation des JSPs
30-May-2025 14:58:24.249 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.deployWAR Le déploiement de l'archive de l'application web [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] s'est terminé en [763] ms
30-May-2025 14:58:26.316 INFO [mysql-cj-abandoned-connection-cleanup] org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading Impossible de charger [], ce chargeur de classes a déjà été arrêté
	java.lang.IllegalStateException: Impossible de charger [], ce chargeur de classes a déjà été arrêté
		at org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading(WebappClassLoaderBase.java:1400)
		at org.apache.catalina.loader.WebappClassLoaderBase.getResource(WebappClassLoaderBase.java:987)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.checkThreadContextClassLoader(AbandonedConnectionCleanupThread.java:123)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:90)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:842)
30-May-2025 14:59:49.469 INFO [http-nio-8080-exec-344] org.hibernate.Version.logVersion HHH000412: Hibernate ORM core version 6.4.4.Final
30-May-2025 14:59:49.785 INFO [http-nio-8080-exec-344] org.hibernate.cache.internal.RegionFactoryInitiator.initiateService HHH000026: Second-level cache disabled
30-May-2025 14:59:49.948 WARN [http-nio-8080-exec-344] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.configure HHH10001002: Using built-in connection pool (not intended for production use)
30-May-2025 14:59:49.952 INFO [http-nio-8080-exec-344] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001005: Loaded JDBC driver class: com.mysql.cj.jdbc.Driver
30-May-2025 14:59:49.952 INFO [http-nio-8080-exec-344] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001012: Connecting with JDBC URL [**************************************]
30-May-2025 14:59:49.953 INFO [http-nio-8080-exec-344] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001001: Connection properties: {password=****, user=root}
30-May-2025 14:59:49.953 INFO [http-nio-8080-exec-344] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001003: Autocommit mode: false
30-May-2025 14:59:49.957 INFO [http-nio-8080-exec-344] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl$PooledConnections.<init> HHH10001115: Connection pool size: 20 (min=1)
30-May-2025 14:59:50.176 WARN [http-nio-8080-exec-344] org.hibernate.dialect.Dialect.checkVersion HHH000511: The 5.5.0 version for [org.hibernate.dialect.MySQLDialect] is no longer supported, hence certain features may not work properly. The minimum supported version is 8.0.0. Check the community dialects project for available legacy versions.
30-May-2025 14:59:50.181 WARN [http-nio-8080-exec-344] org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.constructDialect HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
30-May-2025 14:59:51.872 INFO [http-nio-8080-exec-344] org.hibernate.engine.transaction.jta.platform.internal.JtaPlatformInitiator.initiateService HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
30-May-2025 14:59:51.909 INFO [http-nio-8080-exec-344] org.hibernate.resource.transaction.backend.jdbc.internal.DdlTransactionIsolatorNonJtaImpl.getIsolatedConnection HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@2dbfe0fb] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
30-May-2025 14:59:52.619 INFO [http-nio-8080-exec-349] controller.LoginServlet.doPost Utilisateur connectï¿½ avec succï¿½s: <EMAIL>
30-May-2025 14:59:52.619 INFO [http-nio-8080-exec-344] controller.LoginServlet.doPost Utilisateur connectï¿½ avec succï¿½s: <EMAIL>
30-May-2025 14:59:52.628 INFO [http-nio-8080-exec-340] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 15:00:02.220 SEVERE [http-nio-8080-exec-351] controller.MesReservationsServlet.doGet Erreur lors de la rÃ©cupÃ©ration des rÃ©servations: /mes-reservations.jsp (ligne : [199], colonne : [44]) [${reservation.etat == 'en attente d'annulation'}] contient d'incorrecte(s) expression(s) : [jakarta.el.ELException: Impossible de traiter l'expression [${reservation.etat == 'en attente d'annulation'}]]
30-May-2025 15:00:02.413 SEVERE [http-nio-8080-exec-336] controller.MesReservationsServlet.doGet Erreur lors de la rÃ©cupÃ©ration des rÃ©servations: org.apache.jasper.JasperException: java.lang.ClassNotFoundException: org.apache.jsp.mes_002dreservations_jsp
30-May-2025 15:00:39.635 SEVERE [http-nio-8080-exec-352] controller.MesReservationsServlet.doGet Erreur lors de la rÃ©cupÃ©ration des rÃ©servations: /mes-reservations.jsp (ligne : [199], colonne : [44]) [${reservation.etat == 'en attente d'annulation'}] contient d'incorrecte(s) expression(s) : [jakarta.el.ELException: Impossible de traiter l'expression [${reservation.etat == 'en attente d'annulation'}]]
30-May-2025 15:00:40.434 SEVERE [http-nio-8080-exec-346] controller.MesReservationsServlet.doGet Erreur lors de la rÃ©cupÃ©ration des rÃ©servations: org.apache.jasper.JasperException: java.lang.ClassNotFoundException: org.apache.jsp.mes_002dreservations_jsp
30-May-2025 15:00:55.135 INFO [http-nio-8080-exec-353] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
30-May-2025 15:00:58.829 SEVERE [http-nio-8080-exec-351] controller.MesReservationsServlet.doGet Erreur lors de la rÃ©cupÃ©ration des rÃ©servations: /mes-reservations.jsp (ligne : [199], colonne : [44]) [${reservation.etat == 'en attente d'annulation'}] contient d'incorrecte(s) expression(s) : [jakarta.el.ELException: Impossible de traiter l'expression [${reservation.etat == 'en attente d'annulation'}]]
30-May-2025 15:01:03.792 SEVERE [http-nio-8080-exec-354] controller.MesReservationsServlet.doGet Erreur lors de la rÃ©cupÃ©ration des rÃ©servations: /mes-reservations.jsp (ligne : [199], colonne : [44]) [${reservation.etat == 'en attente d'annulation'}] contient d'incorrecte(s) expression(s) : [jakarta.el.ELException: Impossible de traiter l'expression [${reservation.etat == 'en attente d'annulation'}]]
30-May-2025 15:01:20.016 INFO [http-nio-8080-exec-355] controller.LoginServlet.doPost Tentative de connexion ï¿½chouï¿½e pour l'email: <EMAIL> (mot de passe incorrect)
30-May-2025 15:01:27.022 INFO [http-nio-8080-exec-356] controller.LoginServlet.doPost Utilisateur connectï¿½ avec succï¿½s: <EMAIL>
30-May-2025 15:01:27.025 INFO [http-nio-8080-exec-345] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
30-May-2025 15:01:31.524 INFO [http-nio-8080-exec-357] controller.AdminReservationsServlet.doGet Admin admin consulte toutes les rÃ©servations (17 total)
30-May-2025 15:01:31.606 SEVERE [http-nio-8080-exec-357] controller.AdminReservationsServlet.doGet Erreur lors de la rÃ©cupÃ©ration des rÃ©servations : Une exception s'est produite lors du traitement de [/admin/reservations.jsp] à la ligne [223]

220:                                         </td>
221:                                         <td>
222:                                             <div class="date-info">
223:                                                 <fmt:formatDate value="${reservation.dateReservation}" pattern="dd/MM/yyyy" />
224:                                                 <br>
225:                                                 <small>
226:                                                     <fmt:formatDate value="${reservation.dateReservation}" pattern="HH:mm" />


Stacktrace:
30-May-2025 15:02:29.949 INFO [http-nio-8080-exec-353] controller.AdminReservationsServlet.doGet Admin admin consulte toutes les rÃ©servations (17 total)
30-May-2025 15:02:29.954 SEVERE [http-nio-8080-exec-353] controller.AdminReservationsServlet.doGet Erreur lors de la rÃ©cupÃ©ration des rÃ©servations : Une exception s'est produite lors du traitement de [/admin/reservations.jsp] à la ligne [223]

220:                                         </td>
221:                                         <td>
222:                                             <div class="date-info">
223:                                                 <fmt:formatDate value="${reservation.dateReservation}" pattern="dd/MM/yyyy" />
224:                                                 <br>
225:                                                 <small>
226:                                                     <fmt:formatDate value="${reservation.dateReservation}" pattern="HH:mm" />


Stacktrace:
30-May-2025 15:02:37.070 INFO [http-nio-8080-exec-350] controller.LoginServlet.doPost Utilisateur connectï¿½ avec succï¿½s: <EMAIL>
30-May-2025 15:02:37.076 INFO [http-nio-8080-exec-352] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 15:02:43.881 SEVERE [http-nio-8080-exec-355] controller.MesReservationsServlet.doGet Erreur lors de la rÃ©cupÃ©ration des rÃ©servations: /mes-reservations.jsp (ligne : [199], colonne : [44]) [${reservation.etat == 'en attente d'annulation'}] contient d'incorrecte(s) expression(s) : [jakarta.el.ELException: Impossible de traiter l'expression [${reservation.etat == 'en attente d'annulation'}]]
30-May-2025 15:02:44.496 SEVERE [http-nio-8080-exec-356] controller.MesReservationsServlet.doGet Erreur lors de la rÃ©cupÃ©ration des rÃ©servations: org.apache.jasper.JasperException: java.lang.ClassNotFoundException: org.apache.jsp.mes_002dreservations_jsp
30-May-2025 15:03:16.059 SEVERE [http-nio-8080-exec-357] controller.MesReservationsServlet.doGet Erreur lors de la rÃ©cupÃ©ration des rÃ©servations: /mes-reservations.jsp (ligne : [199], colonne : [44]) [${reservation.etat == 'en attente d'annulation'}] contient d'incorrecte(s) expression(s) : [jakarta.el.ELException: Impossible de traiter l'expression [${reservation.etat == 'en attente d'annulation'}]]
30-May-2025 15:03:20.596 SEVERE [http-nio-8080-exec-359] controller.MesReservationsServlet.doGet Erreur lors de la rÃ©cupÃ©ration des rÃ©servations: /mes-reservations.jsp (ligne : [199], colonne : [44]) [${reservation.etat == 'en attente d'annulation'}] contient d'incorrecte(s) expression(s) : [jakarta.el.ELException: Impossible de traiter l'expression [${reservation.etat == 'en attente d'annulation'}]]
30-May-2025 15:03:50.540 SEVERE [http-nio-8080-exec-351] controller.MesReservationsServlet.doGet Erreur lors de la rÃ©cupÃ©ration des rÃ©servations: /mes-reservations.jsp (ligne : [199], colonne : [44]) [${reservation.etat == 'en attente d'annulation'}] contient d'incorrecte(s) expression(s) : [jakarta.el.ELException: Impossible de traiter l'expression [${reservation.etat == 'en attente d'annulation'}]]
30-May-2025 15:03:51.309 SEVERE [http-nio-8080-exec-354] controller.MesReservationsServlet.doGet Erreur lors de la rÃ©cupÃ©ration des rÃ©servations: org.apache.jasper.JasperException: java.lang.ClassNotFoundException: org.apache.jsp.mes_002dreservations_jsp
30-May-2025 15:03:51.495 SEVERE [http-nio-8080-exec-350] controller.MesReservationsServlet.doGet Erreur lors de la rÃ©cupÃ©ration des rÃ©servations: org.apache.jasper.JasperException: java.lang.ClassNotFoundException: org.apache.jsp.mes_002dreservations_jsp
30-May-2025 15:04:30.263 INFO [http-nio-8080-exec-355] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 15:04:35.823 SEVERE [http-nio-8080-exec-356] controller.MesReservationsServlet.doGet Erreur lors de la rÃ©cupÃ©ration des rÃ©servations: /mes-reservations.jsp (ligne : [199], colonne : [44]) [${reservation.etat == 'en attente d'annulation'}] contient d'incorrecte(s) expression(s) : [jakarta.el.ELException: Impossible de traiter l'expression [${reservation.etat == 'en attente d'annulation'}]]
30-May-2025 15:04:36.512 SEVERE [http-nio-8080-exec-358] controller.MesReservationsServlet.doGet Erreur lors de la rÃ©cupÃ©ration des rÃ©servations: org.apache.jasper.JasperException: java.lang.ClassNotFoundException: org.apache.jsp.mes_002dreservations_jsp
30-May-2025 15:04:48.461 INFO [http-nio-8080-exec-353] controller.LoginServlet.doPost Utilisateur connectï¿½ avec succï¿½s: <EMAIL>
30-May-2025 15:04:48.465 INFO [http-nio-8080-exec-351] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
30-May-2025 15:04:51.204 INFO [http-nio-8080-exec-354] controller.AdminReservationsServlet.doGet Admin admin consulte toutes les rÃ©servations (17 total)
30-May-2025 15:04:51.209 SEVERE [http-nio-8080-exec-354] controller.AdminReservationsServlet.doGet Erreur lors de la rÃ©cupÃ©ration des rÃ©servations : Une exception s'est produite lors du traitement de [/admin/reservations.jsp] à la ligne [223]

220:                                         </td>
221:                                         <td>
222:                                             <div class="date-info">
223:                                                 <fmt:formatDate value="${reservation.dateReservation}" pattern="dd/MM/yyyy" />
224:                                                 <br>
225:                                                 <small>
226:                                                     <fmt:formatDate value="${reservation.dateReservation}" pattern="HH:mm" />


Stacktrace:
30-May-2025 15:20:55.760 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.undeploy Retrait de l'application web ayant pour chemin de contexte [/jeetraing]
30-May-2025 15:20:55.761 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesJdbc L'application web [jeetraing] a enregistré un pilote JDBC [com.mysql.cj.jdbc.Driver], mais ne l'a pas désenregistré avant l'arrêt de l'application.  Pour éviter une fuite de mémoire, le pilote JDBC a été désenregistré de force.
30-May-2025 15:20:55.763 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [mysql-cj-abandoned-connection-cleanup] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/java.lang.Object.wait(Native Method)
 java.base@17.0.12/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:155)
 com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:91)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
30-May-2025 15:20:55.764 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [Hibernate Connection Pool Validation Thread] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.12/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:252)
 java.base@17.0.12/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1672)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
30-May-2025 15:20:55.787 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.deployWAR Déploiement de l'archive [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] de l'application web
30-May-2025 15:20:56.590 INFO [Catalina-utility-1] org.apache.jasper.servlet.TldScanner.scanJars Au moins un fichier JAR a été analysé pour trouver des TLDs mais il n'en contenait pas, le mode "debug" du journal peut être activé pour obtenir une liste complète de JAR scannés sans succès ; éviter d'analyser des JARs inutilement peut améliorer sensiblement le temps de démarrage et le temps de compilation des JSPs
30-May-2025 15:20:56.606 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.deployWAR Le déploiement de l'archive de l'application web [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] s'est terminé en [819] ms
30-May-2025 15:20:57.103 INFO [mysql-cj-abandoned-connection-cleanup] org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading Impossible de charger [], ce chargeur de classes a déjà été arrêté
	java.lang.IllegalStateException: Impossible de charger [], ce chargeur de classes a déjà été arrêté
		at org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading(WebappClassLoaderBase.java:1400)
		at org.apache.catalina.loader.WebappClassLoaderBase.getResource(WebappClassLoaderBase.java:987)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.checkThreadContextClassLoader(AbandonedConnectionCleanupThread.java:123)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:90)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:842)
30-May-2025 15:21:47.864 INFO [http-nio-8080-exec-351] org.hibernate.Version.logVersion HHH000412: Hibernate ORM core version 6.4.4.Final
30-May-2025 15:21:48.025 INFO [http-nio-8080-exec-351] org.hibernate.cache.internal.RegionFactoryInitiator.initiateService HHH000026: Second-level cache disabled
30-May-2025 15:21:48.118 WARN [http-nio-8080-exec-351] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.configure HHH10001002: Using built-in connection pool (not intended for production use)
30-May-2025 15:21:48.121 INFO [http-nio-8080-exec-351] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001005: Loaded JDBC driver class: com.mysql.cj.jdbc.Driver
30-May-2025 15:21:48.121 INFO [http-nio-8080-exec-351] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001012: Connecting with JDBC URL [**************************************?useSSL=false&allowPublicKeyRetrieval=true&serverTimezone=UTC]
30-May-2025 15:21:48.121 INFO [http-nio-8080-exec-351] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001001: Connection properties: {password=****, useUnicode=true, characterEncoding=utf8, user=root}
30-May-2025 15:21:48.121 INFO [http-nio-8080-exec-351] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001003: Autocommit mode: false
30-May-2025 15:21:48.124 INFO [http-nio-8080-exec-351] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl$PooledConnections.<init> HHH10001115: Connection pool size: 10 (min=1)
30-May-2025 15:21:48.266 WARN [http-nio-8080-exec-351] org.hibernate.dialect.Dialect.checkVersion HHH000511: The 5.5.0 version for [org.hibernate.dialect.MySQLDialect] is no longer supported, hence certain features may not work properly. The minimum supported version is 8.0.0. Check the community dialects project for available legacy versions.
30-May-2025 15:21:48.271 WARN [http-nio-8080-exec-351] org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.constructDialect HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
30-May-2025 15:21:48.655 INFO [http-nio-8080-exec-351] org.hibernate.engine.transaction.jta.platform.internal.JtaPlatformInitiator.initiateService HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
