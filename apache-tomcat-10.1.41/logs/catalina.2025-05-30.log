30-May-2025 00:09:53.380 WARNING [http-nio-8080-exec-260] controller.ModifierReservationServlet.doGet Utilisateur non connectï¿½ lors de l'accï¿½s ï¿½ /modifierReservation
30-May-2025 00:10:00.347 INFO [http-nio-8080-exec-261] controller.LoginServlet.doPost Utilisateur connectï¿½ avec succï¿½s: <EMAIL>
30-May-2025 00:10:00.355 INFO [http-nio-8080-exec-264] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:10:11.069 INFO [http-nio-8080-exec-262] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:10:33.631 INFO [http-nio-8080-exec-258] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
30-May-2025 00:11:24.758 INFO [http-nio-8080-exec-261] controller.ModifierReservationServlet.doGet Accï¿½s ï¿½ doGet - ID: 4
30-May-2025 00:11:24.760 INFO [http-nio-8080-exec-261] controller.ModifierReservationServlet.doGet Rï¿½servation rï¿½cupï¿½rï¿½e: model.Reservation@5f64616c
30-May-2025 00:11:24.761 INFO [http-nio-8080-exec-261] controller.ModifierReservationServlet.doGet ï¿½tat de la rï¿½servation: acheté
30-May-2025 00:11:24.761 WARNING [http-nio-8080-exec-261] controller.ModifierReservationServlet.doGet Rï¿½servation non modifiable (ï¿½tat: acheté) pour l'ID: 4
30-May-2025 00:11:37.299 WARNING [http-nio-8080-exec-262] controller.AnnulerReservationServlet.doGet Rï¿½servation non annulable (ï¿½tat: acheté) pour l'ID: 4
30-May-2025 00:11:37.304 INFO [http-nio-8080-exec-259] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
30-May-2025 00:11:39.528 INFO [http-nio-8080-exec-257] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
30-May-2025 00:11:43.798 INFO [http-nio-8080-exec-258] controller.ModifierReservationServlet.doGet Accï¿½s ï¿½ doGet - ID: 13
30-May-2025 00:11:43.803 INFO [http-nio-8080-exec-258] controller.ModifierReservationServlet.doGet Rï¿½servation rï¿½cupï¿½rï¿½e: model.Reservation@12f320b7
30-May-2025 00:11:43.804 INFO [http-nio-8080-exec-258] controller.ModifierReservationServlet.doGet ï¿½tat de la rï¿½servation: acheté
30-May-2025 00:11:43.804 WARNING [http-nio-8080-exec-258] controller.ModifierReservationServlet.doGet Rï¿½servation non modifiable (ï¿½tat: acheté) pour l'ID: 13
30-May-2025 00:11:55.915 INFO [http-nio-8080-exec-263] controller.ModifierReservationServlet.doGet Accï¿½s ï¿½ doGet - ID: 1
30-May-2025 00:11:55.920 INFO [http-nio-8080-exec-263] controller.ModifierReservationServlet.doGet Rï¿½servation rï¿½cupï¿½rï¿½e: model.Reservation@1945470d
30-May-2025 00:11:55.920 INFO [http-nio-8080-exec-263] controller.ModifierReservationServlet.doGet ï¿½tat de la rï¿½servation: acheté
30-May-2025 00:11:55.921 WARNING [http-nio-8080-exec-263] controller.ModifierReservationServlet.doGet Rï¿½servation non modifiable (ï¿½tat: acheté) pour l'ID: 1
30-May-2025 00:12:03.674 INFO [http-nio-8080-exec-264] controller.ModifierReservationServlet.doGet Accï¿½s ï¿½ doGet - ID: 1
30-May-2025 00:12:03.678 INFO [http-nio-8080-exec-264] controller.ModifierReservationServlet.doGet Rï¿½servation rï¿½cupï¿½rï¿½e: model.Reservation@58b81786
30-May-2025 00:12:03.679 INFO [http-nio-8080-exec-264] controller.ModifierReservationServlet.doGet ï¿½tat de la rï¿½servation: acheté
30-May-2025 00:12:03.679 WARNING [http-nio-8080-exec-264] controller.ModifierReservationServlet.doGet Rï¿½servation non modifiable (ï¿½tat: acheté) pour l'ID: 1
30-May-2025 00:12:12.577 WARNING [http-nio-8080-exec-262] controller.AnnulerReservationServlet.doGet Rï¿½servation non annulable (ï¿½tat: acheté) pour l'ID: 1
30-May-2025 00:12:12.583 INFO [http-nio-8080-exec-259] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
30-May-2025 00:13:37.473 INFO [http-nio-8080-exec-256] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
30-May-2025 00:13:44.398 INFO [http-nio-8080-exec-260] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
30-May-2025 00:13:56.856 INFO [http-nio-8080-exec-261] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
30-May-2025 00:14:06.056 INFO [http-nio-8080-exec-265] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
30-May-2025 00:14:11.429 INFO [http-nio-8080-exec-262] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
30-May-2025 00:14:22.338 INFO [http-nio-8080-exec-259] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
30-May-2025 00:14:25.572 INFO [http-nio-8080-exec-257] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
30-May-2025 00:14:25.795 INFO [http-nio-8080-exec-258] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
30-May-2025 00:14:38.703 WARNING [http-nio-8080-exec-263] controller.AnnulerReservationServlet.doGet Rï¿½servation non annulable (ï¿½tat: utilisé) pour l'ID: 5
30-May-2025 00:14:38.709 INFO [http-nio-8080-exec-261] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
30-May-2025 00:15:20.314 WARNING [http-nio-8080-exec-262] controller.AnnulerReservationServlet.doGet Rï¿½servation non annulable (ï¿½tat: acheté) pour l'ID: 8
30-May-2025 00:15:20.320 INFO [http-nio-8080-exec-259] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
30-May-2025 00:15:28.665 WARNING [http-nio-8080-exec-257] controller.AnnulerReservationServlet.doGet Rï¿½servation non annulable (ï¿½tat: acheté) pour l'ID: 6
30-May-2025 00:15:28.670 INFO [http-nio-8080-exec-258] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
30-May-2025 00:15:31.424 INFO [http-nio-8080-exec-256] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
30-May-2025 00:16:31.309 INFO [http-nio-8080-exec-259] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
30-May-2025 00:26:06.688 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.undeploy Retrait de l'application web ayant pour chemin de contexte [/jeetraing]
30-May-2025 00:26:06.690 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesJdbc L'application web [jeetraing] a enregistré un pilote JDBC [com.mysql.cj.jdbc.Driver], mais ne l'a pas désenregistré avant l'arrêt de l'application.  Pour éviter une fuite de mémoire, le pilote JDBC a été désenregistré de force.
30-May-2025 00:26:06.691 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [mysql-cj-abandoned-connection-cleanup] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/java.lang.Object.wait(Native Method)
 java.base@17.0.12/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:155)
 com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:91)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
30-May-2025 00:26:06.692 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [Hibernate Connection Pool Validation Thread] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.12/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:252)
 java.base@17.0.12/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1672)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
30-May-2025 00:26:06.739 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.deployWAR Déploiement de l'archive [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] de l'application web
30-May-2025 00:26:07.451 INFO [mysql-cj-abandoned-connection-cleanup] org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading Impossible de charger [], ce chargeur de classes a déjà été arrêté
	java.lang.IllegalStateException: Impossible de charger [], ce chargeur de classes a déjà été arrêté
		at org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading(WebappClassLoaderBase.java:1400)
		at org.apache.catalina.loader.WebappClassLoaderBase.getResource(WebappClassLoaderBase.java:987)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.checkThreadContextClassLoader(AbandonedConnectionCleanupThread.java:123)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:90)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:842)
30-May-2025 00:26:09.199 INFO [Catalina-utility-1] org.apache.jasper.servlet.TldScanner.scanJars Au moins un fichier JAR a été analysé pour trouver des TLDs mais il n'en contenait pas, le mode "debug" du journal peut être activé pour obtenir une liste complète de JAR scannés sans succès ; éviter d'analyser des JARs inutilement peut améliorer sensiblement le temps de démarrage et le temps de compilation des JSPs
30-May-2025 00:26:09.226 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.deployWAR Le déploiement de l'archive de l'application web [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] s'est terminé en [2 486] ms
30-May-2025 00:28:35.760 INFO [http-nio-8080-exec-266] org.hibernate.Version.logVersion HHH000412: Hibernate ORM core version 6.4.4.Final
30-May-2025 00:28:36.140 INFO [http-nio-8080-exec-266] org.hibernate.cache.internal.RegionFactoryInitiator.initiateService HHH000026: Second-level cache disabled
30-May-2025 00:28:36.409 WARN [http-nio-8080-exec-266] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.configure HHH10001002: Using built-in connection pool (not intended for production use)
30-May-2025 00:28:36.415 INFO [http-nio-8080-exec-266] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001005: Loaded JDBC driver class: com.mysql.cj.jdbc.Driver
30-May-2025 00:28:36.415 INFO [http-nio-8080-exec-266] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001012: Connecting with JDBC URL [**************************************]
30-May-2025 00:28:36.416 INFO [http-nio-8080-exec-266] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001001: Connection properties: {password=****, user=root}
30-May-2025 00:28:36.416 INFO [http-nio-8080-exec-266] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001003: Autocommit mode: false
30-May-2025 00:28:36.421 INFO [http-nio-8080-exec-266] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl$PooledConnections.<init> HHH10001115: Connection pool size: 20 (min=1)
30-May-2025 00:28:36.694 WARN [http-nio-8080-exec-266] org.hibernate.dialect.Dialect.checkVersion HHH000511: The 5.5.0 version for [org.hibernate.dialect.MySQLDialect] is no longer supported, hence certain features may not work properly. The minimum supported version is 8.0.0. Check the community dialects project for available legacy versions.
30-May-2025 00:28:36.701 WARN [http-nio-8080-exec-266] org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.constructDialect HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
30-May-2025 00:28:38.639 INFO [http-nio-8080-exec-266] org.hibernate.engine.transaction.jta.platform.internal.JtaPlatformInitiator.initiateService HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
30-May-2025 00:28:38.684 INFO [http-nio-8080-exec-266] org.hibernate.resource.transaction.backend.jdbc.internal.DdlTransactionIsolatorNonJtaImpl.getIsolatedConnection HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@6486503d] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
30-May-2025 00:28:38.793 WARNING [http-nio-8080-exec-266] controller.MonCompteServlet.doGet Utilisateur non connectï¿½ lors de l'accï¿½s ï¿½ /monCompte
30-May-2025 00:35:43.900 INFO [http-nio-8080-exec-269] controller.LoginServlet.doPost Utilisateur connectï¿½ avec succï¿½s: <EMAIL>
30-May-2025 00:35:43.911 INFO [http-nio-8080-exec-266] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:35:53.728 INFO [http-nio-8080-exec-267] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:35:57.099 INFO [http-nio-8080-exec-270] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:36:23.316 INFO [http-nio-8080-exec-268] controller.DownloadTicketServlet.doGet GÃ©nÃ©ration du PDF pour la rÃ©servation ID: 15
30-May-2025 00:36:23.316 INFO [http-nio-8080-exec-268] controller.DownloadTicketServlet.generatePDF DÃ©but de la gÃ©nÃ©ration du PDF pour la rÃ©servation ID: 15
30-May-2025 00:36:23.580 INFO [http-nio-8080-exec-268] controller.DownloadTicketServlet.generatePDF Ã‰criture du PDF dans la rÃ©ponse pour la rÃ©servation ID: 15
30-May-2025 00:36:23.583 INFO [http-nio-8080-exec-268] controller.DownloadTicketServlet.generatePDF PDF gÃ©nÃ©rÃ© et envoyÃ© avec succÃ¨s pour la rÃ©servation ID: 15
30-May-2025 00:36:38.594 INFO [http-nio-8080-exec-269] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
30-May-2025 00:37:02.262 WARNING [http-nio-8080-exec-270] controller.AnnulerReservationServlet.doGet Rï¿½servation non annulable (ï¿½tat: acheté) pour l'ID: 9
30-May-2025 00:37:02.268 INFO [http-nio-8080-exec-271] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
30-May-2025 00:38:28.975 INFO [http-nio-8080-exec-272] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
30-May-2025 00:39:06.475 WARNING [http-nio-8080-exec-268] controller.AnnulerReservationServlet.doGet Rï¿½servation non annulable (ï¿½tat: acheté) pour l'ID: 4
30-May-2025 00:39:06.480 INFO [http-nio-8080-exec-269] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
30-May-2025 00:41:56.453 INFO [http-nio-8080-exec-266] controller.LoginServlet.doPost Utilisateur connectï¿½ avec succï¿½s: <EMAIL>
30-May-2025 00:41:56.456 INFO [http-nio-8080-exec-267] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
30-May-2025 00:42:56.923 INFO [http-nio-8080-exec-266] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
30-May-2025 00:43:03.615 INFO [http-nio-8080-exec-270] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
30-May-2025 00:43:10.618 INFO [http-nio-8080-exec-272] controller.ReservationEvolutionServlet.doGet doGet - Evolution Data: [[Ljava.lang.Object;@2607df97, [Ljava.lang.Object;@438b6309]
30-May-2025 00:43:10.620 INFO [http-nio-8080-exec-272] controller.ReservationEvolutionServlet.doGet doGet - Labels: [2025-04, 2025-05]
30-May-2025 00:43:10.621 INFO [http-nio-8080-exec-272] controller.ReservationEvolutionServlet.doGet doGet - Data: [0, 15]
30-May-2025 00:43:10.621 INFO [http-nio-8080-exec-272] controller.ReservationEvolutionServlet.doGet doGet - Attributes set - evolutionData: [[Ljava.lang.Object;@2607df97, [Ljava.lang.Object;@438b6309]
30-May-2025 00:43:20.979 INFO [http-nio-8080-exec-275] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:43:37.522 INFO [http-nio-8080-exec-268] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:43:49.895 INFO [http-nio-8080-exec-266] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:43:57.687 INFO [http-nio-8080-exec-267] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:44:05.105 INFO [http-nio-8080-exec-271] dao.StationDAO.update Station updated successfully: 8
30-May-2025 00:44:05.111 INFO [http-nio-8080-exec-270] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:44:17.442 INFO [http-nio-8080-exec-273] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:44:23.984 INFO [http-nio-8080-exec-274] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:44:28.034 INFO [http-nio-8080-exec-275] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:44:32.840 INFO [http-nio-8080-exec-268] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:44:37.395 INFO [http-nio-8080-exec-269] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:44:43.366 INFO [http-nio-8080-exec-267] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:44:43.586 INFO [http-nio-8080-exec-270] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:44:52.496 INFO [http-nio-8080-exec-272] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:45:23.918 INFO [http-nio-8080-exec-275] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:46:10.776 INFO [Catalina-utility-2] org.apache.catalina.startup.HostConfig.undeploy Retrait de l'application web ayant pour chemin de contexte [/jeetraing]
30-May-2025 00:46:10.779 WARNING [Catalina-utility-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesJdbc L'application web [jeetraing] a enregistré un pilote JDBC [com.mysql.cj.jdbc.Driver], mais ne l'a pas désenregistré avant l'arrêt de l'application.  Pour éviter une fuite de mémoire, le pilote JDBC a été désenregistré de force.
30-May-2025 00:46:10.781 WARNING [Catalina-utility-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [mysql-cj-abandoned-connection-cleanup] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/java.lang.Object.wait(Native Method)
 java.base@17.0.12/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:155)
 com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:91)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
30-May-2025 00:46:10.782 WARNING [Catalina-utility-2] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [Hibernate Connection Pool Validation Thread] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.12/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:252)
 java.base@17.0.12/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1672)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
30-May-2025 00:46:10.841 INFO [Catalina-utility-2] org.apache.catalina.startup.HostConfig.deployWAR Déploiement de l'archive [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] de l'application web
30-May-2025 00:46:11.896 INFO [Catalina-utility-2] org.apache.jasper.servlet.TldScanner.scanJars Au moins un fichier JAR a été analysé pour trouver des TLDs mais il n'en contenait pas, le mode "debug" du journal peut être activé pour obtenir une liste complète de JAR scannés sans succès ; éviter d'analyser des JARs inutilement peut améliorer sensiblement le temps de démarrage et le temps de compilation des JSPs
30-May-2025 00:46:11.916 INFO [Catalina-utility-2] org.apache.catalina.startup.HostConfig.deployWAR Le déploiement de l'archive de l'application web [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] s'est terminé en [1 075] ms
30-May-2025 00:46:13.138 WARNING [http-nio-8080-exec-275] controller.AdminDashboardServlet.doGet Utilisateur non connectÃ© lors de l'accÃ¨s au dashboard admin
30-May-2025 00:46:13.161 INFO [mysql-cj-abandoned-connection-cleanup] org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading Impossible de charger [], ce chargeur de classes a déjà été arrêté
	java.lang.IllegalStateException: Impossible de charger [], ce chargeur de classes a déjà été arrêté
		at org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading(WebappClassLoaderBase.java:1400)
		at org.apache.catalina.loader.WebappClassLoaderBase.getResource(WebappClassLoaderBase.java:987)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.checkThreadContextClassLoader(AbandonedConnectionCleanupThread.java:123)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:90)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:842)
30-May-2025 00:46:13.258 INFO [http-nio-8080-exec-274] org.hibernate.Version.logVersion HHH000412: Hibernate ORM core version 6.4.4.Final
30-May-2025 00:46:13.621 INFO [http-nio-8080-exec-274] org.hibernate.cache.internal.RegionFactoryInitiator.initiateService HHH000026: Second-level cache disabled
30-May-2025 00:46:13.809 WARN [http-nio-8080-exec-274] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.configure HHH10001002: Using built-in connection pool (not intended for production use)
30-May-2025 00:46:13.813 INFO [http-nio-8080-exec-274] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001005: Loaded JDBC driver class: com.mysql.cj.jdbc.Driver
30-May-2025 00:46:13.813 INFO [http-nio-8080-exec-274] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001012: Connecting with JDBC URL [**************************************]
30-May-2025 00:46:13.814 INFO [http-nio-8080-exec-274] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001001: Connection properties: {password=****, user=root}
30-May-2025 00:46:13.814 INFO [http-nio-8080-exec-274] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001003: Autocommit mode: false
30-May-2025 00:46:13.820 INFO [http-nio-8080-exec-274] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl$PooledConnections.<init> HHH10001115: Connection pool size: 20 (min=1)
30-May-2025 00:46:14.073 WARN [http-nio-8080-exec-274] org.hibernate.dialect.Dialect.checkVersion HHH000511: The 5.5.0 version for [org.hibernate.dialect.MySQLDialect] is no longer supported, hence certain features may not work properly. The minimum supported version is 8.0.0. Check the community dialects project for available legacy versions.
30-May-2025 00:46:14.079 WARN [http-nio-8080-exec-274] org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.constructDialect HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
30-May-2025 00:46:15.824 INFO [http-nio-8080-exec-274] org.hibernate.engine.transaction.jta.platform.internal.JtaPlatformInitiator.initiateService HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
30-May-2025 00:46:15.867 INFO [http-nio-8080-exec-274] org.hibernate.resource.transaction.backend.jdbc.internal.DdlTransactionIsolatorNonJtaImpl.getIsolatedConnection HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@64f8b4bb] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
30-May-2025 00:47:29.422 WARNING [http-nio-8080-exec-276] controller.MesReservationsServlet.doGet Utilisateur non connectÃ© lors de l'accÃ¨s Ã  /mes-reservations
30-May-2025 00:50:02.225 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.undeploy Retrait de l'application web ayant pour chemin de contexte [/jeetraing]
30-May-2025 00:50:02.227 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesJdbc L'application web [jeetraing] a enregistré un pilote JDBC [com.mysql.cj.jdbc.Driver], mais ne l'a pas désenregistré avant l'arrêt de l'application.  Pour éviter une fuite de mémoire, le pilote JDBC a été désenregistré de force.
30-May-2025 00:50:02.229 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [mysql-cj-abandoned-connection-cleanup] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/java.lang.Object.wait(Native Method)
 java.base@17.0.12/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:155)
 com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:91)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
30-May-2025 00:50:02.230 WARNING [Catalina-utility-1] org.apache.catalina.loader.WebappClassLoaderBase.clearReferencesThreads L'application web [jeetraing] semble avoir démarré un thread nommé [Hibernate Connection Pool Validation Thread] mais ne l'a pas arrêté, ce qui va probablement créer une fuite de mémoire ; la trace du thread est : 
 java.base@17.0.12/jdk.internal.misc.Unsafe.park(Native Method)
 java.base@17.0.12/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:252)
 java.base@17.0.12/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1672)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
 java.base@17.0.12/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
 java.base@17.0.12/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
 java.base@17.0.12/java.lang.Thread.run(Thread.java:842)
30-May-2025 00:50:02.257 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.deployWAR Déploiement de l'archive [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] de l'application web
30-May-2025 00:50:03.365 INFO [Catalina-utility-1] org.apache.jasper.servlet.TldScanner.scanJars Au moins un fichier JAR a été analysé pour trouver des TLDs mais il n'en contenait pas, le mode "debug" du journal peut être activé pour obtenir une liste complète de JAR scannés sans succès ; éviter d'analyser des JARs inutilement peut améliorer sensiblement le temps de démarrage et le temps de compilation des JSPs
30-May-2025 00:50:03.387 INFO [Catalina-utility-1] org.apache.catalina.startup.HostConfig.deployWAR Le déploiement de l'archive de l'application web [C:\Users\<USER>\Downloads\jeetraing\apache-tomcat-10.1.41\webapps\jeetraing.war] s'est terminé en [1 131] ms
30-May-2025 00:50:04.150 INFO [mysql-cj-abandoned-connection-cleanup] org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading Impossible de charger [], ce chargeur de classes a déjà été arrêté
	java.lang.IllegalStateException: Impossible de charger [], ce chargeur de classes a déjà été arrêté
		at org.apache.catalina.loader.WebappClassLoaderBase.checkStateForResourceLoading(WebappClassLoaderBase.java:1400)
		at org.apache.catalina.loader.WebappClassLoaderBase.getResource(WebappClassLoaderBase.java:987)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.checkThreadContextClassLoader(AbandonedConnectionCleanupThread.java:123)
		at com.mysql.cj.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:90)
		at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
		at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
		at java.base/java.lang.Thread.run(Thread.java:842)
30-May-2025 00:53:17.898 INFO [http-nio-8080-exec-283] org.hibernate.Version.logVersion HHH000412: Hibernate ORM core version 6.4.4.Final
30-May-2025 00:53:18.238 INFO [http-nio-8080-exec-283] org.hibernate.cache.internal.RegionFactoryInitiator.initiateService HHH000026: Second-level cache disabled
30-May-2025 00:53:18.413 WARN [http-nio-8080-exec-283] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.configure HHH10001002: Using built-in connection pool (not intended for production use)
30-May-2025 00:53:18.418 INFO [http-nio-8080-exec-283] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001005: Loaded JDBC driver class: com.mysql.cj.jdbc.Driver
30-May-2025 00:53:18.418 INFO [http-nio-8080-exec-283] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001012: Connecting with JDBC URL [**************************************]
30-May-2025 00:53:18.418 INFO [http-nio-8080-exec-283] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001001: Connection properties: {password=****, user=root}
30-May-2025 00:53:18.419 INFO [http-nio-8080-exec-283] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl.buildCreator HHH10001003: Autocommit mode: false
30-May-2025 00:53:18.423 INFO [http-nio-8080-exec-283] org.hibernate.engine.jdbc.connections.internal.DriverManagerConnectionProviderImpl$PooledConnections.<init> HHH10001115: Connection pool size: 20 (min=1)
30-May-2025 00:53:18.650 WARN [http-nio-8080-exec-283] org.hibernate.dialect.Dialect.checkVersion HHH000511: The 5.5.0 version for [org.hibernate.dialect.MySQLDialect] is no longer supported, hence certain features may not work properly. The minimum supported version is 8.0.0. Check the community dialects project for available legacy versions.
30-May-2025 00:53:18.656 WARN [http-nio-8080-exec-283] org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.constructDialect HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
30-May-2025 00:53:20.351 INFO [http-nio-8080-exec-283] org.hibernate.engine.transaction.jta.platform.internal.JtaPlatformInitiator.initiateService HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
30-May-2025 00:53:20.393 INFO [http-nio-8080-exec-283] org.hibernate.resource.transaction.backend.jdbc.internal.DdlTransactionIsolatorNonJtaImpl.getIsolatedConnection HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@dca8d2a] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
30-May-2025 00:53:21.128 INFO [http-nio-8080-exec-283] controller.LoginServlet.doPost Utilisateur connectï¿½ avec succï¿½s: <EMAIL>
30-May-2025 00:53:21.140 INFO [http-nio-8080-exec-284] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:53:35.070 INFO [http-nio-8080-exec-286] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:53:46.247 INFO [http-nio-8080-exec-279] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:54:21.619 INFO [http-nio-8080-exec-285] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
30-May-2025 00:54:33.918 INFO [http-nio-8080-exec-289] controller.ModifierReservationServlet.doGet Accï¿½s ï¿½ doGet - ID: 4
30-May-2025 00:54:33.925 INFO [http-nio-8080-exec-289] controller.ModifierReservationServlet.doGet Rï¿½servation rï¿½cupï¿½rï¿½e: model.Reservation@7bb7838f
30-May-2025 00:54:33.925 INFO [http-nio-8080-exec-289] controller.ModifierReservationServlet.doGet ï¿½tat de la rï¿½servation: acheté
30-May-2025 00:54:33.926 WARNING [http-nio-8080-exec-289] controller.ModifierReservationServlet.doGet Rï¿½servation non modifiable (ï¿½tat: acheté) pour l'ID: 4
30-May-2025 00:54:56.099 INFO [http-nio-8080-exec-287] controller.ModifierReservationServlet.doGet Accï¿½s ï¿½ doGet - ID: 7
30-May-2025 00:54:56.104 INFO [http-nio-8080-exec-287] controller.ModifierReservationServlet.doGet Rï¿½servation rï¿½cupï¿½rï¿½e: model.Reservation@683605c8
30-May-2025 00:54:56.105 INFO [http-nio-8080-exec-287] controller.ModifierReservationServlet.doGet ï¿½tat de la rï¿½servation: acheté
30-May-2025 00:54:56.105 WARNING [http-nio-8080-exec-287] controller.ModifierReservationServlet.doGet Rï¿½servation non modifiable (ï¿½tat: acheté) pour l'ID: 7
30-May-2025 00:55:23.997 WARNING [http-nio-8080-exec-283] controller.AnnulerReservationServlet.doGet Rï¿½servation non annulable (ï¿½tat: acheté) pour l'ID: 15
30-May-2025 00:55:24.003 INFO [http-nio-8080-exec-284] controller.MonCompteServlet.doGet Found 0 reservations to update to 'utilisï¿½'
30-May-2025 00:58:26.746 INFO [http-nio-8080-exec-287] controller.LoginServlet.doPost Utilisateur connectï¿½ avec succï¿½s: <EMAIL>
30-May-2025 00:58:26.750 INFO [http-nio-8080-exec-283] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
30-May-2025 00:58:45.745 INFO [http-nio-8080-exec-288] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:59:10.094 INFO [http-nio-8080-exec-290] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:59:10.233 INFO [http-nio-8080-exec-292] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:59:10.454 INFO [http-nio-8080-exec-291] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:59:10.801 INFO [http-nio-8080-exec-287] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:59:11.033 INFO [http-nio-8080-exec-283] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:59:11.877 INFO [http-nio-8080-exec-284] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:59:12.090 INFO [http-nio-8080-exec-285] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:59:16.856 INFO [http-nio-8080-exec-288] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
30-May-2025 00:59:24.233 INFO [http-nio-8080-exec-289] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
30-May-2025 00:59:27.744 INFO [http-nio-8080-exec-290] dao.StationDAO.findAll Retrieved 10 stations
30-May-2025 00:59:33.379 INFO [http-nio-8080-exec-291] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
30-May-2025 00:59:41.495 INFO [http-nio-8080-exec-283] controller.AdminDashboardServlet.doGet AccÃ¨s au dashboard admin par: <EMAIL>
30-May-2025 00:59:44.278 INFO [http-nio-8080-exec-284] controller.ReservationEvolutionServlet.doGet doGet - Evolution Data: [[Ljava.lang.Object;@257c8939, [Ljava.lang.Object;@72babd5]
30-May-2025 00:59:44.278 INFO [http-nio-8080-exec-284] controller.ReservationEvolutionServlet.doGet doGet - Labels: [2025-04, 2025-05]
30-May-2025 00:59:44.278 INFO [http-nio-8080-exec-284] controller.ReservationEvolutionServlet.doGet doGet - Data: [0, 16]
30-May-2025 00:59:44.279 INFO [http-nio-8080-exec-284] controller.ReservationEvolutionServlet.doGet doGet - Attributes set - evolutionData: [[Ljava.lang.Object;@257c8939, [Ljava.lang.Object;@72babd5]
