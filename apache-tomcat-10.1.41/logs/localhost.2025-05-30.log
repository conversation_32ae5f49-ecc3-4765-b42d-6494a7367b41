30-May-2025 01:17:04.994 SEVERE [http-nio-8080-exec-294] org.apache.catalina.core.ApplicationDispatcher.invoke "Servlet.service()" pour la servlet [jsp] a lancé une exception
	jakarta.el.PropertyNotFoundException: Proprié<PERSON> [dateDepart] introuvable sur le type [model.Voyage]
		at jakarta.el.BeanELResolver$BeanProperties.get(BeanELResolver.java:261)
		at jakarta.el.BeanELResolver.property(BeanELResolver.java:330)
		at jakarta.el.BeanELResolver.getValue(BeanELResolver.java:99)
		at org.apache.jasper.el.JasperELResolver.getValue(JasperELResolver.java:128)
		at org.apache.el.parser.AstValue.getValue(AstValue.java:164)
		at org.apache.el.ValueExpressionImpl.getValue(ValueExpressionImpl.java:152)
		at org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate(PageContextImpl.java:674)
		at org.apache.jsp.admin.reservations_jsp._jspx_meth_fmt_005fformatDate_005f0(reservations_jsp.java:746)
		at org.apache.jsp.admin.reservations_jsp._jspx_meth_c_005fforEach_005f0(reservations_jsp.java:640)
		at org.apache.jsp.admin.reservations_jsp._jspx_meth_c_005fif_005f2(reservations_jsp.java:567)
		at org.apache.jsp.admin.reservations_jsp._jspService(reservations_jsp.java:292)
		at org.apache.jasper.runtime.HttpJspBase.service(HttpJspBase.java:64)
		at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
		at org.apache.jasper.servlet.JspServletWrapper.service(JspServletWrapper.java:436)
		at org.apache.jasper.servlet.JspServlet.serviceJspFile(JspServlet.java:357)
		at org.apache.jasper.servlet.JspServlet.service(JspServlet.java:308)
		at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
		at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
		at org.apache.catalina.core.ApplicationDispatcher.invoke(ApplicationDispatcher.java:612)
		at org.apache.catalina.core.ApplicationDispatcher.processRequest(ApplicationDispatcher.java:394)
		at org.apache.catalina.core.ApplicationDispatcher.doForward(ApplicationDispatcher.java:323)
		at org.apache.catalina.core.ApplicationDispatcher.forward(ApplicationDispatcher.java:268)
		at controller.AdminReservationsServlet.doGet(AdminReservationsServlet.java:83)
		at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
		at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
		at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
		at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
		at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
		at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
		at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
		at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
		at org.apache.catalina.valves.AbstractAccessLogValve.invoke(AbstractAccessLogValve.java:666)
		at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
		at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
		at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
		at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
		at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
		at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1740)
		at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
		at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
		at java.base/java.lang.Thread.run(Thread.java:842)
30-May-2025 01:17:05.005 SEVERE [http-nio-8080-exec-294] org.apache.catalina.core.ApplicationDispatcher.invoke "Servlet.service()" pour la servlet [jsp] a lancé une exception
	jakarta.el.PropertyNotFoundException: Propriété [dateDepart] introuvable sur le type [model.Voyage]
		at jakarta.el.BeanELResolver$BeanProperties.get(BeanELResolver.java:261)
		at jakarta.el.BeanELResolver.property(BeanELResolver.java:330)
		at jakarta.el.BeanELResolver.getValue(BeanELResolver.java:99)
		at org.apache.jasper.el.JasperELResolver.getValue(JasperELResolver.java:128)
		at org.apache.el.parser.AstValue.getValue(AstValue.java:164)
		at org.apache.el.ValueExpressionImpl.getValue(ValueExpressionImpl.java:152)
		at org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate(PageContextImpl.java:674)
		at org.apache.jsp.admin.reservations_jsp._jspx_meth_fmt_005fformatDate_005f0(reservations_jsp.java:746)
		at org.apache.jsp.admin.reservations_jsp._jspx_meth_c_005fforEach_005f0(reservations_jsp.java:640)
		at org.apache.jsp.admin.reservations_jsp._jspx_meth_c_005fif_005f2(reservations_jsp.java:567)
		at org.apache.jsp.admin.reservations_jsp._jspService(reservations_jsp.java:292)
		at org.apache.jasper.runtime.HttpJspBase.service(HttpJspBase.java:64)
		at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
		at org.apache.jasper.servlet.JspServletWrapper.service(JspServletWrapper.java:436)
		at org.apache.jasper.servlet.JspServlet.serviceJspFile(JspServlet.java:357)
		at org.apache.jasper.servlet.JspServlet.service(JspServlet.java:308)
		at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
		at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
		at org.apache.catalina.core.ApplicationDispatcher.invoke(ApplicationDispatcher.java:612)
		at org.apache.catalina.core.ApplicationDispatcher.processRequest(ApplicationDispatcher.java:394)
		at org.apache.catalina.core.ApplicationDispatcher.doForward(ApplicationDispatcher.java:323)
		at org.apache.catalina.core.ApplicationDispatcher.forward(ApplicationDispatcher.java:268)
		at controller.AdminReservationsServlet.doGet(AdminReservationsServlet.java:89)
		at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
		at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
		at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
		at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
		at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
		at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
		at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
		at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
		at org.apache.catalina.valves.AbstractAccessLogValve.invoke(AbstractAccessLogValve.java:666)
		at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
		at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
		at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
		at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
		at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
		at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1740)
		at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
		at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
		at java.base/java.lang.Thread.run(Thread.java:842)
30-May-2025 01:17:05.005 SEVERE [http-nio-8080-exec-294] org.apache.catalina.core.StandardWrapperValve.invoke Servlet.service() du Servlet [controller.AdminReservationsServlet] dans le contexte au chemin [/jeetraing] a retourné une exception [Une exception s'est produite lors du traitement de [/admin/reservations.jsp] à la ligne [163]

160:                                         <td>
161:                                             <div class="date-info">
162:                                                 <i class="fas fa-calendar"></i>
163:                                                 <fmt:formatDate value="${reservation.voyage.dateDepart}" pattern="dd/MM/yyyy" />
164:                                                 <br>
165:                                                 <small>
166:                                                     <i class="fas fa-clock"></i>


Stacktrace:] avec la cause
	jakarta.el.PropertyNotFoundException: Propriété [dateDepart] introuvable sur le type [model.Voyage]
		at jakarta.el.BeanELResolver$BeanProperties.get(BeanELResolver.java:261)
		at jakarta.el.BeanELResolver.property(BeanELResolver.java:330)
		at jakarta.el.BeanELResolver.getValue(BeanELResolver.java:99)
		at org.apache.jasper.el.JasperELResolver.getValue(JasperELResolver.java:128)
		at org.apache.el.parser.AstValue.getValue(AstValue.java:164)
		at org.apache.el.ValueExpressionImpl.getValue(ValueExpressionImpl.java:152)
		at org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate(PageContextImpl.java:674)
		at org.apache.jsp.admin.reservations_jsp._jspx_meth_fmt_005fformatDate_005f0(reservations_jsp.java:746)
		at org.apache.jsp.admin.reservations_jsp._jspx_meth_c_005fforEach_005f0(reservations_jsp.java:640)
		at org.apache.jsp.admin.reservations_jsp._jspx_meth_c_005fif_005f2(reservations_jsp.java:567)
		at org.apache.jsp.admin.reservations_jsp._jspService(reservations_jsp.java:292)
		at org.apache.jasper.runtime.HttpJspBase.service(HttpJspBase.java:64)
		at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
		at org.apache.jasper.servlet.JspServletWrapper.service(JspServletWrapper.java:436)
		at org.apache.jasper.servlet.JspServlet.serviceJspFile(JspServlet.java:357)
		at org.apache.jasper.servlet.JspServlet.service(JspServlet.java:308)
		at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
		at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
		at org.apache.catalina.core.ApplicationDispatcher.invoke(ApplicationDispatcher.java:612)
		at org.apache.catalina.core.ApplicationDispatcher.processRequest(ApplicationDispatcher.java:394)
		at org.apache.catalina.core.ApplicationDispatcher.doForward(ApplicationDispatcher.java:323)
		at org.apache.catalina.core.ApplicationDispatcher.forward(ApplicationDispatcher.java:268)
		at controller.AdminReservationsServlet.doGet(AdminReservationsServlet.java:89)
		at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
		at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
		at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
		at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
		at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
		at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
		at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
		at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
		at org.apache.catalina.valves.AbstractAccessLogValve.invoke(AbstractAccessLogValve.java:666)
		at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
		at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
		at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
		at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
		at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
		at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1740)
		at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
		at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
		at java.base/java.lang.Thread.run(Thread.java:842)
30-May-2025 01:17:28.369 SEVERE [http-nio-8080-exec-297] org.apache.catalina.core.ApplicationDispatcher.invoke "Servlet.service()" pour la servlet [jsp] a lancé une exception
	jakarta.el.PropertyNotFoundException: Propriété [dateDepart] introuvable sur le type [model.Voyage]
		at jakarta.el.BeanELResolver$BeanProperties.get(BeanELResolver.java:261)
		at jakarta.el.BeanELResolver.property(BeanELResolver.java:330)
		at jakarta.el.BeanELResolver.getValue(BeanELResolver.java:99)
		at org.apache.jasper.el.JasperELResolver.getValue(JasperELResolver.java:128)
		at org.apache.el.parser.AstValue.getValue(AstValue.java:164)
		at org.apache.el.ValueExpressionImpl.getValue(ValueExpressionImpl.java:152)
		at org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate(PageContextImpl.java:674)
		at org.apache.jsp.admin.reservations_jsp._jspx_meth_fmt_005fformatDate_005f0(reservations_jsp.java:746)
		at org.apache.jsp.admin.reservations_jsp._jspx_meth_c_005fforEach_005f0(reservations_jsp.java:640)
		at org.apache.jsp.admin.reservations_jsp._jspx_meth_c_005fif_005f2(reservations_jsp.java:567)
		at org.apache.jsp.admin.reservations_jsp._jspService(reservations_jsp.java:292)
		at org.apache.jasper.runtime.HttpJspBase.service(HttpJspBase.java:64)
		at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
		at org.apache.jasper.servlet.JspServletWrapper.service(JspServletWrapper.java:436)
		at org.apache.jasper.servlet.JspServlet.serviceJspFile(JspServlet.java:357)
		at org.apache.jasper.servlet.JspServlet.service(JspServlet.java:308)
		at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
		at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
		at org.apache.catalina.core.ApplicationDispatcher.invoke(ApplicationDispatcher.java:612)
		at org.apache.catalina.core.ApplicationDispatcher.processRequest(ApplicationDispatcher.java:394)
		at org.apache.catalina.core.ApplicationDispatcher.doForward(ApplicationDispatcher.java:323)
		at org.apache.catalina.core.ApplicationDispatcher.forward(ApplicationDispatcher.java:268)
		at controller.AdminReservationsServlet.doGet(AdminReservationsServlet.java:83)
		at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
		at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
		at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
		at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
		at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
		at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
		at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
		at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
		at org.apache.catalina.valves.AbstractAccessLogValve.invoke(AbstractAccessLogValve.java:666)
		at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
		at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
		at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
		at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
		at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
		at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1740)
		at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
		at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
		at java.base/java.lang.Thread.run(Thread.java:842)
30-May-2025 01:17:28.384 SEVERE [http-nio-8080-exec-297] org.apache.catalina.core.ApplicationDispatcher.invoke "Servlet.service()" pour la servlet [jsp] a lancé une exception
	jakarta.el.PropertyNotFoundException: Propriété [dateDepart] introuvable sur le type [model.Voyage]
		at jakarta.el.BeanELResolver$BeanProperties.get(BeanELResolver.java:261)
		at jakarta.el.BeanELResolver.property(BeanELResolver.java:330)
		at jakarta.el.BeanELResolver.getValue(BeanELResolver.java:99)
		at org.apache.jasper.el.JasperELResolver.getValue(JasperELResolver.java:128)
		at org.apache.el.parser.AstValue.getValue(AstValue.java:164)
		at org.apache.el.ValueExpressionImpl.getValue(ValueExpressionImpl.java:152)
		at org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate(PageContextImpl.java:674)
		at org.apache.jsp.admin.reservations_jsp._jspx_meth_fmt_005fformatDate_005f0(reservations_jsp.java:746)
		at org.apache.jsp.admin.reservations_jsp._jspx_meth_c_005fforEach_005f0(reservations_jsp.java:640)
		at org.apache.jsp.admin.reservations_jsp._jspx_meth_c_005fif_005f2(reservations_jsp.java:567)
		at org.apache.jsp.admin.reservations_jsp._jspService(reservations_jsp.java:292)
		at org.apache.jasper.runtime.HttpJspBase.service(HttpJspBase.java:64)
		at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
		at org.apache.jasper.servlet.JspServletWrapper.service(JspServletWrapper.java:436)
		at org.apache.jasper.servlet.JspServlet.serviceJspFile(JspServlet.java:357)
		at org.apache.jasper.servlet.JspServlet.service(JspServlet.java:308)
		at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
		at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
		at org.apache.catalina.core.ApplicationDispatcher.invoke(ApplicationDispatcher.java:612)
		at org.apache.catalina.core.ApplicationDispatcher.processRequest(ApplicationDispatcher.java:394)
		at org.apache.catalina.core.ApplicationDispatcher.doForward(ApplicationDispatcher.java:323)
		at org.apache.catalina.core.ApplicationDispatcher.forward(ApplicationDispatcher.java:268)
		at controller.AdminReservationsServlet.doGet(AdminReservationsServlet.java:89)
		at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
		at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
		at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
		at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
		at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
		at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
		at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
		at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
		at org.apache.catalina.valves.AbstractAccessLogValve.invoke(AbstractAccessLogValve.java:666)
		at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
		at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
		at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
		at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
		at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
		at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1740)
		at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
		at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
		at java.base/java.lang.Thread.run(Thread.java:842)
30-May-2025 01:17:28.384 SEVERE [http-nio-8080-exec-297] org.apache.catalina.core.StandardWrapperValve.invoke Servlet.service() du Servlet [controller.AdminReservationsServlet] dans le contexte au chemin [/jeetraing] a retourné une exception [Une exception s'est produite lors du traitement de [/admin/reservations.jsp] à la ligne [163]

160:                                         <td>
161:                                             <div class="date-info">
162:                                                 <i class="fas fa-calendar"></i>
163:                                                 <fmt:formatDate value="${reservation.voyage.dateDepart}" pattern="dd/MM/yyyy" />
164:                                                 <br>
165:                                                 <small>
166:                                                     <i class="fas fa-clock"></i>


Stacktrace:] avec la cause
	jakarta.el.PropertyNotFoundException: Propriété [dateDepart] introuvable sur le type [model.Voyage]
		at jakarta.el.BeanELResolver$BeanProperties.get(BeanELResolver.java:261)
		at jakarta.el.BeanELResolver.property(BeanELResolver.java:330)
		at jakarta.el.BeanELResolver.getValue(BeanELResolver.java:99)
		at org.apache.jasper.el.JasperELResolver.getValue(JasperELResolver.java:128)
		at org.apache.el.parser.AstValue.getValue(AstValue.java:164)
		at org.apache.el.ValueExpressionImpl.getValue(ValueExpressionImpl.java:152)
		at org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate(PageContextImpl.java:674)
		at org.apache.jsp.admin.reservations_jsp._jspx_meth_fmt_005fformatDate_005f0(reservations_jsp.java:746)
		at org.apache.jsp.admin.reservations_jsp._jspx_meth_c_005fforEach_005f0(reservations_jsp.java:640)
		at org.apache.jsp.admin.reservations_jsp._jspx_meth_c_005fif_005f2(reservations_jsp.java:567)
		at org.apache.jsp.admin.reservations_jsp._jspService(reservations_jsp.java:292)
		at org.apache.jasper.runtime.HttpJspBase.service(HttpJspBase.java:64)
		at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
		at org.apache.jasper.servlet.JspServletWrapper.service(JspServletWrapper.java:436)
		at org.apache.jasper.servlet.JspServlet.serviceJspFile(JspServlet.java:357)
		at org.apache.jasper.servlet.JspServlet.service(JspServlet.java:308)
		at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
		at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
		at org.apache.catalina.core.ApplicationDispatcher.invoke(ApplicationDispatcher.java:612)
		at org.apache.catalina.core.ApplicationDispatcher.processRequest(ApplicationDispatcher.java:394)
		at org.apache.catalina.core.ApplicationDispatcher.doForward(ApplicationDispatcher.java:323)
		at org.apache.catalina.core.ApplicationDispatcher.forward(ApplicationDispatcher.java:268)
		at controller.AdminReservationsServlet.doGet(AdminReservationsServlet.java:89)
		at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
		at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
		at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
		at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
		at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
		at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
		at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
		at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
		at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
		at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
		at org.apache.catalina.valves.AbstractAccessLogValve.invoke(AbstractAccessLogValve.java:666)
		at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
		at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
		at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
		at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
		at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
		at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1740)
		at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
		at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
		at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
		at java.base/java.lang.Thread.run(Thread.java:842)
