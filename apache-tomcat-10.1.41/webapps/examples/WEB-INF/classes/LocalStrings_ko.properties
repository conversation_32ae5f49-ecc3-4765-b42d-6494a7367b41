# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Do not edit this file directly.
# To edit translations see: https://tomcat.apache.org/getinvolved.html#Translations

cookies.cookies=\uadc0\ud558\uc758 \ube0c\ub77c\uc6b0\uc800\uac00 \ub2e4\uc74c \ucfe0\ud0a4\ub4e4\uc744 \ubcf4\ub0c5\ub2c8\ub2e4.
cookies.make-cookie=\uadc0\ud558\uc758 \ube0c\ub77c\uc6b0\uc800\uc5d0 \uc804\uc1a1\ud558\uae30 \uc704\ud55c \ucfe0\ud0a4 \uc0dd\uc131
cookies.name=\uc774\ub984:
cookies.no-cookies=\uadc0\ud558\uc758 \ube0c\ub77c\uc6b0\uc800\ub294 \uc5b4\ub5a4 \ucfe0\ud0a4\ub3c4 \uc804\uc1a1\ud558\uc9c0 \uc54a\uc2b5\ub2c8\ub2e4.
cookies.set=\uadc0\ud558\ub294 \ub2e4\uc74c \ucfe0\ud0a4\ub97c, \uadc0\ud558\uc758 \ube0c\ub77c\uc6b0\uc800\uc5d0 \uc804\uc1a1\ud588\uc2b5\ub2c8\ub2e4.
cookies.title=\ucfe0\ud0a4\ub4e4\uc758 \uc608\uc81c
cookies.value=\uac12:

helloworld.title=\uc548\ub155 \uc138\uacc4\uc5ec!

requestheader.title=\uc694\uccad\uc758 \ud5e4\ub354 \uc608\uc81c

requestinfo.label.method=\uba54\uc18c\ub4dc:
requestinfo.label.pathinfo=\uacbd\ub85c \uc815\ubcf4:
requestinfo.label.protocol=\ud504\ub85c\ud1a0\ucf5c:
requestinfo.label.remoteaddr=\uc6d0\uaca9 \uc8fc\uc18c:
requestinfo.label.requesturi=\uc694\uccad URI:
requestinfo.title=\uc694\uccad \uc815\ubcf4 \uc608\uc81c

requestparams.firstname=\uc774\ub984:
requestparams.lastname=\uc131
requestparams.no-params=\ud30c\ub77c\ubbf8\ud130\ub4e4\uc774 \uc5c6\uc2b5\ub2c8\ub2e4. \ud30c\ub77c\ubbf8\ud130\ub4e4\uc744 \uc785\ub825\ud558\uc2ed\uc2dc\uc624.
requestparams.params-in-req=\uc774 \uc694\uccad\uc758 \ud30c\ub77c\ubbf8\ud130\ub4e4:
requestparams.title=\uc694\uccad \ud30c\ub77c\ubbf8\ud130\ub4e4\uc758 \uc608\uc81c

sessions.adddata=\uadc0\ud558\uc758 \uc138\uc158\uc5d0 \ub370\uc774\ud130\ub97c \ucd94\uac00
sessions.created=\uc0dd\uc131\uc2dc\uac04:
sessions.data=\uadc0\ud558\uc758 \uc138\uc158\uc5d0 \ub2e4\uc74c \ub370\uc774\ud130\uac00 \uc788\uc2b5\ub2c8\ub2e4:
sessions.dataname=\uc138\uc158 \uc18d\uc131 \uc774\ub984:
sessions.datavalue=\uc138\uc158 \uc18d\uc131 \uac12:
sessions.id=\uc138\uc158 ID:
sessions.lastaccessed=\ucd5c\uc885 \uc811\uadfc \uc2dc\uac04:
sessions.title=\uc138\uc158\ub4e4\uc758 \uc608\uc81c
