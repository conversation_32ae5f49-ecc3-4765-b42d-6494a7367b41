# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Do not edit this file directly.
# To edit translations see: https://tomcat.apache.org/getinvolved.html#Translations

cookies.cookies=\u0412\u0430\u0448 \u0431\u0440\u0430\u0443\u0437\u0435\u0440 \u043e\u0442\u043f\u0440\u0430\u0432\u043b\u044f\u0435\u0442 \u0441\u043b\u0435\u0434\u0443\u044e\u0449\u0438\u0435 \u043a\u0443\u043a\u0438:
cookies.make-cookie=\u0421\u043e\u0437\u0434\u0430\u0439\u0442\u0435 \u043a\u0443\u043a\u0443 \u0434\u043b\u044f \u043e\u0442\u043f\u0440\u0430\u0432\u043a\u0438 \u0432 \u0432\u0430\u0448 \u0431\u0440\u0430\u0443\u0437\u0435\u0440
cookies.name=\u0418\u043c\u044f:
cookies.no-cookies=\u0412\u0430\u0448 \u0431\u0440\u0430\u0443\u0437\u0435\u0440 \u043d\u0435 \u043e\u0442\u043f\u0440\u0430\u0432\u043b\u044f\u0435\u0442 \u043d\u0438\u043a\u0430\u043a\u0438\u0445 \u043a\u0443\u043a.
cookies.set=\u0412\u044b \u0442\u043e\u043b\u044c\u043a\u043e \u0447\u0442\u043e \u043e\u0442\u043f\u0440\u0430\u0432\u0438\u043b\u0438 \u0441\u043b\u0435\u0434\u0443\u044e\u0449\u0443\u044e \u043a\u0443\u043a\u0443 \u0432 \u0432\u0430\u0448 \u0431\u0440\u0430\u0443\u0437\u0435\u0440:
cookies.value=\u0417\u043d\u0430\u0447\u0435\u043d\u0438\u0435:

helloworld.title=Hello World!

requestheader.title=\u041f\u0440\u0438\u043c\u0435\u0440 \u0437\u0430\u0433\u043e\u043b\u043e\u0432\u043a\u0430 \u0437\u0430\u043f\u0440\u043e\u0441\u0430

requestinfo.label.method=\u041c\u0435\u0442\u043e\u0434:
requestinfo.label.pathinfo=\u0418\u043d\u0444\u043e\u0440\u043c\u0430\u0446\u0438\u044f \u043e \u043f\u0443\u0442\u0438:
requestinfo.label.protocol=\u041f\u0440\u043e\u0442\u043e\u043a\u043e\u043b:
requestinfo.label.remoteaddr=\u0423\u0434\u0430\u043b\u0435\u043d\u043d\u044b\u0439 \u0430\u0434\u0440\u0435\u0441:
requestinfo.label.requesturi=URI \u0437\u0430\u043f\u0440\u043e\u0441\u0430:
requestinfo.title=\u041f\u0440\u0438\u043c\u0435\u0440 \u0438\u043d\u0444\u043e\u0440\u043c\u0430\u0446\u0438\u0438 \u043e \u0437\u0430\u043f\u0440\u043e\u0441\u0435

requestparams.firstname=\u0418\u043c\u044f:
requestparams.lastname=\u0424\u0430\u043c\u0438\u043b\u0438\u044f:
requestparams.no-params=\u041d\u0435\u0442 \u043f\u0430\u0440\u0430\u043c\u0435\u0442\u0440\u043e\u0432, \u041f\u043e\u0436\u0430\u043b\u0443\u0439\u0441\u0442\u0430 \u0434\u043e\u0431\u0430\u0432\u044c\u0442\u0435 \u043d\u0435\u0441\u043a\u043e\u043b\u044c\u043a\u043e
requestparams.params-in-req=\u041f\u0430\u0440\u0430\u043c\u0435\u0442\u0440\u044b \u0432 \u044d\u0442\u043e\u043c \u0437\u0430\u043f\u0440\u043e\u0441\u0435:
requestparams.title=\u041f\u0440\u0438\u043c\u0435\u0440 \u043f\u0430\u0440\u0430\u043c\u0435\u0442\u0440\u043e\u0432 \u0437\u0430\u043f\u0440\u043e\u0441\u0430

sessions.adddata=\u0414\u043e\u0431\u0430\u0432\u044c\u0442\u0435 \u0434\u0430\u043d\u043d\u044b\u0435 \u0432 \u0432\u0430\u0448\u0443 \u0441\u0435\u0441\u0441\u0438\u044e
sessions.created=\u0421\u043e\u0437\u0434\u0430\u043d\u043e:
sessions.data=\u0412 \u0432\u0430\u0448\u0435\u0439 \u0441\u0435\u0441\u0441\u0438\u0438 \u043d\u0430\u0445\u043e\u0434\u044f\u0442\u0441\u044f \u0441\u043b\u0435\u0434\u0443\u044e\u0449\u0438\u0435 \u0434\u0430\u043d\u043d\u044b\u0435:
sessions.dataname=\u0418\u043c\u044f \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u0430 \u0441\u0435\u0441\u0441\u0438\u0438:
sessions.datavalue=\u0417\u043d\u0430\u0447\u0435\u043d\u0438\u0435 \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u0430 \u0441\u0435\u0441\u0441\u0438\u0438:
sessions.id=ID \u0421\u0435\u0441\u0441\u0438\u0438:
sessions.lastaccessed=\u041f\u043e\u0441\u043b\u0435\u0434\u043d\u0435\u0435 \u043e\u0431\u0440\u0430\u0449\u0435\u043d\u0438\u0435:
sessions.title=\u041f\u0440\u0438\u043c\u0435\u0440 \u0441\u0435\u0441\u0441\u0438\u0439
