<!DOCTYPE html><html><head><meta charset="UTF-8" /><title>Source Code</title></head><body><pre>&lt;%--
 Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
--%>
&lt;%@ taglib prefix="my" uri="http://tomcat.apache.org/jsp2-example-taglib"%>
    &lt;h1>JSP 2.0 Examples - JSP Configuration&lt;/h1>
    &lt;hr>
    &lt;p>Using a &amp;lt;jsp-property-group&amp;gt; element in the web.xml
    deployment descriptor, this JSP page has been configured in the
    following ways:&lt;/p>
    &lt;ul>
      &lt;li>Uses &amp;lt;include-prelude&amp;gt; to include the top banner.&lt;/li>
      &lt;li>Uses &amp;lt;include-coda&amp;gt; to include the bottom banner.&lt;/li>
      &lt;li>Uses &amp;lt;scripting-invalid&amp;gt; true to disable
          &amp;lt;% scripting %&amp;gt; elements&lt;/li>
      &lt;li>Uses &amp;lt;el-ignored&amp;gt; true to disable ${EL} elements&lt;/li>
      &lt;li>Uses &amp;lt;page-encoding&amp;gt; ISO-8859-1 to set the page encoding (though this is the default anyway)&lt;/li>
    &lt;/ul>
    There are various other configuration options that can be used.

</pre></body></html>