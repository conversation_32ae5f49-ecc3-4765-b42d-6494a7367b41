<!DOCTYPE html><html><head><meta charset="UTF-8" /><title>Source Code</title></head><body><pre>&lt;%--
 Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
--%>
&lt;%@ taglib prefix="my" uri="http://tomcat.apache.org/jsp2-example-taglib"%>

&lt;html>
  &lt;head>
    &lt;title>JSP 2.0 Examples - jsp:attribute and jsp:body&lt;/title>
  &lt;/head>
  &lt;body>
    &lt;h1>JSP 2.0 Examples - jsp:attribute and jsp:body&lt;/h1>
    &lt;hr>
    &lt;p>The new &amp;lt;jsp:attribute&amp;gt; and &amp;lt;jsp:body&amp;gt;
    standard actions can be used to specify the value of any standard
    action or custom action attribute.&lt;/p>
    &lt;p>This example uses the &amp;lt;jsp:attribute&amp;gt;
    standard action to use the output of a custom action invocation
    (one that simply outputs "Hello, World!") to set the value of a
    bean property.  This would normally require an intermediary
    step, such as using JSTL's &amp;lt;c:set&amp;gt; action.&lt;/p>
    &lt;br>
    &lt;jsp:useBean id="foo" class="jsp2.examples.FooBean">
      Bean created!  Setting foo.bar...&lt;br>
      &lt;jsp:setProperty name="foo" property="bar">
        &lt;jsp:attribute name="value">
          &lt;my:helloWorld/>
        &lt;/jsp:attribute>
      &lt;/jsp:setProperty>
    &lt;/jsp:useBean>
    &lt;br>
    Result: ${foo.bar}
  &lt;/body>
&lt;/html>
</pre></body></html>