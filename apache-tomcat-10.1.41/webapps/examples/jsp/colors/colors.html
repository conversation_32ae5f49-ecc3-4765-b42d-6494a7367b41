<html>
<!--
 Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->

<body bgcolor= white>
<font size=6 color=red>

<hr>
This web page is an example using JSP and BEANs.
<p>
Guess my favorite two colors

<p> If you fail to guess both of them - you get yellow on red.

<p> If you guess one of them right, either your foreground or
    your background will change to the color that was guessed right.

<p> Guess them both right and your browser foreground/background
    will change to my two favorite colors to display this page.

<hr>
<form method=GET action=colrs.jsp>
Color #1: <input type=text name= color1 size=16>
<br>
Color #2: <input type=text name= color2 size=16>
<p>
<input type=submit name=action value="Submit">
<input type=submit name=action value="Hint">
</form>

</font>
</body>
</html>
