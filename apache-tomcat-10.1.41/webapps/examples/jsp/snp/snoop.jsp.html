<!DOCTYPE html><html><head><meta charset="UTF-8" /><title>Source Code</title></head><body><pre>&lt;%--
 Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
--%>
&lt;html>
&lt;body bgcolor="white">
&lt;h1> Request Information &lt;/h1>
&lt;font size="4">
JSP Request Method: &lt;%= util.HTMLFilter.filter(request.getMethod()) %>
&lt;br>
Request URI: &lt;%= util.HTMLFilter.filter(request.getRequestURI()) %>
&lt;br>
Request Protocol: &lt;%= util.HTMLFilter.filter(request.getProtocol()) %>
&lt;br>
Servlet path: &lt;%= util.HTMLFilter.filter(request.getServletPath()) %>
&lt;br>
Path info: &lt;%= util.HTMLFilter.filter(request.getPathInfo()) %>
&lt;br>
Query string: &lt;%= util.HTMLFilter.filter(request.getQueryString()) %>
&lt;br>
Content length: &lt;%= request.getContentLength() %>
&lt;br>
Content type: &lt;%= util.HTMLFilter.filter(request.getContentType()) %>
&lt;br>
Server name: &lt;%= util.HTMLFilter.filter(request.getServerName()) %>
&lt;br>
Server port: &lt;%= request.getServerPort() %>
&lt;br>
Remote user: &lt;%= util.HTMLFilter.filter(request.getRemoteUser()) %>
&lt;br>
Remote address: &lt;%= util.HTMLFilter.filter(request.getRemoteAddr()) %>
&lt;br>
Remote host: &lt;%= util.HTMLFilter.filter(request.getRemoteHost()) %>
&lt;br>
Authorization scheme: &lt;%= util.HTMLFilter.filter(request.getAuthType()) %>
&lt;br>
Locale: &lt;%= request.getLocale() %>
&lt;hr>
The browser you are using is
&lt;%= util.HTMLFilter.filter(request.getHeader("User-Agent")) %>
&lt;hr>
&lt;/font>
&lt;/body>
&lt;/html>
</pre></body></html>