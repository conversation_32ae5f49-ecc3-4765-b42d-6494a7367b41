<!DOCTYPE html><html><head><meta charset="UTF-8" /><title>Source Code</title></head><body><pre>&lt;%--
 Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
--%>
&lt;%@ page import="sessions.Item" %>
&lt;html>
&lt;head>
&lt;title>Shopping Cart Example&lt;/title>
&lt;/head>

&lt;body bgcolor="white">
&lt;font size = 5 color="#CC0000">

&lt;form type=POST action=carts.jsp>
&lt;br>
Please enter item to add or remove:
&lt;br>
Select Item:

&lt;select name="itemId">
&lt;%
  for (Item item : Item.values()) {
%>
  &lt;option value="&lt;%= item.ordinal() %>">&lt;%= item.getTitle() %>&lt;/option>
&lt;%
  }
%>
&lt;/select>

&lt;br> &lt;br>
&lt;INPUT TYPE=submit name="submit" value="add">
&lt;INPUT TYPE=submit name="submit" value="remove">

&lt;/form>

&lt;/font>
&lt;/body>
&lt;/html>
</pre></body></html>