/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

body {
    font-family: Tahoma, Arial, sans-serif;
}

h1,
h2,
h3,
b {
    color           : white;
    background-color: #525D76;
}

h1 {
    font-size: 22px;
}

h2 {
    font-size: 16px;
}

h3 {
    font-size: 14px;
}

p {
    font-size: 12px;
}

a {
    color: black;
}

.line {
    height          : 1px;
    background-color: #525D76;
    border          : none;
}

table {
    width: 100%;
}

td.page-title {
    text-align    : center;
    vertical-align: top;
    font-family   : sans-serif, <PERSON><PERSON><PERSON>, <PERSON><PERSON>;
    font-weight   : bold;
    background    : white;
    color         : black;
}

td.title {
    text-align    : left;
    vertical-align: top;
    font-family   : sans-serif, Tahoma, Arial;
    font-style    : italic;
    font-weight   : bold;
    background    : #D2A41C;
}

td.header-left {
    text-align    : left;
    vertical-align: top;
    font-family   : sans-serif, Tahoma, Arial;
    font-weight   : bold;
    background    : #FFDC75;
}

td.header-center {
    text-align    : center;
    vertical-align: top;
    font-family   : sans-serif, Tahoma, Arial;
    font-weight   : bold;
    background    : #FFDC75;
}

td.row-left {
    text-align    : left;
    vertical-align: middle;
    font-family   : sans-serif, Tahoma, Arial;
    color         : black;
}

td.row-center {
    text-align    : center;
    vertical-align: middle;
    font-family   : sans-serif, Tahoma, Arial;
    color         : black;
}

td.row-right {
    text-align    : right;
    vertical-align: middle;
    font-family   : sans-serif, Tahoma, Arial;
    color         : black;
}

TH {
    text-align    : center;
    vertical-align: top;
    font-family   : sans-serif, Tahoma, Arial;
    font-weight   : bold;
    background    : #FFDC75;
}

TD {
    text-align    : center;
    vertical-align: middle;
    font-family   : sans-serif, Tahoma, Arial;
    color         : black;
}

form {
    margin: 1;
}

form.inline {
    display: inline;
}

img.tomcat-logo {
    height: 92px;
    float : left;
}