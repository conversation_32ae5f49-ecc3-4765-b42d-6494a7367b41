<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚂 Recherche de Trajets - JEE Training</title>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <a href="${pageContext.request.contextPath}/recherche" class="logo">
                <i class="fas fa-train"></i> JEE Training
            </a>
            <div class="nav-links">
                <c:choose>
                    <c:when test="${sessionScope.user != null}">
                        <a href="${pageContext.request.contextPath}/recherche"><i class="fas fa-search"></i> Rechercher</a>
                        <c:if test="${sessionScope.user.role == 'admin'}">
                            <a href="${pageContext.request.contextPath}/admin/promotions"><i class="fas fa-cog"></i> Admin</a>
                        </c:if>
                        <a href="${pageContext.request.contextPath}/logout"><i class="fas fa-sign-out-alt"></i> Déconnexion</a>
                    </c:when>
                    <c:otherwise>
                        <a href="${pageContext.request.contextPath}/login.jsp"><i class="fas fa-sign-in-alt"></i> Connexion</a>
                        <a href="${pageContext.request.contextPath}/register.jsp"><i class="fas fa-user-plus"></i> Inscription</a>
                    </c:otherwise>
                </c:choose>
            </div>
        </div>
    </nav>

    <!-- Contenu principal -->
    <div class="container">
        <div class="main-content">
            <h1><i class="fas fa-search"></i> Recherche de Trajets</h1>

            <!-- Messages d'erreur -->
            <c:if test="${not empty error}">
                <div class="alert alert-error" role="alert">
                    <i class="fas fa-exclamation-triangle"></i> ${error}
                </div>
            </c:if>

            <!-- Formulaire de recherche -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title"><i class="fas fa-route"></i> Planifiez votre voyage</h3>
                </div>
                <form action="recherche" method="post">
                    <div class="row">
                        <div class="col-2">
                            <div class="form-group">
                                <label for="villeDepart"><i class="fas fa-map-marker-alt"></i> Ville de départ</label>
                                <select id="villeDepart" name="villeDepart" class="form-control" required>
                                    <option value="">Sélectionnez une ville de départ</option>
                                    <c:forEach var="city" items="${cities}">
                                        <option value="${city}" ${city == villeDepart ? 'selected' : ''}>${city}</option>
                                    </c:forEach>
                                </select>
                            </div>
                        </div>
                        <div class="col-2">
                            <div class="form-group">
                                <label for="villeDestination"><i class="fas fa-flag-checkered"></i> Ville de destination</label>
                                <select id="villeDestination" name="villeDestination" class="form-control" required>
                                    <option value="">Sélectionnez une ville d'arrivée</option>
                                    <c:forEach var="city" items="${cities}">
                                        <option value="${city}" ${city == villeDestination ? 'selected' : ''}>${city}</option>
                                    </c:forEach>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="date"><i class="fas fa-calendar-alt"></i> Date de départ</label>
                        <input type="date" id="date" name="date" value="${date}" class="form-control" required>
                    </div>
                    <button type="submit" class="btn btn-primary btn-full">
                        <i class="fas fa-search"></i> Rechercher des trajets
                    </button>
                </form>
            </div>

            <!-- Résultats de la recherche -->
            <c:if test="${not empty voyages}">
                <div class="card mt-20">
                    <div class="card-header">
                        <h3 class="card-title"><i class="fas fa-list"></i> Résultats de la recherche</h3>
                        <p>Nous avons trouvé ${voyages.size()} voyage(s) pour votre recherche</p>
                    </div>

                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th><i class="fas fa-map-marker-alt"></i> Départ</th>
                                    <th><i class="fas fa-flag-checkered"></i> Arrivée</th>
                                    <th><i class="fas fa-clock"></i> Horaires</th>
                                    <th><i class="fas fa-hourglass-half"></i> Durée</th>
                                    <th><i class="fas fa-euro-sign"></i> Prix</th>
                                    <th><i class="fas fa-users"></i> Places</th>
                                    <th><i class="fas fa-tags"></i> Promotions</th>
                                    <th><i class="fas fa-ticket-alt"></i> Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <c:forEach var="voyage" items="${voyages}">
                                    <tr>
                                        <td>
                                            <strong>${voyage.trajet.departStation.name}</strong><br>
                                            <small class="text-muted">${voyage.trajet.departStation.city}</small>
                                        </td>
                                        <td>
                                            <strong>${voyage.trajet.arrivalStation.name}</strong><br>
                                            <small class="text-muted">${voyage.trajet.arrivalStation.city}</small>
                                        </td>
                                        <td>
                                            <div><i class="fas fa-play"></i> ${voyage.heureDepartFormatted}</div>
                                            <div><i class="fas fa-stop"></i> ${voyage.heureArriveeFormatted}</div>
                                        </td>
                                        <td>
                                            <span class="badge">${voyage.duree}</span>
                                        </td>
                                        <td>
                                            <c:set var="originalPrice" value="${voyage.prix}"/>
                                            <c:set var="bestDiscount" value="0"/>
                                            <c:forEach var="promo" items="${voyagePromotions[voyage.id]}">
                                                <c:if test="${promo.discountPercentage > bestDiscount}">
                                                    <c:set var="bestDiscount" value="${promo.discountPercentage}"/>
                                                </c:if>
                                            </c:forEach>
                                            <c:choose>
                                                <c:when test="${bestDiscount > 0}">
                                                    <div class="price-original">${originalPrice} €</div>
                                                    <div class="price-discount">${originalPrice * (1 - bestDiscount / 100)} €</div>
                                                    <small class="promotion">-${bestDiscount}%</small>
                                                </c:when>
                                                <c:otherwise>
                                                    <div class="price-current">${originalPrice} €</div>
                                                </c:otherwise>
                                            </c:choose>
                                        </td>
                                        <td>
                                            <c:choose>
                                                <c:when test="${voyage.placesDisponibles > 10}">
                                                    <span class="badge badge-success">${voyage.placesDisponibles} places</span>
                                                </c:when>
                                                <c:when test="${voyage.placesDisponibles > 0}">
                                                    <span class="badge badge-warning">${voyage.placesDisponibles} places</span>
                                                </c:when>
                                                <c:otherwise>
                                                    <span class="badge badge-danger">Complet</span>
                                                </c:otherwise>
                                            </c:choose>
                                        </td>
                                        <td>
                                            <c:if test="${not empty voyagePromotions[voyage.id]}">
                                                <c:forEach var="promo" items="${voyagePromotions[voyage.id]}">
                                                    <div class="promotion-tag">
                                                        <i class="fas fa-tag"></i> ${promo.code}
                                                        <small>(-${promo.discountPercentage}%)</small>
                                                    </div>
                                                </c:forEach>
                                            </c:if>
                                        </td>
                                        <td>
                                            <c:choose>
                                                <c:when test="${voyage.placesDisponibles > 0}">
                                                    <a href="selection?voyageId=${voyage.id}" class="btn btn-success">
                                                        <i class="fas fa-check"></i> Sélectionner
                                                    </a>
                                                </c:when>
                                                <c:otherwise>
                                                    <button class="btn btn-secondary" disabled>
                                                        <i class="fas fa-times"></i> Indisponible
                                                    </button>
                                                </c:otherwise>
                                            </c:choose>
                                        </td>
                                    </tr>
                                </c:forEach>
                            </tbody>
                        </table>
                    </div>
                </div>
            </c:if>

            <c:if test="${empty voyages && not empty villeDepart}">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    Aucun voyage trouvé pour votre recherche. Essayez avec d'autres villes ou une autre date.
                </div>
            </c:if>
        </div>
    </div>
</body>
</html>