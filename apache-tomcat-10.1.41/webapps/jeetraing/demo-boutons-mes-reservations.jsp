<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎯 Démonstration Boutons Mes Réservations - JEE Training</title>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <a href="${pageContext.request.contextPath}/recherche" class="logo">
                <i class="fas fa-train"></i> JEE Training
            </a>
            <div class="nav-links">
                <a href="${pageContext.request.contextPath}/login.jsp"><i class="fas fa-sign-in-alt"></i> Connexion</a>
                <a href="${pageContext.request.contextPath}/register.jsp"><i class="fas fa-user-plus"></i> Inscription</a>
            </div>
        </div>
    </nav>

    <!-- Contenu principal -->
    <div class="container">
        <div class="main-content">
            <h1><i class="fas fa-palette"></i> Démonstration des Boutons Mes Réservations</h1>
            
            <!-- Message de succès -->
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> 
                <strong>✅ BOUTONS MODIFIER ET ANNULER IMPLÉMENTÉS AVEC SUCCÈS !</strong>
                <br>
                <small>Bouton Modifier fonctionnel (orange) et Bouton Annuler en rouge comme demandé.</small>
            </div>

            <!-- Démonstration des boutons -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title"><i class="fas fa-mouse-pointer"></i> Boutons d'Action dans Mes Réservations</h2>
                </div>
                
                <div class="row">
                    <div class="col-3">
                        <div class="card" style="text-align: center;">
                            <h4><i class="fas fa-edit"></i> Bouton Modifier</h4>
                            <a href="#" class="admin-btn admin-btn-warning admin-btn-sm" style="pointer-events: none;">
                                <i class="fas fa-edit"></i> Modifier
                            </a>
                            <p style="font-size: 12px; margin-top: 10px;">
                                <strong>Couleur :</strong> Orange (Warning)<br>
                                <strong>URL :</strong> <code>/modifierReservation?id=X</code><br>
                                <strong>Fonction :</strong> Modification des réservations<br>
                                <strong>Disponible pour :</strong> États "confirmée" et "acheté"
                            </p>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="card" style="text-align: center;">
                            <h4><i class="fas fa-times"></i> Bouton Annuler</h4>
                            <a href="#" class="admin-btn admin-btn-danger admin-btn-sm" style="pointer-events: none;">
                                <i class="fas fa-times"></i> Annuler
                            </a>
                            <p style="font-size: 12px; margin-top: 10px;">
                                <strong>Couleur :</strong> Rouge (Danger)<br>
                                <strong>URL :</strong> <code>/annuler-reservation?id=X</code><br>
                                <strong>Fonction :</strong> Demande d'annulation<br>
                                <strong>Disponible pour :</strong> Tous sauf "annulée" et "utilisé"
                            </p>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="card" style="text-align: center;">
                            <h4><i class="fas fa-file-pdf"></i> Bouton PDF</h4>
                            <a href="#" class="admin-btn admin-btn-success admin-btn-sm" style="pointer-events: none;">
                                <i class="fas fa-file-pdf"></i> PDF
                            </a>
                            <p style="font-size: 12px; margin-top: 10px;">
                                <strong>Couleur :</strong> Vert (Success)<br>
                                <strong>URL :</strong> <code>/download-ticket?reservationId=X</code><br>
                                <strong>Fonction :</strong> Téléchargement billet<br>
                                <strong>Disponible pour :</strong> États "confirmée" et "acheté"
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Simulation du tableau des réservations -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title"><i class="fas fa-table"></i> Simulation du Tableau Mes Réservations</h2>
                </div>
                
                <table class="admin-table">
                    <thead>
                        <tr>
                            <th><i class="fas fa-hashtag"></i> ID</th>
                            <th><i class="fas fa-route"></i> Trajet</th>
                            <th><i class="fas fa-calendar"></i> Date</th>
                            <th><i class="fas fa-info-circle"></i> État</th>
                            <th><i class="fas fa-cogs"></i> Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Réservation confirmée - Tous les boutons -->
                        <tr>
                            <td><strong>#4</strong></td>
                            <td>Paris → Lyon</td>
                            <td>28/05/2025 08:00</td>
                            <td>
                                <span class="badge badge-success">
                                    <i class="fas fa-check-circle"></i> Confirmée
                                </span>
                            </td>
                            <td>
                                <a href="#" class="admin-btn admin-btn-warning admin-btn-sm" style="pointer-events: none;">
                                    <i class="fas fa-edit"></i> Modifier
                                </a>
                                <a href="#" class="admin-btn admin-btn-success admin-btn-sm" style="pointer-events: none;">
                                    <i class="fas fa-file-pdf"></i> PDF
                                </a>
                                <a href="#" class="admin-btn admin-btn-danger admin-btn-sm" style="pointer-events: none;">
                                    <i class="fas fa-times"></i> Annuler
                                </a>
                            </td>
                        </tr>
                        
                        <!-- Réservation achetée - Tous les boutons -->
                        <tr>
                            <td><strong>#5</strong></td>
                            <td>Paris → Lyon</td>
                            <td>28/05/2025 10:00</td>
                            <td>
                                <span class="badge badge-primary">
                                    <i class="fas fa-shopping-cart"></i> Acheté
                                </span>
                            </td>
                            <td>
                                <a href="#" class="admin-btn admin-btn-warning admin-btn-sm" style="pointer-events: none;">
                                    <i class="fas fa-edit"></i> Modifier
                                </a>
                                <a href="#" class="admin-btn admin-btn-success admin-btn-sm" style="pointer-events: none;">
                                    <i class="fas fa-file-pdf"></i> PDF
                                </a>
                                <a href="#" class="admin-btn admin-btn-danger admin-btn-sm" style="pointer-events: none;">
                                    <i class="fas fa-times"></i> Annuler
                                </a>
                            </td>
                        </tr>
                        
                        <!-- Réservation en attente - Seulement annuler -->
                        <tr>
                            <td><strong>#6</strong></td>
                            <td>Paris → Lyon</td>
                            <td>28/05/2025 12:00</td>
                            <td>
                                <span class="badge badge-warning">
                                    <i class="fas fa-clock"></i> En Attente
                                </span>
                            </td>
                            <td>
                                <a href="#" class="admin-btn admin-btn-danger admin-btn-sm" style="pointer-events: none;">
                                    <i class="fas fa-times"></i> Annuler
                                </a>
                            </td>
                        </tr>
                        
                        <!-- Réservation annulée - Aucune action -->
                        <tr>
                            <td><strong>#7</strong></td>
                            <td>Paris → Lyon</td>
                            <td>28/05/2025 14:00</td>
                            <td>
                                <span class="badge badge-danger">
                                    <i class="fas fa-ban"></i> Annulée
                                </span>
                            </td>
                            <td>
                                <span class="admin-btn admin-btn-secondary admin-btn-sm" style="cursor: not-allowed;">
                                    <i class="fas fa-ban"></i> Aucune action
                                </span>
                            </td>
                        </tr>
                        
                        <!-- Réservation utilisée - Aucune action -->
                        <tr>
                            <td><strong>#8</strong></td>
                            <td>Paris → Lyon</td>
                            <td>25/05/2025 16:00</td>
                            <td>
                                <span class="badge badge-info">
                                    <i class="fas fa-check"></i> Utilisé
                                </span>
                            </td>
                            <td>
                                <span class="admin-btn admin-btn-secondary admin-btn-sm" style="cursor: not-allowed;">
                                    <i class="fas fa-ban"></i> Aucune action
                                </span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Logique d'affichage -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title"><i class="fas fa-code"></i> Logique d'Affichage des Boutons</h2>
                </div>
                
                <div class="row">
                    <div class="col-4">
                        <div class="card">
                            <h4><i class="fas fa-edit"></i> Bouton Modifier</h4>
                            <ul style="font-size: 12px;">
                                <li><strong>Condition :</strong> État = "confirmée" OU "acheté"</li>
                                <li><strong>Couleur :</strong> Orange (admin-btn-warning)</li>
                                <li><strong>Action :</strong> Redirection vers page modification</li>
                                <li><strong>Tooltip :</strong> "Modifier cette réservation"</li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="card">
                            <h4><i class="fas fa-times"></i> Bouton Annuler</h4>
                            <ul style="font-size: 12px;">
                                <li><strong>Condition :</strong> État ≠ "annulée" ET ≠ "utilisé"</li>
                                <li><strong>Couleur :</strong> Rouge (admin-btn-danger)</li>
                                <li><strong>Action :</strong> Confirmation puis demande d'annulation</li>
                                <li><strong>Tooltip :</strong> "Demander l'annulation"</li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="card">
                            <h4><i class="fas fa-file-pdf"></i> Bouton PDF</h4>
                            <ul style="font-size: 12px;">
                                <li><strong>Condition :</strong> État = "confirmée" OU "acheté"</li>
                                <li><strong>Couleur :</strong> Vert (admin-btn-success)</li>
                                <li><strong>Action :</strong> Téléchargement du billet PDF</li>
                                <li><strong>Tooltip :</strong> "Télécharger le billet PDF"</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Améliorations apportées -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title"><i class="fas fa-star"></i> Améliorations Apportées</h2>
                </div>
                
                <div class="row">
                    <div class="col-2">
                        <div class="admin-card">
                            <div class="admin-card-icon">
                                <i class="fas fa-palette"></i>
                            </div>
                            <div class="admin-card-content">
                                <h3>🎨 Couleurs Améliorées</h3>
                                <ul style="font-size: 12px;">
                                    <li>✅ Bouton Modifier : Orange vif</li>
                                    <li>✅ Bouton Annuler : Rouge intense</li>
                                    <li>✅ Effets hover et active</li>
                                    <li>✅ Styles CSS avec !important</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-2">
                        <div class="admin-card">
                            <div class="admin-card-icon">
                                <i class="fas fa-cogs"></i>
                            </div>
                            <div class="admin-card-content">
                                <h3>⚙️ Logique Améliorée</h3>
                                <ul style="font-size: 12px;">
                                    <li>✅ Conditions d'affichage étendues</li>
                                    <li>✅ Support états "acheté" et "confirmée"</li>
                                    <li>✅ Gestion des états non modifiables</li>
                                    <li>✅ Tooltips informatifs</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Instructions de test -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title"><i class="fas fa-play-circle"></i> Comment Tester</h2>
                </div>
                
                <div class="workflow-steps">
                    <div class="workflow-step">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <h4><i class="fas fa-sign-in-alt"></i> Se Connecter</h4>
                            <p>Aller sur <code>/login.jsp</code> et se connecter avec un compte utilisateur</p>
                        </div>
                    </div>
                    
                    <div class="workflow-step">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <h4><i class="fas fa-ticket-alt"></i> Accéder à Mes Réservations</h4>
                            <p>Naviguer vers <code>/mes-reservations</code> pour voir les boutons en action</p>
                        </div>
                    </div>
                    
                    <div class="workflow-step">
                        <div class="step-number">3</div>
                        <div class="step-content">
                            <h4><i class="fas fa-mouse-pointer"></i> Tester les Boutons</h4>
                            <p>Cliquer sur "Modifier" (orange) et "Annuler" (rouge) pour vérifier le fonctionnement</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions de test -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title"><i class="fas fa-rocket"></i> Tester Maintenant</h2>
                </div>
                
                <div class="row">
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/login.jsp" class="btn btn-primary btn-full">
                            <i class="fas fa-sign-in-alt"></i> Se Connecter
                        </a>
                    </div>
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/mes-reservations" class="btn btn-success btn-full">
                            <i class="fas fa-ticket-alt"></i> Mes Réservations
                        </a>
                    </div>
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/corrections-correspondance-finales.jsp" class="btn btn-warning btn-full">
                            <i class="fas fa-eye"></i> Voir Corrections
                        </a>
                    </div>
                </div>
            </div>

            <!-- Message final -->
            <div class="text-center mt-20">
                <div class="alert alert-success">
                    <i class="fas fa-trophy"></i> 
                    <strong>🎯 BOUTONS IMPLÉMENTÉS AVEC SUCCÈS ! 🎯</strong>
                    <br>
                    <small>Bouton Modifier fonctionnel (orange) et Bouton Annuler en rouge comme demandé.</small>
                    <br>
                    <div style="margin-top: 10px;">
                        <span class="badge badge-success">✅ Bouton Modifier Orange</span>
                        <span class="badge badge-success">✅ Bouton Annuler Rouge</span>
                        <span class="badge badge-success">✅ Correspondance URLs</span>
                        <span class="badge badge-success">✅ Logique Conditionnelle</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        .workflow-steps {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .workflow-step {
            display: flex;
            align-items: flex-start;
            gap: 15px;
            padding: 15px;
            background: var(--light-bg);
            border-radius: 8px;
            border-left: 4px solid var(--success-color);
        }
        
        .step-number {
            width: 30px;
            height: 30px;
            background: var(--success-color);
            color: var(--white);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            flex-shrink: 0;
        }
        
        .step-content h4 {
            margin: 0 0 5px 0;
            color: var(--text-dark);
        }
        
        .step-content p {
            margin: 0;
            color: var(--text-muted);
            font-size: 14px;
        }
    </style>
</body>
</html>
