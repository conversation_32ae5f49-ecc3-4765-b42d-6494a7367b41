<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="jakarta.tags.core" prefix="c" %>
<%@ taglib uri="jakarta.tags.fmt" prefix="fmt" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>💰 Revenus Générés - JEE Training</title>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <a href="${pageContext.request.contextPath}/recherche" class="logo">
                <i class="fas fa-train"></i> JEE Training
            </a>
            <div class="nav-links">
                <c:choose>
                    <c:when test="${sessionScope.user != null}">
                        <a href="${pageContext.request.contextPath}/recherche"><i class="fas fa-search"></i> Rechercher</a>
                        <c:if test="${sessionScope.user.role == 'admin'}">
                            <a href="${pageContext.request.contextPath}/admin/promotions"><i class="fas fa-cog"></i> Admin</a>
                        </c:if>
                        <a href="${pageContext.request.contextPath}/logout"><i class="fas fa-sign-out-alt"></i> Déconnexion</a>
                    </c:when>
                    <c:otherwise>
                        <a href="${pageContext.request.contextPath}/login.jsp"><i class="fas fa-sign-in-alt"></i> Connexion</a>
                        <a href="${pageContext.request.contextPath}/register.jsp"><i class="fas fa-user-plus"></i> Inscription</a>
                    </c:otherwise>
                </c:choose>
            </div>
        </div>
    </nav>

    <!-- Contenu principal -->
    <div class="container">
        <div class="main-content">
            <h1><i class="fas fa-euro-sign"></i> Revenus Générés</h1>

            <!-- Breadcrumb -->
            <div class="breadcrumb">
                <a href="${pageContext.request.contextPath}/admin/dashboard"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                <span><i class="fas fa-chevron-right"></i></span>
                <span>Revenus Générés</span>
            </div>

            <!-- Formulaire de calcul -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title"><i class="fas fa-calculator"></i> Calculer les Revenus</h2>
                </div>

                <form method="post" action="${pageContext.request.contextPath}/admin/revenue" class="admin-form">
                    <div class="row">
                        <div class="col-2">
                            <div class="admin-form-group">
                                <label for="startDate"><i class="fas fa-calendar-alt"></i> Date de début</label>
                                <input type="datetime-local" id="startDate" name="startDate" required>
                            </div>
                        </div>
                        <div class="col-2">
                            <div class="admin-form-group">
                                <label for="endDate"><i class="fas fa-calendar-alt"></i> Date de fin</label>
                                <input type="datetime-local" id="endDate" name="endDate" required>
                            </div>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-calculator"></i> Calculer les Revenus
                    </button>
                </form>
            </div>

            <!-- Résultats -->
            <c:if test="${not empty totalRevenue}">
                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title"><i class="fas fa-chart-line"></i> Résultats</h2>
                    </div>

                    <div class="revenue-results">
                        <div class="revenue-period">
                            <i class="fas fa-calendar-week"></i>
                            <strong>Période analysée :</strong>
                            <span>${startDate} à ${endDate}</span>
                        </div>

                        <div class="revenue-total">
                            <div class="revenue-amount">
                                <i class="fas fa-coins"></i>
                                <span class="amount"><fmt:formatNumber value="${totalRevenue}" pattern="#,##0.00"/> €</span>
                            </div>
                            <p>Revenus totaux générés</p>
                        </div>

                        <div class="revenue-stats">
                            <div class="stat-item">
                                <i class="fas fa-chart-bar"></i>
                                <span>Analyse détaillée disponible</span>
                            </div>
                        </div>
                    </div>
                </div>
            </c:if>

            <!-- Actions rapides -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title"><i class="fas fa-bolt"></i> Actions Rapides</h2>
                </div>

                <div class="row">
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/admin/dashboard" class="btn btn-secondary btn-full">
                            <i class="fas fa-arrow-left"></i> Retour Dashboard
                        </a>
                    </div>
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/admin/popular-trajets" class="btn btn-primary btn-full">
                            <i class="fas fa-route"></i> Trajets Populaires
                        </a>
                    </div>
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/admin/reservation-evolution" class="btn btn-warning btn-full">
                            <i class="fas fa-chart-line"></i> Évolution Réservations
                        </a>
                    </div>
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/admin/users" class="btn btn-success btn-full">
                            <i class="fas fa-users"></i> Gestion Utilisateurs
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
