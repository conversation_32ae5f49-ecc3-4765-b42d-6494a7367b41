/* ===== VARIABLES CSS ===== */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --accent-color: #e74c3c;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --light-gray: #ecf0f1;
    --dark-gray: #7f8c8d;
    --white: #ffffff;
    --shadow: 0 2px 10px rgba(0,0,0,0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

/* ===== RESET ET BASE ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--primary-color);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

/* ===== CONTENEUR PRINCIPAL ===== */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.main-content {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 30px;
    margin: 20px 0;
}

/* ===== NAVIGATION ===== */
.navbar {
    background: var(--white);
    box-shadow: var(--shadow);
    padding: 15px 0;
    margin-bottom: 20px;
}

.navbar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.navbar .logo {
    font-size: 24px;
    font-weight: bold;
    color: var(--primary-color);
    text-decoration: none;
}

.navbar .nav-links {
    display: flex;
    gap: 20px;
    align-items: center;
}

.navbar .nav-links a {
    color: var(--primary-color);
    text-decoration: none;
    padding: 8px 16px;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.navbar .nav-links a:hover {
    background: var(--secondary-color);
    color: var(--white);
}

/* ===== TITRES ===== */
h1, h2, h3, h4, h5, h6 {
    margin-bottom: 20px;
    color: var(--primary-color);
}

h1 {
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 30px;
    background: linear-gradient(45deg, var(--secondary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

h2 {
    font-size: 2rem;
    border-bottom: 3px solid var(--secondary-color);
    padding-bottom: 10px;
    margin-bottom: 25px;
}

/* ===== FORMULAIRES ===== */
.form-container {
    max-width: 500px;
    margin: 0 auto;
    background: var(--white);
    padding: 40px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--primary-color);
}

.form-control {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid var(--light-gray);
    border-radius: var(--border-radius);
    font-size: 16px;
    transition: var(--transition);
    background: var(--white);
}

.form-control:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.form-control:hover {
    border-color: var(--secondary-color);
}

/* ===== BOUTONS ===== */
.btn {
    display: inline-block;
    padding: 12px 24px;
    border: none;
    border-radius: var(--border-radius);
    font-size: 16px;
    font-weight: 600;
    text-decoration: none;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
    margin: 5px;
}

.btn-primary {
    background: var(--secondary-color);
    color: var(--white);
}

.btn-primary:hover {
    background: #2980b9;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.btn-success {
    background: var(--success-color);
    color: var(--white);
}

.btn-success:hover {
    background: #229954;
    transform: translateY(-2px);
}

.btn-danger {
    background: var(--accent-color);
    color: var(--white);
}

.btn-danger:hover {
    background: #c0392b;
    transform: translateY(-2px);
}

.btn-secondary {
    background: var(--dark-gray);
    color: var(--white);
}

.btn-secondary:hover {
    background: #6c7b7d;
    transform: translateY(-2px);
}

.btn-full {
    width: 100%;
}

/* ===== TABLEAUX ===== */
.table-container {
    overflow-x: auto;
    margin: 20px 0;
}

.table {
    width: 100%;
    border-collapse: collapse;
    background: var(--white);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow);
}

.table th,
.table td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid var(--light-gray);
}

.table th {
    background: var(--primary-color);
    color: var(--white);
    font-weight: 600;
    text-transform: uppercase;
    font-size: 14px;
    letter-spacing: 1px;
}

.table tr:hover {
    background: #f8f9fa;
}

.table tr:last-child td {
    border-bottom: none;
}

/* ===== ALERTES ===== */
.alert {
    padding: 15px 20px;
    border-radius: var(--border-radius);
    margin: 20px 0;
    border-left: 4px solid;
    font-weight: 500;
}

.alert-success {
    background: #d4edda;
    color: #155724;
    border-left-color: var(--success-color);
}

.alert-error {
    background: #f8d7da;
    color: #721c24;
    border-left-color: var(--accent-color);
}

.alert-info {
    background: #d1ecf1;
    color: #0c5460;
    border-left-color: var(--secondary-color);
}

.alert-warning {
    background: #fff3cd;
    color: #856404;
    border-left-color: var(--warning-color);
}

/* ===== CARTES ===== */
.card {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 25px;
    margin: 20px 0;
    transition: var(--transition);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.card-header {
    border-bottom: 2px solid var(--light-gray);
    padding-bottom: 15px;
    margin-bottom: 20px;
}

.card-title {
    margin: 0;
    color: var(--primary-color);
    font-size: 1.5rem;
}

/* ===== GRILLE ===== */
.row {
    display: flex;
    flex-wrap: wrap;
    margin: -10px;
}

.col {
    flex: 1;
    padding: 10px;
}

.col-2 {
    flex: 0 0 50%;
    padding: 10px;
}

.col-3 {
    flex: 0 0 33.333%;
    padding: 10px;
}

.col-4 {
    flex: 0 0 25%;
    padding: 10px;
}

/* ===== UTILITAIRES ===== */
.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.mt-20 {
    margin-top: 20px;
}

.mb-20 {
    margin-bottom: 20px;
}

.p-20 {
    padding: 20px;
}

.promotion {
    color: var(--success-color);
    font-weight: bold;
}

.price-original {
    text-decoration: line-through;
    color: var(--dark-gray);
}

.price-discount {
    color: var(--accent-color);
    font-weight: bold;
}

.price-current {
    font-size: 1.2rem;
    font-weight: bold;
    color: var(--primary-color);
}

/* ===== BADGES ===== */
.badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-success {
    background: var(--success-color);
    color: var(--white);
}

.badge-warning {
    background: var(--warning-color);
    color: var(--white);
}

.badge-danger {
    background: var(--accent-color);
    color: var(--white);
}

.badge-info {
    background: var(--secondary-color);
    color: var(--white);
}

/* ===== TAGS DE PROMOTION ===== */
.promotion-tag {
    display: inline-block;
    background: linear-gradient(45deg, var(--success-color), #2ecc71);
    color: var(--white);
    padding: 4px 8px;
    border-radius: 15px;
    font-size: 11px;
    margin: 2px;
    font-weight: 600;
}

.promotion-tag i {
    margin-right: 3px;
}

/* ===== TEXTE MUTED ===== */
.text-muted {
    color: var(--dark-gray);
    font-size: 0.9rem;
}

/* ===== ANIMATIONS ===== */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card {
    animation: fadeIn 0.5s ease-out;
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background: #f8f9fa;
    transform: scale(1.01);
}

/* ===== AMÉLIORATIONS FORMULAIRE ===== */
.form-control:focus {
    transform: translateY(-2px);
}

.btn:active {
    transform: translateY(0) scale(0.98);
}

/* ===== LOADER ===== */
.loader {
    border: 3px solid var(--light-gray);
    border-top: 3px solid var(--secondary-color);
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
    margin: 20px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== STYLES SPÉCIAUX POUR SÉLECTION ET CONFIRMATION ===== */

/* Route de voyage */
.travel-route {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: var(--border-radius);
    margin: 15px 0;
}

.station-info {
    text-align: center;
    flex: 1;
}

.station-info i {
    font-size: 1.5rem;
    color: var(--secondary-color);
    margin-bottom: 5px;
    display: block;
}

.route-arrow {
    margin: 0 20px;
    font-size: 1.5rem;
    color: var(--secondary-color);
}

.travel-details p {
    margin: 8px 0;
    padding: 5px 0;
    border-bottom: 1px solid var(--light-gray);
}

.travel-details p:last-child {
    border-bottom: none;
}

/* Grille de préférences */
.preferences-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 10px;
}

.preference-item {
    display: flex;
    align-items: center;
    padding: 10px;
    background: var(--light-gray);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.preference-item:hover {
    background: #ddd;
}

.preference-item input[type="checkbox"] {
    margin-right: 10px;
    transform: scale(1.2);
}

.preference-item label {
    margin: 0;
    cursor: pointer;
    font-weight: 500;
}

.preference-item label i {
    margin-right: 5px;
    color: var(--secondary-color);
}

/* Tags de préférences dans confirmation */
.preferences-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 10px;
}

.preference-tag {
    display: inline-block;
    background: linear-gradient(45deg, var(--secondary-color), #5dade2);
    color: var(--white);
    padding: 4px 10px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
}

.preference-tag i {
    margin-right: 3px;
}

/* Détails de réservation */
.reservation-details p {
    margin: 12px 0;
    padding: 8px 0;
    border-bottom: 1px solid var(--light-gray);
}

.reservation-details p:last-child {
    border-bottom: none;
}

/* Animations pour les pages de confirmation */
@keyframes successPulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(39, 174, 96, 0.7);
    }
    70% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(39, 174, 96, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(39, 174, 96, 0);
    }
}

.alert-success {
    animation: successPulse 2s ease-out;
}

/* Styles d'impression */
@media print {
    .navbar,
    .btn,
    .nav-links {
        display: none !important;
    }

    .main-content {
        box-shadow: none;
        padding: 0;
    }

    .card {
        box-shadow: none;
        border: 1px solid #ddd;
        page-break-inside: avoid;
    }

    .alert {
        border: 1px solid #ddd;
        box-shadow: none;
    }
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    .main-content {
        padding: 20px;
    }

    .navbar .container {
        flex-direction: column;
        gap: 15px;
    }

    .navbar .nav-links {
        flex-wrap: wrap;
        justify-content: center;
    }

    .row {
        flex-direction: column;
    }

    .col,
    .col-2,
    .col-3,
    .col-4 {
        flex: 1 1 100%;
    }

    h1 {
        font-size: 2rem;
    }

    .form-container {
        padding: 20px;
    }
}
