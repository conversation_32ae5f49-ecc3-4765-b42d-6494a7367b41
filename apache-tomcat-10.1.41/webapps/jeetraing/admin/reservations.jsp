<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>👨‍💼 Gestion des Réservations - Admin JEE Training</title>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation Admin -->
    <nav class="navbar">
        <div class="container">
            <a href="${pageContext.request.contextPath}/admin/dashboard" class="logo">
                <i class="fas fa-train"></i> JEE Training Admin
            </a>
            <div class="nav-links">
                <a href="${pageContext.request.contextPath}/admin/dashboard"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                <a href="${pageContext.request.contextPath}/admin/users"><i class="fas fa-users"></i> Utilisateurs</a>
                <a href="${pageContext.request.contextPath}/admin/reservations" class="active"><i class="fas fa-ticket-alt"></i> Réservations</a>
                <a href="${pageContext.request.contextPath}/logout"><i class="fas fa-sign-out-alt"></i> Déconnexion</a>
            </div>
        </div>
    </nav>

    <!-- Contenu principal -->
    <div class="container">
        <div class="main-content">
            <!-- En-tête -->
            <div class="admin-header">
                <h1><i class="fas fa-ticket-alt"></i> Gestion des Réservations</h1>
                <p>Consultez et gérez toutes les réservations du système</p>
            </div>

            <!-- Messages -->
            <c:if test="${not empty success}">
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> ${success}
                </div>
                <c:remove var="success" scope="session"/>
            </c:if>

            <c:if test="${not empty error}">
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-triangle"></i> ${error}
                </div>
                <c:remove var="error" scope="session"/>
            </c:if>

            <!-- Statistiques -->
            <div class="row">
                <div class="col-4">
                    <div class="admin-card">
                        <div class="admin-card-icon">
                            <i class="fas fa-ticket-alt"></i>
                        </div>
                        <div class="admin-card-content">
                            <h3>${totalReservations}</h3>
                            <p>Total Réservations</p>
                        </div>
                    </div>
                </div>
                <div class="col-4">
                    <div class="admin-card">
                        <div class="admin-card-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="admin-card-content">
                            <h3>${confirmedReservations}</h3>
                            <p>Confirmées</p>
                        </div>
                    </div>
                </div>
                <div class="col-4">
                    <div class="admin-card">
                        <div class="admin-card-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="admin-card-content">
                            <h3>${pendingReservations}</h3>
                            <p>En Attente</p>
                        </div>
                    </div>
                </div>
                <div class="col-4">
                    <div class="admin-card">
                        <div class="admin-card-icon">
                            <i class="fas fa-ban"></i>
                        </div>
                        <div class="admin-card-content">
                            <h3>${cancelledReservations}</h3>
                            <p>Annulées</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Chiffre d'affaires -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title"><i class="fas fa-euro-sign"></i> Chiffre d'Affaires</h2>
                </div>
                <div class="revenue-display">
                    <div class="revenue-amount">
                        <fmt:formatNumber value="${totalRevenue}" type="currency" currencySymbol="€" />
                    </div>
                    <p>Revenus générés par les réservations confirmées</p>
                </div>
            </div>

            <!-- Tableau des réservations -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title"><i class="fas fa-list"></i> Toutes les Réservations</h2>
                </div>

                <c:if test="${not empty allReservations}">
                    <div class="table-responsive">
                        <table class="admin-table">
                            <thead>
                                <tr>
                                    <th><i class="fas fa-hashtag"></i> ID</th>
                                    <th><i class="fas fa-user"></i> Utilisateur</th>
                                    <th><i class="fas fa-route"></i> Trajet</th>
                                    <th><i class="fas fa-calendar"></i> Date Voyage</th>
                                    <th><i class="fas fa-star"></i> Classe</th>
                                    <th><i class="fas fa-euro-sign"></i> Prix</th>
                                    <th><i class="fas fa-info-circle"></i> État</th>
                                    <th><i class="fas fa-calendar-plus"></i> Créée le</th>
                                    <th><i class="fas fa-cogs"></i> Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <c:forEach var="reservation" items="${allReservations}">
                                    <tr>
                                        <td><strong>#${reservation.id}</strong></td>
                                        <td>
                                            <div class="user-info">
                                                <i class="fas fa-user"></i>
                                                <span>${reservation.user.username}</span>
                                                <br>
                                                <small>${reservation.user.email}</small>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="route-info">
                                                <div class="departure">
                                                    <i class="fas fa-play"></i>
                                                    ${reservation.voyage.trajet.departStation.name}
                                                </div>
                                                <div class="arrival">
                                                    <i class="fas fa-stop"></i>
                                                    ${reservation.voyage.trajet.arrivalStation.name}
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="date-info">
                                                <i class="fas fa-calendar"></i>
                                                <fmt:formatDate value="${reservation.voyage.dateDepart}" pattern="dd/MM/yyyy" />
                                                <br>
                                                <small>
                                                    <i class="fas fa-clock"></i>
                                                    ${reservation.voyage.heureDepartFormatted}
                                                </small>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="classe-badge classe-${reservation.classe}">
                                                <c:choose>
                                                    <c:when test="${reservation.classe == 'Economique'}">💺</c:when>
                                                    <c:when test="${reservation.classe == 'Deuxieme'}">🥈</c:when>
                                                    <c:when test="${reservation.classe == 'Premiere'}">🥇</c:when>
                                                    <c:otherwise>🎫</c:otherwise>
                                                </c:choose>
                                                ${reservation.classe}
                                            </span>
                                        </td>
                                        <td>
                                            <div class="price-info">
                                                <strong><fmt:formatNumber value="${reservation.voyage.prix}" type="currency" currencySymbol="€" /></strong>
                                            </div>
                                        </td>
                                        <td>
                                            <c:choose>
                                                <c:when test="${reservation.etat == 'confirmée'}">
                                                    <span class="badge badge-success">
                                                        <i class="fas fa-check-circle"></i> Confirmée
                                                    </span>
                                                </c:when>
                                                <c:when test="${reservation.etat == 'acheté'}">
                                                    <span class="badge badge-primary">
                                                        <i class="fas fa-shopping-cart"></i> Acheté
                                                    </span>
                                                </c:when>
                                                <c:when test="${reservation.etat == 'en attente'}">
                                                    <span class="badge badge-warning">
                                                        <i class="fas fa-clock"></i> En Attente
                                                    </span>
                                                </c:when>
                                                <c:when test="${reservation.etat == 'annulée'}">
                                                    <span class="badge badge-danger">
                                                        <i class="fas fa-ban"></i> Annulée
                                                    </span>
                                                </c:when>
                                                <c:when test="${reservation.etat == 'utilisé'}">
                                                    <span class="badge badge-info">
                                                        <i class="fas fa-check"></i> Utilisé
                                                    </span>
                                                </c:when>
                                                <c:otherwise>
                                                    <span class="badge badge-secondary">
                                                        <i class="fas fa-question"></i> ${reservation.etat}
                                                    </span>
                                                </c:otherwise>
                                            </c:choose>
                                        </td>
                                        <td>
                                            <div class="date-info">
                                                <fmt:formatDate value="${reservation.dateCreation}" pattern="dd/MM/yyyy" />
                                                <br>
                                                <small>
                                                    <fmt:formatDate value="${reservation.dateCreation}" pattern="HH:mm" />
                                                </small>
                                            </div>
                                        </td>
                                        <td>
                                            <!-- Actions selon l'état -->
                                            <c:if test="${reservation.etat == 'en attente'}">
                                                <form method="post" style="display: inline;">
                                                    <input type="hidden" name="action" value="confirm">
                                                    <input type="hidden" name="reservationId" value="${reservation.id}">
                                                    <button type="submit" class="admin-btn admin-btn-success admin-btn-sm"
                                                            onclick="return confirm('Confirmer cette réservation ?');">
                                                        <i class="fas fa-check"></i> Confirmer
                                                    </button>
                                                </form>
                                            </c:if>
                                            
                                            <c:if test="${reservation.etat != 'annulée' && reservation.etat != 'utilisé'}">
                                                <form method="post" style="display: inline;">
                                                    <input type="hidden" name="action" value="cancel">
                                                    <input type="hidden" name="reservationId" value="${reservation.id}">
                                                    <button type="submit" class="admin-btn admin-btn-warning admin-btn-sm"
                                                            onclick="return confirm('Annuler cette réservation ?');">
                                                        <i class="fas fa-times"></i> Annuler
                                                    </button>
                                                </form>
                                            </c:if>
                                            
                                            <form method="post" style="display: inline;">
                                                <input type="hidden" name="action" value="delete">
                                                <input type="hidden" name="reservationId" value="${reservation.id}">
                                                <button type="submit" class="admin-btn admin-btn-danger admin-btn-sm"
                                                        onclick="return confirm('ATTENTION : Supprimer définitivement cette réservation ?');">
                                                    <i class="fas fa-trash"></i> Supprimer
                                                </button>
                                            </form>
                                        </td>
                                    </tr>
                                </c:forEach>
                            </tbody>
                        </table>
                    </div>
                </c:if>

                <c:if test="${empty allReservations}">
                    <div class="empty-state">
                        <i class="fas fa-ticket-alt"></i>
                        <h3>Aucune réservation trouvée</h3>
                        <p>Il n'y a actuellement aucune réservation dans le système.</p>
                    </div>
                </c:if>
            </div>

            <!-- Actions rapides -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title"><i class="fas fa-bolt"></i> Actions Rapides</h2>
                </div>
                
                <div class="row">
                    <div class="col-4">
                        <a href="${pageContext.request.contextPath}/admin/dashboard" class="btn btn-primary btn-full">
                            <i class="fas fa-tachometer-alt"></i> Retour Dashboard
                        </a>
                    </div>
                    <div class="col-4">
                        <a href="${pageContext.request.contextPath}/admin/users" class="btn btn-secondary btn-full">
                            <i class="fas fa-users"></i> Gérer Utilisateurs
                        </a>
                    </div>
                    <div class="col-4">
                        <a href="${pageContext.request.contextPath}/admin/voyages" class="btn btn-success btn-full">
                            <i class="fas fa-train"></i> Gérer Voyages
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        .revenue-display {
            text-align: center;
            padding: 20px;
        }
        
        .revenue-amount {
            font-size: 2.5em;
            font-weight: 700;
            color: var(--success-color);
            margin-bottom: 10px;
        }
        
        .user-info {
            display: flex;
            flex-direction: column;
            gap: 3px;
        }
        
        .user-info i {
            color: var(--primary-color);
            margin-right: 5px;
        }
        
        .date-info {
            display: flex;
            flex-direction: column;
            gap: 3px;
            font-size: 13px;
        }
        
        .date-info i {
            color: var(--primary-color);
            margin-right: 5px;
        }
        
        .price-info {
            text-align: center;
            font-size: 14px;
        }
        
        .admin-table td form {
            margin: 2px;
        }
        
        .admin-table td .admin-btn {
            margin: 1px;
        }
    </style>
</body>
</html>
