<!DOCTYPE hibernate-configuration PUBLIC
	"-//Hibernate/Hibernate Configuration DTD 3.0//EN"
	"http://www.hibernate.org/dtd/hibernate-configuration-3.0.dtd">
<hibernate-configuration>
<!-- Configuration des parametres de connexion a la base de donnees -->
<session-factory>
<!-- Configuration de la base de donnees MySQL -->
<property name="hibernate.connection.driver_class">com.mysql.cj.jdbc.Driver</property>
<property name="hibernate.connection.url">***********************************************************************************************************</property>
<property name="hibernate.connection.username">root</property>
<property name="hibernate.connection.password"></property>

<!-- Dialecte pour MySQL 8.x -->
<property name="hibernate.dialect">org.hibernate.dialect.MySQLDialect</property>

<!-- Configuration du pool de connexions -->
<property name="hibernate.connection.pool_size">10</property>

<!-- Activation de la generation automatique du schema -->
<property name="hibernate.hbm2ddl.auto">update</property>

<!-- Configuration des logs SQL -->
<property name="hibernate.show_sql">false</property>
<property name="hibernate.format_sql">true</property>

<!-- Configuration de l'encodage -->
<property name="hibernate.connection.characterEncoding">utf8</property>
<property name="hibernate.connection.useUnicode">true</property>

<!-- Entites a scanner pour Hibernate -->
<mapping class="model.User"/>
<mapping class="model.Trajet"/>
<mapping class="model.Station"/>
<mapping class="model.Voyage"/>
<mapping class="model.Reservation"/>
<mapping class="model.Promotion"/>

</session-factory>
</hibernate-configuration>