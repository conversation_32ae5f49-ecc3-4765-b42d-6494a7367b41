<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Inscription</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            max-width: 500px;
            margin-left: auto;
            margin-right: auto;
        }
        .form-group {
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        label {
            flex: 0 0 120px;
            margin-right: 10px;
        }
        input {
            flex: 1;
            padding: 5px;
            box-sizing: border-box;
        }
        button {
            padding: 8px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        .error {
            color: red;
            margin-bottom: 15px;
            font-size: 14px;
        }
        a {
            color: #007BFF;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <h2>Inscription</h2>
    <c:if test="${not empty error}">
        <p class="error" aria-live="polite">${error}</p>
    </c:if>
    <form action="register" method="post">
        <div class="form-group">
            <label for="username">Nom d'utilisateur :</label>
            <input type="text" id="username" name="username" required aria-describedby="username-error">
        </div>
        <div class="form-group">
            <label for="password">Mot de passe :</label>
            <input type="password" id="password" name="password" required aria-describedby="password-error">
        </div>
        <div class="form-group">
            <label for="email">Email :</label>
            <input type="email" id="email" name="email" required 
                   pattern="^[A-Za-z0-9+_.-]+@(.+)$" 
                   title="Veuillez entrer un email valide (<EMAIL>)" 
                   aria-describedby="email-error">
        </div>
        <button type="submit">S'inscrire</button>
    </form>
    <p><a href="login">Déjà inscrit ? Connectez-vous</a></p>
</body>
</html>