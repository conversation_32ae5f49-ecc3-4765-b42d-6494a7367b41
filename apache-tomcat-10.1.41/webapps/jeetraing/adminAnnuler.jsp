<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>❌ Demandes d'Annulation - JEE Training</title>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <a href="${pageContext.request.contextPath}/recherche" class="logo">
                <i class="fas fa-train"></i> JEE Training
            </a>
            <div class="nav-links">
                <c:choose>
                    <c:when test="${sessionScope.user != null}">
                        <a href="${pageContext.request.contextPath}/recherche"><i class="fas fa-search"></i> Rechercher</a>
                        <c:if test="${sessionScope.user.role == 'admin'}">
                            <a href="${pageContext.request.contextPath}/admin/dashboard"><i class="fas fa-cog"></i> Admin</a>
                        </c:if>
                        <a href="${pageContext.request.contextPath}/logout"><i class="fas fa-sign-out-alt"></i> Déconnexion</a>
                    </c:when>
                    <c:otherwise>
                        <a href="${pageContext.request.contextPath}/login.jsp"><i class="fas fa-sign-in-alt"></i> Connexion</a>
                        <a href="${pageContext.request.contextPath}/register.jsp"><i class="fas fa-user-plus"></i> Inscription</a>
                    </c:otherwise>
                </c:choose>
            </div>
        </div>
    </nav>

    <!-- Contenu principal -->
    <div class="container">
        <div class="main-content">
            <h1><i class="fas fa-times-circle"></i> Demandes d'Annulation</h1>

            <!-- Breadcrumb -->
            <div class="breadcrumb">
                <a href="${pageContext.request.contextPath}/admin/dashboard"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                <span><i class="fas fa-chevron-right"></i></span>
                <span>Demandes d'Annulation</span>
            </div>

            <!-- Messages -->
            <c:if test="${not empty success}">
                <div class="admin-message admin-message-success">
                    <i class="fas fa-check-circle"></i> ${success}
                </div>
            </c:if>
            <c:if test="${not empty error}">
                <div class="admin-message admin-message-error">
                    <i class="fas fa-exclamation-triangle"></i> ${error}
                </div>
            </c:if>
            <!-- Liste des demandes d'annulation -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title"><i class="fas fa-list"></i> Demandes en Attente</h2>
                </div>

                <c:if test="${not empty reservations}">
                    <table class="admin-table">
                        <thead>
                            <tr>
                                <th><i class="fas fa-hashtag"></i> ID</th>
                                <th><i class="fas fa-user"></i> Utilisateur</th>
                                <th><i class="fas fa-route"></i> Trajet</th>
                                <th><i class="fas fa-calendar-alt"></i> Voyage</th>
                                <th><i class="fas fa-cogs"></i> Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <c:forEach var="reservation" items="${reservations}">
                                <tr>
                                    <td><strong>#${reservation.id}</strong></td>
                                    <td>
                                        <div class="user-info">
                                            <i class="fas fa-envelope"></i>
                                            <span>${reservation.user.email}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="route-info">
                                            <div class="departure">
                                                <i class="fas fa-play"></i> ${reservation.voyage.trajet.departStation.name}
                                                <small>(${reservation.voyage.trajet.departStation.city})</small>
                                            </div>
                                            <div class="arrival">
                                                <i class="fas fa-stop"></i> ${reservation.voyage.trajet.arrivalStation.name}
                                                <small>(${reservation.voyage.trajet.arrivalStation.city})</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="voyage-details">
                                            <div class="time-info">
                                                <i class="fas fa-clock"></i> ${reservation.voyage.heureDepartFormatted}
                                            </div>
                                            <span class="classe-badge classe-${reservation.classe}">
                                                <i class="fas fa-star"></i> ${reservation.classe}
                                            </span>
                                        </div>
                                    </td>
                                    <td>
                                        <form action="${pageContext.request.contextPath}/admin/annuler" method="post" style="display:inline;">
                                            <input type="hidden" name="id" value="${reservation.id}">
                                            <input type="hidden" name="action" value="confirmer">
                                            <button type="submit" class="admin-btn admin-btn-success"
                                                    onclick="return confirm('Confirmer l\'annulation de cette réservation ?');">
                                                <i class="fas fa-check"></i> Confirmer
                                            </button>
                                        </form>
                                        <form action="${pageContext.request.contextPath}/admin/annuler" method="post" style="display:inline;">
                                            <input type="hidden" name="id" value="${reservation.id}">
                                            <input type="hidden" name="action" value="rejeter">
                                            <button type="submit" class="admin-btn admin-btn-danger"
                                                    onclick="return confirm('Rejeter cette demande d\'annulation ?');">
                                                <i class="fas fa-times"></i> Rejeter
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                            </c:forEach>
                        </tbody>
                    </table>
                </c:if>

                <c:if test="${empty reservations}">
                    <div class="empty-state">
                        <i class="fas fa-check-circle"></i>
                        <h3>Aucune demande en attente</h3>
                        <p>Toutes les demandes d'annulation ont été traitées.</p>
                    </div>
                </c:if>
            </div>

            <!-- Actions rapides -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title"><i class="fas fa-bolt"></i> Actions Rapides</h2>
                </div>

                <div class="row">
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/admin/dashboard" class="btn btn-secondary btn-full">
                            <i class="fas fa-arrow-left"></i> Retour Dashboard
                        </a>
                    </div>
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/admin/annuler" class="btn btn-primary btn-full">
                            <i class="fas fa-sync"></i> Actualiser
                        </a>
                    </div>
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/admin/users" class="btn btn-warning btn-full">
                            <i class="fas fa-users"></i> Gérer Utilisateurs
                        </a>
                    </div>
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/admin/reservation-evolution" class="btn btn-success btn-full">
                            <i class="fas fa-chart-line"></i> Évolution Réservations
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>