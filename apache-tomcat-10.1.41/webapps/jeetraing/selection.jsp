<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎫 Sélection du Voyage - JEE Training</title>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <a href="${pageContext.request.contextPath}/recherche" class="logo">
                <i class="fas fa-train"></i> JEE Training
            </a>
            <div class="nav-links">
                <c:choose>
                    <c:when test="${sessionScope.user != null}">
                        <c:if test="${sessionScope.user.role == 'user'}">
                            <a href="${pageContext.request.contextPath}/dashboard"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                        </c:if>
                        <c:if test="${sessionScope.user.role == 'admin'}">
                            <a href="${pageContext.request.contextPath}/admin/promotions"><i class="fas fa-cog"></i> Admin</a>
                        </c:if>
                        <a href="${pageContext.request.contextPath}/logout"><i class="fas fa-sign-out-alt"></i> Déconnexion</a>
                    </c:when>
                    <c:otherwise>
                        <a href="${pageContext.request.contextPath}/login.jsp"><i class="fas fa-sign-in-alt"></i> Connexion</a>
                        <a href="${pageContext.request.contextPath}/register.jsp"><i class="fas fa-user-plus"></i> Inscription</a>
                    </c:otherwise>
                </c:choose>
            </div>
        </div>
    </nav>

    <!-- Contenu principal -->
    <div class="container">
        <div class="main-content">
            <h1><i class="fas fa-ticket-alt"></i> Sélection du Voyage</h1>

            <c:if test="${not empty voyage}">
                <!-- Détails du voyage -->
                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title"><i class="fas fa-info-circle"></i> Détails du Voyage Sélectionné</h2>
                    </div>

                    <div class="row">
                        <div class="col-2">
                            <div class="card">
                                <h3><i class="fas fa-route"></i> Itinéraire</h3>
                                <div class="travel-route">
                                    <div class="station-info">
                                        <i class="fas fa-map-marker-alt"></i>
                                        <strong>${voyage.trajet.departStation.name}</strong><br>
                                        <small class="text-muted">${voyage.trajet.departStation.city}</small>
                                    </div>
                                    <div class="route-arrow">
                                        <i class="fas fa-arrow-right"></i>
                                    </div>
                                    <div class="station-info">
                                        <i class="fas fa-flag-checkered"></i>
                                        <strong>${voyage.trajet.arrivalStation.name}</strong><br>
                                        <small class="text-muted">${voyage.trajet.arrivalStation.city}</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-2">
                            <div class="card">
                                <h3><i class="fas fa-clock"></i> Horaires & Prix</h3>
                                <div class="travel-details">
                                    <p><i class="fas fa-play"></i> <strong>Départ :</strong> ${voyage.heureDepartFormatted}</p>
                                    <p><i class="fas fa-stop"></i> <strong>Arrivée :</strong> ${voyage.heureArriveeFormatted}</p>
                                    <p><i class="fas fa-hourglass-half"></i> <strong>Durée :</strong> ${voyage.duree}</p>
                                    <p><i class="fas fa-euro-sign"></i> <strong>Prix :</strong> <span class="price-current">${voyage.prix} €</span></p>
                                    <p><i class="fas fa-users"></i> <strong>Places :</strong>
                                        <span class="badge badge-success">${voyage.placesDisponibles} disponibles</span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Formulaire de réservation -->
                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title"><i class="fas fa-cogs"></i> Options de Réservation</h2>
                    </div>

                    <form action="selection" method="post">
                        <div class="row">
                            <div class="col-2">
                                <div class="form-group">
                                    <label for="classe"><i class="fas fa-star"></i> Classe de voyage</label>
                                    <select id="classe" name="classe" class="form-control" required>
                                        <option value="">Sélectionnez une classe</option>
                                        <option value="Economique">💺 Économique</option>
                                        <option value="Deuxieme">🥈 2ème classe</option>
                                        <option value="Premiere">🥇 1ère classe</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-2">
                                <div class="form-group">
                                    <label for="nextTrajetId"><i class="fas fa-plus-circle"></i> Trajet supplémentaire</label>
                                    <select id="nextTrajetId" name="nextTrajetId" class="form-control">
                                        <option value="">Aucun trajet supplémentaire</option>
                                        <c:forEach var="nextTrajet" items="${nextTrajets}">
                                            <option value="${nextTrajet.id}">
                                                ${nextTrajet.departStation.name} → ${nextTrajet.arrivalStation.name}
                                            </option>
                                        </c:forEach>
                                    </select>
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle"></i>
                                        Continuez votre voyage depuis ${voyage.trajet.arrivalStation.city}
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label><i class="fas fa-heart"></i> Préférences de voyage</label>
                            <div class="preferences-grid">
                                <div class="preference-item">
                                    <input type="checkbox" id="fenetre" name="preferences" value="Fenêtre">
                                    <label for="fenetre"><i class="fas fa-window-maximize"></i> Place côté fenêtre</label>
                                </div>
                                <div class="preference-item">
                                    <input type="checkbox" id="famille" name="preferences" value="Espace famille">
                                    <label for="famille"><i class="fas fa-baby"></i> Espace famille</label>
                                </div>
                                <div class="preference-item">
                                    <input type="checkbox" id="nonfumeur" name="preferences" value="Non-fumeur">
                                    <label for="nonfumeur"><i class="fas fa-ban"></i> Wagon non-fumeur</label>
                                </div>
                            </div>
                        </div>

                        <div class="text-center mt-20">
                            <button type="submit" class="btn btn-success btn-full">
                                <i class="fas fa-check-circle"></i> Confirmer la Réservation
                            </button>
                        </div>
                    </form>
                </div>
            </c:if>

            <div class="text-center mt-20">
                <a href="${pageContext.request.contextPath}/recherche" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Retour à la recherche
                </a>
            </div>
        </div>
    </div>
</body>
</html>