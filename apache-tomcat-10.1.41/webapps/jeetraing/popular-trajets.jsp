<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Trajets Populaires - JEE Training</title>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <a href="${pageContext.request.contextPath}/recherche" class="logo">
                <i class="fas fa-train"></i> JEE Training
            </a>
            <div class="nav-links">
                <c:choose>
                    <c:when test="${sessionScope.user != null}">
                        <a href="${pageContext.request.contextPath}/recherche"><i class="fas fa-search"></i> Rechercher</a>
                        <c:if test="${sessionScope.user.role == 'admin'}">
                            <a href="${pageContext.request.contextPath}/admin/promotions"><i class="fas fa-cog"></i> Admin</a>
                        </c:if>
                        <a href="${pageContext.request.contextPath}/logout"><i class="fas fa-sign-out-alt"></i> Déconnexion</a>
                    </c:when>
                    <c:otherwise>
                        <a href="${pageContext.request.contextPath}/login.jsp"><i class="fas fa-sign-in-alt"></i> Connexion</a>
                        <a href="${pageContext.request.contextPath}/register.jsp"><i class="fas fa-user-plus"></i> Inscription</a>
                    </c:otherwise>
                </c:choose>
            </div>
        </div>
    </nav>

    <!-- Contenu principal -->
    <div class="container">
        <div class="main-content">
            <h1><i class="fas fa-route"></i> Trajets les Plus Populaires</h1>

            <!-- Breadcrumb -->
            <div class="breadcrumb">
                <a href="${pageContext.request.contextPath}/admin/dashboard"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                <span><i class="fas fa-chevron-right"></i></span>
                <span>Trajets Populaires</span>
            </div>

            <!-- Statistiques -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title"><i class="fas fa-chart-bar"></i> Analyse de Popularité</h2>
                </div>

                <c:if test="${not empty popularTrajets}">
                    <table class="admin-table">
                        <thead>
                            <tr>
                                <th><i class="fas fa-trophy"></i> Rang</th>
                                <th><i class="fas fa-route"></i> Trajet</th>
                                <th><i class="fas fa-map-marker-alt"></i> Départ</th>
                                <th><i class="fas fa-map-marker-alt"></i> Arrivée</th>
                                <th><i class="fas fa-ticket-alt"></i> Réservations</th>
                                <th><i class="fas fa-chart-line"></i> Popularité</th>
                            </tr>
                        </thead>
                        <tbody>
                            <c:set var="rank" value="1" />
                            <c:forEach var="entry" items="${popularTrajets}">
                                <tr>
                                    <td>
                                        <c:choose>
                                            <c:when test="${rank == 1}">
                                                <span class="rank-badge rank-gold"><i class="fas fa-crown"></i> #${rank}</span>
                                            </c:when>
                                            <c:when test="${rank == 2}">
                                                <span class="rank-badge rank-silver"><i class="fas fa-medal"></i> #${rank}</span>
                                            </c:when>
                                            <c:when test="${rank == 3}">
                                                <span class="rank-badge rank-bronze"><i class="fas fa-award"></i> #${rank}</span>
                                            </c:when>
                                            <c:otherwise>
                                                <span class="rank-badge"><i class="fas fa-hashtag"></i> ${rank}</span>
                                            </c:otherwise>
                                        </c:choose>
                                    </td>
                                    <td>
                                        <strong>${entry[0].departStation.name} → ${entry[0].arrivalStation.name}</strong>
                                    </td>
                                    <td>
                                        <div class="station-info">
                                            <i class="fas fa-map-marker-alt"></i>
                                            <span>${entry[0].departStation.name}</span>
                                            <small>(${entry[0].departStation.city})</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="station-info">
                                            <i class="fas fa-map-marker-alt"></i>
                                            <span>${entry[0].arrivalStation.name}</span>
                                            <small>(${entry[0].arrivalStation.city})</small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="reservation-count">
                                            <i class="fas fa-users"></i> ${entry[1]} réservations
                                        </span>
                                    </td>
                                    <td>
                                        <div class="popularity-bar">
                                            <div class="popularity-fill" style="width: ${entry[1] * 100 / popularTrajets[0][1]}%"></div>
                                            <span class="popularity-text">${Math.round(entry[1] * 100 / popularTrajets[0][1])}%</span>
                                        </div>
                                    </td>
                                </tr>
                                <c:set var="rank" value="${rank + 1}" />
                            </c:forEach>
                        </tbody>
                    </table>
                </c:if>

                <c:if test="${empty popularTrajets}">
                    <div class="empty-state">
                        <i class="fas fa-route"></i>
                        <h3>Aucune donnée disponible</h3>
                        <p>Il n'y a pas encore de données sur les trajets populaires.</p>
                    </div>
                </c:if>
            </div>

            <!-- Actions rapides -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title"><i class="fas fa-bolt"></i> Actions Rapides</h2>
                </div>

                <div class="row">
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/admin/dashboard" class="btn btn-secondary btn-full">
                            <i class="fas fa-arrow-left"></i> Retour Dashboard
                        </a>
                    </div>
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/admin/revenue" class="btn btn-primary btn-full">
                            <i class="fas fa-euro-sign"></i> Revenus
                        </a>
                    </div>
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/admin/reservation-evolution" class="btn btn-warning btn-full">
                            <i class="fas fa-chart-line"></i> Évolution
                        </a>
                    </div>
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/admin/trajets" class="btn btn-success btn-full">
                            <i class="fas fa-road"></i> Gérer Trajets
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>