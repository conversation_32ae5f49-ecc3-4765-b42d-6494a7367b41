<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Trajets les plus populaires</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>Trajets les plus populaires</h1>
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>Trajet</th>
                    <th>Nombre de réservations</th>
                </tr>
            </thead>
            <tbody>
                <c:forEach var="entry" items="${popularTrajets}">
                    <tr>
                        <td>${entry[0].departStation.name} -> ${entry[0].arrivalStation.name}</td>
                        <td>${entry[1]}</td>
                    </tr>
                </c:forEach>
            </tbody>
        </table>
        <a href="${pageContext.request.contextPath}/admin/dashboard" class="btn btn-primary">Retour au tableau de bord</a>
    </div>
</body>
</html>