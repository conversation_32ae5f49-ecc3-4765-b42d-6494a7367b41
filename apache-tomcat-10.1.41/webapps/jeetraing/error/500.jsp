<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page isErrorPage="true" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Erreur serveur - JeeTrain</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .error-container {
            text-align: center;
            color: white;
            max-width: 600px;
            padding: 2rem;
        }
        .error-code {
            font-size: 8rem;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .error-message {
            font-size: 1.5rem;
            margin-bottom: 2rem;
        }
        .btn-home {
            background: rgba(255,255,255,0.2);
            border: 2px solid white;
            color: white;
            padding: 12px 30px;
            border-radius: 50px;
            text-decoration: none;
            transition: all 0.3s ease;
            margin: 0 10px;
        }
        .btn-home:hover {
            background: white;
            color: #ff6b6b;
        }
        .error-details {
            background: rgba(0,0,0,0.2);
            padding: 1rem;
            border-radius: 10px;
            margin: 2rem 0;
            text-align: left;
            font-family: monospace;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-code">500</div>
        <div class="error-message">
            <i class="fas fa-exclamation-triangle fa-2x mb-3"></i><br>
            Erreur interne du serveur
        </div>
        <p class="mb-4">Une erreur inattendue s'est produite. Nos équipes techniques ont été notifiées.</p>
        
        <% if (exception != null) { %>
        <div class="error-details">
            <strong>Détails de l'erreur :</strong><br>
            <%= exception.getClass().getSimpleName() %>: <%= exception.getMessage() %>
        </div>
        <% } %>
        
        <div>
            <a href="${pageContext.request.contextPath}/" class="btn-home">
                <i class="fas fa-home"></i> Accueil
            </a>
            <a href="${pageContext.request.contextPath}/diagnostic" class="btn-home">
                <i class="fas fa-tools"></i> Diagnostic
            </a>
        </div>
    </div>
</body>
</html>
