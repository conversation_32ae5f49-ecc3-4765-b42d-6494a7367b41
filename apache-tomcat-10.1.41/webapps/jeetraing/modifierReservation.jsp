<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Modifier Réservation</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; max-width: 800px; margin-left: auto; margin-right: auto; }
        h2 { color: #333; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; }
        select, input[type="checkbox"] { padding: 5px; }
        button { padding: 8px 15px; background-color: #007BFF; color: white; border: none; cursor: pointer; }
        button:hover { background-color: #0056b3; }
        .success { color: green; margin-bottom: 15px; }
        a { color: #007BFF; text-decoration: none; }
        a:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <h2>Modifier votre réservation</h2>
    <c:if test="${not empty success}">
        <p class="success">${success}</p>
    </c:if>
    <c:if test="${not empty reservation}">
        <form action="modifierReservation" method="post">
            <input type="hidden" name="id" value="${reservation.id}">
            <div class="form-group">
                <label for="classe">Classe :</label>
                <select id="classe" name="classe" required>
                    <option value="Économique" ${reservation.classe == 'Économique' ? 'selected' : ''}>Économique</option>
                    <option value="2ème" ${reservation.classe == '2ème' ? 'selected' : ''}>2ème</option>
                    <option value="1ère" ${reservation.classe == '1ère' ? 'selected' : ''}>1ère</option>
                </select>
            </div>
            <div class="form-group">
                <label>Préférences :</label>
                <input type="checkbox" name="preferences" value="Fenêtre" ${reservation.preferences.contains('Fenêtre') ? 'checked' : ''}> Fenêtre<br>
                <input type="checkbox" name="preferences" value="Espace famille" ${reservation.preferences.contains('Espace famille') ? 'checked' : ''}> Espace famille<br>
                <input type="checkbox" name="preferences" value="Non-fumeur" ${reservation.preferences.contains('Non-fumeur') ? 'checked' : ''}> Non-fumeur
            </div>
            <button type="submit">Enregistrer les modifications</button>
        </form>
    </c:if>
    <p><a href="monCompte">Retour à mon compte</a></p>
</body>
</html>