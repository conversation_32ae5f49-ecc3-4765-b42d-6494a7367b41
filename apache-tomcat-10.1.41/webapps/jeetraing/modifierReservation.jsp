<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>✏️ Modifier Réservation - JEE Training</title>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <a href="${pageContext.request.contextPath}/recherche" class="logo">
                <i class="fas fa-train"></i> JEE Training
            </a>
            <div class="nav-links">
                <c:choose>
                    <c:when test="${sessionScope.user != null}">
                        <a href="${pageContext.request.contextPath}/recherche"><i class="fas fa-search"></i> Rechercher</a>
                        <c:if test="${sessionScope.user.role == 'admin'}">
                            <a href="${pageContext.request.contextPath}/admin/dashboard"><i class="fas fa-cog"></i> Admin</a>
                        </c:if>
                        <a href="${pageContext.request.contextPath}/logout"><i class="fas fa-sign-out-alt"></i> Déconnexion</a>
                    </c:when>
                    <c:otherwise>
                        <a href="${pageContext.request.contextPath}/login.jsp"><i class="fas fa-sign-in-alt"></i> Connexion</a>
                        <a href="${pageContext.request.contextPath}/register.jsp"><i class="fas fa-user-plus"></i> Inscription</a>
                    </c:otherwise>
                </c:choose>
            </div>
        </div>
    </nav>

    <!-- Contenu principal -->
    <div class="container">
        <div class="main-content">
            <h1><i class="fas fa-edit"></i> Modifier votre Réservation</h1>

            <!-- Breadcrumb -->
            <div class="breadcrumb">
                <a href="${pageContext.request.contextPath}/recherche"><i class="fas fa-search"></i> Recherche</a>
                <span><i class="fas fa-chevron-right"></i></span>
                <a href="${pageContext.request.contextPath}/mes-reservations"><i class="fas fa-ticket-alt"></i> Mes Réservations</a>
                <span><i class="fas fa-chevron-right"></i></span>
                <span>Modifier Réservation</span>
            </div>

            <!-- Messages -->
            <c:if test="${not empty success}">
                <div class="admin-message admin-message-success">
                    <i class="fas fa-check-circle"></i> ${success}
                </div>
            </c:if>
            <c:if test="${not empty error}">
                <div class="admin-message admin-message-error">
                    <i class="fas fa-exclamation-triangle"></i> ${error}
                </div>
            </c:if>

            <c:if test="${not empty reservation}">
                <!-- Détails de la réservation -->
                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title"><i class="fas fa-info-circle"></i> Détails de la Réservation #${reservation.id}</h2>
                    </div>

                    <div class="reservation-details">
                        <div class="row">
                            <div class="col-2">
                                <div class="detail-item">
                                    <label><i class="fas fa-route"></i> Trajet</label>
                                    <span class="detail-value">
                                        ${reservation.voyage.trajet.departStation.name} → ${reservation.voyage.trajet.arrivalStation.name}
                                    </span>
                                </div>
                            </div>
                            <div class="col-2">
                                <div class="detail-item">
                                    <label><i class="fas fa-clock"></i> Heure de Départ</label>
                                    <span class="detail-value">${reservation.voyage.heureDepartFormatted}</span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-2">
                                <div class="detail-item">
                                    <label><i class="fas fa-euro-sign"></i> Prix</label>
                                    <span class="detail-value price-highlight">${reservation.voyage.prix}€</span>
                                </div>
                            </div>
                            <div class="col-2">
                                <div class="detail-item">
                                    <label><i class="fas fa-info-circle"></i> État</label>
                                    <span class="badge badge-success">
                                        <i class="fas fa-check-circle"></i> ${reservation.etat}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Formulaire de modification -->
                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title"><i class="fas fa-edit"></i> Modifier les Détails</h2>
                    </div>

                    <form action="${pageContext.request.contextPath}/modifierReservation" method="post" class="admin-form">
                        <input type="hidden" name="id" value="${reservation.id}">

                        <div class="admin-form-group">
                            <label for="classe"><i class="fas fa-star"></i> Classe de Voyage</label>
                            <select id="classe" name="classe" required class="admin-form-control">
                                <option value="Économique" ${reservation.classe == 'Économique' ? 'selected' : ''}>
                                    💺 Économique - Confort standard
                                </option>
                                <option value="Business" ${reservation.classe == 'Business' ? 'selected' : ''}>
                                    🥂 Business - Confort supérieur
                                </option>
                                <option value="Première" ${reservation.classe == 'Première' ? 'selected' : ''}>
                                    👑 Première - Luxe et service premium
                                </option>
                            </select>
                            <small class="form-text">Choisissez votre niveau de confort pour le voyage</small>
                        </div>

                        <div class="admin-form-group">
                            <label><i class="fas fa-heart"></i> Préférences de Voyage</label>
                            <div class="checkbox-group">
                                <label class="checkbox-container">
                                    <input type="checkbox" name="preferences" value="Fenêtre"
                                           ${reservation.preferences.contains('Fenêtre') ? 'checked' : ''}>
                                    <span class="checkmark"></span>
                                    <i class="fas fa-window-maximize"></i> Place côté fenêtre
                                </label>

                                <label class="checkbox-container">
                                    <input type="checkbox" name="preferences" value="Espace famille"
                                           ${reservation.preferences.contains('Espace famille') ? 'checked' : ''}>
                                    <span class="checkmark"></span>
                                    <i class="fas fa-users"></i> Espace famille
                                </label>

                                <label class="checkbox-container">
                                    <input type="checkbox" name="preferences" value="Non-fumeur"
                                           ${reservation.preferences.contains('Non-fumeur') ? 'checked' : ''}>
                                    <span class="checkmark"></span>
                                    <i class="fas fa-ban"></i> Zone non-fumeur
                                </label>

                                <label class="checkbox-container">
                                    <input type="checkbox" name="preferences" value="Accès mobilité réduite"
                                           ${reservation.preferences.contains('Accès mobilité réduite') ? 'checked' : ''}>
                                    <span class="checkmark"></span>
                                    <i class="fas fa-wheelchair"></i> Accès mobilité réduite
                                </label>

                                <label class="checkbox-container">
                                    <input type="checkbox" name="preferences" value="Repas végétarien"
                                           ${reservation.preferences.contains('Repas végétarien') ? 'checked' : ''}>
                                    <span class="checkmark"></span>
                                    <i class="fas fa-leaf"></i> Repas végétarien
                                </label>
                            </div>
                            <small class="form-text">Sélectionnez vos préférences pour améliorer votre expérience de voyage</small>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save"></i> Enregistrer les Modifications
                            </button>
                            <a href="${pageContext.request.contextPath}/mes-reservations" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Retour à Mes Réservations
                            </a>
                        </div>
                    </form>
                </div>
            </c:if>

            <c:if test="${empty reservation}">
                <div class="card">
                    <div class="empty-state">
                        <i class="fas fa-exclamation-triangle"></i>
                        <h3>Réservation non trouvée</h3>
                        <p>La réservation que vous tentez de modifier n'existe pas ou n'est plus modifiable.</p>
                        <a href="${pageContext.request.contextPath}/mes-reservations" class="btn btn-primary">
                            <i class="fas fa-ticket-alt"></i> Voir Mes Réservations
                        </a>
                    </div>
                </div>
            </c:if>

            <!-- Actions rapides -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title"><i class="fas fa-bolt"></i> Actions Rapides</h2>
                </div>

                <div class="row">
                    <div class="col-4">
                        <a href="${pageContext.request.contextPath}/mes-reservations" class="btn btn-primary btn-full">
                            <i class="fas fa-ticket-alt"></i> Mes Réservations
                        </a>
                    </div>
                    <div class="col-4">
                        <a href="${pageContext.request.contextPath}/recherche" class="btn btn-secondary btn-full">
                            <i class="fas fa-search"></i> Nouvelle Recherche
                        </a>
                    </div>
                    <div class="col-4">
                        <a href="${pageContext.request.contextPath}/monCompte" class="btn btn-warning btn-full">
                            <i class="fas fa-user"></i> Mon Compte
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>