<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Annuler Réservation</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; max-width: 800px; margin-left: auto; margin-right: auto; }
        h2 { color: #333; }
        p { margin-bottom: 15px; }
        button { padding: 8px 15px; background-color: #007BFF; color: white; border: none; cursor: pointer; }
        button:hover { background-color: #0056b3; }
        .success { color: green; margin-bottom: 15px; }
        .error { color: red; margin-bottom: 15px; }
        a { color: #007BFF; text-decoration: none; }
        a:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <h2>Annuler votre réservation</h2>
    <c:if test="${not empty success}">
        <p class="success">${success}</p>
    </c:if>
    <c:if test="${not empty error}">
        <p class="error">${error}</p>
    </c:if>
    <c:if test="${not empty reservation}">
        <p>Êtes-vous sûr de vouloir annuler la réservation pour :</p>
        <p><strong>Gare Départ :</strong> ${reservation.voyage.trajet.departStation.name}</p>
        <p><strong>Gare Arrivée :</strong> ${reservation.voyage.trajet.arrivalStation.name}</p>
        <p><strong>Date :</strong> ${reservation.dateReservation}</p>
        <form action="annulerReservation" method="post">
            <input type="hidden" name="id" value="${reservation.id}">
            <button type="submit">Confirmer l'annulation</button>
        </form>
    </c:if>
    <p><a href="monCompte">Retour à mon compte</a></p>
</body>
</html>