<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Connexion</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: inline-block; width: 100px; }
        input { padding: 5px; }
        .error { color: red; }
    </style>
</head>
<body>
    <h2>Connexion</h2>
    <c:if test="${not empty error}">
        <p class="error">${error}</p>
    </c:if>
    <form action="login" method="post">
        <div class="form-group">
            <label for="email">Email :</label> <!-- Changement de username à email -->
            <input type="email" id="email" name="email" required> <!-- Changement du type et du name -->
        </div>
        <div class="form-group">
            <label for="password">Mot de passe :</label>
            <input type="password" id="password" name="password" required>
        </div>
        <button type="submit">Se connecter</button>
    </form>
    <p><a href="register">Pas encore inscrit ? Inscrivez-vous</a></p>
</body>
</html>