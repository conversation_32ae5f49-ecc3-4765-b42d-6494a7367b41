<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>✅ Confirmation de Réservation - JEE Training</title>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <a href="${pageContext.request.contextPath}/recherche" class="logo">
                <i class="fas fa-train"></i> JEE Training
            </a>
            <div class="nav-links">
                <c:choose>
                    <c:when test="${sessionScope.user != null}">
                        <a href="${pageContext.request.contextPath}/recherche"><i class="fas fa-search"></i> Rechercher</a>
                        <c:if test="${sessionScope.user.role == 'admin'}">
                            <a href="${pageContext.request.contextPath}/admin/promotions"><i class="fas fa-cog"></i> Admin</a>
                        </c:if>
                        <a href="${pageContext.request.contextPath}/logout"><i class="fas fa-sign-out-alt"></i> Déconnexion</a>
                    </c:when>
                    <c:otherwise>
                        <a href="${pageContext.request.contextPath}/login.jsp"><i class="fas fa-sign-in-alt"></i> Connexion</a>
                        <a href="${pageContext.request.contextPath}/register.jsp"><i class="fas fa-user-plus"></i> Inscription</a>
                    </c:otherwise>
                </c:choose>
            </div>
        </div>
    </nav>

    <!-- Contenu principal -->
    <div class="container">
        <div class="main-content">
            <h1><i class="fas fa-check-circle"></i> Confirmation de Réservation</h1>

            <!-- Messages de succès -->
            <c:if test="${not empty success}">
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <strong>Félicitations !</strong> ${success}
                    <br><small><i class="fas fa-ticket-alt"></i> ID de réservation : <strong>${reservationId}</strong></small>
                </div>
            </c:if>

            <!-- Messages d'erreur -->
            <c:if test="${not empty error}">
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-triangle"></i> ${error}
                </div>
            </c:if>

            <c:if test="${not empty voyage}">
                <!-- Récapitulatif de la réservation -->
                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title"><i class="fas fa-receipt"></i> Récapitulatif de votre Réservation</h2>
                    </div>

                    <div class="row">
                        <div class="col-2">
                            <!-- Détails du voyage -->
                            <div class="card">
                                <h3><i class="fas fa-route"></i> Itinéraire</h3>
                                <div class="travel-route">
                                    <div class="station-info">
                                        <i class="fas fa-map-marker-alt"></i>
                                        <strong>${voyage.trajet.departStation.name}</strong><br>
                                        <small class="text-muted">${voyage.trajet.departStation.city}</small>
                                    </div>
                                    <div class="route-arrow">
                                        <i class="fas fa-arrow-right"></i>
                                    </div>
                                    <div class="station-info">
                                        <i class="fas fa-flag-checkered"></i>
                                        <strong>${voyage.trajet.arrivalStation.name}</strong><br>
                                        <small class="text-muted">${voyage.trajet.arrivalStation.city}</small>
                                    </div>
                                </div>

                                <div class="travel-details mt-20">
                                    <p><i class="fas fa-play"></i> <strong>Départ :</strong> ${voyage.heureDepartFormatted}</p>
                                    <p><i class="fas fa-stop"></i> <strong>Arrivée :</strong> ${voyage.heureArriveeFormatted}</p>
                                    <p><i class="fas fa-hourglass-half"></i> <strong>Durée :</strong> ${voyage.duree}</p>
                                    <p><i class="fas fa-euro-sign"></i> <strong>Prix :</strong> <span class="price-current">${voyage.prix} €</span></p>
                                </div>
                            </div>
                        </div>

                        <div class="col-2">
                            <!-- Détails de la réservation -->
                            <div class="card">
                                <h3><i class="fas fa-user-check"></i> Détails de la Réservation</h3>

                                <div class="reservation-details">
                                    <p><i class="fas fa-star"></i> <strong>Classe :</strong>
                                        <span class="badge badge-info">${classe}</span>
                                    </p>

                                    <p><i class="fas fa-info-circle"></i> <strong>État :</strong>
                                        <span class="badge badge-success">${etat}</span>
                                    </p>

                                    <p><i class="fas fa-heart"></i> <strong>Préférences :</strong></p>
                                    <div class="preferences-list">
                                        <c:choose>
                                            <c:when test="${not empty preferences}">
                                                <c:forEach var="pref" items="${preferences}">
                                                    <span class="preference-tag">
                                                        <i class="fas fa-check"></i> ${pref}
                                                    </span>
                                                </c:forEach>
                                            </c:when>
                                            <c:otherwise>
                                                <span class="text-muted">Aucune préférence spécifiée</span>
                                            </c:otherwise>
                                        </c:choose>
                                    </div>

                                    <p><i class="fas fa-coins"></i> <strong>Points de fidélité :</strong>
                                        <span class="badge badge-warning">${loyaltyPoints} points</span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Trajet supplémentaire -->
                <c:if test="${not empty nextTrajetId}">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title"><i class="fas fa-plus-circle"></i> Trajet Supplémentaire</h3>
                        </div>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            Vous avez également réservé un trajet supplémentaire (ID: ${nextTrajetId}).
                            <br>
                            <a href="recherche?villeDepart=${voyage.trajet.arrivalStation.city}" class="btn btn-secondary mt-20">
                                <i class="fas fa-search"></i> Rechercher d'autres voyages depuis ${voyage.trajet.arrivalStation.city}
                            </a>
                        </div>
                    </div>
                </c:if>
            </c:if>

            <!-- Actions -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title"><i class="fas fa-compass"></i> Que souhaitez-vous faire maintenant ?</h3>
                </div>

                <div class="row">
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/recherche" class="btn btn-primary btn-full">
                            <i class="fas fa-search"></i> Nouvelle Recherche
                        </a>
                    </div>
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/selection?voyageId=1" class="btn btn-warning btn-full">
                            <i class="fas fa-ticket-alt"></i> Nouvelle Sélection
                        </a>
                    </div>
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/demo-styles.jsp" class="btn btn-success btn-full">
                            <i class="fas fa-home"></i> Accueil
                        </a>
                    </div>
                    <div class="col-3">
                        <button onclick="window.print()" class="btn btn-warning btn-full">
                            <i class="fas fa-print"></i> Imprimer
                        </button>
                    </div>
                </div>
            </div>

            <!-- Message de remerciement -->
            <div class="text-center mt-20">
                <div class="alert alert-success">
                    <i class="fas fa-heart"></i>
                    <strong>Merci de votre confiance !</strong> Bon voyage avec JEE Training !
                </div>
            </div>
        </div>
    </div>
</body>
</html>