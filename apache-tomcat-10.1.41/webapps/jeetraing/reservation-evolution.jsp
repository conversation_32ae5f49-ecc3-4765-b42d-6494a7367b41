<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📈 Évolution des Réservations - JEE Training</title>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.min.js"></script>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <a href="${pageContext.request.contextPath}/recherche" class="logo">
                <i class="fas fa-train"></i> JEE Training
            </a>
            <div class="nav-links">
                <c:choose>
                    <c:when test="${sessionScope.user != null}">
                        <a href="${pageContext.request.contextPath}/recherche"><i class="fas fa-search"></i> Rechercher</a>
                        <c:if test="${sessionScope.user.role == 'admin'}">
                            <a href="${pageContext.request.contextPath}/admin/dashboard"><i class="fas fa-cog"></i> Admin</a>
                        </c:if>
                        <a href="${pageContext.request.contextPath}/logout"><i class="fas fa-sign-out-alt"></i> Déconnexion</a>
                    </c:when>
                    <c:otherwise>
                        <a href="${pageContext.request.contextPath}/login.jsp"><i class="fas fa-sign-in-alt"></i> Connexion</a>
                        <a href="${pageContext.request.contextPath}/register.jsp"><i class="fas fa-user-plus"></i> Inscription</a>
                    </c:otherwise>
                </c:choose>
            </div>
        </div>
    </nav>

    <!-- Contenu principal -->
    <div class="container">
        <div class="main-content">
            <h1><i class="fas fa-chart-line"></i> Évolution des Réservations</h1>

            <!-- Breadcrumb -->
            <div class="breadcrumb">
                <a href="${pageContext.request.contextPath}/admin/dashboard"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                <span><i class="fas fa-chevron-right"></i></span>
                <span>Évolution des Réservations</span>
            </div>

            <!-- Messages -->
            <c:if test="${not empty errorMessage}">
                <div class="admin-message admin-message-error">
                    <i class="fas fa-exclamation-triangle"></i> ${errorMessage}
                </div>
            </c:if>

            <!-- Formulaire de filtrage -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title"><i class="fas fa-filter"></i> Paramètres d'Analyse</h2>
                </div>

                <form method="post" action="${pageContext.request.contextPath}/admin/reservation-evolution" class="admin-form">
                    <div class="row">
                        <div class="col-2">
                            <div class="admin-form-group">
                                <label for="startDate"><i class="fas fa-calendar-alt"></i> Date de Début</label>
                                <input type="datetime-local" id="startDate" name="startDate" value="${startDate}" required>
                            </div>
                        </div>
                        <div class="col-2">
                            <div class="admin-form-group">
                                <label for="endDate"><i class="fas fa-calendar-alt"></i> Date de Fin</label>
                                <input type="datetime-local" id="endDate" name="endDate" value="${endDate}" required>
                            </div>
                        </div>
                    </div>

                    <div class="admin-form-group">
                        <label for="groupBy"><i class="fas fa-layer-group"></i> Grouper par</label>
                        <select id="groupBy" name="groupBy" disabled>
                            <option value="month" selected>📅 Par Mois</option>
                        </select>
                        <small class="form-text">Actuellement, seul le groupement par mois est disponible.</small>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-chart-bar"></i> Générer le Rapport
                    </button>
                </form>
            </div>
            <!-- Résultats de l'analyse -->
            <c:if test="${empty evolutionData}">
                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title"><i class="fas fa-info-circle"></i> Aucune Donnée</h2>
                    </div>
                    <div class="empty-state">
                        <i class="fas fa-chart-line"></i>
                        <h3>Aucune donnée disponible</h3>
                        <p>Veuillez sélectionner une période avec des réservations pour générer le graphique.</p>
                    </div>
                </div>
            </c:if>

            <c:if test="${not empty evolutionData}">
                <!-- Informations sur la période -->
                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title"><i class="fas fa-calendar-check"></i> Période Analysée</h2>
                    </div>
                    <div class="period-info">
                        <div class="period-badge">
                            <i class="fas fa-play"></i> Du ${startDate}
                        </div>
                        <div class="period-badge">
                            <i class="fas fa-stop"></i> Au ${endDate}
                        </div>
                    </div>
                </div>

                <!-- Graphique -->
                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title"><i class="fas fa-chart-area"></i> Graphique d'Évolution</h2>
                    </div>
                    <div class="chart-container">
                        <canvas id="evolutionChart"></canvas>
                    </div>
                </div>

                <script>
                    try {
                        const ctx = document.getElementById('evolutionChart').getContext('2d');
                        const labels = ${labels};
                        const data = ${data};
                        console.log('Labels:', labels);
                        console.log('Data:', data);

                        new Chart(ctx, {
                            type: 'line',
                            data: {
                                labels: labels,
                                datasets: [{
                                    label: 'Nombre de réservations',
                                    data: data,
                                    borderColor: '#3498db',
                                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                                    fill: true,
                                    tension: 0.4,
                                    pointBackgroundColor: '#3498db',
                                    pointBorderColor: '#2980b9',
                                    pointRadius: 6,
                                    pointHoverRadius: 8
                                }]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                plugins: {
                                    title: {
                                        display: true,
                                        text: '📈 Évolution des Réservations par Mois',
                                        font: {
                                            size: 18,
                                            weight: 'bold'
                                        },
                                        color: '#2c3e50'
                                    },
                                    legend: {
                                        display: true,
                                        position: 'top',
                                        labels: {
                                            usePointStyle: true,
                                            font: { size: 14 }
                                        }
                                    },
                                    tooltip: {
                                        backgroundColor: 'rgba(44, 62, 80, 0.9)',
                                        titleColor: '#ecf0f1',
                                        bodyColor: '#ecf0f1',
                                        borderColor: '#3498db',
                                        borderWidth: 1,
                                        callbacks: {
                                            label: function(context) {
                                                return `📊 Réservations: ${context.raw}`;
                                            }
                                        }
                                    }
                                },
                                scales: {
                                    y: {
                                        beginAtZero: true,
                                        suggestedMax: Math.max(...data) + 2,
                                        title: {
                                            display: true,
                                            text: 'Nombre de Réservations',
                                            font: {
                                                weight: 'bold',
                                                size: 14
                                            },
                                            color: '#2c3e50'
                                        },
                                        grid: {
                                            color: 'rgba(149, 165, 166, 0.2)'
                                        },
                                        ticks: {
                                            stepSize: 1,
                                            color: '#7f8c8d'
                                        }
                                    },
                                    x: {
                                        title: {
                                            display: true,
                                            text: 'Période (Mois)',
                                            font: {
                                                weight: 'bold',
                                                size: 14
                                            },
                                            color: '#2c3e50'
                                        },
                                        grid: {
                                            display: false
                                        },
                                        ticks: {
                                            color: '#7f8c8d'
                                        }
                                    }
                                }
                            }
                        });
                    } catch (e) {
                        console.error('Erreur lors de la création du graphique:', e);
                        document.getElementById('evolutionChart').parentElement.innerHTML =
                            '<div class="alert alert-danger"><i class="fas fa-exclamation-triangle"></i> Erreur lors de la génération du graphique</div>';
                    }
                </script>
            </c:if>

            <!-- Actions rapides -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title"><i class="fas fa-bolt"></i> Actions Rapides</h2>
                </div>

                <div class="row">
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/admin/dashboard" class="btn btn-secondary btn-full">
                            <i class="fas fa-arrow-left"></i> Retour Dashboard
                        </a>
                    </div>
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/admin/reservation-evolution" class="btn btn-primary btn-full">
                            <i class="fas fa-sync"></i> Actualiser
                        </a>
                    </div>
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/admin/revenue" class="btn btn-warning btn-full">
                            <i class="fas fa-euro-sign"></i> Revenus
                        </a>
                    </div>
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/admin/popular-trajets" class="btn btn-success btn-full">
                            <i class="fas fa-star"></i> Trajets Populaires
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>