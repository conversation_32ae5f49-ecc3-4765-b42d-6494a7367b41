<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Historique des Voyages</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; max-width: 800px; margin-left: auto; margin-right: auto; }
        h2 { color: #333; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        a { color: #007BFF; text-decoration: none; }
        a:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <h2>Historique de vos voyages</h2>
    <c:if test="${empty reservations}">
        <p>Aucun voyage utilisé trouvé.</p>
    </c:if>
    <c:if test="${not empty reservations}">
        <table>
            <tr>
                <th>ID Réservation</th>
                <th>Gare Départ</th>
                <th>Gare Arrivée</th>
                <th>Date Réservation</th>
            </tr>
            <c:forEach var="reservation" items="${reservations}">
                <tr>
                    <td>${reservation.id}</td>
                    <td>${reservation.voyage.trajet.departStation.name} (${reservation.voyage.trajet.departStation.city})</td>
                    <td>${reservation.voyage.trajet.arrivalStation.name} (${reservation.voyage.trajet.arrivalStation.city})</td>
                    <td>${reservation.dateReservation}</td>
                </tr>
            </c:forEach>
        </table>
    </c:if>
    <p><a href="recherche">Retour à la recherche</a></p>
</body>
</html>