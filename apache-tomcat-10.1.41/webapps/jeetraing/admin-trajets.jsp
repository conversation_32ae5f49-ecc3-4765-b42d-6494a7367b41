<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🛤️ Gestion des Trajets - JEE Training</title>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <a href="${pageContext.request.contextPath}/recherche" class="logo">
                <i class="fas fa-train"></i> JEE Training
            </a>
            <div class="nav-links">
                <c:choose>
                    <c:when test="${sessionScope.user != null}">
                        <a href="${pageContext.request.contextPath}/recherche"><i class="fas fa-search"></i> Rechercher</a>
                        <c:if test="${sessionScope.user.role == 'admin'}">
                            <a href="${pageContext.request.contextPath}/admin/dashboard"><i class="fas fa-cog"></i> Admin</a>
                        </c:if>
                        <a href="${pageContext.request.contextPath}/logout"><i class="fas fa-sign-out-alt"></i> Déconnexion</a>
                    </c:when>
                    <c:otherwise>
                        <a href="${pageContext.request.contextPath}/login.jsp"><i class="fas fa-sign-in-alt"></i> Connexion</a>
                        <a href="${pageContext.request.contextPath}/register.jsp"><i class="fas fa-user-plus"></i> Inscription</a>
                    </c:otherwise>
                </c:choose>
            </div>
        </div>
    </nav>

    <!-- Contenu principal -->
    <div class="container">
        <div class="main-content">
            <h1><i class="fas fa-road"></i> Gestion des Trajets</h1>

            <!-- Breadcrumb -->
            <div class="breadcrumb">
                <a href="${pageContext.request.contextPath}/admin/dashboard"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                <span><i class="fas fa-chevron-right"></i></span>
                <span>Gestion des Trajets</span>
            </div>

            <!-- Messages -->
            <c:if test="${not empty success}">
                <div class="admin-message admin-message-success">
                    <i class="fas fa-check-circle"></i> ${success}
                </div>
            </c:if>
            <c:if test="${not empty error}">
                <div class="admin-message admin-message-error">
                    <i class="fas fa-exclamation-triangle"></i> ${error}
                </div>
            </c:if>

            <!-- Formulaire d'ajout -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title"><i class="fas fa-plus-circle"></i> Ajouter un Trajet</h2>
                </div>

                <form action="${pageContext.request.contextPath}/admin/trajets" method="post" class="admin-form">
                    <input type="hidden" name="action" value="add">
                    <div class="row">
                        <div class="col-2">
                            <div class="admin-form-group">
                                <label for="departStationId"><i class="fas fa-map-marker-alt"></i> Gare de Départ</label>
                                <select id="departStationId" name="departStationId" required>
                                    <option value="">Sélectionnez une gare de départ</option>
                                    <c:forEach var="station" items="${stations}">
                                        <option value="${station.id}">🚉 ${station.name} (${station.city})</option>
                                    </c:forEach>
                                </select>
                            </div>
                        </div>
                        <div class="col-2">
                            <div class="admin-form-group">
                                <label for="arrivalStationId"><i class="fas fa-map-marker-alt"></i> Gare d'Arrivée</label>
                                <select id="arrivalStationId" name="arrivalStationId" required>
                                    <option value="">Sélectionnez une gare d'arrivée</option>
                                    <c:forEach var="station" items="${stations}">
                                        <option value="${station.id}">🚉 ${station.name} (${station.city})</option>
                                    </c:forEach>
                                </select>
                            </div>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Ajouter le Trajet
                    </button>
                </form>
            </div>

            <!-- Liste des trajets -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title"><i class="fas fa-list"></i> Liste des Trajets</h2>
                </div>

                <c:if test="${not empty trajets}">
                    <table class="admin-table">
                        <thead>
                            <tr>
                                <th><i class="fas fa-hashtag"></i> ID</th>
                                <th><i class="fas fa-play"></i> Gare de Départ</th>
                                <th><i class="fas fa-stop"></i> Gare d'Arrivée</th>
                                <th><i class="fas fa-route"></i> Trajet</th>
                                <th><i class="fas fa-cogs"></i> Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <c:forEach var="trajet" items="${trajets}">
                                <tr>
                                    <td><strong>#${trajet.id}</strong></td>
                                    <td>
                                        <div class="station-info">
                                            <i class="fas fa-train"></i>
                                            <span>${trajet.departStation.name}</span>
                                            <small>(${trajet.departStation.city})</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="station-info">
                                            <i class="fas fa-train"></i>
                                            <span>${trajet.arrivalStation.name}</span>
                                            <small>(${trajet.arrivalStation.city})</small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="trajet-badge">
                                            <i class="fas fa-arrow-right"></i>
                                            ${trajet.departStation.city} → ${trajet.arrivalStation.city}
                                        </span>
                                    </td>
                                    <td>
                                        <form action="${pageContext.request.contextPath}/admin/trajets" method="get" style="display: inline;">
                                            <input type="hidden" name="action" value="edit">
                                            <input type="hidden" name="id" value="${trajet.id}">
                                            <button type="submit" class="admin-btn admin-btn-primary">
                                                <i class="fas fa-edit"></i> Modifier
                                            </button>
                                        </form>
                                        <form action="${pageContext.request.contextPath}/admin/trajets" method="post" style="display: inline;">
                                            <input type="hidden" name="action" value="delete">
                                            <input type="hidden" name="id" value="${trajet.id}">
                                            <button type="submit" class="admin-btn admin-btn-danger"
                                                    onclick="return confirm('Êtes-vous sûr de vouloir supprimer ce trajet ?');">
                                                <i class="fas fa-trash"></i> Supprimer
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                            </c:forEach>
                        </tbody>
                    </table>
                </c:if>

                <c:if test="${empty trajets}">
                    <div class="empty-state">
                        <i class="fas fa-road"></i>
                        <h3>Aucun trajet trouvé</h3>
                        <p>Commencez par créer votre premier trajet.</p>
                    </div>
                </c:if>
            </div>

            <!-- Formulaire de modification -->
            <c:if test="${not empty trajet}">
                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title"><i class="fas fa-edit"></i> Modifier le Trajet</h2>
                    </div>

                    <form action="${pageContext.request.contextPath}/admin/trajets" method="post" class="admin-form">
                        <input type="hidden" name="action" value="update">
                        <input type="hidden" name="id" value="${trajet.id}">
                        <div class="row">
                            <div class="col-2">
                                <div class="admin-form-group">
                                    <label for="editDepartStationId"><i class="fas fa-map-marker-alt"></i> Gare de Départ</label>
                                    <select id="editDepartStationId" name="departStationId" required>
                                        <c:forEach var="station" items="${stations}">
                                            <option value="${station.id}" ${station.id == trajet.departStation.id ? 'selected' : ''}>
                                                🚉 ${station.name} (${station.city})
                                            </option>
                                        </c:forEach>
                                    </select>
                                </div>
                            </div>
                            <div class="col-2">
                                <div class="admin-form-group">
                                    <label for="editArrivalStationId"><i class="fas fa-map-marker-alt"></i> Gare d'Arrivée</label>
                                    <select id="editArrivalStationId" name="arrivalStationId" required>
                                        <c:forEach var="station" items="${stations}">
                                            <option value="${station.id}" ${station.id == trajet.arrivalStation.id ? 'selected' : ''}>
                                                🚉 ${station.name} (${station.city})
                                            </option>
                                        </c:forEach>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-4">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save"></i> Mettre à Jour
                                </button>
                                <a href="${pageContext.request.contextPath}/admin/trajets" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Annuler
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </c:if>

            <!-- Actions rapides -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title"><i class="fas fa-bolt"></i> Actions Rapides</h2>
                </div>

                <div class="row">
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/admin/dashboard" class="btn btn-secondary btn-full">
                            <i class="fas fa-arrow-left"></i> Retour Dashboard
                        </a>
                    </div>
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/admin/trajets" class="btn btn-primary btn-full">
                            <i class="fas fa-sync"></i> Actualiser
                        </a>
                    </div>
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/admin/stations" class="btn btn-warning btn-full">
                            <i class="fas fa-map-marker-alt"></i> Gérer Gares
                        </a>
                    </div>
                    <div class="col-3">
                        <a href="${pageContext.request.contextPath}/admin/voyages" class="btn btn-success btn-full">
                            <i class="fas fa-train"></i> Gérer Voyages
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>