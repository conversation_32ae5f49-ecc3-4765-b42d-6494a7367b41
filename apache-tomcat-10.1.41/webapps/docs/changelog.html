<!DOCTYPE html SYSTEM "about:legacy-compat">
<html lang="en"><head><META http-equiv="Content-Type" content="text/html; charset=UTF-8"><link href="./images/docs-stylesheet.css" rel="stylesheet" type="text/css"><title>Apache Tomcat 10 (10.1.41) - Changelog</title></head><body><div id="wrapper"><header><div id="header"><div><div><div class="logo noPrint"><a href="https://tomcat.apache.org/"><img alt="Tomcat Home" src="./images/tomcat.png"></a></div><div style="height: 1px;"></div><div class="asfLogo noPrint"><a href="https://www.apache.org/" target="_blank"><img src="./images/asf-logo.svg" alt="The Apache Software Foundation" style="width: 266px; height: 83px;"></a></div><h1>Apache Tomcat 10</h1><div class="versionInfo">
            Version 10.1.41,
            <time datetime="2025-05-08">May 8 2025</time></div><div style="height: 1px;"></div><div style="clear: left;"></div></div></div></div></header><div id="middle"><div><div id="mainLeft" class="noprint"><div><nav><div><h2>Links</h2><ul><li><a href="index.html">Docs Home</a></li><li><a href="https://cwiki.apache.org/confluence/display/TOMCAT/FAQ">FAQ</a></li></ul></div><div><h2>User Guide</h2><ul><li><a href="introduction.html">1) Introduction</a></li><li><a href="setup.html">2) Setup</a></li><li><a href="appdev/index.html">3) First webapp</a></li><li><a href="deployer-howto.html">4) Deployer</a></li><li><a href="manager-howto.html">5) Manager</a></li><li><a href="host-manager-howto.html">6) Host Manager</a></li><li><a href="realm-howto.html">7) Realms and AAA</a></li><li><a href="security-manager-howto.html">8) Security Manager</a></li><li><a href="jndi-resources-howto.html">9) JNDI Resources</a></li><li><a href="jndi-datasource-examples-howto.html">10) JDBC DataSources</a></li><li><a href="class-loader-howto.html">11) Classloading</a></li><li><a href="jasper-howto.html">12) JSPs</a></li><li><a href="ssl-howto.html">13) SSL/TLS</a></li><li><a href="ssi-howto.html">14) SSI</a></li><li><a href="cgi-howto.html">15) CGI</a></li><li><a href="proxy-howto.html">16) Proxy Support</a></li><li><a href="mbeans-descriptors-howto.html">17) MBeans Descriptors</a></li><li><a href="default-servlet.html">18) Default Servlet</a></li><li><a href="cluster-howto.html">19) Clustering</a></li><li><a href="balancer-howto.html">20) Load Balancer</a></li><li><a href="connectors.html">21) Connectors</a></li><li><a href="monitoring.html">22) Monitoring and Management</a></li><li><a href="logging.html">23) Logging</a></li><li><a href="apr.html">24) APR/Native</a></li><li><a href="virtual-hosting-howto.html">25) Virtual Hosting</a></li><li><a href="aio.html">26) Advanced IO</a></li><li><a href="maven-jars.html">27) Mavenized</a></li><li><a href="security-howto.html">28) Security Considerations</a></li><li><a href="windows-service-howto.html">29) Windows Service</a></li><li><a href="windows-auth-howto.html">30) Windows Authentication</a></li><li><a href="jdbc-pool.html">31) Tomcat's JDBC Pool</a></li><li><a href="web-socket-howto.html">32) WebSocket</a></li><li><a href="rewrite.html">33) Rewrite</a></li><li><a href="cdi.html">34) CDI 2 and JAX-RS</a></li><li><a href="graal.html">35) AOT/GraalVM Support</a></li></ul></div><div><h2>Reference</h2><ul><li><a href="RELEASE-NOTES.txt">Release Notes</a></li><li><a href="config/index.html">Configuration</a></li><li><a href="api/index.html">Tomcat Javadocs</a></li><li><a href="servletapi/index.html">Servlet 6.0 Javadocs</a></li><li><a href="jspapi/index.html">JSP 3.1 Javadocs</a></li><li><a href="elapi/index.html">EL 5.0 Javadocs</a></li><li><a href="websocketapi/index.html">WebSocket 2.1 Javadocs</a></li><li><a href="jaspicapi/index.html">Authentication 3.0 Javadocs</a></li><li><a href="annotationapi/index.html">Annotations 2.1 Javadocs</a></li><li><a href="https://tomcat.apache.org/connectors-doc/">JK 1.2 Documentation</a></li></ul></div><div><h2>Apache Tomcat Development</h2><ul><li><a href="building.html">Building</a></li><li><a href="changelog.html">Changelog</a></li><li><a href="https://cwiki.apache.org/confluence/display/TOMCAT/Tomcat+Versions">Status</a></li><li><a href="developers.html">Developers</a></li><li><a href="architecture/index.html">Architecture</a></li><li><a href="tribes/introduction.html">Tribes</a></li></ul></div></nav></div></div><div id="mainRight"><div id="content"><h2>Changelog</h2><h3 id="Tomcat_10.1.41_(schultz)"><span id="Tomcat_10.1.41_(schultz)_rtext" style="float: right;"></span> Tomcat 10.1.41 (schultz)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.41_(schultz)/Catalina">Catalina</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Fix use of <code>SSS</code> in <code>SimpleDateFormat</code>
        pattern for <code>AccessLogValve</code>. (rjung)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Process possible path parameters rewrite production in the rewrite
        valve. (remm)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69588">69588</a>: Enable <code>allowLinking</code> to be set on
        <code>PreResources</code>, <code>JarResources</code> and
        <code>PostResources</code>. If not set explicitly, the setting will be
        inherited from the <code>Resources</code>. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69633">69633</a>: Add support for Filters using context root mappings.
        (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69643">69643</a>: Optimize directory listing for large amount of files.
        Patch submitted by  Loic de l'Eprevier. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://github.com/apache/tomcat/pull/843">#843</a>: Fix off by one validation logic for partial PUT ranges
        and associated test case. Submitted by Chenjp. (remm)
      </li>
      <li><img alt="Code: " class="icon" src="./images/code.gif">
        Replace the unused buffer in
        <code>org.apache.catalina.connector.InputBuffer</code> with a static,
        zero length buffer. (markt)
      </li>
      <li><img alt="Code: " class="icon" src="./images/code.gif">
        Refactor GCI servlet to access resources via the
        <code>WebResource</code> API. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69662">69662</a>: Report name in exception message when a naming lookup
        failure occurs. Based on code submitted by Donald Smith. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Ensure that the FORM authentication attribute
        <code>authenticationSessionTimeout</code> works correctly when sessions
        have an infinite timeout when authentication starts. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Provide a content type based on file extension when web application
        resources are accessed via a URL. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.41_(schultz)/Coyote">Coyote</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Code: " class="icon" src="./images/code.gif">
        Refactor the <code>SavedRequestInputFilter</code> so the buffered data
        is used directly rather than copied. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.41_(schultz)/Jasper">Jasper</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69635">69635</a>: Add support to <code>jakarta.el.ImportHandler</code>
        for resolving inner classes. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        <a href="https://github.com/apache/tomcat/pull/842">#842</a>Add support for optimized execution of c:set and c:remove tags, when
        activated via JSP servlet param useNonstandardTagOptimizations.
        (jengebr)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Fix an edge case compilation bug for JSP and tag files on case
        insensitive file systems that was exposed by the test case for
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69635">69635</a>. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.41_(schultz)/Web_applications">Web applications</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=68876">68876</a>: Documentation. Update the UML diagrams for server
        start-up, request processing and authentication using PlantUML and
        include the source files for each diagram. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.41_(schultz)/Other">Other</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Set <code>sun.io.useCanonCaches</code> in <code>service.bat</code>
        Based on pull request <a href="https://github.com/apache/tomcat/pull/841">#841</a> by Paul Lodge. (remm)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update Jacoco to 0.8.13. (remm)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Explicitly set the locale to be used for Javadoc. For official releases,
        this locale will be English (US) to support reproducible builds. (schultz)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update Byte Buddy to 1.17.5. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update Checkstyle to 10.23.1. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update file extension to media type mappings to align with the current
        list used by the Apache Web Server (httpd). (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Improvements to French translations. (remm)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Improvements to Japanese translations provided by tak7iji. (markt)
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.40_(schultz)"><span id="Tomcat_10.1.40_(schultz)_rtext" style="float: right;">release in progress</span> Tomcat 10.1.40 (schultz)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.40_(schultz)/Catalina">Catalina</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Return 400 if the amount of content sent for a partial PUT is
        inconsistent with the range that was specified. (remm)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add a new <code>RateLimiter</code> implementation,
        <code>org.apache.catalina.util.ExactRateLimiter</code>, that can be used
        with <code>org.apache.catalina.filters.RateLimitFilter</code> to provide
        rate limit based on the exact values configured. Based on pull request
        <a href="https://github.com/apache/tomcat/pull/794">#794</a> by Chenjp. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Fix parsing of the <code>time-taken</code> token in the
        <code>ExtendedAccessLogValve</code>. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Fix invocation of the FFM OpenSSL code for setting a SSL engine and
        FIPS mode. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69600">69600</a>: Add IPv6 local addresses (RFC 4193 and RFC 4291) to
        the default internal proxies for the RemoteIpFilter and RemoteIpValve.
        (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69615">69615</a>: Improve integration with the not found class resources
        cache for users who are using a custom web application class loader
        and/or using reflection to dynamically add external repositories to the
        web application class loader. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add a new initialisation parameter to the Default servlet -
        <code>allowPostAsGet</code> - which controls whether a direct request
        (i.e. not a forward or an include) for a static resource using the POST
        method will be processed as if the GET method had been used. If not
        allowed, the request will be rejected. The default behaviour of
        processing the request as if the GET method had been used is unchanged.
        (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69623">69623</a>: Correct a long standing regression that meant that
        calls to <code>ClassLoader.getResource().getContent()</code> failed when
        made from within a web application with resource caching enabled.
        (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69634">69634</a>: Avoid NPE on <code>JsonErrorReportValve</code>.
        (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Add missing <code>throwable</code> stack trace to
        <code>JsonErrorReportValve</code> equivalent to the one from
        <code>ErrorReportValve</code>. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Improve the handling of <code>%nn</code> URL encoding in the
        RewriteValve and document how <code>%nn</code> URL encoding may be used
        with rewrite rules. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Fix a potential exception when calling
        <code>WebappClassLoaderBase.getResource("")</code>. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.40_(schultz)/Coyote">Coyote</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69607">69607</a>: Allow failed initialization of MD5. Based on code
        submitted by Shivam Verma. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69614">69614</a>: HTTP/2 priority frames with an invalid priority field
        value should be ignored. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Improve handling of unexpected errors during HTTP/2 processing. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Add missing code to process an OpenSSL profile, such as
        <code>PROFILE=SYSTEM</code>, using FFM. (remm)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Simplify the process of using a custom SSLContext for an HTTPS enabled
        connector. Based on pull request <a href="https://github.com/apache/tomcat/pull/805">#805</a> by Hakky54. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.40_(schultz)/Jasper">Jasper</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Code: " class="icon" src="./images/code.gif">
        Replace custom URL encoding provided by the JSP runtime library with
        calls to <code>java.net.URLEncoder.encode()</code>. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add compiler using the <code>Java Compiler</code> API, supporting
        exploded web applications. The <code>compilerClassName</code> to use is
        <code>org.apache.jasper.compiler.JavaCompiler</code>. (remm)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add support for specifying Java 25 (with the value <code>25</code>) as
        the compiler source and/or compiler target for JSP compilation. If used
        with an Eclipse JDT compiler version that does not support these values,
        a warning will be logged and the default will be used.
        (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.40_(schultz)/Cluster">Cluster</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Fix resetting cross context sessions in the
        <code>ReplicationValve</code>. (remm)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.40_(schultz)/Web_applications">Web applications</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Documentation. Add a link to the Log4j documentation that describes how
        to use Log4j rather than JULI for Tomcat's internal logging. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Documentation. Document the runtime attributes available to web
        applications via the Request or the ServletContext. Based on pull
        request <a href="https://github.com/apache/tomcat/pull/832">#832</a> by usmazat. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.40_(schultz)/Other">Other</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Revert JSign to 6.0 to avoid a file locking issue. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update to NSIS 3.11. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update to ByteBuddy 1.17.4. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update to Checkstyle 10.21.4. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update to SpotBugs to 4.9.3. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Improvements to French translations. (remm)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Improvements to Japanese translations provided by tak7iji. (markt)
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.39_(schultz)"><span id="Tomcat_10.1.39_(schultz)_rtext" style="float: right;">2025-03-07</span> Tomcat 10.1.39 (schultz)</h3><div class="text">
</div><h3 id="Tomcat_10.1.38_(schultz)"><span id="Tomcat_10.1.38_(schultz)_rtext" style="float: right;">not released</span> Tomcat 10.1.38 (schultz)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.38_(schultz)/Catalina">Catalina</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69602">69602</a>: Fix regression in releases from 12-2024 that were too
        strict and rejected weak etags in the <code>If-Range</code> header with
        a 400 response. Instead will consider it as a failed match since strong
        etags are required for <code>If-Range</code>. (remm)
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.37_(schultz)"><span id="Tomcat_10.1.37_(schultz)_rtext" style="float: right;">not released</span> Tomcat 10.1.37 (schultz)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.37_(schultz)/Catalina">Catalina</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        When looking up class loader resources by resource name, the resource
        name should not start with '/'. If the resource name does start with
        '/', Tomcat is lenient and looks it up as if the '/' was not present.
        When the web application class loader was configured with external
        repositories and names starting with '/' were used for lookups, it was
        possible that cached 'not found' results could effectively hide lookup
        results using the correct resource name. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Enable the JNDIRealm to validate credentials provided to
        <code>HttpServletRequest.login(String username, String password)</code>
        when the realm is configured to use GSSAPI authentication. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Fix a bug in the JRE compatibility detection that incorrectly identified
        Java 19 and Java 20 as supporting Java 21 features. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Improve the checks for exposure to and protection against CVE-2024-56337
        so that reflection is not used unless required. The checks for whether
        the file system is case sensitive or not have been removed. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add support for logging the connection ID (as returned by
        <code>ServletRequest.getServletConnection().getConnectionId()</code>)
        with the <code>AccessLogValve</code> and
        <code>ExtendedAccessLogValve</code>. Based on pull request <a href="https://github.com/apache/tomcat/pull/814">#814</a>
        by Dmole. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Avoid scenarios where temporary files used for partial PUT would not
        be deleted. (remm)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.37_(schultz)/Coyote">Coyote</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69575">69575</a>: Avoid using compression if a response is already
        compressed using <code>compress</code>, <code>deflate</code> or
        <code>zstd</code>. (remm)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Use <code>Transfer-Encoding</code> for compression rather than
        <code>Content-Encoding</code> if the client submits a <code>TE</code>
        header containing <code>gzip</code>. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Fix a race condition in the handling of HTTP/2 stream reset that could
        cause unexpected 500 responses. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.37_(schultz)/Cluster">Cluster</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69598">69598</a>: Add detection of service account token changes to the
        <code>KubernetesMembershipProvider</code> implementation and reload the
        token if it changes. Based on a patch by Miroslav Jezbera. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.37_(schultz)/Other">Other</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add <code>makensis</code> as an option for building the Installer for
        Windows on non-Windows platforms. (rjung/markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update Byte Buddy to 1.17.1. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update Checkstyle to 10.21.3. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update SpotBugs to 4.9.1. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update JSign to 7.1. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to French translations. (remm)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Japanese translations by tak7iji. (markt)
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.36_(schultz)"><span id="Tomcat_10.1.36_(schultz)_rtext" style="float: right;">2025-02-18</span> Tomcat 10.1.36 (schultz)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.36_(schultz)/Catalina">Catalina</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69576">69576</a>: Avoid possible failure initializing
        <code>JreCompat</code> due to uncaught exception introduced for the
        check for CVE-2024-56337. (remm)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.36_(schultz)/Other">Other</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add <code>org.apache.juli.JsonFormatter</code> to format log as one
        line JSON documents. (remm)
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.35_(schultz)"><span id="Tomcat_10.1.35_(schultz)_rtext" style="float: right;">2025-02-10</span> Tomcat 10.1.35 (schultz)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.35_(schultz)/Catalina">Catalina</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Add <code>tableName</code> configuration on the
        <code>DataSourcePropertyStore</code> that may be used by the WebDAV
        Servlet. (remm)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Improve HTTP If headers processing according to RFC 9110. Based on pull
        request <a href="https://github.com/apache/tomcat/pull/796">#796</a> by Chenjp. (remm/markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Allow <code>readOnly</code> attribute configuration on the
        <code>Resources</code> element and allow configure the
        <code>readOnly</code> attribute value of the main resources. The
        attribute value will also be used by the default and WebDAV Servlets.
        (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69285">69285</a>: Optimise the creation of the parameter map for
        included requests. Based on sample code and test cases provided by John
        Engebretson. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69527">69527</a>: Avoid rare cases where a cached resource could be set
        with 0 content length, or could be evicted immediately. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Fix possible edge cases (such as HTTP/1.0) with trying to detect
        requests without body for WebDAV LOCK and PROPFIND. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69528">69528</a>: Add multi-release JAR support for the
        <code>bloom</code> <code>archiveIndexStrategy</code> of the
        <code>Resources</code>. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Improve checks for <code>WEB-INF</code> and <code>META-INF</code> in
        the WebDAV servlet. Based on a patch submitted by Chenjp. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Remove unused session to client map from
        <code>CrawlerSessionManagerValve</code>. Submitted by Brian Matzon.
        (remm)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add a check to ensure that, if one or more web applications are
        potentially vulnerable to CVE-2024-56337, the JVM has been configured to
        protect against the vulnerability and to configure the JVM correctly if
        not. Where one or more web applications are potentially vulnerable to
        CVE-2004-56337 and the JVM cannot be correctly configured or it cannot
        be confirmed that the JVM has been correctly configured, prevent the
        impacted web applications from starting. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        When using the WebDAV servlet with <code>serveSubpathOnly</code> set to
        <code>true</code>, ensure that the destination for any requested WebDAV
        operation is also restricted to the sub-path. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Generate an appropriate <code>Allow</code> HTTP header when the Default
        servlet returns a 405 (method not allowed) response in response to a
        <code>DELETE</code> request because the target resource cannot be
        deleted. Pull request <a href="https://github.com/apache/tomcat/pull/802">#802</a> provided by Chenjp. (markt)
      </li>
      <li><img alt="Code: " class="icon" src="./images/code.gif">
        Refactor creation of <code>RequestDispatcher</code> instances so that
        the processing of the provided path is consistent with normal request
        processing. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add <code>encodedReverseSolidusHandling</code> and
        <code>encodedSolidusHandling</code> attributes to Context to provide
        control over the handling of the path used to created a
        <code>RequestDispatcher</code>. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Handle a potential <code>NullPointerException</code> after an
        <code>IOException</code> occurs on a non-container thread during
        asynchronous processing. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Enhance lifecycle of temporary files used by partial PUT. (remm)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.35_(schultz)/Coyote">Coyote</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Don't log warnings for registered HTTP/2 settings that Tomcat does not
        support. These settings are now silently ignored. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Avoid a rare <code>NullPointerException</code> when recycling the
        <code>Http11InputBuffer</code>. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Lower the log level to debug for logging an invalid socket channel when
        processing poller events for the NIO Connector as this may occur in
        normal usage. (markt)
      </li>
      <li><img alt="Code: " class="icon" src="./images/code.gif">
        Clean-up references to the HTTP/2 stream once request processing has
        completed to aid GC and reduce the size of the HTTP/2 recycled request
        and response cache. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add a new Connector configuration attribute,
        <code>encodedReverseSolidusHandling</code>, to control how
        <code>%5c</code> sequences in URLs are handled. The default behaviour is
        unchanged (decode) keeping in mind that the
        <strong>allowBackslash</strong> attribute determines how the decoded
        URI is processed. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69545">69545</a>: Improve CRLF skipping for the <code>available</code>
        method of the <code>ChunkedInputFilter</code>. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Improve the performance of repeated calls to <code>getHeader()</code>.
        Pull request <a href="https://github.com/apache/tomcat/pull/813">#813</a> provided by Adwait Kumar Singh. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69559">69559</a>: Ensure that the Java 24 warning regarding the use of
        <code> sun.misc.Unsafe::invokeCleaner</code> is only reported by the JRE
        when the code will be used. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.35_(schultz)/Jasper">Jasper</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69508">69508</a>: Correct a regression in the fix for <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69382">69382</a>
        that broke JSP include actions if both the page attribute and the body
        contained parameters. Pull request <a href="https://github.com/apache/tomcat/pull/803">#803</a> provided by Chenjp.
        (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Update the identifier validation in the Expression Language parser to
        reflect that, as of Java 9, <code>_</code> is also a Java keyword and
        may not be used as an identifier. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69521">69521</a>: Update the EL Parser to allow the full range of valid
        characters in an EL identifier as defined by the Java Language
        Specification. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69532">69532</a>: Optimise the creation of
        <code>ExpressionFactory</code> instances. Patch provided by John
        Engebretson. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.35_(schultz)/Web_applications">Web applications</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Documentation. Expand the description of the security implications of
        setting <code>mapperContextRootRedirectEnabled</code> and/or
        <code>mapperDirectoryRedirectEnabled</code> to <code>true</code>.
        (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Documentation. Better document the default for the
        <code>truststoreProvider</code> attribute of a
        <code>SSLHostConfig</code> element. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.35_(schultz)/Other">Other</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update to Commons Daemon 1.4.1. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update the packaged version of the Tomcat Migration Tool for Jakarta EE
        to 1.0.9. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update the internal fork of Commons Pool to 2.12.1. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update Byte Buddy to 1.16.1. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update UnboundID to 7.0.2. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update Checkstyle to 10.21.2. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update SpotBugs to 4.9.0. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to French translations. (remm)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Chinese translations by leeyazhou. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Japanese translations by tak7iji. (markt)
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.34_(schultz)"><span id="Tomcat_10.1.34_(schultz)_rtext" style="float: right;">2024-12-09</span> Tomcat 10.1.34 (schultz)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.34_(schultz)/Catalina">Catalina</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add option to serve resources from subpath only with WebDAV Servlet like
        with DefaultServlet. (michaelo)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Add special handling for the <code>protocols</code> attribute of
        <code>SSLHostConfig</code> in storeconfig. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69442">69442</a>: Fix case sensitive check on <code>content-type</code>
        when parsing request parameters. (remm)
      </li>
      <li><img alt="Code: " class="icon" src="./images/code.gif">
        Refactor duplicate code for extracting media type and subtype from
        <code>content-type</code> into a single method. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Compatibility of generated embedded code with components where
        constructors or property related methods throw a checked exception.
        (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        The previous fix for inconsistent resource metadata during concurrent
        reads and writes was incomplete. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://github.com/apache/tomcat/pull/780">#780</a>: Fix <code>content-range</code> header length. Submitted
        by Chenjp. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69444">69444</a>: Ensure that the
        <code>jakarta.servlet.error.message</code> request attribute is set when
        an application defined error page is called. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Avoid quotes for numeric values in the JSON generated by the status
        servlet. (remm)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add strong ETag support for the WebDAV and default servlet, which can
        be enabled by using the <code>useStrongETags</code> init parameter with
        a value set to <code>true</code>. The ETag generated will be a SHA-1
        checksum of the resource content. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Use client locale for directory listings. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69439">69439</a>: Improve the handling of multiple
        <code>Cache-Control</code> headers in the <code>ExpiresFilter</code>.
        Based on pull request <a href="https://github.com/apache/tomcat/pull/777">#777</a> by Chenjp. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69447">69447</a>: Update the support for caching classes the web
        application class loader cannot find to take account of classes loaded
        from external repositories. Prior to this fix, these classes could be
        incorrectly marked as not found. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69466">69466</a>: Rework handling of HEAD requests. Headers explicitly
        set by users will not be removed and any header present in a HEAD
        request will also be present in the equivalent GET request. There may be
        some headers, as per RFC 9110, section 9.3.2, that are present in a GET
        request that are not present in the equivalent HEAD request. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69471">69471</a>: Log instances of <code>CloseNowException</code> caught
        by <code>ApplicationDispatcher.invoke()</code> at debug level rather
        than error level as they are very likely to have been caused by a client
        disconnection or similar I/O issue. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add a test case for the fix for <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69442">69442</a>. Also refactor
        references to <code>application/x-www-form-urlencoded</code>. Based on
        pull request <a href="https://github.com/apache/tomcat/pull/779">#779</a> by Chenjp. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69476">69476</a>: Catch possible ISE when trying to report PUT failure
        in the <code>DefaultServlet</code>. (remm)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add support for <a href="https://datatracker.ietf.org/doc/draft-ietf-httpapi-ratelimit-headers">RateLimit
        header fields for HTTP (draft)</a> in the <code>RateLimitFilter</code>.
        Based on pull request <a href="https://github.com/apache/tomcat/pull/775">#775</a> provided by Chenjp. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        <a href="https://github.com/apache/tomcat/pull/787">#787</a>: Add regression tests for <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69478">69478</a>. Pull request
        provided by Thomas Krisch. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        The default servlet now rejects HTTP range requests when two or more of
        the requested ranges overlap. Based on pull request <a href="https://github.com/apache/tomcat/pull/782">#782</a>
        provided by Chenjp. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Enhance Content-Range verification for partial PUT requests handled by
        the default servlet. Provided by Chenjp in pull request <a href="https://github.com/apache/tomcat/pull/778">#778</a>.
        (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Harmonize <code>DataSourceStore</code> lookup in the global resources
        to optionally avoid the <code>comp/env</code> prefix which is usually
        not used there. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        As required by RFC 9110, the HTTP <code>Range</code> header will now
        only be processed for <code>GET</code> requests. Based on pull request
        <a href="https://github.com/apache/tomcat/pull/790">#790</a> provided by Chenjp. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Deprecate the <code>useAcceptRanges</code> initialisation parameter for
        the default servlet. It will be removed in Tomcat 12 onwards where it
        will effectively be hard coded to <code>true</code>. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add <code>DataSource</code> based property storage for the
        <code>WebdavServlet</code>. (remm)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.34_(schultz)/Coyote">Coyote</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Align <code>encodedSolidusHandling</code> with the Servlet
        specification. If the pass-through mode is used, any
        <code>%25</code> sequences will now also be passed through to avoid
        errors and/or corruption when the application decodes the path. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.34_(schultz)/Jasper">Jasper</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Follow-up to the fix for <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69381">69381</a>. Apply the optimisation for
        method lookup performance in expression language to an additional
        location. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.34_(schultz)/Web_applications">Web applications</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Documentation. Remove references to the <code>ResourceParams</code>
        element. Support for <code>ResourceParams</code> was removed in Tomcat
        5.5.x. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Documentation. <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69477">69477</a>: Correct name of attribute for
        <code>RemoteIPFilter</code>. The attribute is
        <code>internalProxies</code> rather than
        <code>allowedInternalProxies</code>. Pull request <a href="https://github.com/apache/tomcat/pull/786">#786</a> provided
        by Jorge D&iacute;az. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Examples. Fix broken links when Servlet Request Info example is called
        via a URL that includes a pathInfo component. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Examples. Expand the obfuscation of session cookie values in the request
        header example to JSON responses. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Examples. Add the ability to delete session attributes in the servlet
        session example. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Examples. Add a hard coded limit of 10 attributes per session for the
        servlet session example. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Examples. Add the ability to delete session attributes and add a hard
        coded limit of 10 attributes per session for the JSP form authentication
        example. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Examples. Limit the shopping cart example to only allow adding the
        pre-defined items to the cart. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Examples. Remove JSP calendar example. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.34_(schultz)/Other">Other</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69465">69465</a>: Fix warnings during native image compilation using the
        Tomcat embedded JARs. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update Tomcat's fork of Commons DBCP to 2.13.0. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update EasyMock to 5.5.0. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update Checkstyle to 10.20.2. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update BND to 7.1.0. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to French translations. (remm)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Korean translations. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Chinese translations. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Japanese translations by tak7iji. (markt)
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.33_(schultz)"><span id="Tomcat_10.1.33_(schultz)_rtext" style="float: right;">2024-11-11</span> Tomcat 10.1.33 (schultz)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.33_(schultz)/Other">Other</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">Fix release build issue.</li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.32_(schultz)"><span id="Tomcat_10.1.32_(schultz)_rtext" style="float: right;">not released</span> Tomcat 10.1.32 (schultz)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.32_(schultz)/Catalina">Catalina</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add support for the new Servlet API method
        <code>HttpServletResponse.sendEarlyHints()</code>. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        <fix>55470</fix>: Add debug logging that reports the class path when a
        <code>ClassNotFoundException</code> occurs in the digester or the web
        application class loader. Based on a patch by Ralf Hauser. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69374">69374</a>: Properly separate between table header and body
        in <code>DefaultServlet</code>'s listing. (michaelo)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69373">69373</a>: Make <code>DefaultServlet</code>'s HTML listing
        file last modified rendering better (flexible). (michaelo)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Improve HTML output of <code>DefaultServlet</code>. (michaelo)
      </li>
      <li><img alt="Code: " class="icon" src="./images/code.gif">
        Refactor <code>RateLimitFilter</code> to use <code>FilterBase</code> as
        the base class. The primary advantage for doing this is less code to
        process <code>init-param</code> values. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69370">69370</a>: <code>DefaultServlet</code>'s HTML listing
        uses incorrect labels. (michaelo)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Avoid NPE in <code>CrawlerSessionManagerValve</code> for partially
        mapped requests. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Add missing WebDAV <code>Lock-Token</code> header in the response when
        locking a folder. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Invalid WebDAV lock requests should be rejected with 400. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Fix regression in WebDAV when attempting to unlock a collection. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Verify that destination is not locked for a WebDAV copy operation.
        (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Send 415 response to WebDAV <code>MKCOL</code> operations that include
        a request body since this is optional and unsupported. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Enforce <code>DAV:</code> namespace on WebDAV XML elements. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Do not allow a new WebDAV lock on a child resource if a parent
        collection is locked (RFC 4918 section 6.1). (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        WebDAV <code>DELETE</code> should remove any existing lock on
        successfully deleted resources. (remm)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Remove WebDAV lock null support in accordance with RFC 4918 section 7.3
        and annex D. Instead a lock on a non existing resource will create an
        empty file locked with a regular lock. (remm)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Rewrite implementation of WebDAV shared locks to comply with RFC 4918.
        (remm)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Implement WebDAV <code>If</code> header using code from the Apache
        Jackrabbit project. (remm)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add <code>PropertyStore</code> interface in the WebDAV Servlet,
        to allow implementation of dead properties storage. The store used
        can be configured using the <code>propertyStore</code> init parameter
        of the WebDAV servlet by specifying the class name of the store. A
        simple non persistent implementation is used if no custom store is
        configured. (remm)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Implement WebDAV <code>PROPPATCH</code> method using the newly added
        <code>PropertyStore</code>, and update <code>PROPFIND</code> to support
        it. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Cache not found results when searching for web application class loader
        resources. This addresses performance problems caused by components such
        as <code>java.sql.DriverManager</code> which, in some circumstances,
        will search for the same class repeatedly. In a large web application
        this can cause performance problems. The size of the cache can be
        controlled via the new <code>notFoundClassResourceCacheSize</code> on
        the StandardContext. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Stop after <code>INITIALIZED</code> state should be a noop since it is
        possible for subcomponents to be in <code>FAILED</code> after init.
        (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Fix incorrect web resource cache size calculations when there are
        concurrent <code>PUT</code> and <code>DELETE</code> requests for the
        same resource. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add debug logging for the web resource cache so the current size can be
        tracked as resources are added and removed. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Replace legacy WebDAV <code>opaquelocktoken:</code> scheme for lock
        tokens with <code>urn:uuid:</code> as recommended by RFC 4918, and
        remove <code>secret</code> init parameter. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Concurrent reads and writes (e.g. <code>GET</code> and <code>PUT</code>
        / <code>DELETE</code>) for the same path caused corruption of the
        <code>FileResource</code> where some of the fields were set as if the
        file exists and some were set as if it does not. This resulted in
        inconsistent metadata. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69415">69415</a>: Ensure that the <code>ExpiresFilter</code> only sets
        cache headers on <code>GET</code> and <code>HEAD</code> requests. Also
        skip requests where the application has set <code>Cache-Control:
        no-store</code>. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69419">69419</a>: Improve the performance of
        <code>ServletRequest.getAttribute()</code> when there are multiple
        levels of nested includes. Based on a patch provided by John
        Engebretson. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        All applications to send an early hints informational response by
        calling <code>HttpServletResponse.sendError()</code> with a status code
        of 103. (schultz)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.32_(schultz)/Coyote">Coyote</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Return null SSL session id on zero length byte array returned from the
        SSL implementation. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Skip OpenSSLConf with BoringSSL since it is unsupported. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Create the <code>HttpParser</code> in <code>Http11Processor</code>
        if it is not present on the <code>AbstractHttp11Protocol</code>
        to provide better lifecycle robustness for regular HTTP/1.1. The new
        behavior was introduced on a previous refactoring to improve HTTP/2
        performance. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <code>OpenSSLContext</code> will now throw a
        <code>KeyManagementException</code> if something is known to have gone
        wrong in the <code>init</code> method, which is the behavior
        documented by <code>javax.net.ssl.SSLContext.init</code>. This makes
        error handling more consistent. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69379">69379</a>: The default HEAD response no longer includes the
        payload HTTP header fields as per section 9.3.2 of RFC 9110. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.32_(schultz)/Jasper">Jasper</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Add back tag release method as deprecated in the runtime for compat
        with old generated code. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69399">69399</a>: Fix regression caused by the improvement
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69333">69333</a> which caused the tag <code>release</code> to be called
        when using tag pooling, and to be skipped when not using it.
        Patch submitted by Michal Sobkiewicz. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69381">69381</a>: Improve method lookup performance in expression
        language. When the required method has no arguments there is no need to
        consider casting or coercion and the method lookup process can be
        simplified. Based on pull request <a href="https://github.com/apache/tomcat/pull/770">#770</a> by John Engebretson.
        (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69382">69382</a>: Improve the performance of the JSP include action by
        re-using results of relatively expensive method calls in the generated
        code rather than repeating them. Patch provided by John Engebretson.
        (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69398">69398</a>: Avoid unnecessary object allocation in
        <code>PageContextImpl</code>. Based on a suggestion by John Engebretson.
        (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69406">69406</a>: When using <code>StringInterpreterEnum</code>, do not
        throw an <code>IllegalArgumentException</code> when an invalid
        <code>Enum</code> is encountered. Instead, resolve the value at runtime.
        Patch provided by John Engebretson. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69429">69429</a>: Optimise EL evaluation of method parameters for
        methods that do not accept any parameters. Patch provided by John
        Engebretson. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Further optimise EL evaluation of method parameters. Patch provided by
        Paolo B. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.32_(schultz)/Other">Other</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Switch from DigiCert ONE to ssl.com eSigner for code signing. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update Byte Buddy to 1.15.10. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update CheckStyle to 10.20.0. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to German translations. (remm)
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.31_(schultz)"><span id="Tomcat_10.1.31_(schultz)_rtext" style="float: right;">2024-10-09</span> Tomcat 10.1.31 (schultz)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.31_(schultz)/Catalina">Catalina</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Ensure that <code>ServerAuthModule.initialize()</code> is called when
        a Jakarta Authentication module is configured via
        <code>registerServerAuthModule()</code>. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Ensure that the Jakarta Authentication <code>CallbackHandler</code> only
        creates one <code>GenericPrincipal</code> in the <code>Subject</code>.
        (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        If the Jakarta Authentication process fails with an Exception,
        explicitly set the HTTP response status to 500 as the
        <code>ServerAuthContext</code> may not have set it. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        When persisting the Jakarta Authentication provider configuration,
        create any necessary parent directories that don't already exist.
        (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Correct the logic used to detect errors when deleting temporary files
        associated with persisting the Jakarta Authentication provider
        configuration. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        When processing Jakarta Authentication callbacks, don't overwrite a
        Principal obtained from the <code>PasswordValidationCallback</code> with
        <code>null</code> if the <code>CallerPrincipalCallback</code> does not
        provide a Principal. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Avoid store config backup loss when storing one configuration more than
        once per second. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69359">69359</a>: <code>WebdavServlet</code> duplicates
        <code>getRelativePath()</code> method from super class with
        incorrect Javadoc. (michaelo)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69360">69360</a>: Inconsistent <code>DELETE</code> behavior between
        <code>WebdavServlet</code> and <code>DefaultServlet</code>.
        (michaelo)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Make <code>WebdavServlet</code> properly return the <code>Allow</code>
        header when deletion of a resource is not allowed. (michaelo)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Add log warning if non wildcard mappings are used with the
        <code>WebdavServlet</code>. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69361">69361</a>: Ensure that the order of entires in a multi-status
        response to a WebDAV is consistent with the order in which resources
        were processed. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69362">69362</a>: Provide a better multi-status response when deleting a
        collection via WebDAV fails. Empty directories that cannot be deleted
        will now be included in the response. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69363">69363</a>: Use <code>getPathPrefix()</code> consistently in the
        WebDAV servlet to ensure that the correct path is used when the WebDAV
        servlet is mounted at a sub-path within the web application. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.31_(schultz)/Coyote">Coyote</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69316">69316</a>: Ensure that
        <code>FastHttpDateFormat#getCurrentDate()</code> (used to generate Date
        headers for HTTP responses) generates the correct string for the given
        input. Prior to this change, the output may have wrong by one second in
        some cases. Pull request <a href="https://github.com/apache/tomcat/pull/751">#751</a> provided by Chenjp. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Request start time may not have been accurately recorded for HTTP/1.1
        requests preceded by a large number of blank lines. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add <code>server</code> and <code>serverRemoveAppProvidedValues</code>
        to the list of attributes the HTTP/2 protocol will inherit from the
        HTTP/1.1 connector it is nested within. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Avoid possible crashes when using Apache Tomcat Native, caused by
        destroying SSLContext objects through GC after APR has been terminated.
        (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Improve HTTP/2 handling of trailer fields for requests. Trailer fields
        no longer need to be recieved before the headers of the subsequent
        stream nor are trailer fields for an in progress stream swallowed if the
        Connector is paused before the trailer fields are received. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Ensure the request and response are not recycled too soon for an HTTP/2
        stream when a stream level error is detected during the processing of
        incoming HTTP/2 frames. This could lead to incorrect processing times
        appearing in the access log. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.31_(schultz)/Jasper">Jasper</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69333">69333</a>: Remove unnecessary code from generated JSPs. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69338">69338</a>: Improve the performance of processing expressions that
        include AND or OR operations with more than two operands and expressions
        that use <code>not empty</code>. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69348">69348</a>: Reduce memory consumption in <code>ELContext</code> by
        using lazy initialization for the data structure used to track lambda
        arguments. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.31_(schultz)/Web_applications">Web applications</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        The manager webapp will now be able to access certificates again when
        OpenSSL is used. (remm)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.31_(schultz)/Other">Other</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update Byte Buddy to 1.15.3. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update CheckStyle to 10.18.2. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to French translations. (remm)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Japanese translations by tak7iji. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Chinese translations by Ch_jp. (markt)
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.30_(schultz)"><span id="Tomcat_10.1.30_(schultz)_rtext" style="float: right;">2024-09-17</span> Tomcat 10.1.30 (schultz)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.30_(schultz)/Coyote">Coyote</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Fix <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69320">69320</a>, a regression in the fix for <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69302">69302</a> that
        meant the HTTP/2 processing was likely to be broken for all clients once
        any client sent an HTTP/2 reset frame. (markt)
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.29_(schultz)"><span id="Tomcat_10.1.29_(schultz)_rtext" style="float: right;">2024-09-10</span> Tomcat 10.1.29 (schultz)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.29_(schultz)/Catalina">Catalina</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Improve performance of
        <code>ApplicationHttpRequest.parseParameters()</code>. Based on sample
        code and test cases provided by John Engebretson. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.29_(schultz)/Coyote">Coyote</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Correct a regression in the fix for non-blocking reads of chunked
        request bodies that caused <code>InputStream.available()</code> to
        return a non-zero value when there was no data to read. In some
        circumstances this could cause a blocking read to block waiting for more
        data rather than return the data it had already received. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add a new attribute <code>cookiesWithoutEquals</code> to the
        <code>Rfc6265CookieProcessor</code>. The default behaviour is unchanged.
        (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Ensure that Tomcat sends a TLS close_notify message after receiving one
        from the client when using the <code>OpenSSLImplementation</code>.
        (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69301">69301</a>: Fix trailer headers replacing non-trailer headers when
        writing response headers to the access log. Based on a patch and test
        case provided by hypnoce. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69302">69302</a>: If an HTTP/2 client resets a stream before the request
        body is fully written, ensure that any <code>ReadListener</code> is
        notified via a call to <code>ReadListener.onErrror()</code>. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.29_(schultz)/Jasper">Jasper</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Switch the <code>TldScanner</code> back to logging detailed scan
        results at debug level rather than trace level. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.29_(schultz)/WebSocket">WebSocket</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        If a blocking message write exceeds the timeout, don't attempt the write
        again before throwing the exception. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        An EncodeException being thrown during a message write should not
        automatically cause the connection to close. The application should
        handle the exception and make the decision whether or not to close the
        connection. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.29_(schultz)/Web_applications">Web applications</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Documentation. Align the logging configuration documentation with the
        current defaults. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.29_(schultz)/jdbc-pool">jdbc-pool</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69255">69255</a>: Correct a regression in the fix for <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69206">69206</a>
        that meant exceptions executing statements were wrapped in an
        <code>java.lang.reflect.UndeclaredThrowableException</code> rather than
        the application seeing the original <code>SQLException</code>. Fixed
        by pull request <a href="https://github.com/apache/tomcat/pull/744">#744</a> provided by Michael Clarke. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69279">69279</a>: Correct a regression in the fix for <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69206">69206</a>
        that meant that methods that previously returned a <code>null</code>
        <code>ResultSet</code> were returning a proxy with a null delegate.
        Fixed by pull request <a href="https://github.com/apache/tomcat/pull/745">#745</a> provided by Huub de Beer. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.29_(schultz)/Other">Other</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Exclude the <code>tomcat-coyote-ffm.jar</code> from JAR scanning by
        default. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Change the default log handler level to <code>ALL</code> so log messages
        are not dropped by default if a logger is configured to use trace
        (<code>FINEST</code>) level logging. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update Hamcrest to 3.0. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update EasyMock to 5.4.0. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update Byte Buddy to 1.15.0. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update CheckStyle to 10.18.0. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update the internal fork of Apache Commons BCEL to 6.10.0. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Spanish translations by Fernando. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to French translations. (remm)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Japanese translations by tak7iji. (markt)
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.28_(schultz)"><span id="Tomcat_10.1.28_(schultz)_rtext" style="float: right;">2024-08-06</span> Tomcat 10.1.28 (schultz)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.28_(schultz)/Coyote">Coyote</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Correct regressions in the refactoring that added recycling of the
        coyote request and response to the HTTP/2 processing. (markt)
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.27_(schultz)"><span id="Tomcat_10.1.27_(schultz)_rtext" style="float: right;">not released</span> Tomcat 10.1.27 (schultz)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.27_(schultz)/Catalina">Catalina</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add support for RFC 8297 (Early Hints). Applications can use this
        feature by casting the <code>HttpServletResponse</code> to
        <code>org.apache.catalina.connector.Reponse</code> and then calling the
        method <code>void sendEarlyHints()</code>. This method will be added to
        the Servlet API (removing the need for the cast) in Servlet 6.2 onwards.
        (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69214">69214</a>: Do not reject a CORS request that uses POST but does
        not include a <code>content-type</code> header. Tomcat now correctly
        processes this as a simple CORS request. Based on a patch suggested by
        thebluemountain. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Refactor <code>SpnegoAuthenticator</code> so it uses
        <code>Subject.callAs()</code> rather than <code>Subject.doAs()</code>
        when the available. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.27_(schultz)/Coyote">Coyote</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Ensure that HTTP/2 stream input buffers are only created when there is a
        request body to be read. (markt)
      </li>
      <li><img alt="Code: " class="icon" src="./images/code.gif">
        Refactor creation of HttpParser instances from the Processor level to
        the Protocol level since the parser configuration depends on the
        protocol and the parser is, otherwise, stateless. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Align HTTP/2 with HTTP/1.1 and recycle the container internal request
        and response processing objects by default. This behaviour can be
        controlled via the new <code>discardRequestsAndResponses</code>
        attribute on the HTTP/2 upgrade protocol. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.27_(schultz)/jdbc-pool">jdbc-pool</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69206">69206</a>: Ensure statements returned from <code>Statement</code>
        methods <code>executeQuery()</code>, <code>getResultSet()</code> and
        <code>getGeneratedKeys()</code> are correctly wrapped before being
        returned to the caller. Based on pull request <a href="https://github.com/apache/tomcat/pull/742">#742</a> provided by
        Michael Clarke.
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.27_(schultz)/Other">Other</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Fix packaging regression with missing osgi information following
        addition of the <code>test-only</code> build target. (remm)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update Tomcat Native to 2.0.8. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update Byte Buddy to 1.14.18. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to French translations. (remm)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Japanese translations by tak7iji. (markt)
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.26_(schultz)"><span id="Tomcat_10.1.26_(schultz)_rtext" style="float: right;">2024-07-12</span> Tomcat 10.1.26 (schultz)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.26_(schultz)/Catalina">Catalina</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Allow <code>JAASRealm</code> to use the configuration source to load a
        configured <code>configFile</code>, for easier use with testing. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Add missing algorithm callback to the <code>JAASCallbackHandler</code>.
        (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Add the OpenSSL version number on the APR and OpenSSL status classes.
        (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69131">69131</a>: Expand the implementation of the <code>filter</code>
        value of the Authenticator attribute <code>allowCorsPreflight</code>, so
        that it applies to all requests that match the configured URL patterns
        for the CORS filter, rather than only applying if the CORS filter is
        mapped to <code>/*</code>. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Using the <code>OpenSSLListener</code> will now cause the connector to
        use OpenSSL if available. (remm)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.26_(schultz)/Coyote">Coyote</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Clean and log OpenSSL errors before processing of OpenSSL conf commands
        in the FFM code. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69121">69121</a>: Ensure that the <code>onComplete()</code> event is
        triggered if <code>AsyncListener.onError()</code> dispatches to a target
        that throws an exception. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Following the trailer header field refactoring, <code>-1</code> is no
        longer an allowed value for <code>maxTrailerSize</code>. Adjust
        documentation accordingly. (remm)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Move OpenSSL support using FFM to a separate JAR named
        <code>tomcat-coyote-ffm.jar</code> that advertises Java 22 in its
        manifest. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Fix search for OpenSSL library for FFM on Mac OS so that
        <code>java.library.path</code> is searched. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Add FFM compatibility methods for LibreSSL support. Renegotiation is
        not supported at the moment. (remm)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Add <code>org.apache.tomcat.util.openssl.LIBRARY_NAME</code> (specifies
        the name of the library to load) and
        <code>org.apache.tomcat.util.openssl.USE_SYSTEM_LOAD_LIBRARY</code>
        (set to <code>true</code> to use <code>System.loadLibrary</code> rather
        than the FFM library loading code) to configure the OpenSSL library
        loading using FFM. (remm)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Add FFM compatibility methods for BoringSSL support. Renegotiation is
        not supported in many cases. (remm)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.26_(schultz)/Jasper">Jasper</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Update the optimisation in <code>jakarta.el.ImportHandler</code> so it
        is aware of new classes added to the <code>java.lang</code> package in
        Java 23. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Ensure that an exception in <code>toString()</code> still results in an
        <code>ELException</code> when an object is coerced to a String using
        <code>ExpressionFactory.coerceToType()</code>. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add support for specifying Java 24 (with the value <code>24</code>) as
        the compiler source and/or compiler target for JSP compilation. If used
        with an Eclipse JDT compiler version that does not support these values,
        a warning will be logged and the default will used.
        (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69135">69135</a>: When using include directives in a tag file packaged
        in a JAR file, ensure that context relative includes are processed
        correctly. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69135">69135</a>: When using include directives in a tag file packaged
        in a JAR file, ensure that file relative includes are processed
        correctly. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69135">69135</a>: When using include directives in a tag file packaged
        in a JAR file, ensure that file relative includes are are not permitted
        to access files outside of the <code>/META_INF/tags/</code> directory
        nor outside of the JAR file. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.26_(schultz)/Web_applications">Web applications</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Fix status servlet detailed view of the connectors when using automatic
        port. (remm)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.26_(schultz)/Other">Other</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Add <code>test-only</code> build target to allow running only the
        testsuite, supporting Java versions down to the minimum supported
        to run Tomcat. (rjung)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update UnboundID to 7.0.1. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update to SpotBugs 4.8.6. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Remove cglib dependency as it is not required by the version of EasyMock
        used by the unit tests. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update EasyMock to 5.3.0. This adds a test dependency on Byte-Buddy
        1.14.17. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Czech translations by Vladim&iacute;r Chlup. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to French translations. (remm)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Japanese translations by tak7iji. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Chinese translations by fangzheng. (markt)
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.25_(schultz)"><span id="Tomcat_10.1.25_(schultz)_rtext" style="float: right;">2024-06-19</span> Tomcat 10.1.25 (schultz)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.25_(schultz)/Catalina">Catalina</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add support for shallow copies when using WebDAV. (markt)
      </li>
      <li><img alt="Code: " class="icon" src="./images/code.gif">
        Deprecate the <code>WebdavFixFilter</code> as it is no longer required.
        (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69066">69066</a>: Fix regression in SPNEGO authenticator when
        processing Base64. Submitted by Daniel Lyko. (remm)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add <code>RealmBase.getPrincipal(GSSName, GSSCredential, GSSContext)</code>
        for retrieving extended/additional information from an established
        GSS context. (michaelo)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Correct a regression in the fix for <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=68721">68721</a> that caused some
        instances of <code>LinkageError</code> to be reported as
        <code>ClassNotFoundException</code>. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Ensure that static resources deployed via a JAR file remain accessible
        when the context is configured to use a bloom filter. Based on pull
        request <a href="https://github.com/apache/tomcat/pull/730">#730</a> provided by bergander. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Introduce reference counting so the <code>AprLifecycleListener</code>
        is more robust. This particularly targets more complex embedded
        configurations with multiple server instances with independent
        lifecycles where more than one server instance requires the
        <code>AprLifecycleListener</code>. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.25_(schultz)/Coyote">Coyote</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Fix OpenSSL FFM use of ERR_error_string with a 128 byte buffer,
        and use ERR_error_string_n instead. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Fix a crash on Windows setting CA certificate on null path.
        (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69068">69068</a>: Ensure read timouts are triggered for asynchronous,
        non-blocking reads when using HTTP/2. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=69133">69133</a>: Add task queue size configuration on the
        <code>Connector</code> element, similar to the <code>Executor</code>
        element, for consistency. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Make counting of active HTTP/2 streams per connection more robust.
        (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add support for TLS 1.3 client initiated re-keying. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Improve the algorithm used to identify the IP address to use to unlock
        the acceptor thread when a Connector is listening on all local
        addresses. Interfaces that are configured for point to point connections
        or are not currently up are now skipped. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.25_(schultz)/Jasper">Jasper</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=68546">68546</a>: Small additional optimisation for initial loading of
        Servlet code generated for JSPs. Based on a suggestion by Dan Armstrong.
        (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.25_(schultz)/Web_applications">Web applications</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add the ability to set a sub-title for the Manager web application main
        page. This is intended to allow users with lots of instances to easily
        distinguish them. Based on pull request <a href="https://github.com/apache/tomcat/pull/724">#724</a> by Simon Arame.
        (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.25_(schultz)/Other">Other</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Revert Derby to ********* as that is the latest version of Derby that
        runs on Java 17. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update to Commons Daemon 1.4.0. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update to Objenesis 3.4. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update to Checkstyle 10.17.0. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update to SpotBugs 4.8.5. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to French translations. (remm)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Japanese translations by tak7iji. (markt)
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.24_(schultz)"><span id="Tomcat_10.1.24_(schultz)_rtext" style="float: right;">2024-05-13</span> Tomcat 10.1.24 (schultz)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.24_(schultz)/Catalina">Catalina</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Small performance optimization when logging cookies with no values.
        (schultz)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Correct error handling for asynchronous requests. If the application
        performs an dispatch during <code>AsyncListener.onError()</code> the
        dispatch is now performed rather than completing the request using the
        error page mechanism. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Re-factor ElapsedTimeElement in AbstractAccessLogValve to use a customizable
        style. (schultz)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add more timescale options to AccessLogValve and ExtendedAccessLogValve.
        Allow timescales to apply to "time-taken" token in ExtendedAccessLogValve.
        (schultz)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Fix WebDAV lock null (locks for non existing resources) thread safety
        and removal. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Add periodic checking for WebDAV locks expiration. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Extend <code>Asn1Parser</code> to parse <code>UTF8String</code>s. (michaelo)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Remove MBean metadata for attibutes that have been removed. Based on
        pull request <a href="https://github.com/apache/tomcat/pull/719">#719</a> by Shawn Q. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.24_(schultz)/Coyote">Coyote</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Align non-secure and secure writes with NIO and skip the write attempt
        when there are no bytes to be written. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Allow any positive value for <code>socket.unlockTimeout</code>. If a
        negative or zero value is configured, the default of <code>250ms</code>
        will be used. (mark)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Reduce the time spent waiting for the connector to unlock. The previous
        default of 10s was noticeably too long for cases where the unlock has
        failed. The wait time is now 100ms plus twice
        <code>socket.unlockTimeout</code>. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Ensure that the <code>onAllDataRead()</code> event is triggered when the
        request body uses chunked encoding and is read using non-blocking IO.
        (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=68934">68934</a>: Add debug logging in the latch object when exceeding
        <code>maxConnections</code>. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Refactor trailer field handling to use a <code>MimeHeaders</code>
        instance to store trailer fields. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Ensure that multiple instances of the same trailer field are handled
        correctly. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Fix non-blocking reads of chunked request bodies. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        When an invalid HTTP response header was dropped, an off-by-one error
        meant that the first header in the response was also dropped. Fix based
        on pull request <a href="https://github.com/apache/tomcat/pull/710">#710</a> by foremans. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.24_(schultz)/Jasper">Jasper</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add support for specifying Java 23 (with the value <code>23</code>) as
        the compiler source and/or compiler target for JSP compilation. If used
        with an Eclipse JDT compiler version that does not support these values,
        a warning will be logged and the default will used.
        (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.24_(schultz)/WebSocket">WebSocket</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=68884">68884</a>: Reduce the write timeout when writing WebSocket close
        messages for abnormal closes. The timeout defaults to 50 milliseconds
        and may be controlled using the
        <code>org.apache.tomcat.websocket.ABNORMAL_SESSION_CLOSE_SEND_TIMEOUT</code>
        property in the user properties collection associated with the WebSocket
        session. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.24_(schultz)/Web_applications">Web applications</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Examples: Improve performance of WebSocket chat application when
        multiple clients disconnect at the same time. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Examples: Increase the number of previous messages displayed when using
        the WebSocket chat application. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Examples: Improve performance of WebSocket snake application when
        multiple clients disconnect at the same time. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.24_(schultz)/Other">Other</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Switch to using the Base64 encoder and decoder provided by the JRE
        rather than the version provided by Commons Codec. The internal fork of
        Commons Codec has been deprecated and will be removed in Tomcat 11.
        (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update NSIS to 3.10. (mark0t)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update UnboundID to 7.0.0. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update Checkstyle to 10.16.0. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update JaCoCo to 0.8.12. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update SpotBugs to 4.8.4. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update the internal fork of Apache Commons BCEL to 6.9.0. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update the internal fork of Apache Commons DBCP to 2.12.0. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to French translations. (remm)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Japanese translations by tak7iji. (markt)
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.23_(schultz)"><span id="Tomcat_10.1.23_(schultz)_rtext" style="float: right;">2024-04-23</span> Tomcat 10.1.23 (schultz)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.23_(schultz)/Catalina">Catalina</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Deprecate and remove <code>sessionCounter</code> (replaced by the
        addition of the active session count and the expired session count,
        as a reasonable approximation) and <code>duplicates</code> (which
        does not represent a possible event in current implementations)
        statistics from the session manager. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=68890">68890</a> Align output encoding of JSPs in the Manager webapp
        with the XML declarations in those same files. (schultz)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Update Basic authentication to implement the requirements of RFC 7617
        including the changing of the <code>trimCredentials</code> setting which
        is now defaults to <code>false</code>. Note that the
        <code>trimCredentials</code> setting will be removed in Tomcat 11.
        (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.23_(schultz)/Coyote">Coyote</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Fix bnd jar descriptor to include the OpenSSL FFM support. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Add OpenSSL FFM classes to <code>tomcat-embed-core.jar</code>. (remm)
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.22_(schultz)"><span id="Tomcat_10.1.22_(schultz)_rtext" style="float: right;">not released</span> Tomcat 10.1.22 (schultz)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.22_(schultz)/Other">Other</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Release re-built using correct JDK version.
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.21_(schultz)"><span id="Tomcat_10.1.21_(schultz)_rtext" style="float: right;">not released</span> Tomcat 10.1.21 (schultz)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.21_(schultz)/Catalina">Catalina</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Change the thread-safety mechanism for protecting StandardServer.services
        from a simple synchronized lock to a ReentrantReadWriteLock to allow
        multiple readers to operate simultaneously. Based upon a suggestion by
        Markus Wolfe. (schultz)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Improve Service connectors, Container children and Service executors
        access sync using a ReentrantReadWriteLock. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Improve handling of integer overflow if an attempt is made to upload a
        file via the Servlet API and the file is larger than
        <code>Integer.MAX_VALUE</code>. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=68862">68862</a>: Handle possible response commit when processing read
        errors. (remm)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.21_(schultz)/Coyote">Coyote</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add OpenSSL integration using the FFM API rather than Tomcat Native.
        OpenSSL support may be enabled by adding the
        <code>org.apache.catalina.core.OpenSSLLifecycleListener</code>
        listener on the <code>Server</code> element when using Java 22
        or later. (remm)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.21_(schultz)/Other">Other</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update the internal fork of Apache Commons BCEL to 6.8.2. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update the internal fork of Apache Commons Codec to 1.16.1. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to French translations. (remm)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Japanese translations by tak7iji. (remm)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Chinese translations by leeyazhou. (remm)
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.20_(schultz)"><span id="Tomcat_10.1.20_(schultz)_rtext" style="float: right;">2024-03-25</span> Tomcat 10.1.20 (schultz)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.20_(schultz)/Catalina">Catalina</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Minor performance improvement for building filter chains. Based on
        ideas from pull request <a href="https://github.com/apache/tomcat/pull/702">#702</a> by Luke Miao. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Align error handling for <code>Writer</code> and
        <code>OutputStream</code>. Ensure use of either once the response has
        been recycled triggers a <code>NullPointerException</code> provided that
        <code>discardFacades</code> is configured with the default value of
        <code>true</code>. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=68692">68692</a>: The standard thread pool implementations that are
        configured using the <code>Executor</code> element now implement
        <code>ExecutorService</code> for better support NIO2. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=68495">68495</a>: When restoring a saved POST request after a successful
        FORM authentication, ensure that neither the URI, the query string nor
        the protocol are corrupted when restoring the request body. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        After forwarding a request, attempt to unwrap the response in order to
        suspend it, instead of simply closing it if it was wrapped. Add a new
        <code>suspendWrappedResponseAfterForward</code> boolean attribute on
        <code>Context</code> to control the bahavior, defaulting to
        <code>false</code>. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=68721">68721</a>: Workaround a possible cause of duplicate class
        definitions when using <code>ClassFileTransformer</code>s and the
        transformation of a class also triggers the loading of the same class.
        (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        The rewrite valve should not do a rewrite if the output is identical
        to the input. (remm)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Add a new <code>valveSkip</code> (or <code>VS</code>) rule flag to the
        rewrite valve to allow skipping over the next valve in the Catalina
        pipeline. (remm)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Add <code>highConcurrencyStatus</code> attribute to the
        <code>SemaphoreValve</code> to optionally allow the valve to return an
        error status code to the client when a permit cannot be acquired from
        the semaphore. (remm)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add checking of the "age" of the running Tomcat instance since its
        build-date to the SecurityListener, and log a warning if the server
        is old. (schultz)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        When using the <code>AsyncContext</code>, throw an
        <code>IllegalStateException</code>, rather than allowing an
        <code>NullPointerException</code>, if an attempt is made to use the
        <code>AsyncContext</code> after it has been recycled. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.20_(schultz)/Coyote">Coyote</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Improve the HTTP/2 stream prioritisation process. If a stream uses all
        of the connection windows and still has content to write, it will now be
        added to the backlog immediately rather than waiting until the write
        attempt for the remaining content. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Add <code>threadsMaxIdleTime</code> attribute to the endpoint,
        to allow configuring the amount of time before an internal executor
        will scale back to the configured <code>minSpareThreads</code> size.
        (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Correct a regression in the support for user provided
        <code>SSLContext</code> instances that broke the
        <code>org.apache.catalina.security.TLSCertificateReloadListener</code>.
        (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.20_(schultz)/Jasper">Jasper</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add support for specifying Java 22 (with the value <code>22</code>) as
        the compiler source and/or compiler target for JSP compilation. If used
        with an Eclipse JDT compiler version that does not support these values,
        a warning will be logged and the default will used.
        (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Handle the case where the JSP engine forwards a request/response to a
        Servlet that uses an <code>OutputStream</code> rather than a
        <code>Writer</code>. This was triggering an
        <code>IllegalStateException</code> on code paths where there was a
        subsequent attempt to obtain a <code>Writer</code>. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Correctly handle the case where a tag library is packaged in a JAR file
        and the web application is deployed as a WAR file rather than an
        unpacked directory. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Prevent the web application's ClassLoader from being pinned by the JSP
        compiler if an application uses a custom XMLInputFactory. Based upon a
        suggestion from Simon Niederberger. (schultz)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.20_(schultz)/Cluster">Cluster</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Avoid updating request count stats on async. (remm)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.20_(schultz)/Other">Other</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to French translations. (remm)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Japanese translations by tak7iji. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=57130">57130</a>: Allow digest.(sh|bat) to accept password from a file
        or stdin. (csutherl/schultz)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update Checkstyle to 10.14.1. (markt)
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.19_(schultz)"><span id="Tomcat_10.1.19_(schultz)_rtext" style="float: right;">2024-02-19</span> Tomcat 10.1.19 (schultz)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.19_(schultz)/Catalina">Catalina</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Correct JPMS and OSGi meta-data for <code>tomcat-embed-core.jar</code>
        by removing reference to <code>org.apache.catalina.ssi</code> package
        that is no longer included in the JAR. Based on pull request
        <a href="https://github.com/apache/tomcat/pull/684">#684</a> by Jendrik Johannes. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Fix ServiceBindingPropertySource so that trailing <code>\r\n</code>
        sequences are correctly removed from files containing property values
        when configured to do so. Bug identified by Coverity Scan. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add improvements to the CSRF prevention filter including the ability
        to skip adding nonces for resource name and subtree URL patterns. (schultz)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Review usage of debug logging and downgrade trace or data dumping
        operations from debug level to trace. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=68089">68089</a>: Further improve the performance of request attribute
        access for <code>ApplicationHttpRequest</code> and
        <code>ApplicationRequest</code>. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=68559">68559</a>: Allow asynchronous error handling to write to the
        response after an error during asynchronous processing. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.19_(schultz)/Coyote">Coyote</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Setting a <code>null</code> value for a cookie attribute should remove
        the attribute. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Make asynchronous error handling more robust. Ensure that once a
        connection is marked to be closed, further asynchronous processing
        cannot change that. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Make asynchronous error handling more robust. Ensure that once the call
        to <code>AsyncListener.onError()</code> has returned to the container,
        only container threads can access the <code>AsyncContext</code>. This
        protects against various race conditions that would otherwise occur if
        application threads continued to access the <code>AsyncContext</code>.
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Review usage of debug logging and downgrade trace or data dumping
        operations from debug level to trace. In particular, most of the
        HTTP/2 debug logging has been changed to trace level. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Add support for user provided <code>SSLContext</code> instances
        configured on <code>SSLHostConfigCertificate</code> instances. Based on
        pull request <a href="https://github.com/apache/tomcat/pull/673">#673</a> provided by Hakan Altındağ. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Partial fix for <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=68558">68558</a>: Cache the result of converting to
        <code>String</code> for request URI, HTTP header names and the request
        <code>Content-Type</code> value to improve performance by reducing
        repeated <code>byte[]</code> to <code>String</code> conversions. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Improve error reporting to HTTP/2 clients for header processing errors
        by reporting problems at the end of the frame where the error was
        detected rather than at the end of the headers. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
       Remove the remaining reference to a stream once the stream has been
       recycled. This makes the stream eligible for garbage collection earlier
       and thereby improves scalability. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.19_(schultz)/Jasper">Jasper</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=68546">68546</a>: Generate optimal size and types for JSP imports maps,
        as suggested by John Engebretson. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Review usage of debug logging and downgrade trace or data dumping
        operations from debug level to trace. (remm)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.19_(schultz)/WebSocket">WebSocket</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Correct a regression in the fix for <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66508">66508</a> that could cause an
        <code>UpgradeProcessor</code> leak in some circumstances. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Review usage of debug logging and downgrade trace or data dumping
        operations from debug level to trace. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Ensure that WebSocket connection closure completes if the connection is
        closed when the server side has used the proprietary suspend/resume
        feature to suspend the connection. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.19_(schultz)/Web_applications">Web applications</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add support for responses in JSON format from the examples application
        RequestHeaderExample. (schultz)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.19_(schultz)/Other">Other</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Correct the remaining OSGi contract references in the manifest files to
        refer to the Jakarta EE contract names rather than the Java EE contract
        names. Based on pull request <a href="https://github.com/apache/tomcat/pull/685">#685</a> provided by Paul A. Nicolucci.
        (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update Checkstyle to 10.13.0. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update JSign to 6.0. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update the packaged version of the Tomcat Migration Tool for Jakarta EE
        to 1.0.7. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update Tomcat Native to 2.0.7. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Add strings for debug level messages. (remm)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to French translations. (remm)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Japanese translations by tak7iji. (markt)
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.18_(schultz)"><span id="Tomcat_10.1.18_(schultz)_rtext" style="float: right;">2024-01-09</span> Tomcat 10.1.18 (schultz)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.18_(schultz)/Catalina">Catalina</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=68378">68378</a>: Align extension to MIME type mappings in the global
        web.xml with those in httpd by adding
        <code>application/vnd.geogebra.slides</code> for <code>ggs</code>,
        <code>text/javascript</code> for <code>mjs</code> and
        <code>audio/ogg</code> for opus. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.18_(schultz)/Coyote">Coyote</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Refactor the <code>VirtualThreadExecutor</code> so that it can be used
        by the NIO2 connector which was using platform threads even when
        configured to use virtual threads. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Correct a regression in the fix for <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=67675">67675</a> that broke TLS key
        file parsing for PKCS#8 format keys that do not specify an explicit
        pseudo-random function and rely on the default. This typically affects
        keys generated by OpenSSL 1.0.2. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Allow multiple operations with the same name on introspected mbeans,
        fixing a regression caused by the introduction of a second
        <code>addSslHostConfig</code> method. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Relax the check that the HTTP Host header is consistent with the host
        used in the request line, if any, to make the check case insensitive
        since host names are case insensitive. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=68348">68348</a>: Add support for the partitioned attribute for cookies
        including session cookies. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.18_(schultz)/Web_Applications">Web Applications</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=68035">68035</a>: Additional fix to the Manager application to enable
        the deployment of a web application located in a Host's
        <code>appBase</code> where the web application is specified by a bare
        (no path) WAR or directory name as shown in the documentation. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.18_(schultz)/Other">Other</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update Checkstyle to 10.12.7. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update SpotBugs to 4.8.3. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to French translations. (remm)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Japanese translations by tak7iji. (markt)
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.17_(schultz)"><span id="Tomcat_10.1.17_(schultz)_rtext" style="float: right;">2023-12-12</span> Tomcat 10.1.17 (schultz)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.17_(schultz)/Catalina">Catalina</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Background processes should not be run concurrently with lifecycle
        operations of a container. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Correct unintended escaping of XML in some WebDAV responses. The XML
        list of support locks when provided in response to a PROPFIND request
        was incorrectly XML escaped. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=68227">68227</a>: Ensure that <code>AsyncListener.onComplete()</code> is
        called if <code>AsyncListener.onError()</code> calls
        <code>AsyncContext.dispatch()</code>. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=68228">68228</a>: Use a 408 status code if a read timeout occurs during
        HTTP request processing. Includes a test case based on code provided by
        adwsingh. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.17_(schultz)/Jasper">Jasper</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Code: " class="icon" src="./images/code.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=68119">68119</a>: Refactor the <code>CompositeELResolver</code> to
        improve performance during type conversion operations. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.17_(schultz)/Web_Applications">Web Applications</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Examples. Improve the error handling so snakes associated with a user
        that drops from the network are removed from the game. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.17_(schultz)/Other">Other</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=68124">68124</a>: Migrate sample.war from javax to jakarta. (lihan)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update UnboundID to 6.0.11. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update Checkstyle to 10.12.5. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update SpotBugs to 4.8.2. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update Derby to 10.17.1. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to French translations. (remm)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Japanese translations by tak7iji. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Brazilian Portuguese translations by John William
        Vicente. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Russian translations by usmazat and remm. (markt)
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.16_(schultz)"><span id="Tomcat_10.1.16_(schultz)_rtext" style="float: right;">2023-11-14</span> Tomcat 10.1.16 (schultz)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.16_(schultz)/Catalina">Catalina</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=67667">67667</a>: <code>TLSCertificateReloadListener</code> prints
        unreadable rendering of <code>X509Certificate#getNotAfter()</code>.
        (michaelo)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        The status servlet included in the manager webapp can now output
        statistics as JSON, using the <code>JSON=true</code> URL parameter.
        (remm)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Optionally allow ServiceBindingPropertySource to trim a trailing newline
        from a file containing a property-value. (schultz)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=67793">67793</a>: Ensure the original session timeout is restored after
        FORM authentication if the user refreshes a page during the FORM
        authentication process. Based on a suggestion by Mircea Butmalai.
        (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=67926">67926</a>: <code>PEMFile</code> prints unidentifiable string
        representation of ASN.1 OIDs. (michaelo)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66875">66875</a>: Ensure that setting the request attribute
        <code>jakarta.servlet.error.exception</code> is not sufficient to
        trigger error handling for the current request and response. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=68054">68054</a>: Avoid some file canonicalization calls introduced
        by the fix for <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65433">65433</a>. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=68089">68089</a>: Improve performance of request attribute access for
        <code>ApplicationHttpRequest</code> and <code>ApplicationRequest</code>.
        (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Use a 400 status code to report an error due to a bad request (e.g. an
        invalid trailer header) rather than a 500 status code. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Ensure that an <code>IOException</code> during the reading of the
        request triggers always error handling, regardless of whether the
        application swallows the exception. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.16_(schultz)/Coyote">Coyote</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66670">66670</a>: Add
        <code>SSLHostConfig#certificateKeyPasswordFile</code> and
        <code>SSLHostConfig#certificateKeystorePasswordFile</code>. (michaelo)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        When calling
        <code>SSLHostConfigCertificate.setCertificateKeystore(ks)</code>,
        automatically call
        <code>setCertificateKeystoreType(ks.getType())</code>. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=67628">67628</a>: Clarify how the <code>ciphers</code> attribute of the
        <code>SSLHostConfig</code> is used. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=67666">67666</a>: Ensure TLS connectors using PEM files either work with
        the <code>TLSCertificateReloadListener</code> or, in the rare case that
        they do not, log a warning on Connector start. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=67675">67675</a>: Support a wider range of KDF and ciphers for PEM files
        than the combinations supported by the JVM by default. Specifically,
        support the OpenSSL default of HmacSHA256 and DES-EDE3-CBC. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=67927">67927</a>: Reloading TLS configuration can cause the Connector to
        refuse new connections or the JVM to crash. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=67938">67938</a>: Correct handling of large TLS client hello messages
        that were causing the TLS handshake to fail. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=68026">68026</a>: Convert selected <code>MessageByte</code> values to
        String when first accessed to speed up subsequent accesses and reduce
        garbage collection. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.16_(schultz)/Jasper">Jasper</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=68068">68068</a>: Performance improvement for EL. Based on a suggestion
        by John Engebretson. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.16_(schultz)/WebSocket">WebSocket</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Correct missing metadata in the MANIFEST of the for WebSocket client API
        JAR file. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.16_(schultz)/Web_applications">Web applications</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=68035">68035</a>: Correct a regression in the fix for <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=56248">56248</a>
        that prevented deployment via the Manager of a WAR or directory that was
        already present in the <code>appBase</code> or a context file that was
        already present in the <code>xmlBase</code>. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.16_(schultz)/Other">Other</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=67538">67538</a>: Make use of Ant's <code>&lt;javaversion /&gt;</code> task
        to enfore the mininum Java build version. (michaelo)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update Checkstyle to 10.12.4. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update JaCoCo to 0.8.11. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update SpotBugs to 4.8.0. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update BND to 7.0.0. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        The minimum Java version required to build Tomcat has been raised to
        Java 17. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update the OWB module to Apache OpenWebBeans 4.0.0. (remm)
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.15_(schultz)"><span id="Tomcat_10.1.15_(schultz)_rtext" style="float: right;">2023-10-16</span> Tomcat 10.1.15 (schultz)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.15_(schultz)/Coyote">Coyote</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=67670">67670</a>: Fix regression with HTTP compression after code
        refactoring. (remm)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.15_(schultz)/jdbc-pool">jdbc-pool</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=67664">67664</a>: Correct a regression in the clean-up of unnecessary
        use of fully qualified class names in 10.1.14 that broke the jdbc-pool.
        (markt)
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.14_(schultz)"><span id="Tomcat_10.1.14_(schultz)_rtext" style="float: right;">2023-10-10</span> Tomcat 10.1.14 (schultz)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.14_(schultz)/Catalina">Catalina</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65770">65770</a>: Provide a lifecycle listener that will automatically
        reload TLS configurations a set time before the certificate is due to
        expire. This is intended to be used with third-party tools that
        regularly renew TLS certificates. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Fix handling of an error reading a context descriptor on deployment.
        (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Fix rewrite rule qsd (query string discard) being ignored if qsa was
        also use, while it should instead take precedence. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=67472">67472</a>: Send fewer CORS-related headers when CORS is not
        actually being engaged. (schultz)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improve handling of failures within <code>recycle()</code> methods.
        (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.14_(schultz)/Coyote">Coyote</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=67198">67198</a>: Ensure that the AJP connector attribute
        <code>tomcatAuthorization</code> takes precedence over the
        <code>tomcatAuthentication</code> attribute when processing an
        <code>auth_type</code> attribute received from a proxy server. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=67235">67235</a>: Fix a <code>NullPointerException</code> when an
        <code>AsyncListener</code> handles an error with a dispatch rather than
        a complete. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        When an error occurs during asynchronous processing, ensure that the
        error handling process is only triggered once per asynchronous cycle.
        (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Fix logic issue trying to match no argument method in IntropectionUtil.
        (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Improve thread safety around readNotify and writeNotify in the NIO2
        endpoint. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Avoid rare thread safety issue accessing message digest map. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Improve statistics collection for upgraded connections under load.
        (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Align validation of HTTP trailer fields with standard fields. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Improvements to HTTP/2 overhead protection. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.14_(schultz)/Jasper">Jasper</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=67080">67080</a>: Improve performance of EL expressions in JSPs that use
        implicit objects. Based on suggestions by John Engebretson, Anurag Dubey
        and Christopher Schultz. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.14_(schultz)/Other">Other</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update the internal fork of Apache Commons FileUpload to 7a8c324
        (2023-09-16, 1.x-SNAPSHOT). Due to significant refactoring in the 2.x
        branch requiring additional Commons IO dependencies, Tomcat has switched
        to tracking the 1.x branch. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add the <code>Bundle-License</code> header to the JAR manifest for all
        Tomcat JARs. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update UnboundID to 6.0.10. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update Checkstyle to 10.12.3. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update Tomcat Native to 2.0.6. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update Commons Pool to 2.12.0. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=67611">67611</a>: Correct the download link in BUILDING.txt. (lihan)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to French translations. (remm)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Japanese translations by tak7iji. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Russian translations by usmazat. (markt)
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.13_(markt)"><span id="Tomcat_10.1.13_(markt)_rtext" style="float: right;">2023-08-25</span> Tomcat 10.1.13 (markt)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.13_(markt)/Catalina">Catalina</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        If an application or library sets both a non-500 error code and the
        <code>jakarta.servlet.error.exception</code> request attribute, use the
        provided error code during error page processing rather than assuming an
        error code of 500. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Update code comments and Tomcat output to use MiB for 1024 * 1024 bytes
        and KiB for 1024 bytes rather than MB and kB. (martk)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Avoid protocol relative redirects in FORM authentication. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.13_(markt)/Web_applications">Web applications</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Documentation. Update documentation to use MiB for 1024 * 1024 bytes and
        KiB for 1024 bytes rather than MB and kB. (martk)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.13_(markt)/Other">Other</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Chinese translations. (lihan)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to French translations. (remm)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Japanese translations by tak7iji. (markt)
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.12_(markt)"><span id="Tomcat_10.1.12_(markt)_rtext" style="float: right;">2023-08-14</span> Tomcat 10.1.12 (markt)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.12_(markt)/Catalina">Catalina</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66680">66680</a>: When serializing a session during the session
        presistence process, do not log a warning that null Principals are not
        serializable. Pull request <a href="https://github.com/apache/tomcat/pull/638">#638</a> provided by tsryo. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Catch <code>NamingException</code> in <code>JNDIRealm#getPrincipal</code>.
        It is used in Java up to 17 to signal closed connections. (fschumacher)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66822">66822</a>: Use the same naming format in log messages for
        Connector instances as the associated ProtocolHandler instance. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        The parts count should also lower the actual
        <code>maxParameterCount</code> used for parsing parameters if parts are
        parsed first. (remm)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.12_(markt)/Coyote">Coyote</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Correct a regression introduced in 10.1.11 and use the correct
        constant when constructing the default value for the
        <code>certificateKeystoreFile</code> attribute of an
        <code>SSLHostConfigCertificate</code> instance. (markt)
      </li>
      <li><img alt="Code: " class="icon" src="./images/code.gif">
        Refactor HTTP/2 implementation to reduce pinning when using virtual
        threads. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Pass through ciphers referring to an OpenSSL profile, such as
        <code>PROFILE=SYSTEM</code> instead of producing an error trying to
        parse it. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66841">66841</a>: Ensure that <code>AsyncListener.onError()</code> is
        called after an error during asynchronous processing with HTTP/2.
        (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66842">66842</a>: When using asynchronous I/O (the default), include
        DATA frames when calculating the HTTP/2 overhead count to ensure that
        connections are not prematurely terminated. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Correct a race condition that could cause spurious RST messages to be
        sent after the response had been written to an HTTP/2 stream. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.12_(markt)/WebSocket">WebSocket</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66681">66681</a>: Fix a <code>NullPointerException</code> when flushing
        batched messages with compression enabled using
        <code>permessage-deflate</code>. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.12_(markt)/jdbc-pool">jdbc-pool</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Fix the <code>releaseIdleCounter</code> does not increment when testAllIdle
        releases them. Pull request <a href="https://github.com/apache/tomcat/pull/241">#241</a> provided by Arun Chaitanya Miriappalli
        (lihan)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Fix the <code>ConnectionState</code> state will be inconsistent with actual
        state on the connection when an exception occurs while writing. Pull request
        <a href="https://github.com/apache/tomcat/pull/643">#643</a> provided by Wenjun Xiao. (lihan)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.12_(markt)/Other">Other</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update NSIS to 3.09. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update Checkstyle to 10.12.2. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to French translations. (remm)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Japanese translations. Contributed by tak7iji and
        Shirayuking. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66829">66829</a>: Fix quoting so users can use the <code>_RUNJAVA</code>
        environment variable as intended on Windows when the path to the Java
        executable contains spaces. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66834">66834</a>: Correct the OSGi contract references in the manifest
        files to refer to the Jakarta EE contract names rather than the Java EE
        contract names. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update Tomcat Native to 2.0.5. (markt)
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.11_(schultz)"><span id="Tomcat_10.1.11_(schultz)_rtext" style="float: right;">2023-07-10</span> Tomcat 10.1.11 (schultz)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.11_(schultz)/Catalina">Catalina</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=59232">59232</a>: Add
        <code>org.apache.catalina.core.ContextNamingInfoListener</code>,
        a listener which creates context naming information environment entries.
        (michaelo)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66665">66665</a>: Add
        <code>org.apache.catalina.core.PropertiesRoleMappingListener</code>,
        a listener which populates the context's role mapping from a properties
        file. (michaelo)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Fix an edge case where intra-web application symlinks would be followed
        if the web applications were deliberately crafted to allow it even when
        <code>allowLinking</code> was set to <code>false</code>. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Add utlity config file resource lookup on <code>Context</code> to allow
        looking up resources from the webapp (prefixed with
        <code>webapp:</code>) and make the resource lookup API more visible.
        (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Fix potential database connection leaks in
        <code>DataSourceUserDatabase</code> identified by Coverity Scan. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Make parsing of <code>ExtendedAccessLogValve</code> patterns more
        robust. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.11_(schultz)/Coyote">Coyote</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66627">66627</a>: Restore the documented behaviour of
        <code>MessageBytes.getType()</code> that it returns the type of the
        original content rather than reflecting the most recent conversion.
        (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66635">66635</a>: Correct certificate logging on start-up so it
        differentiates between keystore based keys/certificates and PEM file
        based keys/certificates and logs the relevant information for each.
        (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Refactor blocking reads and writes for the NIO connector to remove
        code paths that could allow a notification from the Poller to be missed
        resuting in a timeout rather than the expected read or write. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Refactor waiting for an HTTP/2 stream or connection window update to
        handle spurious wake-ups during the wait. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.11_(schultz)/WebSocket">WebSocket</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Improve handling of error conditions for the WebSocket server,
        particularly during Tomcat shutdown. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Correct a regression in the fix for <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66574">66574</a> that meant the
        WebSocket session could return false for <code>onOpen()</code> before
        the <code>onClose()</code> event had been completed. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.11_(schultz)/Web_applications">Web applications</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Documentation. Expand the security guidance to cover the embedded use
        case and add notes on the uses made of the <code>java.io.tmpdir</code>
        system property. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66662">66662</a>: Documentation. Fix a typo in the name of the
        <strong>algorithms</strong> attribute in the configuration section for
        the Digest authentication valve. Pull request <a href="https://github.com/apache/tomcat/pull/629">#629</a> provided by
        gohilmca. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.11_(schultz)/Other">Other</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Include the Windows specific binary distributions in the files uploaded
        to Maven Central. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to French translations. (remm)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Japanese translations. Contributed by tak7iji. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update UnboundID to 6.0.9. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update Checkstyle to 10.12.1. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update BND to 6.4.1. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update JSign to 5.0. (markt/rjung)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Align documentation for maxParameterCount to match hard-coded defaults.
        Contributed by Michal Sobkiewicz. (schultz)
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.10_(schultz)"><span id="Tomcat_10.1.10_(schultz)_rtext" style="float: right;">2023-06-12</span> Tomcat 10.1.10 (schultz)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.10_(schultz)/Catalina">Catalina</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Code: " class="icon" src="./images/code.gif">
        Move the management of the utility executor from the
        <code>init()</code>/<code>destroy()</code> methods of components to the
        <code>start()</code>/<code>stop()</code> methods. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add <code>org.apache.catalina.core.StandardVirtualThreadExecutor</code>,
        a virtual thread based executor that may be used with one or more
        Connectors to process requests received by those Connectors using
        virtual threads. This Executor requires a minimum Java version of Java
        21. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66513">66513</a>: Add a per session Semaphore to the
        <code>PersistentValve</code> that ensures that, within a single Tomcat
        instance, there is no more than one concurrent request per session. Also
        expand the debug logging to include whether a request bypasses the Valve
        and the reason if a request fails to obtain the per session Semaphore.
        (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66609">66609</a>: Ensure that the default servlet correctly escapes
        file names in directory listings when using XML output. Based on pull
        request <a href="https://github.com/apache/tomcat/pull/621">#621</a> by Alex Kachanov. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
       <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66618">66618</a>: Add a numeric last modified field to the XML directory
       listings produced by the default servlet to enable sorting in the XSLT.
       Pull request <a href="https://github.com/apache/tomcat/pull/622">#622</a> by Alex Kachanov. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66621">66621</a>: Attempts to lock a collection with WebDAV may
        incorrectly fail if a child collection has an expired lock. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66622">66622</a>: Deprecate the <code>xssProtectionEnabled</code>
        setting from the <code>HttpHeaderSecurityFilter</code> and change the
        default value to <code>false</code> as support for the associated HTTP
        header has been removed from all major browsers. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.10_(schultz)/Coyote">Coyote</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update the HTTP/2 implementation to use the prioritization scheme
        defined in RFC 9218 rather than the one defined in RFC 7540.
        (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66602">66602</a>: not sending WINDOW_UPDATE when dataLength is ZERO
        on call SwallowedDataFramePayload. Pull request #619 by
        ledefe. (lihan)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.10_(schultz)/Other">Other</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update to Commons Daemon 1.3.4. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to French translations. (remm)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update Checkstyle to 10.12.0. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update the packaged version of the Apache Tomcat Native Library to 2.0.4
        to pick up the Windows binaries built with with OpenSSL 3.0.9. (markt)
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.9_(schultz)"><span id="Tomcat_10.1.9_(schultz)_rtext" style="float: right;">2023-05-19</span> Tomcat 10.1.9 (schultz)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.9_(schultz)/Catalina">Catalina</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66567">66567</a>: Fix missing <code>IllegalArgumentException</code>
        after the Tomcat code was converted to using URI instead of URL. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Escape timestamp output in <code>AccessLogValve</code> if a
        <code>SimpleDateFormat</code> is used which contains verbatim
        characters that need escaping. (rjung)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Change output of vertical tab in <code>AccessLogValve</code> from
        <code>\v</code> to <code>\u000b</code>. (rjung)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Improve performance of escaping in <code>AccessLogValve</code>
        roughly by a factor of two. (rjung)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Improve <code>JsonAccessLogValve</code>: support more patterns
        like for headers and attributes. Those will be logged as sub objects.
        (rjung)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://github.com/apache/tomcat/pull/613">#613</a>: Fix possible partial corrupted file copies when using
        file locking protection or the manager servlet. Submitted
        by Jack Shirazi. (remm)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add RateLimitFilter which can be used to mitigate DoS and Brute Force
        attacks. (isapir)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.9_(schultz)/Coyote">Coyote</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add support for a new character set, <code>gb18030-2022</code> -
        introduced in Java 21, to the character set caching mechanism. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Fix an edge case in HTTP header parsing and ensure that HTTP headers
        without names are treated as invalid. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Deprecate the HTTP Connector settings <code>rejectIllegalHeader</code>
        and <code>allowHostHeaderMismatch</code> as they have been removed in
        Tomcat 11 onwards. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66591">66591</a>: Fix a regression introduced in the fix for
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66512">66512</a> that meant that an AJP Send Headers was not sent for
        responses where no HTTP headers were set. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.9_(schultz)/Jasper">Jasper</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66582">66582</a>: Account for EL having stricter requirements for static
        imports than JSPs when adding JSP static imports to the EL context.
        (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.9_(schultz)/WebSocket">WebSocket</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66574">66574</a>: Refactor WebSocket session close to remove the lock on
        the <code>SocketWrapper</code> which was a potential cause of deadlocks
        if the application code used simulated blocking. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66575">66575</a>: Avoid unchecked use of the backing array of a
        buffer provided by the user in the compression transformation. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Improve exception handling when flushing batched messages during
        WebSocket session close. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66581">66581</a>: Update <code>AsyncChannelGroupUtil</code> to align it
        with the current defaults for AsynchronousChannelGroup. Pull request
        <a href="https://github.com/apache/tomcat/pull/612">#612</a> by Matthew Painter. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.9_(schultz)/Other">Other</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to French translations. (remm)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Chinese translations. (lihan)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update Checkstyle to 10.10.0. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update Jacoco to 0.8.10. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update the packaged version of the Tomcat Migration Tool for Jakarta EE
        to 1.0.7. (markt)
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.8_(schultz)"><span id="Tomcat_10.1.8_(schultz)_rtext" style="float: right;">2023-04-19</span> Tomcat 10.1.8 (schultz)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.8_(schultz)/Catalina">Catalina</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65995">65995</a>: Implement RFC 9239 and use
        <code>text/javascript</code> as the media type for JavaScript rather
        than <code>application/javascript</code>. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add an access log valve that uses a json format. Based on pull request
        <a href="https://github.com/apache/tomcat/pull/539">#539</a> provided by Thomas Meyer. (remm)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Harden the FORM authentication process against DoS attacks by using a
        reduced session timeout if the FORM authentication process creates a
        session. The duration of this timeout is configured by the
        <code>authenticationSessionTimeout</code> attribute of the FORM
        authenticator. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66527">66527</a>: Correct the Javadoc for the
        <code>Tomcat.addWebapp()</code> methods that incorrectly stated that the
        <code>docBase</code> parameter could be a relative path. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66524">66524</a> Correct eviction ordering in WebResource cache to
        be LRU as intended. (schultz)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Use server.xml to reduce the default value of
        <code>maxParameterCount</code> from 10,000 to 1,000. If not configured
        in server.xml, the default remains 10,000. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Update Digest authentication support to align with RFC 7616. This adds a
        new configuration attribute, <code>algorithms</code>, to the
        <code>DigestAuthenticator</code> with a default of
        <code>SHA-256,MD5</code>. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Add support code for custom user attributes in <code>RealmBase</code>.
        Based on code from <a href="https://github.com/apache/tomcat/pull/473">#473</a> by Carsten Klein. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Expand the set of HTTP request headers considered sensitive that should
        be skipped when generating a response to a <code>TRACE</code> request.
        This aligns with 11.0.x. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66541">66541</a>: Improve handling for cached resources for resources
        that use custom URL schemes. The scheme specific <code>equals()</code>
        and <code>hashCode()</code> algorithms, if present, will now be used for
        URLs for these resources. This addresses a potential performance issue
        with some OSGi custom URL schemes that can trigger potentially slow DNS
        lookups in some configurations. Based on a patch provided by Tom
        Whitmore. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        When using a custom session manager deployed as part of the web
        application, avoid <code>ClassNotFoundException</code>s when validating
        session IDs extracted from requests. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66543">66543</a>: Give <code>StandardContext#fireRequestDestroyEvent</code>
         its own log message. (fschumacher)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66554">66554</a>: Initialize Random during server initialization to
        avoid possible JVM thread creation in the webapp context on some
        platforms. (remm)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Make the server utility executor available to webapps using a Servlet
        context attribute named
        <code>org.apache.tomcat.util.threads.ScheduledThreadPoolExecutor</code>. (remm)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.8_(schultz)/Coyote">Coyote</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        JSON filter should support specific escaping for common special
        characters as defined in RFC 8259. Based on code submitted by
        Thomas Meyer. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66511">66511</a>: Fix <code>GzipOutputFilter</code> (used for compressed
        HTTP responses) when used with direct buffers. Patch suggested by Arjen
        Poutsma. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66512">66512</a>: Align AJP handling of invalid HTTP response headers
        (they are now removed from the response) with HTTP. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66530">66530</a>: Correct a regression in the fix for bug
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66442">66442</a> that meant that streams without a response body did not
        decrement the active stream count when completing leading to
        <code>ERR_HTTP2_SERVER_REFUSED_STREAM</code> for some connections.
        (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.8_(schultz)/Jasper">Jasper</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Fix bug that meant some instances of coercing a
        <code>LambdaExpression</code> to a functional interface invocation
        failed. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66536">66536</a>: Fix parsing of tag files that meant that tag
        directives could be ignored for some tag files. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.8_(schultz)/Cluster">Cluster</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66535">66535</a>: Redefine the <code>maxValidTime</code> attribute of
        <code>FarmWarDeployer</code> to be the maximum time allowed between
        receiving parts of a transferred file before the transfer is cancelled
        and the associated resources cleaned-up. A new warning message will be
        logged if the file transfer is cancelled. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.8_(schultz)/WebSocket">WebSocket</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66508">66508</a>: When using WebSocket with NIO2, avoid waiting for
        a timeout before sending the close frame if an I/O error occurs during a
        write. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66548">66548</a>: Expand the validation of the value of the
        <code>Sec-Websocket-Key</code> header in the HTTP upgrade request that
        initiates a WebSocket connection. The value is not decoded but it is
        checked for the correct length and that only valid characters from the
        base64 alphabet are used. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.8_(schultz)/Web_applications">Web applications</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66542">66542</a>: Documentation. Update the JNDI documentation to
        replace references to JavaMail with references to Jakarta Mail. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.8_(schultz)/Other">Other</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to French translations. (remm)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Japanese translations. Contributed by Shirayuking and
        tak7iji. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Chinese translations. Contributed by totoo. (markt)
      </li>
      <li><img alt="Code: " class="icon" src="./images/code.gif">
        Refactor code using <code>MD5Encoder</code> to use
        <code>HexUtils.toHexString()</code>. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66507">66507</a>: Fix a bug that <code>$JAVA_OPTS</code> is not passed
        to the jvm in <code>catalina.sh</code> when calling <code>version</code>.
        Patch suggested by Eric Hamilton. (lihan)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update the internal fork of Commons DBCP to f131286 (2023-03-08,
        2.10.0-SNAPSHOT). This corrects a regression introduced in 10.1.5.
        (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Improve the error messages if <code>JRE_HOME</code> or
        <code>JAVA_HOME</code> are not set correctly. On windows, align the
        handling of <code>JRE_HOME</code> and <code>JAVA_HOME</code> for the
        start-up scripts and the service install script. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update to the Eclipse JDT compiler 4.27. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update UnboundID to 6.0.8. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update Checkstyle to 10.9.3. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update Jacoco to 0.8.9. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Enhance PEMFile to load from an InputStream. Patch provided by
        Romain Manni-Bucau. (schultz)
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.7_(schultz)"><span id="Tomcat_10.1.7_(schultz)_rtext" style="float: right;">2023-03-03</span> Tomcat 10.1.7 (schultz)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.7_(schultz)/General">General</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Fix a bug that memory allocation is larger than limit in
        <code>SynchronizedStack</code> to reduce memory footprint. (lihan)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.7_(schultz)/Catalina">Catalina</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add support for <code>txt:</code> and <code>rnd:</code> rewrite map
        types from mod_rewrite. Based on a pull request <a href="https://github.com/apache/tomcat/pull/591">#591</a>
        provided by Dimitrios Soumis. (remm)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Provide a more appropriate response (501 rather than 400) when rejecting
        an HTTP request using the CONNECT method. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66488">66488</a>: Correct a regression introduced in the fix for bug
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66196">66196</a> that meant that the HTTP headers and/or request line
        could get corrupted (one part overwriting another part) within a single
        request. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66491">66491</a>: Revert the switch to using the ServiceLoader mechanism
        to load the custom URL protocol handlers that Tomcat uses. The original
        system property based approach has been restored. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.7_(schultz)/Coyote">Coyote</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add a check for the validity of the scheme pseudo-header in HTTP/2.
        (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66482">66482</a>: Restore inline state after async operation in NIO2,
        to account the fact that unexpected exceptions are sometimes thrown
        by the implementation. Patch submitted by zhougang. (remm)
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.6_(schultz)"><span id="Tomcat_10.1.6_(schultz)_rtext" style="float: right;">2023-02-24</span> Tomcat 10.1.6 (schultz)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.6_(schultz)/Catalina">Catalina</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Allow a Valve to access cookies from a request that cannot be mapped to
        a Context. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66438">66438</a>: Correct names of Jakarta modules in JPMS metadata.
        (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Switch to using the ServiceLoader mechanism to load the custom URL
        protocol handlers that Tomcat uses. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Avoid possible ISE when scanning from bad JAR URLs, to restore the
        previous behavior following the removal of Java 9+ reflection code which
        caught the ISE. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Refactor uses of <code>String.replaceAll()</code> to use
        <code>String.replace()</code> where regular expressions where not being
        used. Pull request <a href="https://github.com/apache/tomcat/pull/581">#581</a> provided by Andrei Briukhov. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add error report valve that allows redirecting to of proxying from an
        external web server. Based on code and ideas from pull request
        <a href="https://github.com/apache/tomcat/pull/506">#506</a> provided by Max Fortun. (remm)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66470">66470</a>: Add the Shared Address Space defined by RFC 6598
        (**********/10) to the regular expression used to identify internal
        proxies for the <code>RemoteIpFilter</code> and
        <code>RemoteIpValve</code>. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66471">66471</a>: Fix JSessionId secure attribute missing When
        <code>RemoteIpFilter</code> determines that this request was submitted
        via a secure channel. (lihan)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.6_(schultz)/Coyote">Coyote</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Log basic information for each configured TLS certificate when Tomcat
        starts. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66442">66442</a>: When an HTTP/2 response must not include a body,
        ensure that the end of stream flag is set on the headers frame and that
        no data frame is sent. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66455">66455</a>: Fix the cause of a potential
        <code>ClassCastException</code> when processing a
        <code>WINDOW_UPDATE</code> frame on an HTTP/2 connection where the flow
        control window for the overall connection has been exhausted. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Fix a regression introduced in 10.1.0-M17 that prevented HTTP/2
        connections from timing out when using a Connector configured with
        <code>useAsyncIO=true</code> (the default). (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Provided dedicated loggers
        (<code>org.apache.tomcat.util.net.NioEndpoint.certificate</code> /
        <code>org.apache.tomcat.util.net.Nio2Endpoint.certificate</code>) for
        logging of configured TLS certificates. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.6_(schultz)/Jasper">Jasper</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66419">66419</a>: Fix calls from expression language to a method that
        accepts varargs when only one argument was passed. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66441">66441</a>: Make imports of static fields in JSPs visible to any
        EL expressions used on the page. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.6_(schultz)/Web_applications">Web applications</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66429">66429</a>: Documentation. Limit access to the documentation web
        application to localhost by default. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66429">66429</a>: Examples. Limit access to the examples web application
        to localhost by default. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.6_(schultz)/Other">Other</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update BND to 6.4.0. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Korean translations. (woonsan)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update the packaged version of the Apache Tomcat Native Library to 2.0.3
        to pick up the Windows binaries built with with OpenSSL 3.0.8. (markt)
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.5_(markt)"><span id="Tomcat_10.1.5_(markt)_rtext" style="float: right;">2023-01-13</span> Tomcat 10.1.5 (markt)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.5_(markt)/Catalina">Catalina</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66388">66388</a>: Correct a regression in the refactoring that replaced
        the use of the <code>URL</code> constructors. The regression broke
        lookups for resources that contained one or more characters in their
        name that required escaping when used in a URI path. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66392">66392</a>: Change the default value of <code>AccessLogValve</code>'s
        file encoding to UTF-8 and update documentation. (lihan)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66393">66393</a>: Align <code>ExtendedAccessLogValve</code>'s x-P(XXX) with the
        documentation. (lihan)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.5_(markt)/Coyote">Coyote</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        When resetting an HTTP/2 stream because the final response has been
        generated before the request has been fully read, use the HTTP/2 error
        code <code>NO_ERROR</code> so that client does not discard the response.
        Based on a suggestion by Lorenzo Dalla Vecchia. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66385">66385</a>: Correct a bug in HTTP/2 where a non-blocking read for
        a new frame with the NIO2 connector was incorrectly made using the read
        timeout leading to unexpected stream closure. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.5_(markt)/Jasper">Jasper</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66370">66370</a>: Change the default of the
        <code>org.apache.el.GET_CLASSLOADER_USE_PRIVILEGED</code> system
        property to <code>true</code> unless the EL library is running on Tomcat
        in which case the default remains <code>false</code> as the EL library
        is already called from within a privileged block and skipping the
        unnecessary privileged block improves performance. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add support for specifying Java 21 (with the value <code>21</code>) as
        the compiler source and/or compiler target for JSP compilation. If used
        with an Eclipse JDT compiler version that does not support these values,
        a warning will be logged and the default will used.
        (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.5_(markt)/Other">Other</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update the internal fork of Apache Commons BCEL to 2ee2bff (2023-01-03,
        6.7.1-SNAPSHOT). (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update the internal fork of Apache Commons Codec to 3eafd6c (2023-01-03,
        1.16-SNAPSHOT). (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update the internal fork of Apache Commons FileUpload to 34eb241
        (2023-01-03, 2.0-SNAPSHOT). (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update the internal fork of Apache Commons DBCP to f131286 (2023-01-03,
        2.10.0-SNAPSHOT). (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Japanese translations. Contributed by Shirayuking.
        (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Portuguese translations. Contributed by Guilherme
        Cust&oacute;dio. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update to the Eclipse JDT compiler 4.26. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update Checkstyle to 10.6.0. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update Unboundid to 6.0.7. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update SpotBugs to 4.7.3. (markt)
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.4_(markt)"><span id="Tomcat_10.1.4_(markt)_rtext" style="float: right;">2022-12-09</span> Tomcat 10.1.4 (markt)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.4_(markt)/Other">Other</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update the packaged version of the Apache Tomcat Migration Tool for
        Jakarta EE to 1.0.6. (markt)
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.3_(markt)"><span id="Tomcat_10.1.3_(markt)_rtext" style="float: right;">not released</span> Tomcat 10.1.3 (markt)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.3_(markt)/Catalina">Catalina</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Correct the default implementation of
        <code>HttpServletRequest.isTrailerFieldsReady()</code> to return
        <code>true</code> so it is consistent with the default implementation of
        <code>HttpServletRequest.getTrailerFields()</code> and with the Servlet
        API provided by the Jakarta EE project. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Refactor <code>WebappLoader</code> so it only has a runtime dependency
        on the migration tool for Jakarta EE if configured to use the converter
        as classes are loaded. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Improve the behavior of the credential handler attribute that is set in
        the Servlet context so that it actually reflects what is used during
        authentication. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66359">66359</a>: Update javadoc for RemoteIpValve and RemoteIpFilter with
        correct <code>protocolHeader</code> default value of "X-Forwarded-Proto".
        (lihan)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.3_(markt)/Coyote">Coyote</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        When an HTTP/2 stream was reset, the current active stream count was not
        reduced. If enough resets occurred on a connection, the current active
        stream count limit was reached and no new streams could be created on
        that connection. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.3_(markt)/Web_applications">Web applications</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66348">66348</a>: Update the JARs listed in the class loader
        documentation and note which ones are optional. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Documentation. Replace references in the application developer's guide
        to CVS with more general references to a source code control system.
        (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.3_(markt)/Other">Other</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Code: " class="icon" src="./images/code.gif">
        Refactor code base to replace use of URL constructors. While they are
        deprecated in Java 20 onwards, the reasons for deprecation are valid for
        all versions so move away from them now. (markt)
      </li>
      <li><img alt="Code: " class="icon" src="./images/code.gif">
        Refine the Tomcat native image metadata to avoid including unintended
        non-Tomcat resources. Pull request <a href="https://github.com/apache/tomcat/pull/569">#569</a> provided by S&eacute;bastien
        Deleuze. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update the internal fork of Apache Commons BCEL to b015e90 (2022-11-28,
        6.7.0-RC1). (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update the internal fork of Apache Commons Codec to ae32a3f (2022-11-29,
        1.16-SNAPSHOT). (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update to Commons Daemon 1.3.3. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update the internal fork of Apache Commons FileUpload to aa8eff6
        (2022-11-29, 2.0-SNAPSHOT). (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Japanese translations. Contributed by Shirayuking and
        tak7iji. (markt)
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.2_(markt)"><span id="Tomcat_10.1.2_(markt)_rtext" style="float: right;">2022-11-14</span> Tomcat 10.1.2 (markt)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.2_(markt)/Catalina">Catalina</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66209">66209</a>: Add a configuration option to allow bloom filters used
        to index JAR files to be retained for the lifetime of the web
        application. Prior to this addition, the indexes were always flushed by
        the periodic calls to <code>WebResourceRoot.gc()</code>. As part of this
        addition, configuration of archive indexing moves from
        <code>Context</code> to <code>WebResourceRoot</code>. Based on a patch
        provided by Rahul Jaisimha. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66330">66330</a>: Correct a regression introduced when fixing
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=62897">62897</a> that meant any value configured for
        <code>skipMemoryLeakChecksOnJvmShutdown</code> on the
        <code>Context</code> was ignored and the default was always used.
        (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66331">66331</a>: Fix a regression in refactoring for <code>Stack</code>
        on the <code>SystemLogHandler</code> which caught incorrect exception.
        (lihan)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66338">66338</a>: Fix a regression that caused a nuance in refactoring
        for <code>ErrorReportValve</code>. (lihan)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Escape values used to construct output for the
        <code>JsonErrorReportValve</code> to ensure that it always outputs valid
        JSON. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.2_(markt)/Coyote">Coyote</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Correct the date format used with the expires attribute of HTTP cookies.
        A single space rather than a single dash should be used to separate the
        day, month and year components to be compliant with RFC 6265. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Include the name of the current stream state in the error message when a
        stream is cancelled due to an attempt to write to the stream when it is
        in a state that does not permit writes. (markt)
      </li>
      <li><img alt="Code: " class="icon" src="./images/code.gif">
        NIO writes never return -1 so refactor <code>CLOSED_NIO_CHANNEL</code>
        not to do so and remove checks for this return value. Based on
        <a href="https://github.com/apache/tomcat/pull/562">#562</a> by tianshuang. (markt)
      </li>
      <li><img alt="Code: " class="icon" src="./images/code.gif">
        Remove unnecessary code that exposed the <code>asyncTimeout</code> to
        components that never used it. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.2_(markt)/Jasper">Jasper</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66294">66294</a>: Make the use of a privileged block to obtain the
        thread context class loader added to address <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=62080">62080</a> optional
        and disabled by default. This is now controlled by the
        <code>org.apache.el.GET_CLASSLOADER_USE_PRIVILEGED</code> system
        property. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66317">66317</a>: Fix for Lambda coercion security manager missing
        privileges. Based on pull request #557 by Isaac Rivera Rivas (lihan)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66325">66325</a>: Fix concurrency issue in evaluation of expression
        language containing lambda expressions. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.2_(markt)/jdbc-pool">jdbc-pool</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66346">66346</a>: Ensure all JDBC pool JARs are reproducible. Pull
        request <a href="https://github.com/apache/tomcat/pull/566">#566</a> provided by John Neffenger. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.2_(markt)/Other">Other</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update to Commons Daemon 1.3.2. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66323">66323</a>: Move module start up parameters from
        <code>JDK_JAVA_OPTIONS</code> to <code>JAVA_OPTS</code> now that the
        minimum Java version is 11 and these options are always required. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Chinese translations. Contributed by DigitalCat and
        lihan. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to French translations. Contributed by Mathieu Bouchard.
        (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Japanese translations. Contributed by Shirayuking.
        (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Correct a regression in the removal of the APR connector that broke
        Graal native image support. Pull request <a href="https://github.com/apache/tomcat/pull/564">#564</a> provided by
        S&eacute;bastien Deleuze. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update the packaged version of the Apache Tomcat Native Library to 2.0.2
        to pick up the Windows binaries built with with OpenSSL 3.0.7. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update the packaged version of the Apache Tomcat Migration Tool for
        Jakarta EE to 1.0.5. (markt)
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.1_(markt)"><span id="Tomcat_10.1.1_(markt)_rtext" style="float: right;">2022-10-11</span> Tomcat 10.1.1 (markt)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.1_(markt)/Catalina">Catalina</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Update the <code>RewriteValve</code> to perform pattern matching using
        dotall mode to avoid unexpected behaviour if the URL includes encoded
        line terminators. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.1_(markt)/Coyote">Coyote</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66276">66276</a>: Fix incorrect class cast when adding
        a descendant of HTTP/2 streams. (lihan)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66281">66281</a>: Fix unexpected timeouts that may appear as client
        disconnections when using HTTP/2 and NIO2. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Enforce the requirement of RFC 7230 onwards that a request with a
        malformed <code>content-length</code> header should always be rejected
        with a 400 response. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.1_(markt)/Jasper">Jasper</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66277">66277</a>: Fix regressions in refactoring from <code>Stack</code>
        <code>ArrayDeque</code>.
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add support for specifying Java 20 (with the value <code>20</code>) as
        the compiler source and/or compiler target for JSP compilation. If used
        with an Eclipse JDT compiler version that does not support these values,
        a warning will be logged and the default will used.
        (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.1_(markt)/Web_applications">Web applications</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Documentation. Document the <code>nonceRequestParameterName</code>
        attribute for the <code>CsrfPreventionFilter</code>. Based on
        <a href="https://github.com/apache/tomcat/pull/553">#553</a> by Mert &Uuml;lkg&uuml;n. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.1_(markt)/Other">Other</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update to the Eclipse JDT compiler 4.23. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update Objenesis to 3.2. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update UnboundID to 6.0.6. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update Checkstyle to 10.3.4. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update JaCoCo to 0.8.8. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update SpotBugs to 4.7.2. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update JSign to 4.2. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update Derby to *********. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Chinese translations. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Czech translations. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to French translations. (remm)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Japanese translations. Contributed by tak7iji and
        Shirayuking. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Korean translations. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Spanish translations. (markt)
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.0_(markt)"><span id="Tomcat_10.1.0_(markt)_rtext" style="float: right;">2022-09-26</span> Tomcat 10.1.0 (markt)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.0_(markt)/Coyote">Coyote</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update Panama OpenSSL code for the extensive Java 20 changes. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Fix a regression in refactoring for Hashtables which caused mbeans to
        lose many of their attributes. (remm)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0_(markt)/Jasper">Jasper</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66203">66203</a>: Log an error message when the JSP compiler is unable
        to create the output directory for the generated code. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0_(markt)/Other">Other</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Further automation to the build process to reduce the number of manual
        steps that release managers must perform. (markt)
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.0-M20_(markt)"><span id="Tomcat_10.1.0-M20_(markt)_rtext" style="float: right;">not released</span> Tomcat 10.1.0-M20 (markt)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.0-M20_(markt)/Coyote">Coyote</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Prepare OpenSSL Panama module for Java 20 API changes. (remm)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M20_(markt)/Other">Other</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update the Apache Tomcat migration tool for Jakarta EE library to 1.0.4.
        (markt)
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.0-M19_(markt)"><span id="Tomcat_10.1.0-M19_(markt)_rtext" style="float: right;">not released</span> Tomcat 10.1.0-M19 (markt)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.0-M19_(markt)/Coyote">Coyote</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Correct a regression in the previous fix for <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66236">66236</a>. (markt)
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.0-M18_(markt)"><span id="Tomcat_10.1.0-M18_(markt)_rtext" style="float: right;">not released</span> Tomcat 10.1.0-M18 (markt)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.0-M18_(markt)/Catalina">Catalina</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Correct handling of HTTP TRACE requests where there are multiple
        instances of an HTTP header with the same name. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Implement the requirements of RFC 7231 and do not include sensitive
        headers in responses to HTTP TRACE requests. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Implement the clarification in RFC 9110 that the units in HTTP range
        specifiers are case insensitive. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Properly-escape role and group information when writing
        MemoryUserDatabase to an XML file. (schultz)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Move control of XML-export logic from individual support classes into
        MemoryUserDatabase.save(). Deprecate and discontinue use of MemoryUser,
        MemoryRole, and MemoryGroup classes. (schultz)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66183">66183</a>: When logging cookie values in an access log valve and
        there are multiple cookies with the same name, log all cookie values
        rather than just the first. Based on pull request <a href="https://github.com/apache/tomcat/pull/541">#541</a> by Han
        Li. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66184">66184</a>: Ensure that JULI root loggers have a default level of
        <code>INFO</code>. Pull request <a href="https://github.com/apache/tomcat/pull/533">#533</a> provided by Piotr P.
        Karwasz. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Improve handling of stack overflow errors when parsing SSI expressions.
        (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66120">66120</a>: Enable FORM authentication to work correctly if
        session persistence and restoration occurs during the authentication
        process. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66233">66233</a>: Include an error message when sending a 400 response
        because a request has too many cookies. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        When web application deployment fails due to JARs with duplicate
        fragment names, improve the error message by listing the JARs that
        contain the duplicates. Based on pull request <a href="https://github.com/apache/tomcat/pull/535">#535</a> by Mads
        Rolsdorph. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Replace logging thread for JULI's <code>AsyncFileHandler</code> with an
        executor to protect against failure of the logging thread. Based on pull
        request <a href="https://github.com/apache/tomcat/pull/545">#545</a> by Piotr P. Karwasz. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M18_(markt)/Coyote">Coyote</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Avoid potential NPE by skipping duplicate accept check when using a Unix
        Domain Socket. Based on <a href="https://github.com/apache/tomcat/pull/532">#532</a> by Han Li. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Address an edge case in HTTP header parsing that allowed CRCRLF to be
        used as a valid line terminator. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Ensure HTTP/2 requests that include connection specific headers are
        rejected. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        When processing HTTP/2 requests, allow a <code>host</code> header to be
        used in place of an <code>:authority</code> header. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        When processing HTTP/2 requests, allow a <code>host</code> header and an
        <code>:authority</code> header to  be present providing they are
        consistent. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        When processing HTTP/2 requests, reject requests containing multiple
        <code>host</code> headers. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Make parsing of invalid filename directives in
        <code>Content-Disposition</code> headers more robust. Invalid filename
        directives will now be ignored rather than triggering a 500 response.
        (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66194">66194</a>: Log HTTP/2 stream closures (usually caused by client
        errors) via a <code>UserDataHelper</code> to broadly align it with the
        behaviour of HTTP/1.1 for parsing issues and exceeding limits. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66196">66196</a>: Align HTTP/1.1 with HTTP/2 and throw an exception when
        attempting to commit a response with an header value that includes one
        or more characters with a code point above 255. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66236">66236</a>: Implement support for the special values zero and
        minus one when configuring <code>maxSavePostSize</code> for a Connector
        when used in conjunction with TLS renegotiation. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66240">66240</a>: Avoid int overflow when parsing octets by limiting
        the maximum value to 255. Based on a PR <a href="https://github.com/apache/tomcat/pull/548">#548</a> by Stefan Mayr.
        (lihan)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://github.com/apache/tomcat/pull/550">#550</a>: Correctly handle case where a Servlet responds to a
        request with an expectation with a 2xx response without reading the
        request body. Pull request provided by Malay Shah. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://github.com/apache/tomcat/pull/551">#551</a>: Avoid potential IndexOutOfBoundsException by fixing
        incorrect check when matching HTTP/2 preface. Submitted by 刘文章.
        (lihan)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M18_(markt)/Jasper">Jasper</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Improve handling of stack overflow errors when parsing EL expressions.
        (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Correct parsing of integer and floating point literals in EL expressions
        so that larger values are correctly parsed to <code>BigInteger</code>
        and <code>BigDecimal</code> respectively. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66235">66235</a>: Fix various issues with the bean resolver used for
        Graal. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Improve the performance of the <code>ImportHandler</code> in the
        Expression Language implementation. This removes a previous optimisation
        that is now detrimental rather than helpful. Pull request <a href="https://github.com/apache/tomcat/pull/547">#547</a>
        provided by rmannibucau. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Improve handling of EL error messages so instances of Number are not
        formatted in unexpected ways. (markt/kkolinko)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Switch to using ELException rather than IllegalArgumentException when a
        type conversion fails during an EL arithmetic operation. This is an EL
        error so ELException seems more appropriate. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Fix a bug in <code>MethodExpression</code> handling that triggered an
        error when invoking a static method on an instance of the class rather
        than directly on the class. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Use <code>BigInteger.remainder()</code> rather than
        <code>BigInteger.mod()</code> when performing the modulus operation for
        instances of <code>BigInteger</code> as part of an EL expression.
        (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M18_(markt)/Cluster">Cluster</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        To aid future additions of new functionality, rather than throw an
        <code>IllegalArgumentException</code> if a <code>DeltaRequest</code> is
        passed an unrecognised action type, a warning message will now be
        logged. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66120">66120</a>: Enable FORM authentication to work correctly if
        session failover occurs during the authentication process. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M18_(markt)/WebSocket">WebSocket</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=62312">62312</a>: Add support for authenticating WebSocket clients with
        an HTTP forward proxy when establishing a connection to a WebSocket
        endpoint via a forward proxy that requires authentication. Based on a
        patch provided by Joe Mokos. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M18_(markt)/Other">Other</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Ensure that zip archives use UTC for file modification times to ensure
        repeatable builds across time zones. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Chinese translations. (lihan)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Czech translations. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to French translations. (remm)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to German translations. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Japanese translations. Contributed by tak7iji and
        Shirayuking. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Korean translations. Contributed by 수현. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Brazilian Portuguese translations. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Russian translations. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Spanish translations. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update the Apache Tomcat migration tool for Jakarta EE library to 1.0.3.
        (markt)
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.0-M17_(markt)"><span id="Tomcat_10.1.0-M17_(markt)_rtext" style="float: right;">2022-07-20</span> Tomcat 10.1.0-M17 (markt)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.0-M17_(markt)/Catalina">Catalina</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66104">66104</a>: Avoid error message by not trying to clean up old
        files from the logging directory before the directory has been created.
        Based on <a href="https://github.com/apache/tomcat/pull/521">#521</a> by HanLi. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update the Jakarta Common Annotations API to 2.1.1. This deprecates the
        <code>ManagedBean</code> annotation which will be removed in a future
        release. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M17_(markt)/Coyote">Coyote</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Provide dedicated loggers
        (<code>org.apache.tomcat.util.net.NioEndpoint.handshake</code> /
        <code>org.apache.tomcat.util.net.Nio2Endpoint.handshake</code>) for TLS
        handshake failures. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Enable the use of the FIPS provider for TLS enabled Connectors when
        using Tomcat Native 1.2.34 onwards built with OpenSSL 3.0.x onwards.
        (markt)
      </li>
      <li><img alt="Code: " class="icon" src="./images/code.gif">
        Remove the <code>jvmRoute</code> system property used to configure a
        default value for the <code>jvmRoute</code> attribute of an Engine.
        (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update experimental Panama modules with support for OpenSSL 3.0+.
        OpenSSL 1.1 remains supported. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Correct a regression in the refactoring to support experimentation with
        project Loom that broke HTTP/2 support if async IO was disabled. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Fix duplicate Poller registration with HTTP/2, NIO and async IO that
        could cause HTTP/2 connections to unexpectedly fail. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Refactor Panama module to better take advantage of the Panama preview
        API updates and fixes. Improves memory session usage and avoids some
        allocations. Review from Maurizio Cimadamore. (remm)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update the minimum recommended version of the Tomcat Native Library to
        2.0.1. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M17_(markt)/Jasper">Jasper</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add support for specifying Java 19 (with the value <code>19</code>) as
        the compiler source and/or compiler target for JSP compilation. If used
        with an Eclipse JDT compiler version that does not support these values,
        a warning will be logged and the default will used.
        (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M17_(markt)/WebSocket">WebSocket</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Remove configuration settings related to the restriction on WebSocket
        endpoint deployment that was removed in version 2.1 of the
        specification. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M17_(markt)/Web_applications">Web applications</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Documentation. <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=62245">62245</a>: Include <code>contextXsltFile</code>
        when discussing options for configuring directory listings. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Examples. Fix CVE-2022-34305, a low severity XSS vulnerability in the
        Form authentication example. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Documentation. Expand the description of the <code>useSendfile</code>
        attribute for HTTP/2 and reference the possibility of file locking when
        using this feature on Windows operating systems. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M17_(markt)/Other">Other</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update to bnd 6.3.1. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        The minimum Ant version required to build Tomcat 10.1.x is now 1.10.2.
        (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add additional automation to the build process to reduce the number of
        manual steps that release managers must perform. (schultz)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Implement support for reproducible builds. Reproducible builds are
        independent of operating system but require the same Ant version and
        same JDK (vendor and version) to be used as associated version
        information is embedded in a number of build outputs such as JAR file
        manifests. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update the minimum supported version of Tomcat Native to 1.2.34 to allow
        the removal of the deprecated Java API associated with features that
        will be removed in Tomcat Native 2.0.x. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Remove and/or update references to the removed
        <code>org.apache.tomcat.util.threads.res</code> package. The
        <code>LocalStrings*.properties</code> files in that package were moved
        to <code>org.apache.tomcat.util.threads</code> package for consistency
        with the rest of the Tomcat code base. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66134">66134</a>: The NSIS based Tomcat installer for Windows now
        correctly handles the combination of <code>TomcatAdminRoles</code>
        defined in a configuration file and selecting the Manager and/or
        Host Manager web applications in the installer's GUI. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update the OWB module to Apache OpenWebBeans 2.0.27. (remm)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update the CXF module to Apache CXF 3.5.3. (remm)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update the Apache Tomcat migration tool for Jakarta EE library to 1.0.1.
        (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update the packaged version of the Tomcat Native Library to 2.0.1 to
        pick up the Windows binaries built with with OpenSSL 3.0.5. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to French translations. (remm)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Japanese translations contributed tak7iji. (markt)
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.0-M16_(markt)"><span id="Tomcat_10.1.0-M16_(markt)_rtext" style="float: right;">2022-06-09</span> Tomcat 10.1.0-M16 (markt)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.0-M16_(markt)/Catalina">Catalina</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Update the memory leak protection code to support stopping application
        created executor threads when running on Java 19 and later. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Improve the error message if a required <code>--add-opens</code> option
        is missing. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Disable the memory leak correction code enabled by the Context attribute
        <code>clearReferencesObjectStreamClassCaches</code> when running on a
        JRE that includes a fix for the underlying memory leak. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://github.com/apache/tomcat/pull/515">#515</a>: Avoid deadlock on startup with some utility executor
        configurations. Submitted by Han Li. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66068">66068</a>: Ensure that the changes made to a request by the
        <code>RemoteIPValve</code> persist after the request is put into
        asynchronous mode. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Include the major version in the recommended version used for Tomcat
        Native with the <code>AprLifecycleListener</code>. (markt)
      </li>
      <li><img alt="Code: " class="icon" src="./images/code.gif">
        Remove the reporting of the unused APR feature flags. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M16_(markt)/Coyote">Coyote</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Additional fix for <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65118">65118</a>. Fix a potential
        <code>NullPointerException</code> when pruning closed HTTP/2 streams
        from the connection. (markt)
      </li>
      <li><img alt="Code: " class="icon" src="./images/code.gif">
        Refactor synchronization blocks locking on <code>SocketWrapper</code> to
        use <code>ReentrantLock</code> to support users wishing to experiment
        with project Loom. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66076">66076</a>: When using TLS with non-blocking writes and the NIO
        connector, ensure that flushing the buffers attempts to empty all of the
        output buffers. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66084">66084</a>: Correctly calculate bytes written to a response. Pull
        request <a href="https://github.com/apache/tomcat/pull/516">#516</a> provided by aooohan HanLi. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Correct a regression in the support added for encrypted PKCS#1 formatted
        private keys in the previous release that broke support for unencrypted
        PKCS#1 formatted private keys. (jfclere/markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Remove support for NPN when using the Tomcat Native Connector as NPN was
        never standardised and browser support for NPN was removed several years
        ago. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M16_(markt)/Jasper">Jasper</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Update XML schema used for generated web fragments to use the Servlet
        6.0 web fragment schema. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Update the XML schema used by the web fragment defined for the Jasper EL
        JAR to use the Servlet 6.0 web fragment schema. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Update <code>ImportHandler</code> optimisation for new classes
        introduced in Java 19. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Web_Socket">Web Socket</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Update the XML schema used by the web fragment defined for the WebSocket
        JAR to use the Servlet 6.0 web fragment schema. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M16_(markt)/Web_applications">Web applications</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66064">66064</a>: Update the building page in the documentation web
        application to reflect changes in required Java version and source
        repository. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Documentation. Make the description of the HTTP/1.1 configuration
        attributes that control the maximum allowed HTTP header size more
        specific. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M16_(markt)/Tribes">Tribes</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Increase the default buffer size for replication messages from 43800 to
        65536 bytes. This is expected to improve performance for large messages
        when running on Linux based systems. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M16_(markt)/Other">Other</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to French translations. (remm)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Japanese translations contributed by Shirayuking and
        tak7iji. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Chinese translations contributed by Dingzi2012. (markt)
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.0-M15_(markt)"><span id="Tomcat_10.1.0-M15_(markt)_rtext" style="float: right;">2022-05-16</span> Tomcat 10.1.0-M15 (markt)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.0-M15_(markt)/Catalina">Catalina</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Code: " class="icon" src="./images/code.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65853">65853</a>: Refactor the <code>CsrfPreventionFilter</code> to make
        it easier for sub-classes to modify the nonce generation and storage.
        Based on suggestions by Marvin Fr&ouml;hlich. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65991">65991</a>: Avoid NPE with <code>SSLAuthenticator</code> when
        <code>boundOnInit</code> is used on a connector, during the check
        for client certificate authentication availability. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66009">66009</a>: Use <code>getSubjectX500Principal().toString()</code>
        rather than <code>getSubjectX500Principal().getName(...)</code> to
        retrieve a certificate DN, to match the output of the deprecated
        <code>getSubjectDN().getName()</code> that was used previously. (remm)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Revert the change in 10.1.0-M11 that added a mapping of
        <code>Shift_JIS</code> for the <code>ja</code> locale to the default
        mappings used by <code>ServletResponse.setLocale()</code> as it
        caused regressions for applications using UTF-8. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Provide a property source that sources values from Kubernetes service
        bindings. Pull request <a href="https://github.com/apache/tomcat/pull/512">#512</a> provided by Sumit Kulhadia and
        Gareth Evans. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M15_(markt)/Coyote">Coyote</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        <a href="https://github.com/apache/tomcat/pull/501">#501</a>: Add new <code>maxHttpRequestHeaderSize</code> and
        <code>maxHttpResponseHeaderSize</code> attributes which allow setting
        the maximum HTTP header sizes independently. If not specified, the
        value of the <code>maxHttpHeaderSize</code> connector attribute will
        be used. Submitted by Zhongming Hua. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        The root cause of the Linux kernel duplicate accept bug has been
        identified along with the version of the kernel that includes the fix.
        The error message displayed when this bug occurs has been updated to
        reflect this new information and to advise users to update to a version
        of the OS that uses kernel 5.10 or later. Thanks to Christopher Gual for
        the research into this issue. (markt)
      </li>
      <li><img alt="Code: " class="icon" src="./images/code.gif">
        Remove the custom UTF-8 decoder that was introduced to work around
        various UTF-8 decoding bugs in Java. These issues were fixed in early
        Java 8 releases. Now the minimum Java version is 11, we can be sure that
        Tomcat will not be running on a JRE where these issues are present.
        (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66023">66023</a>: Improve the fix for <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65726">65726</a> and support HTTP
        upgrade with a request body for a wider set of use cases. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66035">66035</a>: Add NULL check on the SSL session reference in the
        Panama code before accessing the session id and creation time. (remm)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add support for encrypted PKCS#1 formatted private keys when configuring
        the internal, in memory key store. Based on <a href="https://github.com/apache/tomcat/pull/511">#511</a>.
        (jfclere/markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Remove the <code>prestartminSpareThreads</code> attribute of the
        <code>StandardThreadExecutor</code> since all core threads are always
        started by default making this attribute meaningless. Pull request
        <a href="https://github.com/apache/tomcat/pull/510">#510</a> provided by Aooohan. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M15_(markt)/Jasper">Jasper</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        To align with the JSP 3.1 specification, make the
        <code>jsp:plugin</code> action a NO-OP. No HTML will be generated as a
        result the <code>jsp:plugin</code> action being included in a JSP. This
        is be because the associated HTML elements are no longer supported by
        any major browser. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66031">66031</a>: Fix NPE when using a custom JspFactory. Patch by
        Jean-Louis Monteiro. (remm)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Webapps">Webapps</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66008">66008</a>: In the documentation web application, clarify the
        recommendation for the use the <code>trimSpaces</code> option for Jasper
        in production environments. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Update the documentation web application to state that the
        <code>EncryptInterceptor</code> does not provide sufficient protection
        to run Tomcat clustering over an untrusted network. This is
        CVE-2022-29885. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M15_(markt)/Other">Other</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Chinese translations contributed by shawn. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to French translations. (remm)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to German translations contributed by Thomas Hoffmann.
        (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Japanese translations contributed by Shirayuking.
        (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Korean translations. (woonsan)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update to Commons Daemon 1.3.1. This fixes a known regression in 1.3.0
        when configuring the Windows service with custom scripts as described in
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=66055">66055</a>. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update to JSign 4.1. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update the packaged version of the Tomcat Native Library to 1.2.33 to
        pick up Windows binaries built with OpenSSL 1.1.1o.(markt)
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.0-M14_(markt)"><span id="Tomcat_10.1.0-M14_(markt)_rtext" style="float: right;">2022-04-01</span> Tomcat 10.1.0-M14 (markt)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.0-M14_(markt)/Catalina">Catalina</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65736">65736</a>: Disable the <code>forceString</code> option for the
        JNDI <code>BeanFactory</code> and replace it with an automatic search
        for an alternative setter with the same name that accepts a
        <code>String</code>. This is a security hardening measure. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
         Remove the <code>WebappClassLoaderBase.getResources()</code> method as
         it is not used and if something accidentally exposes the class loader
         this method can be used to gain access to Tomcat internals. (markt)
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.0-M13_(markt)"><span id="Tomcat_10.1.0-M13_(markt)_rtext" style="float: right;">not released</span> Tomcat 10.1.0-M13 (markt)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.0-M13_(markt)/Catalina">Catalina</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Code: " class="icon" src="./images/code.gif">
        Update the JASPIC 2.0 API to Jakarta Authentication 3.0 (JASPIC was
        renamed for Jakarta EE 10) including the implementation of the new
        methods on <code>AuthConfigFactory</code>. (markt)
      </li>
      <li><img alt="Code: " class="icon" src="./images/code.gif">
        Harden the CredentialHandler implementations by switching to a
        constant-time implementation for credential comparisons. (schultz/markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M13_(markt)/Coyote">Coyote</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Use a constant for the default TLS cipher suite. This will allow
        skipping setting it in some cases (for example, it does not make
        sense for OpenSSL TLS 1.3). (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://github.com/apache/tomcat/pull/487">#487</a>: Improve logging of unknown settings frames. Pull request
        by Thomas Hoffmann. (remm)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65975">65975</a>: Add a warning if a TLS virtual host is configured with
        optional certificate authentication and the containing connector is also
        configured to support HTTP/2 as HTTP/2 does not permit optional
        certificate authentication. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65975">65975</a>: Add a warning if a TLS virtual host is configured for
        TLS 1.3 with a JSSE implementation and a web application is configured
        for <code>CLIENT-CERT</code> authentication. <code>CLIENT-CERT</code>
        authentication requires post-handshake authentication (PHA) when used
        with TLS 1.3 but the JSSE TLS 1.3 implementation does not support PHA.
        (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Improve the recycling of Processor objects to make it more robust.
        (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M13_(markt)/Jasper">Jasper</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65959">65959</a>: Serialize Function as String[] rather Class[]. (remm)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M13_(markt)/Web_applications">Web applications</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65947">65947</a>: Correct the name of HTTP/1.1 configuration property
        (<code>maxHttpHeaderSize</code>) that is inherited by the HTTP/2 upgrade
        protocol. Thanks to Thomas Hoffmann. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65952">65952</a>: Align <code>--add-opens</code> configuration for jsvc
        with the current Tomcat scripts. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Correct the AJP and HTTP/1.1 Connector configuration pages in the
        documentation web application to show which attributes are applicable to
        all Connectors and which are implementation specific. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M13_(markt)/Other">Other</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Correct a spelling mistake in the German translations. Thanks to Thomas
        Hoffmann. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65951">65951</a>: Use the <code>tomcat.output</code> property for OSGi
        bundle manifest paths. (isapir)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update to Commons Daemon 1.3.0. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update to Checkstyle 10.0. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update to SpotBugs 4.6.0. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Expand the <code>spotbugs</code> Ant task to also cover test code.
        (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update to bnd 6.2.0. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Remove OSGi annotations dependency as it is no longer required with bnd
        6.2.0. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update to the Eclipse JDT compiler 4.23. (markt)
      </li>
      <li><img alt="Code: " class="icon" src="./images/code.gif">
        Refactor the resource files for the Apache Tomcat installer for Windows
        so that all the resource files are located in a single directory in the
        source tree. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update the packaged version of the Tomcat Native Library to 1.2.32 to
        pick up Windows binaries built with OpenSSL 1.1.1n.(markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Chinese translations contributed by 15625988003. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to French translations. (remm)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Japanese translations contributed by tak7iji. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Expand coverage of translations for <code>jakarta.el</code> package.
        Based on <a href="https://github.com/apache/tomcat/pull/488">#488</a> from Volodymyr Siedlecki. (markt)
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.0-M12_(markt)"><span id="Tomcat_10.1.0-M12_(markt)_rtext" style="float: right;">2022-03-14</span> Tomcat 10.1.0-M12 (markt)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.0-M12_(markt)/Catalina">Catalina</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://github.com/apache/tomcat/pull/477">#477</a>: Update the default list of JARs to skip to include the
        Apache Log4j JAR for Jakarta EE platforms. Pull request by Michael
        Seele. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65921">65921</a>: The <code>type</code> substitution flag for the
        rewrite valve should set the content type for the response, not the
        request. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://github.com/apache/tomcat/pull/479">#479</a>: Enable the rewrite valve to redirect requests when the
        original request cannot be mapped to a context. This typically happens
        when no ROOT context is defined. Pull request by elkman. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65940">65940</a>: Fix <code>NullPointerException</code> if an exception
        occurs during the destruction of a Servlet. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M12_(markt)/Coyote">Coyote</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Fix regression introduced with <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65757">65757</a> bugfix which better
        identified non request threads but which introduced a similar problem
        when user code was doing sequential operations in a single thread.
        Test case code submitted by Istvan Szekely. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Fix potential thread-safety issue that could cause HTTP/1.1 request
        processing to wait, and potentially timeout, waiting for additional
        data when the full request has been received. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Throw <code>IOException</code> rather than
        <code>IllegalStateException</code> when the application attempts to
        write to an HTTP/2 stream after the client has closed the stream.
        (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M12_(markt)/Jasper">Jasper</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        When resolving methods in EL expressions that use beans and/or static
        fields, ensure that any custom type conversion is considered when
        identifying the method to call. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M12_(markt)/Web_applications">Web applications</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Correct the name of the <code>value</code> attribute in the new
        documentation of <code>OpenSSLConfCmd</code> elements. (rjung)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M12_(markt)/WebSocket">WebSocket</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Fix typo in JPMS substitution configuration for WebSocket client module.
        (markt)
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.0-M11_(markt)"><span id="Tomcat_10.1.0-M11_(markt)_rtext" style="float: right;">2022-02-28</span> Tomcat 10.1.0-M11 (markt)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.0-M11_(markt)/Catalina">Catalina</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add <code>ha-api-*.jar</code> and <code>jaxws-rt-*.jar</code> to the
        list of JARs to skip when scanning for TLDs, web fragments and
        annotations. (michaelo)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Expand the default mappings used by
        <code>ServletResponse.setLocale()</code> to include a mapping from the
        <code>ja</code> locale to the <code>Shift_JIS</code> encoding. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65806">65806</a>: Improve the handling of session ID generation when the
        default algorithm for <code>SecureRandom</code> (<code>SHA1PRNG</code>)
        is not supported by the configured providers as will be the case for a
        FIPS compliant configuration. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        <a href="https://github.com/apache/tomcat/pull/463">#463</a>: Add support for additional user attributes to
        <code>TomcatPrincipal</code> and <code>GenericPrincipal</code>.
        Patch provided by Carsten Klein. (michaelo)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://github.com/apache/tomcat/pull/464">#464</a>: Fall back to the class loader used to load JULI when the
        thread context class loader is not set. In a normal Tomcat
        configuration, this will be the system class loader. Based on a pull
        request by jackshirazi. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://github.com/apache/tomcat/pull/469">#469</a>: Include the Jakarata Annotations API in the classes that
        Tomcat will not load from web applications. Pull request provided by
        ppkarwasz. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Fix a potential <code>StringIndexOutOfBoundsException</code> exception
        when generating a WebDAV multi-status response after an error during a
        copy or delete. Report the paths relative to the server root for any
        resources with an error. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Improve the format of WebDAV XML responses to make them easier for
        humans to read. The change ensures that there is always a line break
        before starting a new element. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Improve validation of the <code>Destination</code> header for WebDAV
        <code>MOVE</code> and <code>COPY</code> requests. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M11_(markt)/Coyote">Coyote</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Correct a regression in the fix for <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65454">65454</a> that meant that
        <code>minSpareThreads</code> and <code>maxThreads</code> settings were
        ignored when the Connector used an internal executor. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65776">65776</a>: Improve the detection of the Linux duplicate accept
        bug and reduce (hopefully avoid) instances of false positives. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65848">65848</a>: Revert the change that attempted to align the
        behaviour of client certificate authentication with NIO or NIO2 with
        OpenSSL for TLS between MacOS and Linux/Windows as the root cause was
        traced to configuration differences. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://github.com/apache/tomcat/pull/467">#467</a>: When system time moves backwards (e.g. after clock
        correction), ensure that the cached formatted current date used for
        HTTP headers tracks this change. Pull request provided by zhenguoli.
        (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M11_(markt)/Jasper">Jasper</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://github.com/apache/tomcat/pull/474">#474</a>: Prevent a tag file from corrupting the ELContext of the
        calling page. Pull request provided by Dmitri Blinov. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Minor optimisation of serialization for <code>FunctionMapperImpl</code>
        in response to pull request <a href="https://github.com/apache/tomcat/pull/476">#476</a>. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M11_(markt)/Web_applications">Web applications</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Remove the applet example from the example web application as applets
        are no longer supported in any major browser. (markt)
      </li>
      <li><img alt="Code: " class="icon" src="./images/code.gif">
        Refactor a small number of pages in the examples web application to
        avoid an issue with reproducible builds due to differences in file
        ordering across different operating systems with Ant's zip task. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Better documentation for the <code>protocol</code> attribute of the
        <code>JNDIRealm</code>. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Clarify the settings described in the documentation web application to
        configure a cluster using static membership. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add information on the <code>OpenSSLConf</code> and
        <code>OpenSSLConfCmd</code> elements to the HTTP SSL configuration page
        in the documentation web applications. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M11_(markt)/jdbc-pool">jdbc-pool</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Code: " class="icon" src="./images/code.gif">
        Use LF line endings for text files in JARs to support reproducible
        builds across different operating systems. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M11_(markt)/Other">Other</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Code: " class="icon" src="./images/code.gif">
        Use LF line endings for text files in JARs to support reproducible
        builds across different operating systems. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Fix dependencies for individual test targets in Ant build file. Based on
        <a href="https://github.com/apache/tomcat/pull/468">#468</a> provided by Totoo chenyonghui. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update the OWB module to Apache OpenWebBeans 2.0.26. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Revert the cherry-pick of JavaDoc fix from DBCP applied in 10.1.0.M9
        that broke the <code>DataSourceMXBean</code> by using a type that isn't
        supported by MXBeans. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Chinese translations contributed by cloudgyb, totoo and
        Chenyonghui1028. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to French translations. (remm)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to German translations contributed by Andreas Abraham.
        (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Japanese translations contributed by tak7iji and
        Shirayuking. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Korean translations. (woonsan)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Spanish translations contributed by ceciliabarudi.
        (markt)
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.0-M10_(markt)"><span id="Tomcat_10.1.0-M10_(markt)_rtext" style="float: right;">2022-01-20</span> Tomcat 10.1.0-M10 (markt)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.0-M10_(markt)/Coyote">Coyote</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Correct a regression in the fix for <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65785">65785</a> that broke HTTP/2
        server push. (markt)
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.0-M9_(markt)"><span id="Tomcat_10.1.0-M9_(markt)_rtext" style="float: right;">not released</span> Tomcat 10.1.0-M9 (markt)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.0-M9_(markt)/Catalina">Catalina</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Add missing check in <code>SessionCookieConfig.setAttribute()</code> to
        ensure that the method fails if called after the web application has
        started. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Add additional locking to <code>DataSourceUserDatabase</code> to provide
        improved protection for concurrent modifications. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Add recycling check in the input and output stream isReady to try to
        give a more informative ISE when the facade has been recycled. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Make the calculation of the session storage location more robust when
        using file based persistent storage. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M9_(markt)/Coyote">Coyote</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65726">65726</a>: Implement support for HTTP/1.1 upgrade when the
        request includes a body. The maximum permitted size of the body is
        controlled by <code>maxSavePostSize</code>. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Restore pre-starting of <code>minSpareThreads</code> lost in the fix for
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65454">65454</a>. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Revert the previous fix for <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65714">65714</a> and implement a more
        comprehensive fix. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Allow freeing up context on JVM shutdown in the OpenSSL Panama module
        by properly using a shared scope. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65757">65757</a>: Missing initial IO listener notification on Servlet
        container dispatch to another container thread. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Expand the fix for <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65757">65757</a> so that rather than just checking if
        processing is happening on a container thread, the check is now if
        processing is happening on the container thread currently allocated to
        this request/response. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Improve the fix for RST frame ordering added in 10.1.0-M8 to avoid a
        potential deadlock on some systems in non-default configurations.
        (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65767">65767</a>:  Add support for certificates that use keys encrypted
        using PBES2. Based on a pull request provided by xiezhaokun. (markt)
      </li>
      <li><img alt="Code: " class="icon" src="./images/code.gif">
        Refactor testing whether a String is a valid HTTP token. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65785">65785</a>: Perform additional validation of HTTP headers when
        using HTTP/2. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        When a Connector or Endpoint is paused, ensure that only new connections
        and new requests on existing connections are stopped while allowing in
        progress requests to run to completion. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Explicitly release ByteBuffer instances associated with pooled channels
        when stopping the NioEndpoint and Nio2Endpoint. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Narrow the scope of the logging of invalid cookie headers to just the
        invalid cookie rather than the whole cookie header. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M9_(markt)/Jasper">Jasper</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65724">65724</a>: Fix missing messages for some
        <code>PropertyNotWritableException</code>s caused by a typo in the name
        used for a resource string. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add support for specifying Java 18 (with the value <code>18</code>) as
        the compiler source and/or compiler target for JSP compilation. If used
        with an Eclipse JDT compiler version that does not support these values,
        a warning will be logged and the default will used.
        (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        To align with the JSP 3.1 specification that requires Java 11 as a
        minimum, make the default JSP source version and target version Java 11.
        (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M9_(markt)/WebSocket">WebSocket</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Remove the <code>ALLOW_UNSUPPORTED_EXTENSIONS</code> system property. As
        per RFC 6455, all extensions are optional. If an endpoint declares an
        extension that isn't supported there is no need to trigger an error. The
        extension can just be excluded from the result of the negotiation.
        (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Remove the <code>DISABLE_BUILTIN_EXTENSIONS</code>. It was added to
        enable Tomcat to pass the WebSocket TCK but after updates to the TCK, it
        is no longer required. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add support for POJO WebSocket endpoints to the programmatic upgrade
        that allows applications to opt to upgrade an HTTP connection to
        WebSocket. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add support for the WebSocket 2.1 client-side API for configuring TLS
        connection for wss client connections. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65763">65763</a>: Improve handling of WebSocket connection close if a
        message write times out before the message is fully written. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M9_(markt)/Other">Other</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update the OWB module to Apache OpenWebBeans 2.0.25. (remm)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update the CXF module to Apache CXF 3.5.0. (remm)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Chinese translations contributed by zhnnn. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to French translations. (remm)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Japanese translations contributed by Shirayuking, yoshy
        and tak7iji. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Korean translations. (woonsan)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Spanish translations contributed by Israel. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update SpotBugs to 4.5.2. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update to the Eclipse JDT compiler 4.22. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update the NSIS installer to 3.08. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update UnboundID to 6.0.3. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update CheckStyle to 9.2.1. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update BND to 6.1.0. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update OSGI annotations to 1.1.1. (markt)
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.0-M8_(markt)"><span id="Tomcat_10.1.0-M8_(markt)_rtext" style="float: right;">2021-12-08</span> Tomcat 10.1.0-M8 (markt)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.0-M8_(markt)/Catalina">Catalina</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Log warning if a listener is not nested inside a Server element
        although it must have been. (michaelo)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Where the getter can be called safely, remove the checks for
        <code>ServletContext</code> getters called from a
        <code>contextInitialized()</code> method of a
        <code>ServletContextListener</code> that was not defined in a
        <code>web.xml</code> file, a <code>web-fragment.xml</code> file nor
        annotated with <code>WebListener</code>. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Make SPNEGO authentication more robust for the case where the provided
        credential has expired. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Limit cookie support to RFC 6265 to align with recent updates to the
        Servlet specification. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65684">65684</a>: Fix a potential <code>NullPointerException</code> when
        using JULI. (markt)
      </li>
      <li><img alt="Docs: " class="icon" src="./images/docs.gif">
        Document conditions under which the <code>AprLifecycleListener</code>
        can be used to avoid JVM crashes. (michaelo)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Refactor the <code>AsyncFileHandler</code> to reduce the possibility of
        log messages being lost on shutdown. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Refactor the <code>AsyncFileHandler</code> to remove the need for the
        <code>org.apache.juli.AsyncLoggerPollInterval</code>. If set, this
        property now has no effect. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add debug logging to the <code>RestCsrfPreventionFilter</code>. Based on
        pull request <a href="https://github.com/apache/tomcat/pull/452">#452</a> by Polina Georgieva. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M8_(markt)/Coyote">Coyote</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Use implicit scopes in the OpenSSL Panama module to tie the cleanup of
        OpenSSL memory to the Java GC. (remm)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Provide protection against a known <a href="https://bugs.launchpad.net/ubuntu/+source/linux/+bug/1924298">OS
        bug</a> that causes the acceptor to report an incoming connection more
        than once. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Avoid unnecessary duplicate read registrations for blocking I/O with the
        NIO connector. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65677">65677</a>: Improve exception handling for errors during HTTP/1.1
        reads with NIO2. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        When an error occurs that triggers a stream reset, ensure that the first
        <code>RST</code> frame sent to the client is the one associated with the
        error that triggered the reset. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65714">65714</a>: Fix exceptions when the security manager is enabled
        and the first request received after starting is an HTTP request to a
        TLS enabled NIO2 connector. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Ensure that using NIO or NIO2 with OpenSSL for TLS behaves the same way
        on MacOS as it does on Linux and Windows when no trusted certificate
        authorities are configured and reject all client certificates. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Avoid a potential deadlock during the concurrent processing of incoming
        HTTP/2 frames for a stream and that stream being reset. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M8_(markt)/WebSocket">WebSocket</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Update the WebSocket API packaging to remove the copy of the client API
        from the server API and replace it with a dependency on the client API.
        This aligns Tomcat with changes in the WebSocket 2.1 specification.
        (markt)
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.0-M7_(markt)"><span id="Tomcat_10.1.0-M7_(markt)_rtext" style="float: right;">2021-11-15</span> Tomcat 10.1.0-M7 (markt)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.0-M7_(markt)/Catalina">Catalina</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Code: " class="icon" src="./images/code.gif">
        Refactor <code>HttpServlet</code> so the default <code>doHead()</code>
        implementation now calls <code>doGet()</code> and relies on the
        container to ensure that the response body is not sent. The previous
        behaviour (wrapping the response) may be enabled per Servlet by setting
        the <code>jakarta.servlet.http.legacyDoHead</code> Servlet
        initialisation parameter to <code>true</code>. This aligns Tomcat with
        recent changes updates for Servlet 6.0 in the Jakarta Servlet
        specification project. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add support for setting generic attributes for session cookies. This
        aligns Apache Tomcat with recent changes in the Jakarta Servlet
        specification project. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Do not add a trailing <code>/</code> to a request URI during
        canonicalization. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Invalid byte sequences (typically in %nn form) in a request URi that are
        not valid for the given URI encoding now trigger a 400 response. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Ensure that a request URI starts with a <code>/</code>. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add a new Connector option, <code>rejectSuspiciousURIs</code> that will
        causes 'suspicious' (see the Servlet 6.0 specification) URIs to be
        rejected with a 400 response. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Improve robustness of JNDIRealm for exceptions occurring when getting
        the connection. Also add missing close when running into issues
        getting the passord of a user. (remm)
      </li>
      <li><img alt="Docs: " class="icon" src="./images/docs.gif">
        Add Javadoc comment which listeners must be nested within
        <code>Server</code> elements only. (michaelo)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add support for custom caching strategies for web application resources.
        This initial implementation allows control over whether or not a
        resource is cached. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M7_(markt)/Coyote">Coyote</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Code: " class="icon" src="./images/code.gif">
        Improve performance of Connector shutdown - primarily to reduce the time
        it takes to run the test suite. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        <a href="https://github.com/apache/tomcat/pull/457">#457</a>: Add a <code>toString()</code> method to
        <code>MimeHeader</code> to aid debugging. (dblevins)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add experimental OpenSSL support through the Panama API incubating in
        Java 17, with support for OpenSSL 1.1+. This no longer requires
        tomcat-native or APR. Please refer to the <code>openssl-java17</code>
        module for more details. (remm)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M7_(markt)/Jasper">Jasper</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Regenerate the EL parser using JavaCC 7.0.10. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Fix a bug that prevented the EL parser correctly parsing a literal Map
        that used variables rather than literals for both keys and values.
        (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Ensure that the <code>getType()</code> method of any
        <code>ELResolver</code> implementation returns <code>null</code> if
        either the <code>ELResolver</code> or the resolved property is read-only
        to align Tomcat with recent updates in the Jakarta EL specification
        project. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Implement an alternative solution to support the JSP page directive
        attribute <code>isThreadSafe</code> now that the
        <code>SingleThreadModel</code> interface has been removed from the
        Servlet API. The new approach synchronizes the <code>service()</code>
        method.
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M7_(markt)/WebSocket">WebSocket</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Add a new method<code>
        ServerEndpointConfig.Configurator.getContainerDefaultConfigurator()
        </code>to align with recent updates in the WebSocket specification
        project. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Add a new method <code>ServerContainer.upgradeHttpToWebSocket()</code>
        to align with recent updates in the WebSocket specification project.
        (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M7_(markt)/Tribes">Tribes</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://github.com/apache/tomcat/pull/454">#454</a>: Differentiate warning messages in
        <code>KubernetesMembershipProvider</code> so that the missing attribute
        is clear to the user. PR provided by Hal Deadman. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M7_(markt)/Other">Other</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Switch from Cobertura to JaCoCo for code coverage as Cobertura does not
        support code coverage for code compiled for Java 11 onwards. It also
        removes the need to use a single thread to run the tests. (markt)
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.0-M6_(markt)"><span id="Tomcat_10.1.0-M6_(markt)_rtext" style="float: right;">2021-10-01</span> Tomcat 10.1.0-M6 (markt)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.0-M6_(markt)/Catalina">Catalina</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Provide the DataSource in the constructor of
        <code>DataSourceUserDatabase</code>, since it is always global. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Fix delete then create object manipulations with
        <code>DataSourceUserDatabase</code>. (remm)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Remove all deprecated code from the Servlet API to align Tomcat with
        recent changes in the Jakarta Servlet specification project. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add the currently available Jakarta EE 10 schemas from the Jakarta EE
        schema project. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Implement the new connection ID and request ID API for Servlet 6.0.
        (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65553">65553</a>: Implement a work-around for a
        <a href="https://bugs.openjdk.java.net/browse/JDK-8273874">JRE bug</a>
        that can trigger a memory leak when using the JNDI realm. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65586">65586</a>: Fix the bloom filter used to improve performance of
        archive file look ups in the web resources implementation so it works
        correctly for directory lookups whether or not the provided directory
        name includes the trailing <code>/</code>. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://github.com/apache/tomcat/pull/451">#451</a>: Improve the usefulness of the thread name cache used in
        JULI. Pull request provided by t-gergely. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M6_(markt)/Coyote">Coyote</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65563">65563</a>: Correct parsing of HTTP <code>Content-Range</code>
        headers. Tomcat was incorrectly requiring an <code>=</code> character
        after <code>bytes</code>. Fix based on pull request <a href="https://github.com/apache/tomcat/pull/449">#449</a> by
        Thierry Gu&eacute;rin. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Correct a potential <code>StackOverflowException</code> with HTTP/2 and
        sendfile. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Further improvements in the management of the connection flow control
        window. This addresses various bugs that caused streams to incorrectly
        report that they had timed out waiting for an allocation from the
        connection flow control window. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65577">65577</a>: Fix a <code>AccessControlException</code> reporting
        when running an NIO2 connector with TLS enabled. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Reclassify TLS ciphers that use AESCCM8 as medium security rather than
        high security to align with recent changes in OpenSSL. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Fix an issue that caused some Servlet non-blocking API reads of the HTTP
        request body to incorrectly use blocking IO. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M6_(markt)/Jasper">Jasper</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Code: " class="icon" src="./images/code.gif">
        Deprecate <code>ELResolver.getFeatureDescriptors</code> to align Tomcat
        with recent updates in the Jakarta EL specification project. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add support for default methods to <code>BeanRELResolver</code> to align
        Tomcat with recent updates in the Jakarta EL specification project.
        (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add support for <code>MethodReference</code> and the associated getter
        on <code>MethodExpression</code> to align Tomcat with recent updates in
        the Jakarta EL specification project. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Refactor <code>ScopedAttributeELResolver</code> to separate out the
        functionality that is unrelated to scoped attributes into two new
        resolvers: <code>ImportELResolver</code> and
        <code>NotFoundELResolver</code>. This aligns Tomcat with recent updates
        to the Jakarta Server Pages specification. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Fix the implementation of <code>MethodExpression.getMethodInfo()</code>
        so that it returns the expected value rather than failing when the
        method expression is defined with the parameter values in the expression
        rather than the types being passed explicitly to
        <code>ExpressionFactory.createMethodExpression()</code>. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add support for a new page/tag directive <code>errorOnELNotFound</code>
        that can be used to trigger an identifier if an EL expression in a
        page/tag contains an identifier that cannot be resolved. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M6_(markt)/WebSocket">WebSocket</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        The internal upgrade handler should close the associated
        <code>WebConnection</code> on destroy. (remm)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M6_(markt)/Web_applications">Web applications</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update the web applications that are included with Apache Tomcat to use
        the Jakarta EE 10 schema for web.xml. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Clarify the JASPIC configuration options in the documentation web
        application. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M6_(markt)/Other">Other</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65585">65585</a>: Update obsolete comments at the start of the
        <code>build.properties.default</code> file. (markt)
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.0-M5_(markt)"><span id="Tomcat_10.1.0-M5_(markt)_rtext" style="float: right;">2021-09-10</span> Tomcat 10.1.0-M5 (markt)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.0-M5_(markt)/Catalina">Catalina</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Enable Tomcat to start if an (old) XML parser is configured that does
        not support <code>allow-java-encodings</code>. A warning will be logged
        if such an XML parser is detected. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Change the behaviour of custom error pages. If an error occurs after the
        response is committed, once the custom error page content has been added
        to the response the connection is now closed immediately rather than
        closed cleanly. i.e. the last chunk that marks the end of the response
        body is no longer sent. This acts as an additional signal to the client
        that the request experienced an error. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65479">65479</a>: When handling requests using JASPIC authentication,
        ensure that <code>PasswordValidationCallback.getResult()</code> returns
        the result of the password validation rather than always returning
        <code>false</code>. Fixed via pull request <a href="https://github.com/apache/tomcat/pull/438">#438</a> provided by
        Robert Rodewald. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Improve the reusability of the <code>UserDatabase</code> by adding
        intermediate concrete implementation classes and allowing to do
        partial database updates on <code>save</code>. (remm)
      </li>
      <li><img alt="Code: " class="icon" src="./images/code.gif">
        Refactor the authenticators to delegate the check for preemptive
        authentication to the individual authenticators where an authentication
        scheme specific check can be performed. Based on pull request
        <a href="https://github.com/apache/tomcat/pull/444">#444</a> by Robert Rodewald. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add a <code>UserDatabase</code> implementation as a superset of the
        <code>DataSourceRealm</code> functionality. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Make sure the dynamic Principal returned by
        <code>UserDatabaseRealm</code> stays up to date with the database
        contents, and add an option to have it be static, similar to the other
        realms. (remm)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add <code>derby-*.jar</code> to the list of JARs to skip when scanning
        for TLDs, web fragments and annotations. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://github.com/apache/tomcat/pull/447">#447</a>. Correct JPMS metadata for catalina.jar. Pull request
        provided by Hui Wang. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M5_(markt)/Coyote">Coyote</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Correct a logic error that meant setting
        <code>certificateKeystoreFile</code> to <code>NONE</code> did not have
        the expected effect. <code>NONE</code> was incorrectly treated as a file
        path. Patch provided by Mikael Sterner. (markt)
      </li>
      <li><img alt="Code: " class="icon" src="./images/code.gif">
        Remove the deprecated APR/Native connector which includes the HTTP APR
        and the AJP APR connector. Also remove the Java interfaces to the
        APR/Native library that are not used by the OpenSSL integration for the
        NIO and NIO2 connectors. (markt)
      </li>
      <li><img alt="Code: " class="icon" src="./images/code.gif">
        Refactor the JSSE/OpenSSL integration to avoid the use of
        <code>finalize()</code>. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65505">65505</a>: When an HTTP header value is removed, ensure that the
        order of the remaining header values is unchanged. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M5_(markt)/WebSocket">WebSocket</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65506">65506</a>: Fix write timeout check that was using the read
        timeout value. Patch submitted by Gustavo Mahlow. (remm)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M5_(markt)/Web_applications">Web applications</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Remove unnecessary Context settings from the examples web application.
        (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Document default value for <code>unpackWARs</code> and related clean-up.
        Pull request <a href="https://github.com/apache/tomcat/pull/439">#439</a> provided by Robert Rodewald. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Clarify the documentation of the <code>compressionMinSize</code> and
        <code>compressibleMimeType</code> HTTP <code>Connector</code>
        attributes. Pull request <a href="https://github.com/apache/tomcat/pull/442">#442</a> provided by crisgeek. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M5_(markt)/Tribes">Tribes</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Code: " class="icon" src="./images/code.gif">
        Refactor the <code>ParallelNioSender</code> to avoid the use of
        <code>finalize()</code>. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M5_(markt)/Other">Other</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Fix failing build when building on non-English locales. Pull request
        <a href="https://github.com/apache/tomcat/pull/441">#441</a> provided by Dachuan J. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update to JSign version 4.0 to enable code signing without the need for
        the installation of additional client tools. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Add Apache Derby 10.15.2.0 to the testsuite dependencies, for JDBC
        and DataSource testing. (remm)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Update the internal fork of Apache Commons BCEL to 40d5eb4 (2021-09-01,
        6.6.0-SNAPSHOT). Code clean-up only. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Update the internal fork of Apache Commons Codec to fd44e6b (2021-09-01,
        1.16-SNAPSHOT). Minor refactoring. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65661">65661</a>: Update the internal fork of Apache Commons FileUpload
        to 33d2d79 (2021-09-01, 2.0-SNAPSHOT). Refactoring and code clean-up. As
        a result of Commons File Upload now using
        <code>java.nio.file.Files</code>, applications using multi-part uploads
        need to ensure that the JVM is configured with sufficient direct memory
        to store all in progress multi-part uploads. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Update the internal fork of Apache Commons Pool to 2.11.1 (2021-08-17).
        Improvements, code clean-up and refactoring. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Update the internal fork of Apache Commons DBCP to 2.9.0 (2021-08-03).
        Improvements, code clean-up and refactoring. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update the packaged version of the Tomcat Native Library to 1.2.31 to
        pick up Windows binaries built with OpenSSL 1.1.1l.(markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Switch to the CDN as the primary download location for ASF dependencies.
        (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Chinese translations contributed by syseal, wolibo,
        ZhangJieWen and DigitalFatCat. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to French translations. (remm)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Japanese translations contributed by tak7iji. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Korean translations. (woonsan)
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.0-M4_(markt)"><span id="Tomcat_10.1.0-M4_(markt)_rtext" style="float: right;">2021-08-06</span> Tomcat 10.1.0-M4 (markt)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.0-M4_(markt)/WebSocket">WebSocket</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Correct a regression in the Java 8 to Java 11 changes made in 10.1.0-M3
        that caused all WebSocket end points to fail to register. (markt)
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.0-M3_(markt)"><span id="Tomcat_10.1.0-M3_(markt)_rtext" style="float: right;">not released</span> Tomcat 10.1.0-M3 (markt)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.0-M3_(markt)/General">General</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update the minimum required Java version to Java 11. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M3_(markt)/Catalina">Catalina</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Code: " class="icon" src="./images/code.gif">
        Incremented the supported Jakarta Servlet version to 6.0 to align with
        the current development branch of the Jakarta Servlet specification.
        Plans have changed and the next iteration of the Servlet specification
        will be 6.0 rather than 5.1. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65411">65411</a>: Always close the connection when an uncaught
        <code>NamingException</code> occurs to avoid connection locking.
        Submitted by Ole Ostergaard. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65433">65433</a>: Correct a regression in the fix for <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65397">65397</a>
        where a <code>StringIndexOutOfBoundsException</code> could be triggered
        if the canonical path of the target of a symlink was shorter than the
        canonical path of the directory in which the symlink had been created.
        Patch provided by Cedomir Igaly. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65443">65443</a>: Refactor the <code>CorsFilter</code> to make it easier
        to extend. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        To avoid unnecessary cache revalidation, do not add an HTTP
        <code>Expires</code> header when setting adding an HTTP header of
        <code>CacheControl: private</code>. (markt)
      </li>
      <li><img alt="Code: " class="icon" src="./images/code.gif">
        Refactor JULI's custom <code>LogManager</code>, the
        web application class loader implementation, the web resources
        implementation, the <code>JreLeakPreventionListener</code>
        implementation and the <code>StandardJarScanner</code> implementation to
        remove Java 8 specific code now that the minimum Java version has been
        increased to 11. (markt)
      </li>
      <li><img alt="Code: " class="icon" src="./images/code.gif">
        Remove all references to the endorsed standards override feature and the
        specifying of optional packages (extensions) in the manifest as these
        are not supported in Java 11. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M3_(markt)/Coyote">Coyote</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        When writing an HTTP/2 response via sendfile (only enabled when
        <code>useAsyncIO</code> is true) the connection flow control window was
        sometimes ignored leading to various error conditions. sendfile now
        checks both the stream and connection flow control windows before
        writing. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add debug logging for writing an HTTP/2 response via sendfile. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Correct bugs in the HTTP/2 connection flow control management that meant
        it was possible for a connection to stall waiting for a connection flow
        control window update that had already arrived. Any streams on that
        connection that were trying to write when this happened would time out.
        (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65448">65448</a>: When using TLS with NIO, it was possible for a
        blocking response write to hang just before the final TLS packet
        associated with the response until the connection timed out at which
        point the final packet would be sent and the connection closed. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65454">65454</a>: Fix a race condition that could result in a delay to
        a new request. The new request could be queued to wait for an existing
        request to finish processing rather than the thread pool creating a new
        thread to process the new request. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65460">65460</a>: Correct a regression introduced in the previous
        release in the change to reduce the number of small HTTP/2 window
        updates sent for streams. A logic error meant that small window updates
        for the connection were dropped. This meant that the connection flow
        window slowly reduced over time until nothing could be sent. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Remove NIO workarounds and code that is no longer needed with Java 11.
        (remm)
      </li>
      <li><img alt="Code: " class="icon" src="./images/code.gif">
        Refactor the endpoints to remove Java 8 specific code now that the
        minimum Java version has been increased to 11. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M3_(markt)/Jasper">Jasper</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Code: " class="icon" src="./images/code.gif">
        Add additional generics to the EL API to align with the latest changes
        in the EL specification project. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Enable EL lambda expressions to be coerced to functional interfaces.
        This is an implementation of a proposed extension to the Jakarta
        Expression Language specification. (markt)
      </li>
      <li><img alt="Code: " class="icon" src="./images/code.gif">
        Refactor the EL API and implementation to remove Java 8 specific code
        now that the minimum Java version has been increased to 11. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M3_(markt)/WebSocket">WebSocket</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Code: " class="icon" src="./images/code.gif">
        Refactor the WebSocket implementation to remove Java 8 specific code now
        that the minimum Java version has been increased to 11. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M3_(markt)/Web_applications">Web applications</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65404">65404</a>: Correct a regression in the fix for <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=63362">63362</a>
        that caused the server status page in the Manager web application to be
        truncated if HTTP upgrade was used such as when starting a WebSocket
        connection. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M3_(markt)/Other">Other</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Chinese translations contributed by ZhangJieWen and
        chengzheyan. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to French translations. (remm)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Japanese translations contributed by tak7iji. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Korean translations. (woonsan)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Use of GraalVM native images no longer automatically disables JMX
        support. JMX support may still be disabled by calling
        <code>org.apache.tomcat.util.modeler.Registry.disableRegistry()</code>.
        (markt)
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.0-M2_(markt)"><span id="Tomcat_10.1.0-M2_(markt)_rtext" style="float: right;">2021-07-02</span> Tomcat 10.1.0-M2 (markt)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.0-M2_(markt)/Catalina">Catalina</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Code: " class="icon" src="./images/code.gif">
        Refactor the <code>RemoteIpValve</code> to use the common utility method
        for list to comma separated string conversion. (markt)
      </li>
      <li><img alt="Code: " class="icon" src="./images/code.gif">
        Refactor <code>JNDIRealm$JNDIConnection</code> so its fields are
        accessible to sub-classes of <code>JNDIRealm</code>. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Fix serialization warnings in <code>UserDatabasePrincipal</code>
        reported by SpotBugs. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65397">65397</a>: Calls to
        <code>ServletContext.getResourcePaths()</code> no longer include
        symbolic links in the results unless <code>allowLinking</code> has been
        set to <code>true</code>. If a resource is skipped because of this
        change, a warning will be logged as this typically indicates a
        configuration issue. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M2_(markt)/Coyote">Coyote</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65368">65368</a>: Improve handling of clean closes of inbound TLS
        connections. Treat them the same way as clean closes of non-TLS
        connections rather than as unknown errors. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Modify the HTTP/2 connector not to sent small updates for stream flow
        control windows to the user agent as, depending on how the user agent is
        written, this may trigger small writes from the user agent that in turn
        trigger the overhead protection. Small updates for stream flow control
        windows are now combined with subsequent flow control window updates for
        that stream to ensure that all stream flow control window updates sent
        from Tomcat are larger than <code>overheadWindowUpdateThreshold</code>.
        (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add additional debug logging to track the current state of the HTTP/2
        overhead count that Tomcat uses to detect and close potentially
        malicious connections. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Many HTTP/2 requests from browsers will trigger one overhead frame and
        one non-overhead frame. Change the overhead calculation so that a
        non-overhead frame reduces the current overhead count by 2 rather than
        1. This means that, over time, the overhead count for a well-behaved
        connection will trend downwards. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Change the initial HTTP/2 overhead count from <code>-10</code> to
        <code>-10 * overheadCountFactor</code>. This means that, regardless of
        the value chosen for <code>overheadCountFactor</code>, when a connection
        opens 10 overhead frames in a row will be required to trigger the
        overhead protection. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Increase the default <code>overheadCountFactor</code> from
        <code>1</code> to <code>10</code> and change the reduction in overhead
        count for a non-overhead frame from <code>-2</code> to <code>-20</code>.
        This allows for a larger range (0-20) to be used for
        <code>overheadCountFactor</code> providing for finer-grained control.
        (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Modify the parsing of HTTP header values that use the
        <code>1#token</code> to ignore empty elements as per RFC 7230 section 7
        instead of treating the presence of empty elements as an error. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Expand the unit tests for <code>HttpServlet.doHead()</code> and correct
        the flushing of the response buffer. The buffer used to behave as if it
        was one byte smaller than the configured size. The buffer was flushed
        (and the response committed if required) when the buffer was full. The
        buffer is now flushed (and the response committed if required) if the
        buffer is full and there is more data to write. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Fix an issue where concurrent HTTP/2 writes (or concurrent reads) to the
        same connection could hang and eventually timeout when async IO was
        enabled (it is enabled by default). (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M2_(markt)/Jasper">Jasper</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65387">65387</a>: Correct a regression in the fix for <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65124">65124</a>
        and restore the local definition of <code>out</code> for tags that
        implement <code>TryCatchFinally</code>. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65390">65390</a>: Correct a regression in the fix for <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65124">65124</a>
        and restore code that was removed in error leading to JSP compilation
        failures in some circumstances. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update to the Eclipse JDT compiler 4.20. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add support for specifying Java 17 (with the value <code>17</code>) as
        the compiler source and/or compiler target for JSP compilation. If used
        with an Eclipse JDT compiler version that does not support these values,
        a warning will be logged and the latest supported version will used.
        (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65377">65377</a>: Update the Java code generation for JSPs not to use
        the boxed primitive constructors as they have been deprecated in Java 9
        and marked for future removal in Java 16. <code>valueOf()</code> is now
        used instead. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M2_(markt)/WebSocket">WebSocket</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Code: " class="icon" src="./images/code.gif">
        Refactor the <code>DigestAuthenticator</code> to reuse a shared
        <code>SecureRandom</code> instance rather than create a new one to
        generate the <code>cnonce</code> if required. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M2_(markt)/Web_applications">Web applications</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65385">65385</a>: Correct the link in the documentation web application
        the Maven Central repository. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M2_(markt)/Other">Other</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Use JSign to integrate the build script with the code signing service to
        enable release builds to be created on Linux as well as Windows. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update the OWB module to Apache OpenWebBeans 2.0.23. (remm)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update the CXF module to Apache CXF 3.4.4. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65369">65369</a> / <a href="https://github.com/apache/tomcat/pull/422">#422</a>: Add the additional
        <code>--add-opens=...</code> options required for running Tomcat on Java
        16 onwards to the <code>service.bat</code> script to align it with the
        other start-up scripts. PR provided by MCMicS. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to French translations. (remm)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Korean translations. (woonsan)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update JUnit to version 4.13.2. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update EasyMock to 4.3. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update Objenesis to 3.2. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update UnboundID to 6.0.0. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update CheckStyle to 8.43. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update SpotBugs to 4.2.3. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update OSGi annotations to 1.1.0. (markt)
      </li>
    </ul>
  </div></div>
</div><h3 id="Tomcat_10.1.0-M1_(markt)"><span id="Tomcat_10.1.0-M1_(markt)_rtext" style="float: right;">2021-06-15</span> Tomcat 10.1.0-M1 (markt)</h3><div class="text">
  <div class="subsection"><h4 id="Tomcat_10.1.0-M1_(markt)/General">General</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Code: " class="icon" src="./images/code.gif">
        This release contains all of the changes up to and including those in
        Apache Tomcat 10.0.6 plus the additional changes listed below. (markt)
      </li>
      <li><img alt="Code: " class="icon" src="./images/code.gif">
        Remove code previously marked for removal in Tomcat 10.1.x. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M1_(markt)/Catalina">Catalina</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Code: " class="icon" src="./images/code.gif">
        Incremented the supported Jakarta Servlet version to 5.1 to align with
        the current development branch of the Jakarta Servlet specification.
        (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65301">65301</a>: <code>RemoteIpValve</code> will now avoid getting
        the local host name when it is not needed. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65308">65308</a>: NPE in JNDIRealm when no <code>userRoleAttribute</code>
        is given. (fschumacher)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        <a href="https://github.com/apache/tomcat/pull/412">#412</a>: Add commented out, sample users for the Tomcat Manager app
        to the default <code>tomcat-users.xml</code> file. Based on a PR by
        Arnaud Dagnelies. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        <a href="https://github.com/apache/tomcat/pull/418">#418</a>: Add a new option, <code>pass-through</code>, to the
        default servlet's <code>useBomIfPresent</code> initialization parameter
        that causes the default servlet to leave any BOM in place when
        processing a static file and not to use the BOM to determine the
        encoding of the file. Based on a pull request by Jean-Louis Monteiro.
        (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://github.com/apache/tomcat/pull/419">#419</a>: When processing POST requests of type
        <code>multipart/form-data</code> for parts without a filename that are
        added to the parameter map in String form, check the size of the part
        before attempting conversion to String. Pull request provided by
        tianshuang. (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Implement the new <code>Cookie</code> methods
        <code>setAttribute()</code>, <code>getAttribute()</code> and
        <code>getAttributes()</code> introduced in Servlet 6.0. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        AprLifecycleListener does not show dev version suffix for libtcnative
        and libapr. (michaelo)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Refactor principal handling in <code>UserDatabaseRealm</code> using
        an inner class that extends <code>GenericPrincipal</code>. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Enable the default <code>doHead()</code> implementation in
        <code>HttpServlet</code> to correctly handle responses where the content
        length needs to be represented as a long since it is larger than the
        maximum value that can be represented by an int. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Avoid synchronization on roles verification for the memory
        <code>UserDatabase</code>. (remm)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Fix the default <code>doHead()</code> implementation in
        <code>HttpServlet</code> to correctly handle responses where the Servlet
        calls <code>ServletResponse.reset()</code> and/or
        <code>ServletResponse.resetBuffer()</code>. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Fix the default <code>doHead()</code> implementation in
        <code>HttpServlet</code> to correctly handle responses generated using
        the Servlet non-blocking API. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M1_(markt)/Coyote">Coyote</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65303">65303</a>: Fix a possible <code>NullPointerException</code> if
        an error occurs on an HTTP/1.1 connection being upgraded to HTTP/2 or on
        a pushed HTTP/2 stream. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Simplify AprEndpoint socket bind for all platforms. (michaelo)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65340">65340</a>: Add missing check for a negative return value for
        <code>Hpack.decodeInteger</code> in the <code>HpackDecoder</code>,
        which could cause a <code>NegativeArraySizeException</code> exception.
        Submitted by Thomas, and verified the fix is present in the donated
        hpack code in a further update. (remm)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Add debug logging for HTTP/2 HPACK header decoding. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Correct parsing of HTTP headers consisting of a list of tokens so that a
        header with an empty token is treated consistently regardless of whether
        the empty token is at the start, middle or end of the list of tokens.
        (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Remove support for the <code>identity</code> transfer encoding. The
        inclusion of this encoding in RFC 2616 was an error that was corrected
        in 2001. Requests using this transfer encoding will now receive a 501
        response. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Process transfer encoding headers from both HTTP 1.0 and HTTP 1.1
        clients. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Ensure that if the transfer encoding header contains the
        <code>chunked</code>, that the <code>chunked</code> encoding is the
        final encoding listed. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M1_(markt)/Jasper">Jasper</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Code: " class="icon" src="./images/code.gif">
        Incremented the supported Jakarta Expression Language version to 5.0 to
        align with the current development branch of the Jakarta Expression
        Language specification. (markt)
      </li>
      <li><img alt="Code: " class="icon" src="./images/code.gif">
        Review code used to generate Java source from JSPs and tags and remove
        code found to be unnecessary. (markt)
      </li>
      <li><img alt="Code: " class="icon" src="./images/code.gif">
        Refactor use of internal <code>ChildInfo</code> class to use compile
        time type checking rather than run time type checking. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65124">65124</a>: Partial fix. When generating Java source code to call
        a tag handler, only define the local variable <code>JspWriter out</code>
        when it is going to be used. (markt)
      </li>
      <li><img alt="Code: " class="icon" src="./images/code.gif">
        Add generics to the EL 5.0 API to align with the current EL 5.0
        development branch. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update the <code>web-fragment.xml</code> included in
        <code>jasper.jar</code> and <code>jasper-el.jar</code> to use the
        Servlet 5.0 schema. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Update JspC to generate <code>web.xml</code> and
        <code>web-fragment.xml</code> files using Servlet 5.0 schemas. (markt)
      </li>
      <li><img alt="Code: " class="icon" src="./images/code.gif">
        Remove the deprecated method
        <code>MethodExpression.isParmetersProvided()</code> from the EL API to
        align with the current EL 5.0 development branch. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65358">65358</a>: Improve expression language method matching for
        methods with varargs. Where multiple methods may match the provided
        parameters, the method that requires the fewest varargs is preferred.
        (markt)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65332">65332</a>: Add a commented out section in
        <code>catalina.policy</code> that provides the necessary permissions to
        compile JSPs with javac when running on Java 9 onwards with a security
        manager. It is commented out as it will cause errors if used with
        earlier Java versions. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M1_(markt)/WebSocket">WebSocket</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65317">65317</a>: When using <code>permessage-deflate</code>, the
        WebSocket connection was incorrectly closed if the uncompressed payload
        size was an exact multiple of 8192. Based on a patch provided by Saksham
        Verma. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update the <code>web-fragment.xml</code> included in
        <code>tomcat-websocket.jar</code> to use the Servlet 5.0 schema. (markt)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65342">65342</a>: Correct a regression introduced with the fix for
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65262">65262</a> that meant Tomcat's WebSocket implementation would only
        work with Tomcat's implementation of the Jakarta WebSocket API. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M1_(markt)/Web_applications">Web applications</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        Improve the description of the <code>maxConnections</code> and
        <code>acceptCount</code> attributes in the Connector section of the
        documentation web application. (markt)
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Tomcat_10.1.0-M1_(markt)/Other">Other</h4><div class="text">
    <ul class="changelog">
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to French translations. (remm)
      </li>
      <li><img alt="Add: " class="icon" src="./images/add.gif">
        Improvements to Korean translations. (woonsan)
      </li>
      <li><img alt="Fix: " class="icon" src="./images/fix.gif">
        <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=65362">65362</a>: Correct a regression in the previous release. The
        change to create OSGi <code>Require-Capability</code> sections in
        manifests for Jakarta API JARs manually rather than with bnd annotations
        did not add the necessary manual entries to the embedded JARs. (markt)
      </li>
      <li><img alt="Update: " class="icon" src="./images/update.gif">
        Update the packaged version of the Tomcat Native Library to 1.2.30. Also
        update the minimum recommended version to 1.2.30. (markt)
      </li>
    </ul>
  </div></div>
</div></div></div></div></div><footer><div id="footer">
    Copyright &copy; 1999-2025, The Apache Software Foundation
    <br>
    Apache Tomcat, Tomcat, Apache, the Apache Tomcat logo and the Apache logo
    are either registered trademarks or trademarks of the Apache Software
    Foundation.
    </div></footer></div></body></html>