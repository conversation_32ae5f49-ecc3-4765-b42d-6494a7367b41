<!DOCTYPE html SYSTEM "about:legacy-compat">
<html lang="en"><head><META http-equiv="Content-Type" content="text/html; charset=UTF-8"><link href="./images/docs-stylesheet.css" rel="stylesheet" type="text/css"><title>Apache Tomcat 10 (10.1.41) - Proxy Support How-To</title><meta name="author" content="<PERSON>"></head><body><div id="wrapper"><header><div id="header"><div><div><div class="logo noPrint"><a href="https://tomcat.apache.org/"><img alt="Tomcat Home" src="./images/tomcat.png"></a></div><div style="height: 1px;"></div><div class="asfLogo noPrint"><a href="https://www.apache.org/" target="_blank"><img src="./images/asf-logo.svg" alt="The Apache Software Foundation" style="width: 266px; height: 83px;"></a></div><h1>Apache Tomcat 10</h1><div class="versionInfo">
            Version 10.1.41,
            <time datetime="2025-05-08">May 8 2025</time></div><div style="height: 1px;"></div><div style="clear: left;"></div></div></div></div></header><div id="middle"><div><div id="mainLeft" class="noprint"><div><nav><div><h2>Links</h2><ul><li><a href="index.html">Docs Home</a></li><li><a href="https://cwiki.apache.org/confluence/display/TOMCAT/FAQ">FAQ</a></li></ul></div><div><h2>User Guide</h2><ul><li><a href="introduction.html">1) Introduction</a></li><li><a href="setup.html">2) Setup</a></li><li><a href="appdev/index.html">3) First webapp</a></li><li><a href="deployer-howto.html">4) Deployer</a></li><li><a href="manager-howto.html">5) Manager</a></li><li><a href="host-manager-howto.html">6) Host Manager</a></li><li><a href="realm-howto.html">7) Realms and AAA</a></li><li><a href="security-manager-howto.html">8) Security Manager</a></li><li><a href="jndi-resources-howto.html">9) JNDI Resources</a></li><li><a href="jndi-datasource-examples-howto.html">10) JDBC DataSources</a></li><li><a href="class-loader-howto.html">11) Classloading</a></li><li><a href="jasper-howto.html">12) JSPs</a></li><li><a href="ssl-howto.html">13) SSL/TLS</a></li><li><a href="ssi-howto.html">14) SSI</a></li><li><a href="cgi-howto.html">15) CGI</a></li><li><a href="proxy-howto.html">16) Proxy Support</a></li><li><a href="mbeans-descriptors-howto.html">17) MBeans Descriptors</a></li><li><a href="default-servlet.html">18) Default Servlet</a></li><li><a href="cluster-howto.html">19) Clustering</a></li><li><a href="balancer-howto.html">20) Load Balancer</a></li><li><a href="connectors.html">21) Connectors</a></li><li><a href="monitoring.html">22) Monitoring and Management</a></li><li><a href="logging.html">23) Logging</a></li><li><a href="apr.html">24) APR/Native</a></li><li><a href="virtual-hosting-howto.html">25) Virtual Hosting</a></li><li><a href="aio.html">26) Advanced IO</a></li><li><a href="maven-jars.html">27) Mavenized</a></li><li><a href="security-howto.html">28) Security Considerations</a></li><li><a href="windows-service-howto.html">29) Windows Service</a></li><li><a href="windows-auth-howto.html">30) Windows Authentication</a></li><li><a href="jdbc-pool.html">31) Tomcat's JDBC Pool</a></li><li><a href="web-socket-howto.html">32) WebSocket</a></li><li><a href="rewrite.html">33) Rewrite</a></li><li><a href="cdi.html">34) CDI 2 and JAX-RS</a></li><li><a href="graal.html">35) AOT/GraalVM Support</a></li></ul></div><div><h2>Reference</h2><ul><li><a href="RELEASE-NOTES.txt">Release Notes</a></li><li><a href="config/index.html">Configuration</a></li><li><a href="api/index.html">Tomcat Javadocs</a></li><li><a href="servletapi/index.html">Servlet 6.0 Javadocs</a></li><li><a href="jspapi/index.html">JSP 3.1 Javadocs</a></li><li><a href="elapi/index.html">EL 5.0 Javadocs</a></li><li><a href="websocketapi/index.html">WebSocket 2.1 Javadocs</a></li><li><a href="jaspicapi/index.html">Authentication 3.0 Javadocs</a></li><li><a href="annotationapi/index.html">Annotations 2.1 Javadocs</a></li><li><a href="https://tomcat.apache.org/connectors-doc/">JK 1.2 Documentation</a></li></ul></div><div><h2>Apache Tomcat Development</h2><ul><li><a href="building.html">Building</a></li><li><a href="changelog.html">Changelog</a></li><li><a href="https://cwiki.apache.org/confluence/display/TOMCAT/Tomcat+Versions">Status</a></li><li><a href="developers.html">Developers</a></li><li><a href="architecture/index.html">Architecture</a></li><li><a href="tribes/introduction.html">Tribes</a></li></ul></div></nav></div></div><div id="mainRight"><div id="content"><h2>Proxy Support How-To</h2><h3 id="Table_of_Contents">Table of Contents</h3><div class="text">
<ul><li><a href="#Introduction">Introduction</a></li><li><a href="#Apache_httpd_Proxy_Support">Apache httpd Proxy Support</a></li></ul>
</div><h3 id="Introduction">Introduction</h3><div class="text">

<p>Using standard configurations of Tomcat, web applications can ask for
the server name and port number to which the request was directed for
processing.  When Tomcat is running standalone with the
<a href="config/http.html">HTTP/1.1 Connector</a>, it will generally
report the server name specified in the request, and the port number on
which the <strong>Connector</strong> is listening.  The servlet API
calls of interest, for this purpose, are:</p>
<ul>
<li><code>ServletRequest.getServerName()</code>: Returns the host name of the server to which the request was sent.</li>
<li><code>ServletRequest.getServerPort()</code>: Returns the port number of the server to which the request was sent.</li>
<li><code>ServletRequest.getLocalName()</code>: Returns the host name of the Internet Protocol (IP) interface on which the request was received.</li>
<li><code>ServletRequest.getLocalPort()</code>:  Returns the Internet Protocol (IP) port number of the interface on which the request was received.</li>
</ul>

<p>When you are running behind a proxy server (or a web server that is
configured to behave like a proxy server), you will sometimes prefer to
manage the values returned by these calls.  In particular, you will
generally want the port number to reflect that specified in the original
request, not the one on which the <strong>Connector</strong> itself is
listening.  You can use the <code>proxyName</code> and <code>proxyPort</code>
attributes on the <code>&lt;Connector&gt;</code> element to configure
these values.</p>

<p>Proxy support can take many forms.  The following sections describe
proxy configurations for several common cases.</p>

</div><h3 id="Apache_httpd_Proxy_Support">Apache httpd Proxy Support</h3><div class="text">

<p>Apache httpd 1.3 and later versions support an optional module
(<code>mod_proxy</code>) that configures the web server to act as a proxy
server.  This can be used to forward requests for a particular web application
to a Tomcat instance, without having to configure a web connector such as
<code>mod_jk</code>.  To accomplish this, you need to perform the following
tasks:</p>
<ol>
<li><p>Configure your copy of Apache so that it includes the
    <code>mod_proxy</code> module.  If you are building from source,
    the easiest way to do this is to include the
    <code>--enable-module=proxy</code> directive on the
    <code>./configure</code> command line.</p></li>
<li><p>If not already added for you, make sure that you are loading the
    <code>mod_proxy</code> module at Apache startup time, by using the
    following directives in your <code>httpd.conf</code> file:</p>
<div class="codeBox"><pre><code>LoadModule proxy_module  {path-to-modules}/mod_proxy.so
</code></pre></div></li>
<li><p>Include two directives in your <code>httpd.conf</code> file for
    each web application that you wish to forward to Tomcat.  For
    example, to forward an application at context path <code>/myapp</code>:</p>
<div class="codeBox"><pre><code>ProxyPass         /myapp  http://localhost:8081/myapp
ProxyPassReverse  /myapp  http://localhost:8081/myapp</code></pre></div>
    <p>which tells Apache to forward URLs of the form
    <code>http://localhost/myapp/*</code> to the Tomcat connector
    listening on port 8081.</p></li>
<li><p>Configure your copy of Tomcat to include a special
    <code>&lt;Connector&gt;</code> element, with appropriate
    proxy settings, for example:</p>
<div class="codeBox"><pre><code>&lt;Connector port="8081" ...
              proxyName="www.mycompany.com"
              proxyPort="80"/&gt;</code></pre></div>
    <p>which will cause servlets inside this web application to think that
    all proxied requests were directed to <code>www.mycompany.com</code>
    on port 80.</p></li>
<li><p>It is legal to omit the <code>proxyName</code> attribute from the
    <code>&lt;Connector&gt;</code> element.  If you do so, the value
    returned by <code>request.getServerName()</code> will by the host
    name on which Tomcat is running.  In the example above, it would be
    <code>localhost</code>.</p></li>
<li><p>If you also have a <code>&lt;Connector&gt;</code> listening on port
    8080 (nested within the same <a href="config/service.html">Service</a>
    element), the requests to either port will share the same set of
    virtual hosts and web applications.</p></li>
<li><p>You might wish to use the IP filtering features of your operating
    system to restrict connections to port 8081 (in this example) to
    be allowed <strong>only</strong> from the server that is running
    Apache.</p></li>
<li><p>Alternatively, you can set up a series of web applications that are
    only available via proxying, as follows:</p>
    <ul>
    <li>Configure another <code>&lt;Service&gt;</code> that contains
        only a <code>&lt;Connector&gt;</code> for the proxy port.</li>
    <li>Configure appropriate <a href="config/engine.html">Engine</a>,
        <a href="config/host.html">Host</a>, and
        <a href="config/context.html">Context</a> elements for the virtual hosts
        and web applications accessible via proxying.</li>
    <li>Optionally, protect port 8081 with IP filters as described
        earlier.</li>
    </ul></li>
<li><p>When requests are proxied by Apache, the web server will be recording
    these requests in its access log.  Therefore, you will generally want to
    disable any access logging performed by Tomcat itself.</p></li>
</ol>

<p>When requests are proxied in this manner, <strong>all</strong> requests
for the configured web applications will be processed by Tomcat (including
requests for static content).  You can improve performance by using the
<code>mod_jk</code> web connector instead of <code>mod_proxy</code>.
<code>mod_jk</code> can be configured so that the web server serves static
content that is not processed by filters or security constraints defined
within the web application's deployment descriptor
(<code>/WEB-INF/web.xml</code>).</p>

</div></div></div></div></div><footer><div id="footer">
    Copyright &copy; 1999-2025, The Apache Software Foundation
    <br>
    Apache Tomcat, Tomcat, Apache, the Apache Tomcat logo and the Apache logo
    are either registered trademarks or trademarks of the Apache Software
    Foundation.
    </div></footer></div></body></html>