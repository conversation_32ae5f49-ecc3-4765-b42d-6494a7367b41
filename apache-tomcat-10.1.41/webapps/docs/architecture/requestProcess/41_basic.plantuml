@startuml

' Licensed to the Apache Software Foundation (ASF) under one or more
' contributor license agreements.  See the NOTICE file distributed with
' this work for additional information regarding copyright ownership.
' The ASF licenses this file to You under the Apache License, Version 2.0
' (the "License"); you may not use this file except in compliance with
' the License.  You may obtain a copy of the License at
'
'     http://www.apache.org/licenses/LICENSE-2.0
'
' Unless required by applicable law or agreed to in writing, software
' distributed under the License is distributed on an "AS IS" BASIS,
' WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
' See the License for the specific language governing permissions and
' limitations under the License.

hide footbox
skinparam style strictuml

participant MimeHeaders
participant CoyoteRequest
participant Request
activate ErrorReportValve

ErrorReportValve -> StandardHostValve ++: invoke()
StandardHostValve -> Request ++: getContext()
return
StandardHostValve -> Context ++: bind()
return
StandardHostValve -> Context ++: getPipeline()
return
StandardHostValve -> "StandardPipeline\n(Context)" ++: getFirst()
return
StandardHostValve -> BasicAuthenticator ++: invoke()
BasicAuthenticator -> Context ++: getRealm()
return
BasicAuthenticator -> LockoutRealm ++: findSecurityContraints()
return
BasicAuthenticator -> LockoutRealm ++: hasUserDataPermission()
return
BasicAuthenticator -> BasicAuthenticator ++: doAuthenticate()
BasicAuthenticator -> Request ++: getCoyoteRequest()
return
BasicAuthenticator -> CoyoteRequest ++: getMimeHeaders()
return
BasicAuthenticator -> MimeHeaders ++: getValue("authorization")
return
BasicAuthenticator --> BasicCredentials **:
BasicAuthenticator -> BasicCredentials ++: getUserName()
return
BasicAuthenticator -> BasicCredentials ++: getPassword()
return
BasicAuthenticator -> Context ++: getRealm()
return
BasicAuthenticator -> LockoutRealm ++: authenticate()
LockoutRealm -> UserDatabaseRealm ++: authenticate()
UserDatabaseRealm -> UserDatabaseRealm ++: getPassword(username)
UserDatabaseRealm -> CredentialHandler ++: matches()
return
UserDatabaseRealm -> UserDatabaseRealm ++: getPrincipal()
return
return
return
return
return
BasicAuthenticator -> Realm ++: hasResourcePermission()
return
BasicAuthenticator -> BasicAuthenticator ++: getNext()
return
BasicAuthenticator -> StandardContextValve ++: invoke()
note right of StandardContextValve
  Standard Servlet request
  processing continues from
  this point
end note

@enduml