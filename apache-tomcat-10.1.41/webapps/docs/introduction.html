<!DOCTYPE html SYSTEM "about:legacy-compat">
<html lang="en"><head><META http-equiv="Content-Type" content="text/html; charset=UTF-8"><link href="./images/docs-stylesheet.css" rel="stylesheet" type="text/css"><title>Apache Tomcat 10 (10.1.41) - Introduction</title><meta name="author" content="Robert Slifka"></head><body><div id="wrapper"><header><div id="header"><div><div><div class="logo noPrint"><a href="https://tomcat.apache.org/"><img alt="Tomcat Home" src="./images/tomcat.png"></a></div><div style="height: 1px;"></div><div class="asfLogo noPrint"><a href="https://www.apache.org/" target="_blank"><img src="./images/asf-logo.svg" alt="The Apache Software Foundation" style="width: 266px; height: 83px;"></a></div><h1>Apache Tomcat 10</h1><div class="versionInfo">
            Version 10.1.41,
            <time datetime="2025-05-08">May 8 2025</time></div><div style="height: 1px;"></div><div style="clear: left;"></div></div></div></div></header><div id="middle"><div><div id="mainLeft" class="noprint"><div><nav><div><h2>Links</h2><ul><li><a href="index.html">Docs Home</a></li><li><a href="https://cwiki.apache.org/confluence/display/TOMCAT/FAQ">FAQ</a></li></ul></div><div><h2>User Guide</h2><ul><li><a href="introduction.html">1) Introduction</a></li><li><a href="setup.html">2) Setup</a></li><li><a href="appdev/index.html">3) First webapp</a></li><li><a href="deployer-howto.html">4) Deployer</a></li><li><a href="manager-howto.html">5) Manager</a></li><li><a href="host-manager-howto.html">6) Host Manager</a></li><li><a href="realm-howto.html">7) Realms and AAA</a></li><li><a href="security-manager-howto.html">8) Security Manager</a></li><li><a href="jndi-resources-howto.html">9) JNDI Resources</a></li><li><a href="jndi-datasource-examples-howto.html">10) JDBC DataSources</a></li><li><a href="class-loader-howto.html">11) Classloading</a></li><li><a href="jasper-howto.html">12) JSPs</a></li><li><a href="ssl-howto.html">13) SSL/TLS</a></li><li><a href="ssi-howto.html">14) SSI</a></li><li><a href="cgi-howto.html">15) CGI</a></li><li><a href="proxy-howto.html">16) Proxy Support</a></li><li><a href="mbeans-descriptors-howto.html">17) MBeans Descriptors</a></li><li><a href="default-servlet.html">18) Default Servlet</a></li><li><a href="cluster-howto.html">19) Clustering</a></li><li><a href="balancer-howto.html">20) Load Balancer</a></li><li><a href="connectors.html">21) Connectors</a></li><li><a href="monitoring.html">22) Monitoring and Management</a></li><li><a href="logging.html">23) Logging</a></li><li><a href="apr.html">24) APR/Native</a></li><li><a href="virtual-hosting-howto.html">25) Virtual Hosting</a></li><li><a href="aio.html">26) Advanced IO</a></li><li><a href="maven-jars.html">27) Mavenized</a></li><li><a href="security-howto.html">28) Security Considerations</a></li><li><a href="windows-service-howto.html">29) Windows Service</a></li><li><a href="windows-auth-howto.html">30) Windows Authentication</a></li><li><a href="jdbc-pool.html">31) Tomcat's JDBC Pool</a></li><li><a href="web-socket-howto.html">32) WebSocket</a></li><li><a href="rewrite.html">33) Rewrite</a></li><li><a href="cdi.html">34) CDI 2 and JAX-RS</a></li><li><a href="graal.html">35) AOT/GraalVM Support</a></li></ul></div><div><h2>Reference</h2><ul><li><a href="RELEASE-NOTES.txt">Release Notes</a></li><li><a href="config/index.html">Configuration</a></li><li><a href="api/index.html">Tomcat Javadocs</a></li><li><a href="servletapi/index.html">Servlet 6.0 Javadocs</a></li><li><a href="jspapi/index.html">JSP 3.1 Javadocs</a></li><li><a href="elapi/index.html">EL 5.0 Javadocs</a></li><li><a href="websocketapi/index.html">WebSocket 2.1 Javadocs</a></li><li><a href="jaspicapi/index.html">Authentication 3.0 Javadocs</a></li><li><a href="annotationapi/index.html">Annotations 2.1 Javadocs</a></li><li><a href="https://tomcat.apache.org/connectors-doc/">JK 1.2 Documentation</a></li></ul></div><div><h2>Apache Tomcat Development</h2><ul><li><a href="building.html">Building</a></li><li><a href="changelog.html">Changelog</a></li><li><a href="https://cwiki.apache.org/confluence/display/TOMCAT/Tomcat+Versions">Status</a></li><li><a href="developers.html">Developers</a></li><li><a href="architecture/index.html">Architecture</a></li><li><a href="tribes/introduction.html">Tribes</a></li></ul></div></nav></div></div><div id="mainRight"><div id="content"><h2>Introduction</h2><h3 id="Table_of_Contents">Table of Contents</h3><div class="text">
<ul><li><a href="#Introduction">Introduction</a></li><li><a href="#Terminology">Terminology</a></li><li><a href="#Directories_and_Files">Directories and Files</a></li><li><a href="#CATALINA_HOME_and_CATALINA_BASE">CATALINA_HOME and CATALINA_BASE</a><ol><li><a href="#Why_Use_CATALINA_BASE">Why Use CATALINA_BASE</a></li><li><a href="#Contents_of_CATALINA_BASE">Contents of CATALINA_BASE</a></li><li><a href="#How_to_Use_CATALINA_BASE">How to Use CATALINA_BASE</a></li></ol></li><li><a href="#Configuring_Tomcat">Configuring Tomcat</a></li><li><a href="#Where_to_Go_for_Help">Where to Go for Help</a></li></ul>
</div><h3 id="Introduction">Introduction</h3><div class="text">

<p>For administrators and web developers alike, there are some important bits
of information you should familiarize yourself with before starting out. This
document serves as a brief introduction to some of the concepts and
terminology behind the Tomcat container. As well, where to go when you need
help.</p>

</div><h3 id="Terminology">Terminology</h3><div class="text">

<p>In the course of reading these documents, you will run across a number of
terms; some specific to Tomcat, and others defined by the
<a href="https://cwiki.apache.org/confluence/display/TOMCAT/Specifications">Servlet and
JSP specifications</a>.</p>

<ul>
<li><strong>Context</strong> - In a nutshell, a Context is a
    web application.</li>
</ul>
<p>That is it. If you find any more terms we need to add to this section, please
do let us know.</p>

</div><h3 id="Directories_and_Files">Directories and Files</h3><div class="text">

<p>These are some of the key tomcat directories:</p>

<ul>
<li><strong>/bin</strong> - Startup, shutdown, and other scripts. The
    <code>*.sh</code> files (for Unix systems) are functional duplicates of
    the <code>*.bat</code> files (for Windows systems).  Since the Win32
    command-line lacks certain functionality, there are some additional
    files in here.</li>
<li><strong>/conf</strong> - Configuration files and related DTDs.  The most
    important file in here is server.xml.  It is the main configuration file
    for the container.</li>
<li><strong>/logs</strong> - Log files are here by default.</li>
<li><strong>/webapps</strong> - This is where your webapps go.</li>
</ul>

</div><h3 id="CATALINA_HOME_and_CATALINA_BASE">CATALINA_HOME and CATALINA_BASE</h3><div class="text">
  <p>Throughout the documentation, there are references to the two following
    properties:
    <ul>
      <li>
        <strong>CATALINA_HOME</strong>: Represents the root of your Tomcat
        installation, for example <code>/home/<USER>/apache-tomcat-9.0.10</code>
        or <code>C:\Program Files\apache-tomcat-9.0.10</code>.
      </li>
      <li>
        <strong>CATALINA_BASE</strong>: Represents the root of a runtime
        configuration of a specific Tomcat instance. If you want to have
        multiple Tomcat instances on one machine, use the <code>CATALINA_BASE</code>
        property.
      </li>
    </ul>
  </p>
  <p>
    If you set the properties to different locations, the CATALINA_HOME location
    contains static sources, such as <code>.jar</code> files, or binary files.
    The CATALINA_BASE location contains configuration files, log files, deployed
    applications, and other runtime requirements.
  </p>
  <div class="subsection"><h4 id="Why_Use_CATALINA_BASE">Why Use CATALINA_BASE</h4><div class="text">
    <p>
      By default, CATALINA_HOME and CATALINA_BASE point to the same directory.
      Set CATALINA_BASE manually when you require running multiple Tomcat
      instances on one machine. Doing so provides the following benefits:
    </p>
    <ul>
      <li>
        Easier management of upgrading to a newer version of Tomcat. Because all
        instances with single CATALINA_HOME location share one set of
        <code>.jar</code> files and binary files, you can easily upgrade the files
        to newer version and have the change propagated to all Tomcat instances
        using the same CATALIA_HOME directory.
      </li>
      <li>
        Avoiding duplication of the same static <code>.jar</code> files.
      </li>
      <li>
        The possibility to share certain settings, for example the <code>setenv</code> shell
        or bat script file (depending on your operating system).
      </li>
    </ul>
  </div></div>
  <div class="subsection"><h4 id="Contents_of_CATALINA_BASE">Contents of CATALINA_BASE</h4><div class="text">
    <p>
      Before you start using CATALINA_BASE, first consider and create the
      directory tree used by CATALINA_BASE. Note that if you do not create
      all the recommended directories, Tomcat creates the directories
      automatically. If it fails to create the necessary directory, for example
      due to permission issues, Tomcat will either fail to start, or may not
      function correctly.
    </p>
    <p>
      Consider the following list of directories:
      <ul>
        <li>
          <p>
            The <code>bin</code> directory with the <code>setenv.sh</code>,
            <code>setenv.bat</code>, and <code>tomcat-juli.jar</code> files.
          </p>
          <p>
            <i>Recommended:</i> No.
          </p>
          <p>
            <i>Order of lookup:</i> CATALINA_BASE is checked first; fallback is provided
            to CATALINA_HOME.
          </p>
        </li>
        <li>
          <p>
            The <code>lib</code> directory with further resources to be added on
            classpath.
          </p>
          <p>
            <i>Recommended:</i> Yes, if your application depends on external libraries.
          </p>
          <p>
            <i>Order of lookup:</i> CATALINA_BASE is checked first; CATALINA_HOME is
            loaded second.
          </p>
        </li>
        <li>
          <p>
            The <code>logs</code> directory for instance-specific log files.
          </p>
          <p>
            <i>Recommended:</i> Yes.
          </p>
        </li>
        <li>
          <p>
            The <code>webapps</code> directory for automatically loaded web
            applications.
          </p>
          <p>
            <i>Recommended:</i> Yes, if you want to deploy applications.
          </p>
          <p>
            <i>Order of lookup:</i> CATALINA_BASE only.
          </p>
        </li>
        <li>
          <p>
            The <code>work</code> directory that contains temporary working
            directories for the deployed web applications.
          </p>
          <p>
            <i>Recommended:</i> Yes.
          </p>
        </li>
        <li>
          <p>
            The <code>temp</code> directory used by the JVM for temporary files.
          </p>
          <p>
            <i>Recommended:</i> Yes.
          </p>
        </li>
      </ul>
    </p>
    <p>
      We recommend you not to change the <code>tomcat-juli.jar</code> file.
      However, in case you require your own logging implementation, you can
      replace the <code>tomcat-juli.jar</code> file in a CATALINA_BASE location
      for the specific Tomcat instance.
    </p>
    <p>
      We also recommend you copy all configuration files from the
      <code>CATALINA_HOME/conf</code> directory into the
      <code>CATALINA_BASE/conf/</code> directory. In case a configuration file
      is missing in CATALINA_BASE, there is no fallback to CATALINA_HOME.
      Consequently, this may cause failure.
    </p>
    <p>
      At minimum, CATALINA_BASE must contain:
      <ul>
        <li>conf/server.xml</li>
        <li>conf/web.xml</li>
      </ul>
      That includes the <code>conf</code> directory. Otherwise, Tomcat fails
      to start, or fails to function properly.
    </p>
    <p>
      For advanced configuration information, see the
      <a href="https://tomcat.apache.org/tomcat-9.0-doc/RUNNING.txt">
        RUNNING.txt
      </a> file.
    </p>
  </div></div>
  <div class="subsection"><h4 id="How_to_Use_CATALINA_BASE">How to Use CATALINA_BASE</h4><div class="text">
    <p>
      The CATALINA_BASE property is an environment variable. You can set it
      before you execute the Tomcat start script, for example:
      <ul>
        <li>On Unix: <code>CATALINA_BASE=/tmp/tomcat_base1 bin/catalina.sh start</code></li>
        <li>On Windows: <code>CATALINA_BASE=C:\tomcat_base1 bin/catalina.bat start</code></li>
      </ul>
    </p>
  </div></div>
</div><h3 id="Configuring_Tomcat">Configuring Tomcat</h3><div class="text">

<p>This section will acquaint you with the basic information used during
the configuration of the container.</p>

<p>All of the information in the configuration files is read at startup,
meaning that any change to the files necessitates a restart of the container.
</p>

</div><h3 id="Where_to_Go_for_Help">Where to Go for Help</h3><div class="text">

<p>While we've done our best to ensure that these documents are clearly
written and easy to understand, we may have missed something.  Provided
below are various web sites and mailing lists in case you get stuck.</p>

<p>Keep in mind that some of the issues and solutions vary between the
major versions of Tomcat.  As you search around the web, there will be
some documentation that is not relevant to Tomcat 10, but
only to earlier versions.</p>

<ul>
<li>Current document - most documents will list potential hangups. Be sure
    to fully read the relevant documentation as it will save you much time
    and effort. There's nothing like scouring the web only to find out that
    the answer was right in front of you all along!</li>
<li><a href="https://cwiki.apache.org/confluence/display/TOMCAT/FAQ">Tomcat FAQ</a></li>
<li><a href="https://cwiki.apache.org/confluence/display/TOMCAT/">Tomcat WIKI</a></li>
<li>Tomcat FAQ at <a href="http://www.jguru.com/faq/home.jsp?topic=Tomcat">jGuru</a></li>
<li>Tomcat mailing list archives - numerous sites archive the Tomcat mailing
    lists. Since the links change over time, clicking here will search
    <a href="https://www.google.com/search?q=tomcat+mailing+list+archives">Google</a>.
    </li>
<li>The TOMCAT-USER mailing list, which you can subscribe to
    <a href="https://tomcat.apache.org/lists.html">here</a>. If you don't
    get a reply, then there's a good chance that your question was probably
    answered in the list archives or one of the FAQs.  Although questions
    about web application development in general are sometimes asked and
    answered, please focus your questions on Tomcat-specific issues.</li>
<li>The TOMCAT-DEV mailing list, which you can subscribe to
    <a href="https://tomcat.apache.org/lists.html">here</a>.  This list is
    <strong>reserved</strong> for discussions about the development of Tomcat
    itself.  Questions about Tomcat configuration, and the problems you run
    into while developing and running applications, will normally be more
    appropriate on the TOMCAT-USER list instead.</li>
</ul>

<p>And, if you think something should be in the docs, by all means let us know
on the TOMCAT-DEV list.</p>

</div></div></div></div></div><footer><div id="footer">
    Copyright &copy; 1999-2025, The Apache Software Foundation
    <br>
    Apache Tomcat, Tomcat, Apache, the Apache Tomcat logo and the Apache logo
    are either registered trademarks or trademarks of the Apache Software
    Foundation.
    </div></footer></div></body></html>