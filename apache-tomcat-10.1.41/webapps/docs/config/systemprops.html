<!DOCTYPE html SYSTEM "about:legacy-compat">
<html lang="en"><head><META http-equiv="Content-Type" content="text/html; charset=UTF-8"><link href="../images/docs-stylesheet.css" rel="stylesheet" type="text/css"><title>Apache Tomcat 10 Configuration Reference (10.1.41) - System Properties</title></head><body><div id="wrapper"><header><div id="header"><div><div><div class="logo noPrint"><a href="https://tomcat.apache.org/"><img alt="Tomcat Home" src="../images/tomcat.png"></a></div><div style="height: 1px;"></div><div class="asfLogo noPrint"><a href="https://www.apache.org/" target="_blank"><img src="../images/asf-logo.svg" alt="The Apache Software Foundation" style="width: 266px; height: 83px;"></a></div><h1>Apache Tomcat 10 Configuration Reference</h1><div class="versionInfo">
            Version 10.1.41,
            <time datetime="2025-05-08">May 8 2025</time></div><div style="height: 1px;"></div><div style="clear: left;"></div></div></div></div></header><div id="middle"><div><div id="mainLeft" class="noprint"><div><nav><div><h2>Links</h2><ul><li><a href="../index.html">Docs Home</a></li><li><a href="index.html">Config Ref. Home</a></li><li><a href="https://cwiki.apache.org/confluence/display/TOMCAT/FAQ">FAQ</a></li><li><a href="#comments_section">User Comments</a></li></ul></div><div><h2>Top Level Elements</h2><ul><li><a href="server.html">Server</a></li><li><a href="service.html">Service</a></li></ul></div><div><h2>Executors</h2><ul><li><a href="executor.html">Executor</a></li></ul></div><div><h2>Connectors</h2><ul><li><a href="http.html">HTTP/1.1</a></li><li><a href="http2.html">HTTP/2</a></li><li><a href="ajp.html">AJP</a></li></ul></div><div><h2>Containers</h2><ul><li><a href="context.html">Context</a></li><li><a href="engine.html">Engine</a></li><li><a href="host.html">Host</a></li><li><a href="cluster.html">Cluster</a></li></ul></div><div><h2>Nested Components</h2><ul><li><a href="cookie-processor.html">CookieProcessor</a></li><li><a href="credentialhandler.html">CredentialHandler</a></li><li><a href="globalresources.html">Global Resources</a></li><li><a href="jar-scanner.html">JarScanner</a></li><li><a href="jar-scan-filter.html">JarScanFilter</a></li><li><a href="listeners.html">Listeners</a></li><li><a href="loader.html">Loader</a></li><li><a href="manager.html">Manager</a></li><li><a href="realm.html">Realm</a></li><li><a href="resources.html">Resources</a></li><li><a href="sessionidgenerator.html">SessionIdGenerator</a></li><li><a href="valve.html">Valve</a></li></ul></div><div><h2>Cluster Elements</h2><ul><li><a href="cluster.html">Cluster</a></li><li><a href="cluster-manager.html">Manager</a></li><li><a href="cluster-channel.html">Channel</a></li><li><a href="cluster-membership.html">Channel/Membership</a></li><li><a href="cluster-sender.html">Channel/Sender</a></li><li><a href="cluster-receiver.html">Channel/Receiver</a></li><li><a href="cluster-interceptor.html">Channel/Interceptor</a></li><li><a href="cluster-valve.html">Valve</a></li><li><a href="cluster-deployer.html">Deployer</a></li><li><a href="cluster-listener.html">ClusterListener</a></li></ul></div><div><h2>web.xml</h2><ul><li><a href="filter.html">Filter</a></li></ul></div><div><h2>Other</h2><ul><li><a href="runtime-attributes.html">Runtime attributes</a></li><li><a href="systemprops.html">System properties</a></li><li><a href="jaspic.html">Jakarta Authentication</a></li></ul></div></nav></div></div><div id="mainRight"><div id="content"><h2>System Properties</h2><h3 id="Table_of_Contents">Table of Contents</h3><div class="text">
<ul><li><a href="#Introduction">Introduction</a></li><li><a href="#Property_replacements">Property replacements</a></li><li><a href="#Expression_Language">Expression Language</a></li><li><a href="#Specifications">Specifications</a></li><li><a href="#Logging">Logging</a></li><li><a href="#JAR_Scanning">JAR Scanning</a></li><li><a href="#Websockets">Websockets</a></li><li><a href="#Other">Other</a></li></ul>
</div><h3 id="Introduction">Introduction</h3><div class="text">
  <p>The following sections list the system properties that may be set to modify
  the default Tomcat behaviour.</p>
</div><h3 id="Property_replacements">Property replacements</h3><div class="text">
  <table class="defaultTable"><tr><th style="width: 15%;">
          Property
        </th><th style="width: 85%;">
          Description
        </th></tr><tr><td><code class="propertyName">org.apache.tomcat.util.digester. PROPERTY_SOURCE</code></td><td>
      <p>Set this to a comma separated list of fully qualified name of classes
         that implement
         <code>org.apache.tomcat.util.IntrospectionUtils.PropertySource</code>.
         Required to have a public constructor with no arguments.</p>
      <p>Use this to add a property source, that will be invoked when
         <code>${parameter:-default-value}</code> denoted parameters (with
         optional default values) are found in the XML files that Tomcat
         parses.</p>
      <p>Property replacement from the specified property source on the JVM
         system properties can also be done using the
         <code>REPLACE_SYSTEM_PROPERTIES</code> system property.</p>
      <p><code>org.apache.tomcat.util.digester.ServiceBindingPropertySource</code>
        can be used to replace parameters from any Kubernetes service bindings
        that follows the <a href="https://servicebinding.io/">servicebinding.io</a> spec</p>
      <p><code>org.apache.tomcat.util.digester.EnvironmentPropertySource</code>
         can be used to replace parameters from the process' environment
         variables, e.g. injected ConfigMaps or Secret objects in container
         based systems like OpenShift or Kubernetes.</p>
      <p><code>org.apache.tomcat.util.digester.SystemPropertySource</code>
         does replacement with system properties. It is always enabled,
         but can also be specified as part of the property value.</p>
    </td></tr><tr><td><code class="propertyName">org.apache.tomcat.util.digester. REPLACE_SYSTEM_PROPERTIES</code></td><td>
      <p>Set this boolean system property to <code>true</code> to cause
         property replacement from the digester property source on the JVM
         system properties.</p>
    </td></tr></table>

</div><h3 id="Expression_Language">Expression Language</h3><div class="text">
  <table class="defaultTable"><tr><th style="width: 15%;">
          Property
        </th><th style="width: 85%;">
          Description
        </th></tr><tr><td><code class="propertyName">org.apache.el. GET_CLASSLOADER_USE_PRIVILEGED</code></td><td>
      <p>Controls whether the EL API classes make use of a privileged block to
      obtain the thread context class loader. When using the EL API within
      Apache Tomcat this does not need to be set as all calls are already
      wrapped in a privileged block further up the stack. It may be required if
      using the EL API under a SecurityManager outside of Apache Tomcat.</p>
      <p>If not specified, the default of <code>false</code> will be used.</p>
    </td></tr><tr><td><code class="propertyName">org.apache.el.BeanELResolver. CACHE_SIZE</code></td><td>
      <p>The number of jakarta.el.BeanELResolver.BeanProperties objects that will
      be cached by the EL Parser.</p>
      <p>If not specified, the default of <code>1000</code> will be used.</p>
    </td></tr><tr><td><code class="propertyName">org.apache.el.ExpressionBuilder. CACHE_SIZE</code></td><td>
      <p>The number of parsed EL expressions that will be cached by the EL
      Parser.</p>
      <p>If not specified, the default of <code>5000</code> will be used.</p>
    </td></tr><tr><td><code class="propertyName">org.apache.el.parser. COERCE_TO_ZERO</code></td><td>
      <p>If <code>true</code>, when coercing <code>null</code>s to objects of
      type Number, Character or Boolean the result will be <code>0</code> for
      Number and Character types and <code>false</code> for Boolean as required
      by the EL 2.2 and earlier specifications. If this property is
      <code>false</code> the result of the coercion will be <code>null</code> as
      required by the EL 3.0 specification.</p>
      <p>If not specified, the default value of <code>false</code> will be
      used.</p>
    </td></tr><tr><td><code class="propertyName">org.apache.el.parser. SKIP_IDENTIFIER_CHECK</code></td><td>
      <p>If <code>true</code>, when parsing expressions, identifiers will not be
      checked to ensure that they conform to the Java Language Specification for
      Java identifiers.</p>
      <p>If not specified, the default value of <code>false</code> will be used.</p>
    </td></tr></table>
</div><h3 id="Specifications">Specifications</h3><div class="text">

  <table class="defaultTable"><tr><th style="width: 15%;">
          Property
        </th><th style="width: 85%;">
          Description
        </th></tr><tr><td><code class="propertyName">org.apache.catalina. STRICT_SERVLET_COMPLIANCE</code></td><td>
      <p>The default value of this system property is <code>false</code>.</p>
      <p>If this is <code>true</code> the default values will be changed
      to <code>true</code> instead of <code>false</code> for:</p>
      <ul>
      <li>The <code>alwaysAccessSession</code> attribute of any
          <a href="context.html">Context</a> element.</li>
      <li>The <code>contextGetResourceRequiresSlash</code> attribute of any
          <a href="context.html">Context</a> element.</li>
      <li>The <code>dispatcherWrapsSameObject</code> attribute of any
          <a href="context.html">Context</a> element.</li>
      <li>The <code>resourceOnlyServlets</code> attribute of any
          <a href="context.html">Context</a> element.</li>
      <li>The <code>tldValidation</code> attribute of any
          <a href="context.html">Context</a> element.</li>
      <li>The <code>useRelativeRedirects</code> attribute of any
          <a href="context.html">Context</a> element.</li>
      <li>The <code>xmlNamespaceAware</code> attribute of any
          <a href="context.html">Context</a> element.</li>
      <li>The <code>xmlValidation</code> attribute of any
          <a href="context.html">Context</a> element.</li>
      <li>The <code>sessionActivityCheck</code> attribute of any
          <a href="manager.html">Manager</a> element.</li>
      <li>The <code>sessionLastAccessAtStart</code> attribute of any
          <a href="manager.html">Manager</a> element.</li>
      </ul>
    </td></tr></table>

</div><h3 id="Logging">Logging</h3><div class="text">

  <table class="defaultTable"><tr><th style="width: 15%;">
          Property
        </th><th style="width: 85%;">
          Description
        </th></tr><tr><td><code class="propertyName">org.apache.juli.formatter</code></td><td>
      <p>If no logging configuration file is specified and no logging configuration class is specified
         using the <code>java.util.logging.config.class</code> and <code>java.util.logging.config.file</code>
         properties the default logging framework <code>org.apache.juli</code> will use the default
         <code>java.util.logging.SimpleFormatter</code> for all console output.
         To simply override the console output formatter, one can use the described property. Example:
         <code>-Dorg.apache.juli.formatter=org.apache.juli.OneLineFormatter</code></p>
    </td></tr><tr><td><code class="propertyName">org.apache.juli. AsyncMaxRecordCount</code></td><td>
      <p>The maximum number of log records that the JULI AsyncFileHandler will queue in memory.
         New records are added to the queue and get asynchronously removed from the queue
         and written to the files by a single writer thread.
         When the queue is full and a new record is being logged
         the log record will be handled based on the <code>org.apache.juli.AsyncOverflowDropType</code> setting.</p>
      <p>The default value is <code>10000</code> records.
         This number represents the global number of records, not on a per handler basis.
      </p>
    </td></tr><tr><td><code class="propertyName">org.apache.juli. AsyncOverflowDropType</code></td><td>
      <p>When the queue of log records of the JULI AsyncFileHandler is full,
         new log records are handled according to the following setting:
      </p>
         <ul>
           <li><code>1</code> - the newest record in the queue will be dropped and not logged</li>
           <li><code>2</code> - the oldest record in the queue will be dropped and not logged</li>
           <li><code>3</code> - suspend the logging thread until older records got written to the log file and the queue is no longer full.
                                This is the only setting that ensures that no messages get lost.</li>
           <li><code>4</code> - drop the current log record</li>
         </ul>
      <p>The default value is <code>1</code> (drop the newest record in the queue).</p>
    </td></tr><tr><td><code class="propertyName">org.apache.juli.logging. UserDataHelper.CONFIG</code></td><td>
      <p>The type of logging to use for errors generated by invalid input data.
         The options are: <code>DEBUG_ALL</code>, <code>INFO_THEN_DEBUG</code>,
         <code>INFO_ALL</code> and <code>NONE</code>. When
         <code>INFO_THEN_DEBUG</code> is used, the period for which errors are
         logged at DEBUG rather than INFO is controlled by the system property
         <code>org.apache.juli.logging.UserDataHelper.SUPPRESSION_TIME</code>.
         </p>
      <p>The default value is <code>INFO_THEN_DEBUG</code>.</p>
      <p>The errors currently logged using this system are:</p>
         <ul>
         <li>invalid cookies;</li>
         <li>invalid parameters;</li>
         <li>too many headers, too many parameters (hitting
           <code>maxHeaderCount</code> or <code>maxParameterCount</code> limits
           of a <a href="http.html">connector</a>).</li>
         <li>invalid host names</li>
         <li>HTTP/2 stream closures</li>
         </ul>
         <p>Other errors triggered by invalid input data may be added to this
         system in later versions.</p>
    </td></tr><tr><td><code class="propertyName">org.apache.juli.logging. UserDataHelper.SUPPRESSION_TIME</code></td><td>
      <p>When using <code>INFO_THEN_DEBUG</code> for
         <code>org.apache.juli.logging.UserDataHelper.CONFIG</code> this system
         property controls how long messages are logged at DEBUG after a message
         has been logged at INFO. Once this period has elapsed, the next message
         will be logged at INFO followed by a new suppression period where
         messages are logged at DEBUG and so on. The value is measured
         in seconds.</p>
      <p>A value of <code>0</code> is equivalent to using <code>INFO_ALL</code>
         for <code>org.apache.juli.logging.UserDataHelper.CONFIG</code>.</p>
      <p>A negative value means an infinite suppression period.</p>
      <p>The default value is <code>86400</code> (24 hours).</p>
    </td></tr></table>

</div><h3 id="JAR_Scanning">JAR Scanning</h3><div class="text">

  <table class="defaultTable"><tr><th style="width: 15%;">
          Property
        </th><th style="width: 85%;">
          Description
        </th></tr><tr><td><code class="propertyName">tomcat.util.scan. StandardJarScanFilter.jarsToSkip</code></td><td>
      <p>A list of comma-separated file name patterns that is used as the default
         value for <code>pluggabilitySkip</code> and <code>tldSkip</code>
         attributes of the standard
         <a href="jar-scan-filter.html">JarScanFilter</a> implementation.</p>
      <p>The coded default is empty, however the system property is set in
         a default Tomcat installation via the
         <code>$CATALINA_BASE/conf/catalina.properties</code> file.</p>
    </td></tr><tr><td><code class="propertyName">tomcat.util.scan. StandardJarScanFilter.jarsToScan</code></td><td>
      <p>A list of comma-separated file name patterns that is used as the default
         value for <code>pluggabilityScan</code> and <code>tldScan</code>
         attributes of the standard
         <a href="jar-scan-filter.html">JarScanFilter</a> implementation.</p>
      <p>The coded default is empty, however the system property is set in
         a default Tomcat installation via the
         <code>$CATALINA_BASE/conf/catalina.properties</code> file.</p>
    </td></tr></table>

</div><h3 id="Websockets">Websockets</h3><div class="text">

  <table class="defaultTable"><tr><th style="width: 15%;">
          Property
        </th><th style="width: 85%;">
          Description
        </th></tr><tr><td><code class="propertyName">org.apache.tomcat. websocket.DEFAULT_BUFFER_SIZE</code></td><td>
      <p>The default size for buffers used in the Websockets container.</p>
      <p>The default value is <code>8192</code> which corresponds to 8 KiB.</p>
    </td></tr><tr><td><code class="propertyName">org.apache.tomcat. websocket.DEFAULT_ORIGIN_HEADER_VALUE</code></td><td>
      <p>Default value of the origin header that will be sent by the client
         during the upgrade handshake.</p>
      <p>The default is null so that no origin header is sent.</p>
    </td></tr><tr><td><code class="propertyName">org.apache.tomcat. websocket.DEFAULT_PROCESS_PERIOD</code></td><td>
      <p>The number of periodic ticks between periodic processing which
         involves in particular session expiration checks.</p>
      <p>The default value is <code>10</code> which corresponds to 10
         seconds.</p>
    </td></tr></table>

</div><h3 id="Other">Other</h3><div class="text">

  <table class="defaultTable"><tr><th style="width: 15%;">
          Property
        </th><th style="width: 85%;">
          Description
        </th></tr><tr><td><code class="propertyName">catalina.useNaming</code></td><td>
      <p>If this is <code>false</code> it will override the
      <code>useNaming</code> attribute for all <a href="context.html">
      Context</a> elements.</p>
    </td></tr><tr><td><code class="propertyName">javax.sql.DataSource.Factory</code></td><td>
      <p>The class name of the factory to use to create resources of type
      <code>javax.sql.DataSource</code>. If not specified the default of
      <code>org.apache.tomcat.dbcp.dbcp2.BasicDataSourceFactory</code> is used
      which is a package renamed (to avoid conflicts) copy of
      <a href="https://commons.apache.org/dbcp">Apache Commons DBCP 2</a>.</p>
    </td></tr><tr><td><code class="propertyName">jakarta.mail.Session.Factory</code></td><td>
      <p>The class name of the factory to use to create resources of type
      <code>jakarta.mail.Session</code>. If not specified the default of
      <code>org.apache.naming.factory.MailSessionFactory</code> is used.</p>
    </td></tr><tr><td><code class="propertyName">catalina.config</code></td><td>
      <p>The location from which to load the catalina.properties configuration
      file. This may be an absolute URL, a relative (to the current working
      directory) URL or an alternative file name in which case Tomcat will
      attempt to load the file from the default location of
      <code>$CATALINA_BASE/conf/</code>.</p>
    </td></tr><tr><td><code class="propertyName">tomcat.util.buf.StringCache.byte.enabled</code></td><td>
      <p>If <code>true</code>, the String cache is enabled for
      <code>ByteChunk</code>.</p>
      <p>If not specified, the default value of <code>false</code> will be used.</p>
    </td></tr><tr><td><code class="propertyName">tomcat.util.buf.StringCache.char.enabled</code></td><td>
      <p>If <code>true</code>, the String cache is enabled for
      <code>CharChunk</code>.</p>
      <p>If not specified, the default value of <code>false</code> will be used.</p>
    </td></tr><tr><td><code class="propertyName">tomcat.util.buf.StringCache.trainThreshold</code></td><td>
      <p>The number of times <code>toString()</code> must be called before the
      cache is activated.</p>
      <p>If not specified, the default value of <code>20000</code> will be used.</p>
    </td></tr><tr><td><code class="propertyName">tomcat.util.buf.StringCache.cacheSize</code></td><td>
      <p>The size of the String cache.</p>
      <p>If not specified, the default value of <code>200</code> will be used.</p>
    </td></tr><tr><td><code class="propertyName">org.apache.tomcat.util.buf.UriUtil. WAR_SEPARATOR</code></td><td>
      <p>The character to use to separate the WAR file and WAR content parts of
      a WAR URL using the custom WAR scheme provided by Tomcat. This is
      equivalent to how <code>!</code> is used in JAR URLs.</p>
      <p>If not specified, the default value of <code>*</code> will be used.</p>
    </td></tr><tr><td><code class="propertyName">tomcat.util.buf.StringCache.maxStringSize</code></td><td>
      <p>The maximum length of String that will be cached.</p>
      <p>If not specified, the default value of <code>128</code> will be used.</p>
    </td></tr><tr><td><code class="propertyName">org.apache.tomcat.util. http.FastHttpDateFormat.CACHE_SIZE</code></td><td>
      <p>The size of the cache to use parsed and formatted date value.</p>
      <p>If not specified, the default value of <code>1000</code> will be used.</p>
    </td></tr><tr><td><code class="propertyName">org.apache.tomcat.util.openssl.LIBRARY_NAME</code></td><td>
      <p>Specify the library name of OpenSSL.</p>
      <p>If not specified, the default value of <code>ssl</code> will be used,
      except on MacOS where a dylib versioned name will be used, in the form of
      <code>ssl.v</code> where v is a version number.</p>
    </td></tr><tr><td><code class="propertyName">org.apache.tomcat.util.openssl.USE_SYSTEM_LOAD_LIBRARY</code></td><td>
      <p>Use <code>System.loadLibrary</code> to load OpenSSL rather than
      the FFM library loading code.</p>
      <p>If not specified, the default value of <code>false</code> will be used.</p>
    </td></tr><tr><td><code class="propertyName">org.apache.catalina.startup. EXIT_ON_INIT_FAILURE</code></td><td>
      <p>If <code>true</code>, the server will exit if an exception happens
      during the server initialization phase. To support this feature, this
      system property is used as the default for the
      <strong>throwOnFailure</strong> attribute of a Connector.</p>
      <p>If not specified, the default value of <code>false</code> will be
      used.</p>
    </td></tr><tr><td><code class="propertyName">org.apache.catalina.startup. RealmRuleSet.MAX_NESTED_REALM_LEVELS</code></td><td>
      <p>The CombinedRealm allows nested Realms. This property controls the
      maximum permitted number of levels of nesting.</p>
      <p>If not specified, the default value of <code>3</code> will be used.</p>
    </td></tr><tr><td><code class="propertyName">org.apache.catalina.startup. CredentialHandlerRuleSet.MAX_NESTED_LEVELS</code></td><td>
      <p>The NestedCredentialHandler allows nested CredentialHandlers. This
      property controls the maximum permitted number of levels of nesting.</p>
      <p>If not specified, the default value of <code>3</code> will be used.</p>
    </td></tr><tr><td><code class="propertyName">org.apache.catalina.tribes.io. BufferPool.DEFAULT_POOL_SIZE</code></td><td>
      <p>The size of the buffer pool which is used by Tribes in bytes.</p>
      <p>If not specified, the default value of <code>100*1024*1024</code>
      (100 MiB) will be used.</p>
    </td></tr></table>

</div></div></div></div></div><footer><div id="footer">
    Copyright &copy; 1999-2025, The Apache Software Foundation
    <br>
    Apache Tomcat, Tomcat, Apache, the Apache Tomcat logo and the Apache logo
    are either registered trademarks or trademarks of the Apache Software
    Foundation.
    </div></footer></div></body></html>