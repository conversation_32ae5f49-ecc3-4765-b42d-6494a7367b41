<!DOCTYPE html SYSTEM "about:legacy-compat">
<html lang="en"><head><META http-equiv="Content-Type" content="text/html; charset=UTF-8"><link href="../images/docs-stylesheet.css" rel="stylesheet" type="text/css"><title>Apache Tomcat 10 Configuration Reference (10.1.41) - The Cookie Processor Component</title></head><body><div id="wrapper"><header><div id="header"><div><div><div class="logo noPrint"><a href="https://tomcat.apache.org/"><img alt="Tomcat Home" src="../images/tomcat.png"></a></div><div style="height: 1px;"></div><div class="asfLogo noPrint"><a href="https://www.apache.org/" target="_blank"><img src="../images/asf-logo.svg" alt="The Apache Software Foundation" style="width: 266px; height: 83px;"></a></div><h1>Apache Tomcat 10 Configuration Reference</h1><div class="versionInfo">
            Version 10.1.41,
            <time datetime="2025-05-08">May 8 2025</time></div><div style="height: 1px;"></div><div style="clear: left;"></div></div></div></div></header><div id="middle"><div><div id="mainLeft" class="noprint"><div><nav><div><h2>Links</h2><ul><li><a href="../index.html">Docs Home</a></li><li><a href="index.html">Config Ref. Home</a></li><li><a href="https://cwiki.apache.org/confluence/display/TOMCAT/FAQ">FAQ</a></li><li><a href="#comments_section">User Comments</a></li></ul></div><div><h2>Top Level Elements</h2><ul><li><a href="server.html">Server</a></li><li><a href="service.html">Service</a></li></ul></div><div><h2>Executors</h2><ul><li><a href="executor.html">Executor</a></li></ul></div><div><h2>Connectors</h2><ul><li><a href="http.html">HTTP/1.1</a></li><li><a href="http2.html">HTTP/2</a></li><li><a href="ajp.html">AJP</a></li></ul></div><div><h2>Containers</h2><ul><li><a href="context.html">Context</a></li><li><a href="engine.html">Engine</a></li><li><a href="host.html">Host</a></li><li><a href="cluster.html">Cluster</a></li></ul></div><div><h2>Nested Components</h2><ul><li><a href="cookie-processor.html">CookieProcessor</a></li><li><a href="credentialhandler.html">CredentialHandler</a></li><li><a href="globalresources.html">Global Resources</a></li><li><a href="jar-scanner.html">JarScanner</a></li><li><a href="jar-scan-filter.html">JarScanFilter</a></li><li><a href="listeners.html">Listeners</a></li><li><a href="loader.html">Loader</a></li><li><a href="manager.html">Manager</a></li><li><a href="realm.html">Realm</a></li><li><a href="resources.html">Resources</a></li><li><a href="sessionidgenerator.html">SessionIdGenerator</a></li><li><a href="valve.html">Valve</a></li></ul></div><div><h2>Cluster Elements</h2><ul><li><a href="cluster.html">Cluster</a></li><li><a href="cluster-manager.html">Manager</a></li><li><a href="cluster-channel.html">Channel</a></li><li><a href="cluster-membership.html">Channel/Membership</a></li><li><a href="cluster-sender.html">Channel/Sender</a></li><li><a href="cluster-receiver.html">Channel/Receiver</a></li><li><a href="cluster-interceptor.html">Channel/Interceptor</a></li><li><a href="cluster-valve.html">Valve</a></li><li><a href="cluster-deployer.html">Deployer</a></li><li><a href="cluster-listener.html">ClusterListener</a></li></ul></div><div><h2>web.xml</h2><ul><li><a href="filter.html">Filter</a></li></ul></div><div><h2>Other</h2><ul><li><a href="runtime-attributes.html">Runtime attributes</a></li><li><a href="systemprops.html">System properties</a></li><li><a href="jaspic.html">Jakarta Authentication</a></li></ul></div></nav></div></div><div id="mainRight"><div id="content"><h2>The Cookie Processor Component</h2><h3 id="Table_of_Contents">Table of Contents</h3><div class="text">
<ul><li><a href="#Introduction">Introduction</a></li><li><a href="#Attributes">Attributes</a><ol><li><a href="#Common_Attributes">Common Attributes</a></li><li><a href="#Standard_Implementation">Standard Implementation</a></li></ol></li><li><a href="#Nested_Components">Nested Components</a></li><li><a href="#Special_Features">Special Features</a></li></ul>
</div><h3 id="Introduction">Introduction</h3><div class="text">

  <p>The <strong>CookieProcessor</strong> element represents the component that
  parses received cookie headers into <code>jakarta.servlet.http.Cookie</code>
  objects accessible through <code>HttpServletRequest.getCookies()</code> and
  converts <code>jakarta.servlet.http.Cookie</code> objects added to the response
  through <code>HttpServletResponse.addCookie()</code> to the HTTP headers
  returned to the client.</p>

  <p>A CookieProcessor element MAY be nested inside a
  <a href="context.html">Context</a> component. If it is not included, a default
  implementation will be created automatically.</p>

</div><h3 id="Attributes">Attributes</h3><div class="text">

  <div class="subsection"><h4 id="Common_Attributes">Common Attributes</h4><div class="text">

    <p>All implementations of <strong>CookieProcessor</strong> support the
    following attributes:</p>

    <table class="defaultTable"><tr><th style="width: 15%;">
          Attribute
        </th><th style="width: 85%;">
          Description
        </th></tr><tr id="Attributes_Common Attributes_className"><td><code class="attributeName">className</code></td><td>
        <p>Java class name of the implementation to use. This class must
        implement the <code>org.apache.tomcat.util.http.CookieProcessor</code>
        interface. If not specified, the standard value (defined below) will be
        used.</p>
      </td></tr></table>

  </div></div>


  <div class="subsection"><h4 id="Standard_Implementation">Standard Implementation</h4><div class="text">

    <p>The standard implementation of <strong>CookieProcessor</strong> is
    <code>org.apache.tomcat.util.http.Rfc6265CookieProcessor</code>.</p>

    <p>This cookie processor is based on RFC6265 with the following changes to
    support better interoperability:</p>

    <ul>
      <li>Values 0x80 to 0xFF are permitted in cookie-octet to support the use
      of UTF-8 in cookie values as used by HTML 5.</li>
      <li>For cookies without a value, the '=' is not required after the name as
      some browsers do not sent it.</li>
    </ul>

    <p>The RFC 6265 cookie processor is generally more lenient than the legacy
    cookie parser. In particular:</p>

    <ul>
      <li>The '<code>=</code>' and '<code>/</code>' characters are always
      permitted in a cookie value.</li>
      <li>Name only cookies are always permitted.</li>
      <li>The cookie header is always preserved.</li>
    </ul>

    <p>The <strong>RFC 6265 Cookie Processor</strong> supports the following
    additional attributes.</p>

    <table class="defaultTable"><tr><th style="width: 15%;">
          Attribute
        </th><th style="width: 85%;">
          Description
        </th></tr><tr id="Attributes_Standard Implementation_cookiesWithoutEquals"><td><code class="attributeName">cookiesWithoutEquals</code></td><td>
        <p>Determines how a cookie received from a user agent should be
        interpreted when the name value pair does not contain an equals sign.
        The default value is <code>name</code> which means that the cookie will
        be treated as a cookie with a name but no value. The other option is
        <code>ignore</code> which means the cookie will be ignored. From Tomcat
        12 onwards the default will be <code>ignore</code>.</p>
      </td></tr><tr id="Attributes_Standard Implementation_partitioned"><td><code class="attributeName">partitioned</code></td><td>
       <p>Should the Partitioned flag be set on cookies? Defaults to <code>false</code>.</p>
       <p>Note: The name of the attribute used to indicate a partitioned cookie as part of
       <a href="https://developers.google.com/privacy-sandbox/3pcd#partitioned">CHIPS</a> is not defined by an RFC and
       may change in a non-backwards compatible way once equivalent functionality is included in an RFC.</p>
      </td></tr><tr id="Attributes_Standard Implementation_sameSiteCookies"><td><code class="attributeName">sameSiteCookies</code></td><td>
        <p>Enables setting same-site cookie attribute.</p>

        <p>If value is <code>unset</code> then the same-site cookie attribute
        won't be set. This is the default value.</p>

        <p>If value is <code>none</code> then the same-site cookie attribute
        will be set and the cookie will always be sent in cross-site requests.</p>

        <p>If value is <code>lax</code> then the browser only sends the cookie
        in same-site requests and cross-site top level GET requests.</p>

        <p>If value is <code>strict</code> then the browser prevents sending the
        cookie in any cross-site request.</p>
      </td></tr></table>

  </div></div>

</div><h3 id="Nested_Components">Nested Components</h3><div class="text">

  <p>No element may be nested inside a <strong>CookieProcessor</strong>.</p>

</div><h3 id="Special_Features">Special Features</h3><div class="text">

  <p>No special features are associated with a <strong>CookieProcessor</strong>
  element.</p>

</div></div></div></div></div><footer><div id="footer">
    Copyright &copy; 1999-2025, The Apache Software Foundation
    <br>
    Apache Tomcat, Tomcat, Apache, the Apache Tomcat logo and the Apache logo
    are either registered trademarks or trademarks of the Apache Software
    Foundation.
    </div></footer></div></body></html>