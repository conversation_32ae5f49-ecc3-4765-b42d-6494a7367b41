<!DOCTYPE html SYSTEM "about:legacy-compat">
<html lang="en"><head><META http-equiv="Content-Type" content="text/html; charset=UTF-8"><link href="../images/docs-stylesheet.css" rel="stylesheet" type="text/css"><title>Apache Tomcat 10 Configuration Reference (10.1.41) - The ClusterManager object</title><meta name="author" content="Filip Hanik"></head><body><div id="wrapper"><header><div id="header"><div><div><div class="logo noPrint"><a href="https://tomcat.apache.org/"><img alt="Tomcat Home" src="../images/tomcat.png"></a></div><div style="height: 1px;"></div><div class="asfLogo noPrint"><a href="https://www.apache.org/" target="_blank"><img src="../images/asf-logo.svg" alt="The Apache Software Foundation" style="width: 266px; height: 83px;"></a></div><h1>Apache Tomcat 10 Configuration Reference</h1><div class="versionInfo">
            Version 10.1.41,
            <time datetime="2025-05-08">May 8 2025</time></div><div style="height: 1px;"></div><div style="clear: left;"></div></div></div></div></header><div id="middle"><div><div id="mainLeft" class="noprint"><div><nav><div><h2>Links</h2><ul><li><a href="../index.html">Docs Home</a></li><li><a href="index.html">Config Ref. Home</a></li><li><a href="https://cwiki.apache.org/confluence/display/TOMCAT/FAQ">FAQ</a></li><li><a href="#comments_section">User Comments</a></li></ul></div><div><h2>Top Level Elements</h2><ul><li><a href="server.html">Server</a></li><li><a href="service.html">Service</a></li></ul></div><div><h2>Executors</h2><ul><li><a href="executor.html">Executor</a></li></ul></div><div><h2>Connectors</h2><ul><li><a href="http.html">HTTP/1.1</a></li><li><a href="http2.html">HTTP/2</a></li><li><a href="ajp.html">AJP</a></li></ul></div><div><h2>Containers</h2><ul><li><a href="context.html">Context</a></li><li><a href="engine.html">Engine</a></li><li><a href="host.html">Host</a></li><li><a href="cluster.html">Cluster</a></li></ul></div><div><h2>Nested Components</h2><ul><li><a href="cookie-processor.html">CookieProcessor</a></li><li><a href="credentialhandler.html">CredentialHandler</a></li><li><a href="globalresources.html">Global Resources</a></li><li><a href="jar-scanner.html">JarScanner</a></li><li><a href="jar-scan-filter.html">JarScanFilter</a></li><li><a href="listeners.html">Listeners</a></li><li><a href="loader.html">Loader</a></li><li><a href="manager.html">Manager</a></li><li><a href="realm.html">Realm</a></li><li><a href="resources.html">Resources</a></li><li><a href="sessionidgenerator.html">SessionIdGenerator</a></li><li><a href="valve.html">Valve</a></li></ul></div><div><h2>Cluster Elements</h2><ul><li><a href="cluster.html">Cluster</a></li><li><a href="cluster-manager.html">Manager</a></li><li><a href="cluster-channel.html">Channel</a></li><li><a href="cluster-membership.html">Channel/Membership</a></li><li><a href="cluster-sender.html">Channel/Sender</a></li><li><a href="cluster-receiver.html">Channel/Receiver</a></li><li><a href="cluster-interceptor.html">Channel/Interceptor</a></li><li><a href="cluster-valve.html">Valve</a></li><li><a href="cluster-deployer.html">Deployer</a></li><li><a href="cluster-listener.html">ClusterListener</a></li></ul></div><div><h2>web.xml</h2><ul><li><a href="filter.html">Filter</a></li></ul></div><div><h2>Other</h2><ul><li><a href="runtime-attributes.html">Runtime attributes</a></li><li><a href="systemprops.html">System properties</a></li><li><a href="jaspic.html">Jakarta Authentication</a></li></ul></div></nav></div></div><div id="mainRight"><div id="content"><h2>The ClusterManager object</h2><h3 id="Table_of_Contents">Table of Contents</h3><div class="text">
<ul><li><a href="#Introduction">Introduction</a></li><li><a href="#The_<Manager>">The &lt;Manager&gt;</a></li><li><a href="#Attributes">Attributes</a><ol><li><a href="#Common_Attributes">Common Attributes</a></li><li><a href="#org.apache.catalina.ha.session.DeltaManager_Attributes">org.apache.catalina.ha.session.DeltaManager Attributes</a></li><li><a href="#org.apache.catalina.ha.session.BackupManager_Attributes">org.apache.catalina.ha.session.BackupManager Attributes</a></li></ol></li><li><a href="#Nested_Components">Nested Components</a></li></ul>
</div><h3 id="Introduction">Introduction</h3><div class="text">
  <p>A cluster manager is an extension to Tomcat's session manager interface,
  <code>org.apache.catalina.Manager</code>.
  A cluster manager must implement the
  <code>org.apache.catalina.ha.ClusterManager</code> and is solely  responsible
  for how the session is replicated.<br>
  There are currently two different managers, the
  <code>org.apache.catalina.ha.session.DeltaManager</code> replicates deltas of
  session data to all members in the cluster. This implementation is proven and
  works very well, but has a limitation as it requires the cluster members to be
  homogeneous, all nodes must deploy the same applications and be exact
  replicas. The <code>org.apache.catalina.ha.session.BackupManager</code> also
  replicates deltas but only to one backup node. The location of the backup node
  is known to all nodes in the cluster. It also supports heterogeneous
  deployments, so the manager knows at what locations the web application is
  deployed.</p>
</div><h3 id="The_<Manager>">The &lt;Manager&gt;</h3><div class="text">
  <p>The <code>&lt;Manager&gt;</code> element defined inside the
  <code>&lt;Cluster&gt;</code> element is the template defined for all web
  applications that are marked <code>&lt;distributable/&gt;</code> in their
  <code>web.xml</code> file. However, you can still override the manager
  implementation on a per web application basis, by putting the
  <code>&lt;Manager&gt;</code> inside the <code>&lt;Context&gt;</code> element
  either in the <code><a href="context.html">context.xml</a></code> file or the
  <code><a href="index.html">server.xml</a></code> file.</p>
</div><h3 id="Attributes">Attributes</h3><div class="text">
  <div class="subsection"><h4 id="Common_Attributes">Common Attributes</h4><div class="text">
    <table class="defaultTable"><tr><th style="width: 15%;">
          Attribute
        </th><th style="width: 85%;">
          Description
        </th></tr><tr id="Attributes_Common Attributes_className"><td><strong><code class="attributeName">className</code></strong></td><td>
      </td></tr><tr id="Attributes_Common Attributes_name"><td><code class="attributeName">name</code></td><td>
        <b>The name of this cluster manager, the name is used to identify a
        session manager on a node. The name might get modified by the
        <code>Cluster</code> element to make it unique in the container.</b>
      </td></tr><tr id="Attributes_Common Attributes_notifyListenersOnReplication"><td><code class="attributeName">notifyListenersOnReplication</code></td><td>
        Set to <code>true</code> if you wish to have session listeners notified
        when session attributes are being replicated or removed across Tomcat
        nodes in the cluster.
      </td></tr><tr id="Attributes_Common Attributes_processExpiresFrequency"><td><code class="attributeName">processExpiresFrequency</code></td><td>
        <p>Frequency of the session expiration, and related manager operations.
        Manager operations will be done once for the specified amount of
        backgroundProcess calls (i.e., the lower the amount, the more often the
        checks will occur). The minimum value is 1, and the default value is 6.
        </p>
      </td></tr><tr id="Attributes_Common Attributes_secureRandomClass"><td><code class="attributeName">secureRandomClass</code></td><td>
        <p>Name of the Java class that extends
        <code>java.security.SecureRandom</code> to use to generate session IDs.
        If not specified, the default value is
        <code>java.security.SecureRandom</code>.</p>
      </td></tr><tr id="Attributes_Common Attributes_secureRandomProvider"><td><code class="attributeName">secureRandomProvider</code></td><td>
        <p>Name of the provider to use to create the
        <code>java.security.SecureRandom</code> instances that generate session
        IDs. If an invalid algorithm and/or provider is specified, the Manager
        will use the platform default provider and the default algorithm. If not
        specified, the platform default provider will be used.</p>
      </td></tr><tr id="Attributes_Common Attributes_secureRandomAlgorithm"><td><code class="attributeName">secureRandomAlgorithm</code></td><td>
        <p>Name of the algorithm to use to create the
        <code>java.security.SecureRandom</code> instances that generate session
        IDs. If an invalid algorithm and/or provider is specified, the Manager
        will use the platform default provider and the default algorithm. If not
        specified, the default algorithm of SHA1PRNG will be used. If the
        default algorithm is not supported, the platform default will be used.
        To specify that the platform default should be used, do not set the
        secureRandomProvider attribute and set this attribute to the empty
        string.</p>
      </td></tr><tr id="Attributes_Common Attributes_recordAllActions"><td><code class="attributeName">recordAllActions</code></td><td>
        <p>Flag whether send all actions for session across Tomcat cluster
        nodes. If set to false, if already done something to the same attribute,
        make sure don't send multiple actions across Tomcat cluster nodes.
        In that case, sends only the actions that have been added at last.
        Default is <code>false</code>.</p>
      </td></tr></table>
  </div></div>
  <div class="subsection"><h4 id="org.apache.catalina.ha.session.DeltaManager_Attributes">org.apache.catalina.ha.session.DeltaManager Attributes</h4><div class="text">
    <table class="defaultTable"><tr><th style="width: 15%;">
          Attribute
        </th><th style="width: 85%;">
          Description
        </th></tr><tr id="Attributes_org.apache.catalina.ha.session.DeltaManager Attributes_expireSessionsOnShutdown"><td><code class="attributeName">expireSessionsOnShutdown</code></td><td>
        When a web application is being shutdown, Tomcat issues an expire call
        to each session to notify all the listeners. If you wish for all
        sessions to expire on all nodes when a shutdown occurs on one node, set
        this value to <code>true</code>.
        Default value is <code>false</code>.
      </td></tr><tr id="Attributes_org.apache.catalina.ha.session.DeltaManager Attributes_maxActiveSessions"><td><code class="attributeName">maxActiveSessions</code></td><td>
        The maximum number of active sessions that will be created by this
        Manager, or -1 (the default) for no limit. For this manager, all
        sessions are counted as active sessions irrespective if whether or not
        the current node is the primary node for the session.
      </td></tr><tr id="Attributes_org.apache.catalina.ha.session.DeltaManager Attributes_notifySessionListenersOnReplication"><td><code class="attributeName">notifySessionListenersOnReplication</code></td><td>
        Set to <code>true</code> if you wish to have session listeners notified
        when sessions are created and expired across Tomcat nodes in the
        cluster.
      </td></tr><tr id="Attributes_org.apache.catalina.ha.session.DeltaManager Attributes_notifyContainerListenersOnReplication"><td><code class="attributeName">notifyContainerListenersOnReplication</code></td><td>
        Set to <code>true</code> if you wish to have container listeners notified
        across Tomcat nodes in the cluster.
      </td></tr><tr id="Attributes_org.apache.catalina.ha.session.DeltaManager Attributes_stateTransferTimeout"><td><code class="attributeName">stateTransferTimeout</code></td><td>
        The time in seconds to wait for a session state transfer to complete
        from another node when a node is starting up.
        Default value is <code>60</code> seconds.
      </td></tr><tr id="Attributes_org.apache.catalina.ha.session.DeltaManager Attributes_sendAllSessions"><td><code class="attributeName">sendAllSessions</code></td><td>
        Flag whether send sessions as split blocks.
        If set to <code>true</code>, send all sessions as one big block.
        If set to <code>false</code>, send sessions as split blocks.
        Default value is <code>true</code>.
      </td></tr><tr id="Attributes_org.apache.catalina.ha.session.DeltaManager Attributes_sendAllSessionsSize"><td><code class="attributeName">sendAllSessionsSize</code></td><td>
        The number of sessions in a session block message. This value is
        effective only when <code>sendAllSessions</code> is <code>false</code>.
        Default is <code>1000</code>.
      </td></tr><tr id="Attributes_org.apache.catalina.ha.session.DeltaManager Attributes_sendAllSessionsWaitTime"><td><code class="attributeName">sendAllSessionsWaitTime</code></td><td>
        Wait time between sending of session block messages. This value is
        effective only when <code>sendAllSessions</code> is <code>false</code>.
        Default is <code>2000</code> milliseconds.
      </td></tr><tr id="Attributes_org.apache.catalina.ha.session.DeltaManager Attributes_sessionAttributeNameFilter"><td><code class="attributeName">sessionAttributeNameFilter</code></td><td>
        <p>A regular expression used to filter which session attributes will be
        replicated. An attribute will only be replicated if its name matches
        this pattern. If the pattern is zero length or <code>null</code>, all
        attributes are eligible for replication. The pattern is anchored so the
        session attribute name must fully match the pattern. As an example, the
        value <code>(userName|sessionHistory)</code> will only replicate the
        two session attributes named <code>userName</code> and
        <code>sessionHistory</code>. If not specified, the default value of
        <code>null</code> will be used.</p>
      </td></tr><tr id="Attributes_org.apache.catalina.ha.session.DeltaManager Attributes_sessionAttributeValueClassNameFilter"><td><code class="attributeName">sessionAttributeValueClassNameFilter</code></td><td>
        <p>A regular expression used to filter which session attributes will be
        replicated. An attribute will only be replicated if the implementation
        class name of the value matches this pattern. If the pattern is zero
        length or <code>null</code>, all attributes are eligible for
        replication. The pattern is anchored so the fully qualified class name
        must fully match the pattern. If not specified, the default value of
        <code>null</code> will be used unless a <code>SecurityManager</code> is
        enabled in which case the default will be
        <code>java\\.lang\\.(?:Boolean|Integer|Long|Number|String)</code>.</p>
      </td></tr><tr id="Attributes_org.apache.catalina.ha.session.DeltaManager Attributes_stateTimestampDrop"><td><code class="attributeName">stateTimestampDrop</code></td><td>
        When this node sends a <code>GET_ALL_SESSIONS</code> message to other
        node, all session messages that are received as a response are queued.
        If this attribute is set to <code>true</code>, the received session
        messages (except any <code>GET_ALL_SESSIONS</code> sent by other nodes)
        are filtered by their timestamp. A message is dropped if it is not a
        <code>GET_ALL_SESSIONS</code> message and its timestamp is earlier than
        the timestamp of our <code>GET_ALL_SESSIONS</code> message.
        If set to <code>false</code>, all queued session messages are handled.
        Default is <code>true</code>.
      </td></tr><tr id="Attributes_org.apache.catalina.ha.session.DeltaManager Attributes_warnOnSessionAttributeFilterFailure"><td><code class="attributeName">warnOnSessionAttributeFilterFailure</code></td><td>
        <p>If <strong>sessionAttributeNameFilter</strong> or
        <strong>sessionAttributeValueClassNameFilter</strong> blocks an
        attribute, should this be logged at <code>WARN</code> level? If
        <code>WARN</code> level logging is disabled then it will be logged at
        <code>DEBUG</code>. The default value of this attribute is
        <code>false</code> unless a <code>SecurityManager</code> is enabled in
        which case the default will be <code>true</code>.</p>
      </td></tr></table>
  </div></div>
  <div class="subsection"><h4 id="org.apache.catalina.ha.session.BackupManager_Attributes">org.apache.catalina.ha.session.BackupManager Attributes</h4><div class="text">
    <table class="defaultTable"><tr><th style="width: 15%;">
          Attribute
        </th><th style="width: 85%;">
          Description
        </th></tr><tr id="Attributes_org.apache.catalina.ha.session.BackupManager Attributes_mapSendOptions"><td><code class="attributeName">mapSendOptions</code></td><td>
        The backup manager uses a replicated map, this map is sending and
        receiving messages. You can setup the flag for how this map is sending
        messages, the default value is <code>6</code>(synchronous).<br>
        Note that if you use asynchronous messaging it is possible for update
        messages for a session to be processed by the receiving node in a
        different order to the order in which they were sent.
      </td></tr><tr id="Attributes_org.apache.catalina.ha.session.BackupManager Attributes_maxActiveSessions"><td><code class="attributeName">maxActiveSessions</code></td><td>
        The maximum number of active sessions that will be created by this
        Manager, or -1 (the default) for no limit. For this manager, only
        sessions where the current node is the primary node for the session are
        considered active sessions.
      </td></tr><tr id="Attributes_org.apache.catalina.ha.session.BackupManager Attributes_rpcTimeout"><td><code class="attributeName">rpcTimeout</code></td><td>
        Timeout for RPC message used for broadcast and transfer state from
        another map.
        Default value is <code>15000</code> milliseconds.
      </td></tr><tr id="Attributes_org.apache.catalina.ha.session.BackupManager Attributes_sessionAttributeNameFilter"><td><code class="attributeName">sessionAttributeNameFilter</code></td><td>
        <p>A regular expression used to filter which session attributes will be
        replicated. An attribute will only be replicated if its name matches
        this pattern. If the pattern is zero length or <code>null</code>, all
        attributes are eligible for replication. The pattern is anchored so the
        session attribute name must fully match the pattern. As an example, the
        value <code>(userName|sessionHistory)</code> will only replicate the
        two session attributes named <code>userName</code> and
        <code>sessionHistory</code>. If not specified, the default value of
        <code>null</code> will be used.</p>
      </td></tr><tr id="Attributes_org.apache.catalina.ha.session.BackupManager Attributes_sessionAttributeValueClassNameFilter"><td><code class="attributeName">sessionAttributeValueClassNameFilter</code></td><td>
        <p>A regular expression used to filter which session attributes will be
        replicated. An attribute will only be replicated if the implementation
        class name of the value matches this pattern. If the pattern is zero
        length or <code>null</code>, all attributes are eligible for
        replication. The pattern is anchored so the fully qualified class name
        must fully match the pattern. If not specified, the default value of
        <code>null</code> will be used unless a <code>SecurityManager</code> is
        enabled in which case the default will be
        <code>java\\.lang\\.(?:Boolean|Integer|Long|Number|String)</code>.</p>
      </td></tr><tr id="Attributes_org.apache.catalina.ha.session.BackupManager Attributes_terminateOnStartFailure"><td><code class="attributeName">terminateOnStartFailure</code></td><td>
        Set to true if you wish to terminate replication map when replication
        map fails to start. If replication map is terminated, associated context
        will fail to start. If you set this attribute to false, replication map
        does not end. It will try to join the map membership in the heartbeat.
        Default value is <code>false</code> .
      </td></tr><tr id="Attributes_org.apache.catalina.ha.session.BackupManager Attributes_warnOnSessionAttributeFilterFailure"><td><code class="attributeName">warnOnSessionAttributeFilterFailure</code></td><td>
        <p>If <strong>sessionAttributeNameFilter</strong> or
        <strong>sessionAttributeValueClassNameFilter</strong> blocks an
        attribute, should this be logged at <code>WARN</code> level? If
        <code>WARN</code> level logging is disabled then it will be logged at
        <code>DEBUG</code>. The default value of this attribute is
        <code>false</code> unless a <code>SecurityManager</code> is enabled in
        which case the default will be <code>true</code>.</p>
      </td></tr><tr id="Attributes_org.apache.catalina.ha.session.BackupManager Attributes_accessTimeout"><td><code class="attributeName">accessTimeout</code></td><td>
        The timeout for a ping message. If a remote map does not respond within
        this timeout period, its regarded as disappeared.
        Default value is <code>5000</code> milliseconds.
      </td></tr></table>
  </div></div>
</div><h3 id="Nested_Components">Nested Components</h3><div class="text">
  <h3>All Manager Implementations</h3>
  <p>All Manager implementations allow nesting of a
  <strong>&lt;SessionIdGenerator&gt;</strong> element. It defines
  the behavior of session id generation.  All implementations
  of the <a href="sessionidgenerator.html">SessionIdGenerator</a> allow the
  following attributes:
  </p>
  <table class="defaultTable"><tr><th style="width: 15%;">
          Attribute
        </th><th style="width: 85%;">
          Description
        </th></tr><tr id="_Nested Components_sessionIdLength"><td><code class="attributeName">sessionIdLength</code></td><td>
      <p>The length of the session ID may be changed with the
      <strong>sessionIdLength</strong> attribute.
      </p>
    </td></tr></table>
</div></div></div></div></div><footer><div id="footer">
    Copyright &copy; 1999-2025, The Apache Software Foundation
    <br>
    Apache Tomcat, Tomcat, Apache, the Apache Tomcat logo and the Apache logo
    are either registered trademarks or trademarks of the Apache Software
    Foundation.
    </div></footer></div></body></html>