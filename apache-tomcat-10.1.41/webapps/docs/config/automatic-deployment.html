<!DOCTYPE html SYSTEM "about:legacy-compat">
<html lang="en"><head><META http-equiv="Content-Type" content="text/html; charset=UTF-8"><link href="../images/docs-stylesheet.css" rel="stylesheet" type="text/css"><title>Apache Tomcat 10 Configuration Reference (10.1.41) - Automatic Deployment - Use cases</title></head><body><div id="wrapper"><header><div id="header"><div><div><div class="logo noPrint"><a href="https://tomcat.apache.org/"><img alt="Tomcat Home" src="../images/tomcat.png"></a></div><div style="height: 1px;"></div><div class="asfLogo noPrint"><a href="https://www.apache.org/" target="_blank"><img src="../images/asf-logo.svg" alt="The Apache Software Foundation" style="width: 266px; height: 83px;"></a></div><h1>Apache Tomcat 10 Configuration Reference</h1><div class="versionInfo">
            Version 10.1.41,
            <time datetime="2025-05-08">May 8 2025</time></div><div style="height: 1px;"></div><div style="clear: left;"></div></div></div></div></header><div id="middle"><div><div id="mainLeft" class="noprint"><div><nav><div><h2>Links</h2><ul><li><a href="../index.html">Docs Home</a></li><li><a href="index.html">Config Ref. Home</a></li><li><a href="https://cwiki.apache.org/confluence/display/TOMCAT/FAQ">FAQ</a></li><li><a href="#comments_section">User Comments</a></li></ul></div><div><h2>Top Level Elements</h2><ul><li><a href="server.html">Server</a></li><li><a href="service.html">Service</a></li></ul></div><div><h2>Executors</h2><ul><li><a href="executor.html">Executor</a></li></ul></div><div><h2>Connectors</h2><ul><li><a href="http.html">HTTP/1.1</a></li><li><a href="http2.html">HTTP/2</a></li><li><a href="ajp.html">AJP</a></li></ul></div><div><h2>Containers</h2><ul><li><a href="context.html">Context</a></li><li><a href="engine.html">Engine</a></li><li><a href="host.html">Host</a></li><li><a href="cluster.html">Cluster</a></li></ul></div><div><h2>Nested Components</h2><ul><li><a href="cookie-processor.html">CookieProcessor</a></li><li><a href="credentialhandler.html">CredentialHandler</a></li><li><a href="globalresources.html">Global Resources</a></li><li><a href="jar-scanner.html">JarScanner</a></li><li><a href="jar-scan-filter.html">JarScanFilter</a></li><li><a href="listeners.html">Listeners</a></li><li><a href="loader.html">Loader</a></li><li><a href="manager.html">Manager</a></li><li><a href="realm.html">Realm</a></li><li><a href="resources.html">Resources</a></li><li><a href="sessionidgenerator.html">SessionIdGenerator</a></li><li><a href="valve.html">Valve</a></li></ul></div><div><h2>Cluster Elements</h2><ul><li><a href="cluster.html">Cluster</a></li><li><a href="cluster-manager.html">Manager</a></li><li><a href="cluster-channel.html">Channel</a></li><li><a href="cluster-membership.html">Channel/Membership</a></li><li><a href="cluster-sender.html">Channel/Sender</a></li><li><a href="cluster-receiver.html">Channel/Receiver</a></li><li><a href="cluster-interceptor.html">Channel/Interceptor</a></li><li><a href="cluster-valve.html">Valve</a></li><li><a href="cluster-deployer.html">Deployer</a></li><li><a href="cluster-listener.html">ClusterListener</a></li></ul></div><div><h2>web.xml</h2><ul><li><a href="filter.html">Filter</a></li></ul></div><div><h2>Other</h2><ul><li><a href="runtime-attributes.html">Runtime attributes</a></li><li><a href="systemprops.html">System properties</a></li><li><a href="jaspic.html">Jakarta Authentication</a></li></ul></div></nav></div></div><div id="mainRight"><div id="content"><h2>Automatic Deployment - Use cases</h2><h3 id="Table_of_Contents">Table of Contents</h3><div class="text">
<ul><li><a href="#Introduction">Introduction</a></li><li><a href="#Key">Key</a></li><li><a href="#New_files">New files</a></li><li><a href="#Deleted_files">Deleted files</a></li><li><a href="#Modified_files">Modified files</a></li><li><a href="#Added_files">Added files</a></li><li><a href="#Notes">Notes</a></li></ul>
</div><h3 id="Introduction">Introduction</h3><div class="text">

  <p>This page defines the expected behaviour of the automatic deployer in many
  typical use cases. This is a complex area of Tomcat's functionality.
  While any difference between this document and Tomcat's behaviour is a
  bug, the fix may be to change this document, Tomcat's behaviour or
  both.</p>

</div><h3 id="Key">Key</h3><div class="text">

  <table class="detail-table">
    <tr>
      <th>Term</th><th>Description</th>
    </tr>
    <tr>
      <td>XML</td>
      <td>An XML configuration file located in the Host's
          <em>configBase</em>. It must contain a single &lt;Context&gt; element
          and may contain optional nested elements. It does not define an
          explicit <em>docBase</em> attribute. It represents a single web
          application. It is often referred to as a context.xml file.</td>
    </tr><tr>
      <td>XML+EW</td>
      <td>An XML configuration file located in the Host's
          <em>configBase</em>. It must contain a single &lt;Context&gt; element
          and may contain optional nested elements. It includes an explicit
          <em>docBase</em> attribute that points to an external WAR. It
          represents a single web application. It is often referred to as a
          context.xml file.</td>
    </tr><tr>
      <td>XML+ED</td>
      <td>An XML configuration file located in the Host's
          <em>configBase</em>. It must contain a single &lt;Context&gt; element
          and may contain optional nested elements. It includes an explicit
          <em>docBase</em> attribute that points to an external directory. It
          represents a single web application. It is often referred to as a
          context.xml file.</td>
    </tr><tr>
      <td>WAR</td>
      <td>A WAR file located in the Host's <em>appBase</em>. The WAR does
          not include an embedded context.xml file.</td>
    </tr><tr>
      <td>WAR+XML</td>
      <td>A WAR file located in the Host's <em>appBase</em>. The WAR does
          include an embedded context.xml file.</td>
    </tr><tr>
      <td>DIR</td>
      <td>A directory located in the Host's <em>appBase</em>. The directory
          does not include an embedded context.xml file.</td>
    </tr><tr>
      <td>DIR+XML</td>
      <td>A directory located in the Host's <em>appBase</em>. The directory
          does include an embedded context.xml file.</td>
    </tr><tr>
      <td>redeploy</td>
      <td>The Context object that represents the web application is destroyed
          and a new Context object is created. If present and permitted by the
          configuration, this new Context object is created by parsing the
          context.xml file. The web.xml file is parsed during the application
          start process. Any sessions stored in the standard Manager in the
          default configuration will not be persisted. Any requests to the web
          application during the redeploy will be handled as if the web
          application is not deployed.</td>
    </tr><tr>
      <td>reload</td>
      <td>The Context object that represents the web application is stopped and
          then started. The web.xml file is parsed during the application start
          process. Any sessions stored in the standard Manager in the default
          configuration will not be persisted. Any requests to the web
          application during the reload will be held until the reload completes
          at which point they will continue using the reloaded web application.
          </td>
    </tr>
  </table>

</div><h3 id="New_files">New files</h3><div class="text">

  <p>This section describes Tomcat's behaviour when the automatic
  deployment process discovers a new web application.</p>

  <table class="detail-table">
    <tr>
      <th rowspan="2">Starting artifact(s)</th>
      <th colspan="3">Configuration Settings</th>
      <th colspan="4">Result</th>
    </tr>
    <tr>
      <th>deployXML</th><th>copyXML</th><th>unpackWARs</th>
      <th>XML</th><th>WAR</th><th>DIR</th><th>Notes</th>
    </tr>
    <tr>
      <td>XML</td>
      <td>either</td><td>either</td><td>either</td>
      <td>Y</td><td>N</td><td>N</td><td>1, 2, 3</td>
    </tr>
    <tr>
      <td>XML+EW</td>
      <td>either</td><td>either</td><td>false</td>
      <td>Y</td><td>N</td><td>N</td><td>1</td>
    </tr>
    <tr>
      <td>XML+EW</td>
      <td>either</td><td>either</td><td>true</td>
      <td>Y</td><td>N</td><td>Y</td><td>1</td>
    </tr>
    <tr>
      <td>XML+ED</td>
      <td>either</td><td>either</td><td>either</td>
      <td>Y</td><td>N</td><td>N</td><td>1, 2</td>
    </tr>
    <tr>
      <td>WAR+XML</td>
      <td>false</td><td>either</td><td>false</td>
      <td>N</td><td>Y</td><td>N</td><td>4</td>
    </tr>
    <tr>
      <td>WAR+XML</td>
      <td>false</td><td>either</td><td>true</td>
      <td>N</td><td>Y</td><td>Y</td><td>4</td>
    </tr>
    <tr>
      <td>WAR+XML</td>
      <td>true</td><td>false</td><td>false</td>
      <td>N</td><td>Y</td><td>N</td><td></td>
    </tr>
    <tr>
      <td>WAR+XML</td>
      <td>true</td><td>false</td><td>true</td>
      <td>N</td><td>Y</td><td>Y</td><td></td>
    </tr>
    <tr>
      <td>WAR+XML</td>
      <td>true</td><td>true</td><td>false</td>
      <td>Y</td><td>Y</td><td>N</td><td></td>
    </tr>
    <tr>
      <td>WAR+XML</td>
      <td>true</td><td>true</td><td>true</td>
      <td>Y</td><td>Y</td><td>Y</td><td></td>
    </tr>
    <tr>
      <td>WAR</td>
      <td>either</td><td>either</td><td>false</td>
      <td>N</td><td>Y</td><td>N</td><td></td>
    </tr>
    <tr>
      <td>WAR</td>
      <td>either</td><td>either</td><td>true</td>
      <td>N</td><td>Y</td><td>Y</td><td></td>
    </tr>
    <tr>
      <td>DIR+XML</td>
      <td>false</td><td>either</td><td>either</td>
      <td>N</td><td>N</td><td>Y</td><td>4</td>
    </tr>
    <tr>
      <td>DIR+XML</td>
      <td>true</td><td>false</td><td>either</td>
      <td>N</td><td>N</td><td>Y</td><td></td>
    </tr>
    <tr>
      <td>DIR+XML</td>
      <td>true</td><td>true</td><td>either</td>
      <td>Y</td><td>N</td><td>Y</td><td></td>
    </tr>
    <tr>
      <td>DIR</td>
      <td>false</td><td>either</td><td>either</td>
      <td>N</td><td>N</td><td>Y</td><td></td>
    </tr>
  </table>

</div><h3 id="Deleted_files">Deleted files</h3><div class="text">

  <p>This section describes Tomcat's behaviour when the automatic
  deployment process detects that a web application file has been deleted.</p>

  <p>When a file is deleted or modified any redeploy resources that are listed
  after the modified/deleted resource are themselves deleted (and possibly
  re-created). The order of redeploy resources is:</p>

  <ol>
    <li>WAR</li>
    <li>DIR</li>
    <li>XML</li>
    <li>global resources</li>
  </ol>

  <p>There are some exceptions to the deletion rule above:</p>

  <ul>
    <li>global resources are never deleted</li>
    <li>external resources are never deleted</li>
    <li>if the WAR or DIR has been modified then the XML file is only deleted if
       <em>copyXML</em> is <code>true</code> and <em>deployXML</em> is
       <code>true</code></li>
  </ul>

  <p>In the following table:</p>

  <ul>
    <li>'-' means "unchanged from not present". i.e. the artifact wasn't present
        before the change and isn't present after it either. '-' rather than 'N'
        is used to focus attention on what changes.</li>
    <li>'R' means that the directory is re-created by expanding the WAR file.
        This will only happen if <em>unpackWARs</em> is <code>true</code>.</li>
    <li>'XW' means that the if the WAR contains a META-INF/context.xml file it
        will be extracted and placed in the Host's <em>configBase</em>.
        This only happens if <em>copyXML</em> is <code>true</code> and
        <em>deployXML</em> is <code>true</code>.</li>
    <li>'XD' means that the if the directory contains a META-INF/context.xml
        file it will be copied to the Host's <em>configBase</em>. This only
        happens if <em>copyXML</em> is <code>true</code> and <em>deployXML</em>
        is <code>true</code>.</li>
  </ul>

  <table class="detail-table">
    <tr>
      <th colspan="3">Artifacts present</th>
      <th rowspan="2">Artifact removed</th>
      <th colspan="4">Artifacts remaining</th>
    </tr>
    <tr>
      <th>XML</th><th>WAR</th><th>DIR</th>
      <th>XML</th><th>WAR</th><th>DIR</th><th>Notes</th>
    </tr>
    <tr>
      <td>N</td><td>N</td><td>Y</td>
      <td>DIR</td>
      <td>-</td><td>-</td><td>N</td><td></td>
    </tr>
    <tr>
      <td>N</td><td>Y</td><td>N</td>
      <td>WAR</td>
      <td>-</td><td>N</td><td>-</td><td></td>
    </tr>
    <tr>
      <td>N</td><td>Y</td><td>Y</td>
      <td>DIR</td>
      <td>-</td><td>Y</td><td>R</td><td></td>
    </tr>
    <tr>
      <td>N</td><td>Y</td><td>Y</td>
      <td>WAR</td>
      <td>-</td><td>N</td><td>N</td><td></td>
    </tr>
    <tr>
      <td>Y</td><td>N</td><td>N</td>
      <td>XML</td>
      <td>N</td><td>-</td><td>-</td><td></td>
    </tr>
    <tr>
      <td>Y</td><td>N</td><td>Y</td>
      <td>DIR</td>
      <td>N</td><td>-</td><td>N</td><td>5</td>
    </tr>
    <tr>
      <td>Y</td><td>N</td><td>Y</td>
      <td>XML</td>
      <td>XD</td><td>-</td><td>Y</td><td></td>
    </tr>
    <tr>
      <td>Y</td><td>Y</td><td>N</td>
      <td>WAR</td>
      <td>N</td><td>N</td><td>-</td><td>5</td>
    </tr>
    <tr>
      <td>Y</td><td>Y</td><td>N</td>
      <td>XML</td>
      <td>XW</td><td>Y</td><td>-</td><td></td>
    </tr>
    <tr>
      <td>Y</td><td>Y</td><td>Y</td>
      <td>DIR</td>
      <td>XW</td><td>Y</td><td>R</td><td></td>
    </tr>
    <tr>
      <td>Y</td><td>Y</td><td>Y</td>
      <td>WAR</td>
      <td>N</td><td>N</td><td>N</td><td></td>
    </tr>
    <tr>
      <td>Y</td><td>Y</td><td>Y</td>
      <td>XML</td>
      <td>XW</td><td>Y</td><td>Y</td><td></td>
    </tr>
    <tr>
      <td>Y</td><td>Y (external)</td><td>N</td>
      <td>WAR</td>
      <td>Y</td><td>N</td><td>-</td><td>3</td>
    </tr>
    <tr>
      <td>Y</td><td>Y (external)</td><td>N</td>
      <td>XML</td>
      <td>N</td><td>Y (external)</td><td>-</td><td>6</td>
    </tr>
    <tr>
      <td>Y</td><td>N</td><td>Y (external)</td>
      <td>DIR</td>
      <td>Y</td><td>-</td><td>N</td><td>3</td>
    </tr>
    <tr>
      <td>Y</td><td>N</td><td>Y (external)</td>
      <td>XML</td>
      <td>N</td><td>-</td><td>Y (external)</td><td>6</td>
    </tr>
    <tr>
      <td>Y</td><td>Y (external)</td><td>Y</td>
      <td>DIR</td>
      <td>Y</td><td>Y (external)</td><td>R</td><td></td>
    </tr>
    <tr>
      <td>Y</td><td>Y (external)</td><td>Y</td>
      <td>WAR</td>
      <td>Y</td><td>N</td><td>N</td><td>3</td>
    </tr>
    <tr>
      <td>Y</td><td>Y (external)</td><td>Y</td>
      <td>XML</td>
      <td>N</td><td>Y (external)</td><td>N</td><td>6</td>
    </tr>
  </table>

</div><h3 id="Modified_files">Modified files</h3><div class="text">

  <p>This section describes Tomcat's behaviour when the automatic
  deployment process detects that a web application file has been modified.</p>

  <p>In the following table:</p>

  <ul>
    <li>'-' means "unchanged from not present". i.e. the artifact wasn't present
        before the change and isn't present after it either. '-' rather than 'N'
        is used to focus attention on what changes.</li>
    <li>'M' means that the artifact has been modified.</li>
    <li>'R' means that the directory is deleted and re-created by expanding the
        WAR file. This will only happen if <em>unpackWARs</em> is
        <code>true</code>.</li>
  </ul>

  <table class="detail-table">
    <tr>
      <th colspan="3">Artifacts present</th>
      <th rowspan="2">Artifact modified</th>
      <th colspan="4">Artifacts remaining</th>
    </tr>
    <tr>
      <th>XML</th><th>WAR</th><th>DIR</th>
      <th>XML</th><th>WAR</th><th>DIR</th><th>Action</th>
    </tr>
    <tr>
      <td>N</td><td>N</td><td>Y</td>
      <td>DIR</td>
      <td>-</td><td>-</td><td>M</td><td>None</td>
    </tr>
    <tr>
      <td>N</td><td>Y</td><td>N</td>
      <td>WAR</td>
      <td>-</td><td>M</td><td>-</td><td>Redeploy</td>
    </tr>
    <tr>
      <td>N</td><td>Y</td><td>Y</td>
      <td>DIR</td>
      <td>-</td><td>Y</td><td>M</td><td>None</td>
    </tr>
    <tr>
      <td>N</td><td>Y</td><td>Y</td>
      <td>WAR</td>
      <td>-</td><td>M</td><td>R</td><td>Redeploy</td>
    </tr>
    <tr>
      <td>Y</td><td>N</td><td>N</td>
      <td>XML</td>
      <td>M</td><td>-</td><td>-</td><td>Redeploy</td>
    </tr>
    <tr>
      <td>Y</td><td>N</td><td>Y</td>
      <td>DIR</td>
      <td>Y</td><td>-</td><td>M</td><td>None</td>
    </tr>
    <tr>
      <td>Y</td><td>N</td><td>Y</td>
      <td>XML</td>
      <td>M</td><td>-</td><td>Y</td><td>Redeploy</td>
    </tr>
    <tr>
      <td>Y</td><td>Y</td><td>N</td>
      <td>WAR</td>
      <td>Y</td><td>M</td><td>-</td><td>Reload</td>
    </tr>
    <tr>
      <td>Y</td><td>Y</td><td>N</td>
      <td>XML</td>
      <td>M</td><td>Y</td><td>-</td><td>Redeploy</td>
    </tr>
    <tr>
      <td>Y</td><td>Y</td><td>Y</td>
      <td>DIR</td>
      <td>Y</td><td>Y</td><td>M</td><td>None</td>
    </tr>
    <tr>
      <td>Y</td><td>Y</td><td>Y</td>
      <td>WAR</td>
      <td>Y</td><td>M</td><td>R</td><td>Reload</td>
    </tr>
    <tr>
      <td>Y</td><td>Y</td><td>Y</td>
      <td>XML</td>
      <td>M</td><td>Y</td><td>Y</td><td>Redeploy</td>
    </tr>
    <tr>
      <td>Y</td><td>Y(external)</td><td>N</td>
      <td>WAR</td>
      <td>Y</td><td>M(external)</td><td>-</td><td>Reload</td>
    </tr>
    <tr>
      <td>Y</td><td>Y(external)</td><td>N</td>
      <td>XML</td>
      <td>M</td><td>Y(external)</td><td>-</td><td>Redeploy</td>
    </tr>
    <tr>
      <td>Y</td><td>N</td><td>Y(external)</td>
      <td>DIR</td>
      <td>Y</td><td>-</td><td>M(external)</td><td>None</td>
    </tr>
    <tr>
      <td>Y</td><td>N</td><td>Y(external)</td>
      <td>XML</td>
      <td>M</td><td>-</td><td>Y(external)</td><td>Redeploy</td>
    </tr>
    <tr>
      <td>Y</td><td>Y(external)</td><td>Y</td>
      <td>DIR</td>
      <td>Y</td><td>Y(external)</td><td>M</td><td>None</td>
    </tr>
    <tr>
      <td>Y</td><td>Y(external)</td><td>Y</td>
      <td>WAR</td>
      <td>Y</td><td>M(external)</td><td>R</td><td>Reload</td>
    </tr>
    <tr>
      <td>Y</td><td>Y(external)</td><td>Y</td>
      <td>XML</td>
      <td>M</td><td>Y(external)</td><td>Y</td><td>Redeploy</td>
    </tr>
  </table>

</div><h3 id="Added_files">Added files</h3><div class="text">

  <p>This is treated as if the added file has been modified with the following
  additional actions:</p>

  <ul>
    <li>If a WAR is added, any DIR is removed and may be recreated depending on
        <em>unpackWARs</em>.</li>
    <li>If an XML file is added that refers to an external <em>docBase</em> any
        WAR or DIR in the appBase will be removed. The DIR may be recreated if
        the external resource is a WAR and <em>unpackWARs</em> is true.</li>
    <li>If a DIR is added when a WAR already exists and <em>unpackWARs</em> is
        <code>false</code>, the DIR will be ignored but a warning will be
        logged when the DIR is first detected. If the WAR is removed, the DIR
        will be left and may be deployed via automatic deployment.</li>
    <li>If a WAR is added to the <em>appBase</em> when an external WAR already
        exists, the WAR in the <em>appBase</em> will be ignored but a warning
        will be logged when the WAR in the <em>appBase</em> is first detected.
        If the external WAR is removed, the WAR in the <em>appBase</em> will be
        left and may be deployed via automatic deployment.</li>
    <li>If an XML file is added to the META-INF directory of an application
        deployed from that DIR, the application will always be redeployed. The
        result will be the same as for a new deployment.</li>
  </ul>

</div><h3 id="Notes">Notes</h3><div class="text">

  <ol>
    <li><em>deployXML</em> and <em>copyXML</em> are ignored since an XML file
        was discovered in the <em>configBase</em>.</li>
    <li><em>unpackWARs</em> is ignored since there is no WAR file.</li>
    <li>The context will fail to start because there is no content in the
        expected <em>docBase</em>.</li>
    <li>The web application fails to deploy because it contains an embedded
        META-INF/context.xml, <em>deployXML</em> is <code>false</code> and an
        XML has not been provided in the <em>configBase</em>.</li>
    <li>The XML file is only deleted if <em>copyXML</em> is <code>true</code>
        and <em>deployXML</em> is <code>true</code>.</li>
    <li>Although the external resource is still present, the web application is
        fully undeployed as Tomcat has no knowledge of the external resource.
        </li>
  </ol>

</div></div></div></div></div><footer><div id="footer">
    Copyright &copy; 1999-2025, The Apache Software Foundation
    <br>
    Apache Tomcat, Tomcat, Apache, the Apache Tomcat logo and the Apache logo
    are either registered trademarks or trademarks of the Apache Software
    Foundation.
    </div></footer></div></body></html>