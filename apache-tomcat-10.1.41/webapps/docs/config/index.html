<!DOCTYPE html SYSTEM "about:legacy-compat">
<html lang="en"><head><META http-equiv="Content-Type" content="text/html; charset=UTF-8"><link href="../images/docs-stylesheet.css" rel="stylesheet" type="text/css"><title>Apache Tomcat 10 Configuration Reference (10.1.41) - Overview</title><meta name="author" content="<PERSON>"></head><body><div id="wrapper"><header><div id="header"><div><div><div class="logo noPrint"><a href="https://tomcat.apache.org/"><img alt="Tomcat Home" src="../images/tomcat.png"></a></div><div style="height: 1px;"></div><div class="asfLogo noPrint"><a href="https://www.apache.org/" target="_blank"><img src="../images/asf-logo.svg" alt="The Apache Software Foundation" style="width: 266px; height: 83px;"></a></div><h1>Apache Tomcat 10 Configuration Reference</h1><div class="versionInfo">
            Version 10.1.41,
            <time datetime="2025-05-08">May 8 2025</time></div><div style="height: 1px;"></div><div style="clear: left;"></div></div></div></div></header><div id="middle"><div><div id="mainLeft" class="noprint"><div><nav><div><h2>Links</h2><ul><li><a href="../index.html">Docs Home</a></li><li><a href="index.html">Config Ref. Home</a></li><li><a href="https://cwiki.apache.org/confluence/display/TOMCAT/FAQ">FAQ</a></li><li><a href="#comments_section">User Comments</a></li></ul></div><div><h2>Top Level Elements</h2><ul><li><a href="server.html">Server</a></li><li><a href="service.html">Service</a></li></ul></div><div><h2>Executors</h2><ul><li><a href="executor.html">Executor</a></li></ul></div><div><h2>Connectors</h2><ul><li><a href="http.html">HTTP/1.1</a></li><li><a href="http2.html">HTTP/2</a></li><li><a href="ajp.html">AJP</a></li></ul></div><div><h2>Containers</h2><ul><li><a href="context.html">Context</a></li><li><a href="engine.html">Engine</a></li><li><a href="host.html">Host</a></li><li><a href="cluster.html">Cluster</a></li></ul></div><div><h2>Nested Components</h2><ul><li><a href="cookie-processor.html">CookieProcessor</a></li><li><a href="credentialhandler.html">CredentialHandler</a></li><li><a href="globalresources.html">Global Resources</a></li><li><a href="jar-scanner.html">JarScanner</a></li><li><a href="jar-scan-filter.html">JarScanFilter</a></li><li><a href="listeners.html">Listeners</a></li><li><a href="loader.html">Loader</a></li><li><a href="manager.html">Manager</a></li><li><a href="realm.html">Realm</a></li><li><a href="resources.html">Resources</a></li><li><a href="sessionidgenerator.html">SessionIdGenerator</a></li><li><a href="valve.html">Valve</a></li></ul></div><div><h2>Cluster Elements</h2><ul><li><a href="cluster.html">Cluster</a></li><li><a href="cluster-manager.html">Manager</a></li><li><a href="cluster-channel.html">Channel</a></li><li><a href="cluster-membership.html">Channel/Membership</a></li><li><a href="cluster-sender.html">Channel/Sender</a></li><li><a href="cluster-receiver.html">Channel/Receiver</a></li><li><a href="cluster-interceptor.html">Channel/Interceptor</a></li><li><a href="cluster-valve.html">Valve</a></li><li><a href="cluster-deployer.html">Deployer</a></li><li><a href="cluster-listener.html">ClusterListener</a></li></ul></div><div><h2>web.xml</h2><ul><li><a href="filter.html">Filter</a></li></ul></div><div><h2>Other</h2><ul><li><a href="runtime-attributes.html">Runtime attributes</a></li><li><a href="systemprops.html">System properties</a></li><li><a href="jaspic.html">Jakarta Authentication</a></li></ul></div></nav></div></div><div id="mainRight"><div id="content"><h2>Overview</h2><h3 id="Overview">Overview</h3><div class="text">

<p>This manual contains reference information about all of the configuration
directives that can be included in a <code>conf/server.xml</code> file to
configure the behavior of the Tomcat Servlet/JSP container.  It does not
attempt to describe which configuration directives should be used to perform
specific tasks - for that, see the various <em>How-To</em> documents on the
main index page.</p>

<p>Tomcat configuration files are formatted as schemaless XML; elements and
attributes are case-sensitive. Apache Ant-style variable substitution
is supported; a system property with the name <code>propname</code> may be
used in a configuration file using the syntax <code>${propname}</code>. All
system properties are available including those set using the <code>-D</code>
syntax, those automatically made available by the JVM and those configured in
the <code>$CATALINA_BASE/conf/catalina.properties</code> file.
</p>

<p>The configuration element descriptions are organized into the following
major categories:</p>
<ul>
<li><strong>Top Level Elements</strong> - <code>&lt;Server&gt;</code> is the
    root element of the entire configuration file, while
    <code>&lt;Service&gt;</code> represents a group of Connectors that is
    associated with an Engine.</li>
<li><strong>Connectors</strong> - Represent the interface between external
    clients sending requests to (and receiving responses from) a particular
    Service.</li>
<li><strong>Containers</strong> - Represent components whose function is to
    process incoming requests, and create the corresponding responses.
    An Engine handles all requests for a Service, a Host handles all requests
    for a particular virtual host, and a Context handles all requests for a
    specific web application.</li>
<li><strong>Nested Components</strong> - Represent elements that can be
    nested inside the element for a Container.  Some elements can be nested
    inside any Container, while others can only be nested inside a
    Context.</li>
</ul>

<p>For each element, the corresponding documentation follows this general
outline:</p>
<ul>
<li><strong>Introduction</strong> - Overall description of this particular
    component.  There will be a corresponding Java <em>interface</em> (in
    the <code>org.apache.catalina</code> package) that is implemented by one
    or more standard implementations.</li>
<li><strong>Attributes</strong> - The set of attributes that are legal for
    this element.  Generally, this will be subdivided into <em>Common</em>
    attributes that are supported by all implementations of the corresponding
    Java interface, and <em>Standard Implementation</em> attributes that are
    specific to a particular Java class that implements this interface.
    The names of required attributes are <strong>bolded</strong>.</li>
<li><strong>Nested Components</strong> - Enumerates which of the <em>Nested
    Components</em> can be legally nested within this element.</li>
<li><strong>Special Features</strong> - Describes the configuration of a large
    variety of special features (specific to each element type) that are
    supported by the standard implementation of this interface.</li>
</ul>

</div></div></div></div></div><footer><div id="footer">
    Copyright &copy; 1999-2025, The Apache Software Foundation
    <br>
    Apache Tomcat, Tomcat, Apache, the Apache Tomcat logo and the Apache logo
    are either registered trademarks or trademarks of the Apache Software
    Foundation.
    </div></footer></div></body></html>