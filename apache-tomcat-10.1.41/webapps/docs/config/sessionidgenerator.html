<!DOCTYPE html SYSTEM "about:legacy-compat">
<html lang="en"><head><META http-equiv="Content-Type" content="text/html; charset=UTF-8"><link href="../images/docs-stylesheet.css" rel="stylesheet" type="text/css"><title>Apache Tomcat 10 Configuration Reference (10.1.41) - The SessionIdGenerator Component</title></head><body><div id="wrapper"><header><div id="header"><div><div><div class="logo noPrint"><a href="https://tomcat.apache.org/"><img alt="Tomcat Home" src="../images/tomcat.png"></a></div><div style="height: 1px;"></div><div class="asfLogo noPrint"><a href="https://www.apache.org/" target="_blank"><img src="../images/asf-logo.svg" alt="The Apache Software Foundation" style="width: 266px; height: 83px;"></a></div><h1>Apache Tomcat 10 Configuration Reference</h1><div class="versionInfo">
            Version 10.1.41,
            <time datetime="2025-05-08">May 8 2025</time></div><div style="height: 1px;"></div><div style="clear: left;"></div></div></div></div></header><div id="middle"><div><div id="mainLeft" class="noprint"><div><nav><div><h2>Links</h2><ul><li><a href="../index.html">Docs Home</a></li><li><a href="index.html">Config Ref. Home</a></li><li><a href="https://cwiki.apache.org/confluence/display/TOMCAT/FAQ">FAQ</a></li><li><a href="#comments_section">User Comments</a></li></ul></div><div><h2>Top Level Elements</h2><ul><li><a href="server.html">Server</a></li><li><a href="service.html">Service</a></li></ul></div><div><h2>Executors</h2><ul><li><a href="executor.html">Executor</a></li></ul></div><div><h2>Connectors</h2><ul><li><a href="http.html">HTTP/1.1</a></li><li><a href="http2.html">HTTP/2</a></li><li><a href="ajp.html">AJP</a></li></ul></div><div><h2>Containers</h2><ul><li><a href="context.html">Context</a></li><li><a href="engine.html">Engine</a></li><li><a href="host.html">Host</a></li><li><a href="cluster.html">Cluster</a></li></ul></div><div><h2>Nested Components</h2><ul><li><a href="cookie-processor.html">CookieProcessor</a></li><li><a href="credentialhandler.html">CredentialHandler</a></li><li><a href="globalresources.html">Global Resources</a></li><li><a href="jar-scanner.html">JarScanner</a></li><li><a href="jar-scan-filter.html">JarScanFilter</a></li><li><a href="listeners.html">Listeners</a></li><li><a href="loader.html">Loader</a></li><li><a href="manager.html">Manager</a></li><li><a href="realm.html">Realm</a></li><li><a href="resources.html">Resources</a></li><li><a href="sessionidgenerator.html">SessionIdGenerator</a></li><li><a href="valve.html">Valve</a></li></ul></div><div><h2>Cluster Elements</h2><ul><li><a href="cluster.html">Cluster</a></li><li><a href="cluster-manager.html">Manager</a></li><li><a href="cluster-channel.html">Channel</a></li><li><a href="cluster-membership.html">Channel/Membership</a></li><li><a href="cluster-sender.html">Channel/Sender</a></li><li><a href="cluster-receiver.html">Channel/Receiver</a></li><li><a href="cluster-interceptor.html">Channel/Interceptor</a></li><li><a href="cluster-valve.html">Valve</a></li><li><a href="cluster-deployer.html">Deployer</a></li><li><a href="cluster-listener.html">ClusterListener</a></li></ul></div><div><h2>web.xml</h2><ul><li><a href="filter.html">Filter</a></li></ul></div><div><h2>Other</h2><ul><li><a href="runtime-attributes.html">Runtime attributes</a></li><li><a href="systemprops.html">System properties</a></li><li><a href="jaspic.html">Jakarta Authentication</a></li></ul></div></nav></div></div><div id="mainRight"><div id="content"><h2>The SessionIdGenerator Component</h2><h3 id="Table_of_Contents">Table of Contents</h3><div class="text">
<ul><li><a href="#Introduction">Introduction</a></li><li><a href="#Attributes">Attributes</a><ol><li><a href="#Common_Attributes">Common Attributes</a></li><li><a href="#Standard_Implementation">Standard Implementation</a></li></ol></li></ul>
</div><h3 id="Introduction">Introduction</h3><div class="text">

  <p>The <strong>SessionIdGenerator</strong> element represents the <em>session
  id generator</em> that will be used to create session ids used by
  web application HTTP sessions.</p>

  <p>A SessionIdGenerator element MAY be nested inside a
  <a href="manager.html">Manager</a> component.  If it is not included,
  a default SessionIdGenerator configuration will be created automatically, which
  is sufficient for most requirements, &mdash; see
  <em>Standard SessionIdGenerator Implementation</em> below for the details
  of this configuration.</p>

</div><h3 id="Attributes">Attributes</h3><div class="text">

  <div class="subsection"><h4 id="Common_Attributes">Common Attributes</h4><div class="text">

    <p>All implementations of <strong>SessionIdGenerator</strong>
    support the following attributes:</p>

    <table class="defaultTable"><tr><th style="width: 15%;">
          Attribute
        </th><th style="width: 85%;">
          Description
        </th></tr><tr id="Attributes_Common Attributes_className"><td><code class="attributeName">className</code></td><td>
        <p>Java class name of the implementation to use.  This class must
        implement the <code>org.apache.catalina.SessionIdGenerator</code> interface.
        If not specified, the standard value (defined below) will be used.</p>
      </td></tr><tr id="Attributes_Common Attributes_jvmRoute"><td><code class="attributeName">jvmRoute</code></td><td>
        <p>A routing identifier for this Tomcat instance. It will be added
        to the session id to allow for stateless stickiness routing by
        load balancers. The details on how the <code>jvmRoute</code>
        will be included in the id are implementation dependent.
        See <a href="#Standard_Implementation">Standard Implementation</a>
        for the default behavior.</p>

        <p><strong>NOTE</strong> - The value for this property is inherited
        automatically from the <code>jvmRoute</code> attribute of the
        <a href="engine.html">Engine</a> element.</p>
      </td></tr><tr id="Attributes_Common Attributes_sessionIdLength"><td><code class="attributeName">sessionIdLength</code></td><td>
        <p>The length of session ids created by this SessionIdGenerator.
        The details on how the <code>sessionIdLength</code>
        influences the session id length are implementation dependent.
        See <a href="#Standard_Implementation">Standard Implementation</a>
        for the default behavior.</p>
      </td></tr></table>

  </div></div>


  <div class="subsection"><h4 id="Standard_Implementation">Standard Implementation</h4><div class="text">

    <p>Tomcat provides a standard implementations of <strong>SessionIdGenerator</strong>
    for use.</p>

    <h3>Standard SessionIdGenerator Implementation</h3>

    <p>The standard implementation of <strong>SessionIdGenerator</strong> is
    <strong>org.apache.catalina.util.StandardSessionIdGenerator</strong>.
    It supports the following attributes:</p>

    <table class="defaultTable"><tr><th style="width: 15%;">
          Attribute
        </th><th style="width: 85%;">
          Description
        </th></tr><tr id="Attributes_Standard Implementation_jvmRoute"><td><code class="attributeName">jvmRoute</code></td><td>
        <p>A routing identifier for this Tomcat instance. It will be added
        to the end of the session id separated by a ".".</p>
      </td></tr><tr id="Attributes_Standard Implementation_sessionIdLength"><td><code class="attributeName">sessionIdLength</code></td><td>
        <p>The length of session ids created by this SessionIdGenerator.
        More precisely the session id length is twice the value of
        <code>sessionIdLength</code> plus the length of the trailing
        <code>jvmRoute</code> if given. The factor 2 is because
        the session id is constructed using <code>sessionIdLength</code>
        random bytes, each byte being encoded in two hex characters
        in the actual id. The default value is 16.</p>
      </td></tr></table>

  </div></div>


</div></div></div></div></div><footer><div id="footer">
    Copyright &copy; 1999-2025, The Apache Software Foundation
    <br>
    Apache Tomcat, Tomcat, Apache, the Apache Tomcat logo and the Apache logo
    are either registered trademarks or trademarks of the Apache Software
    Foundation.
    </div></footer></div></body></html>