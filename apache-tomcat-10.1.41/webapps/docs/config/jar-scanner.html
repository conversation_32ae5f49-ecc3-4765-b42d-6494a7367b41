<!DOCTYPE html SYSTEM "about:legacy-compat">
<html lang="en"><head><META http-equiv="Content-Type" content="text/html; charset=UTF-8"><link href="../images/docs-stylesheet.css" rel="stylesheet" type="text/css"><title>Apache Tomcat 10 Configuration Reference (10.1.41) - The Jar Scanner Component</title></head><body><div id="wrapper"><header><div id="header"><div><div><div class="logo noPrint"><a href="https://tomcat.apache.org/"><img alt="Tomcat Home" src="../images/tomcat.png"></a></div><div style="height: 1px;"></div><div class="asfLogo noPrint"><a href="https://www.apache.org/" target="_blank"><img src="../images/asf-logo.svg" alt="The Apache Software Foundation" style="width: 266px; height: 83px;"></a></div><h1>Apache Tomcat 10 Configuration Reference</h1><div class="versionInfo">
            Version 10.1.41,
            <time datetime="2025-05-08">May 8 2025</time></div><div style="height: 1px;"></div><div style="clear: left;"></div></div></div></div></header><div id="middle"><div><div id="mainLeft" class="noprint"><div><nav><div><h2>Links</h2><ul><li><a href="../index.html">Docs Home</a></li><li><a href="index.html">Config Ref. Home</a></li><li><a href="https://cwiki.apache.org/confluence/display/TOMCAT/FAQ">FAQ</a></li><li><a href="#comments_section">User Comments</a></li></ul></div><div><h2>Top Level Elements</h2><ul><li><a href="server.html">Server</a></li><li><a href="service.html">Service</a></li></ul></div><div><h2>Executors</h2><ul><li><a href="executor.html">Executor</a></li></ul></div><div><h2>Connectors</h2><ul><li><a href="http.html">HTTP/1.1</a></li><li><a href="http2.html">HTTP/2</a></li><li><a href="ajp.html">AJP</a></li></ul></div><div><h2>Containers</h2><ul><li><a href="context.html">Context</a></li><li><a href="engine.html">Engine</a></li><li><a href="host.html">Host</a></li><li><a href="cluster.html">Cluster</a></li></ul></div><div><h2>Nested Components</h2><ul><li><a href="cookie-processor.html">CookieProcessor</a></li><li><a href="credentialhandler.html">CredentialHandler</a></li><li><a href="globalresources.html">Global Resources</a></li><li><a href="jar-scanner.html">JarScanner</a></li><li><a href="jar-scan-filter.html">JarScanFilter</a></li><li><a href="listeners.html">Listeners</a></li><li><a href="loader.html">Loader</a></li><li><a href="manager.html">Manager</a></li><li><a href="realm.html">Realm</a></li><li><a href="resources.html">Resources</a></li><li><a href="sessionidgenerator.html">SessionIdGenerator</a></li><li><a href="valve.html">Valve</a></li></ul></div><div><h2>Cluster Elements</h2><ul><li><a href="cluster.html">Cluster</a></li><li><a href="cluster-manager.html">Manager</a></li><li><a href="cluster-channel.html">Channel</a></li><li><a href="cluster-membership.html">Channel/Membership</a></li><li><a href="cluster-sender.html">Channel/Sender</a></li><li><a href="cluster-receiver.html">Channel/Receiver</a></li><li><a href="cluster-interceptor.html">Channel/Interceptor</a></li><li><a href="cluster-valve.html">Valve</a></li><li><a href="cluster-deployer.html">Deployer</a></li><li><a href="cluster-listener.html">ClusterListener</a></li></ul></div><div><h2>web.xml</h2><ul><li><a href="filter.html">Filter</a></li></ul></div><div><h2>Other</h2><ul><li><a href="runtime-attributes.html">Runtime attributes</a></li><li><a href="systemprops.html">System properties</a></li><li><a href="jaspic.html">Jakarta Authentication</a></li></ul></div></nav></div></div><div id="mainRight"><div id="content"><h2>The Jar Scanner Component</h2><h3 id="Table_of_Contents">Table of Contents</h3><div class="text">
<ul><li><a href="#Introduction">Introduction</a></li><li><a href="#Attributes">Attributes</a><ol><li><a href="#Common_Attributes">Common Attributes</a></li><li><a href="#Standard_Implementation">Standard Implementation</a></li></ol></li><li><a href="#Nested_Components">Nested Components</a></li><li><a href="#Special_Features">Special Features</a></li></ul>
</div><h3 id="Introduction">Introduction</h3><div class="text">

  <p>The <strong>Jar Scanner</strong> element represents the component that is
  used to scan the web application for JAR files and directories of class files.
  It is typically used during web application start to identify configuration
  files such as TLDs or web-fragment.xml files that must be processed as part of
  the web application initialisation.</p>

  <p>A Jar Scanner element MAY be nested inside a
  <a href="context.html">Context</a> component.</p>

  <p>For example you can include the bootstrap classpath when scanning for jar
  files:</p>
<div class="codeBox"><pre><code>&lt;Context&gt;
  ...
  &lt;JarScanner scanBootstrapClassPath="true"/&gt;
  ...
&lt;/Context&gt;</code></pre></div>

  <p>If a Jar Scanner element is not included, a default Jar Scanner configuration
  will be created automatically, which is sufficient for most requirements.</p>

</div><h3 id="Attributes">Attributes</h3><div class="text">

  <div class="subsection"><h4 id="Common_Attributes">Common Attributes</h4><div class="text">

    <p>All implementations of <strong>Jar Scanner</strong>
    support the following attributes:</p>

    <table class="defaultTable"><tr><th style="width: 15%;">
          Attribute
        </th><th style="width: 85%;">
          Description
        </th></tr><tr id="Attributes_Common Attributes_className"><td><code class="attributeName">className</code></td><td>
        <p>Java class name of the implementation to use.  This class must
        implement the <code>org.apache.tomcat.JarScanner</code> interface.
        If not specified, the standard value (defined below) will be used.</p>
      </td></tr></table>

  </div></div>


  <div class="subsection"><h4 id="Standard_Implementation">Standard Implementation</h4><div class="text">

    <p>The standard implementation of <strong>Jar Scanner</strong> is
    <strong>org.apache.tomcat.util.scan.StandardJarScanner</strong>.
    It supports the following additional attributes (in addition to the
    common attributes listed above):</p>

    <table class="defaultTable"><tr><th style="width: 15%;">
          Attribute
        </th><th style="width: 85%;">
          Description
        </th></tr><tr id="Attributes_Standard Implementation_scanAllDirectories"><td><code class="attributeName">scanAllDirectories</code></td><td>
       <p>If <code>true</code>, any directories found on the classpath will be
       checked to see if they are expanded JAR files.
       The default is <code>false</code>.</p>
       <p>Tomcat determines if a directory is an expanded JAR file by looking
       for a META-INF sub-directory. Only if the META-INF sub-directory exists,
       the directory is assumed to be an expanded JAR file. Note that for scans
       for matches to <code>@HandlesTypes</code> annotations, all directories
       will be scanned irrespective of the presence or not of a META-INF
       sub-directory.</p>
      </td></tr><tr id="Attributes_Standard Implementation_scanAllFiles"><td><code class="attributeName">scanAllFiles</code></td><td>
       <p>If <code>true</code>, any files found on the classpath will be checked
       to see if they are Jar files rather than relying on the file extension
       being <code>.jar</code>. The default is <code>false</code>.</p>
      </td></tr><tr id="Attributes_Standard Implementation_scanClassPath"><td><code class="attributeName">scanClassPath</code></td><td>
       <p>If <code>true</code>, the full web application classpath, including
       the shared and common classloaders and the system classpath (but not the
       bootstrap classpath) will be scanned for Jar files in addition to the web
       application. The default is <code>true</code>.</p>
      </td></tr><tr id="Attributes_Standard Implementation_scanBootstrapClassPath"><td><code class="attributeName">scanBootstrapClassPath</code></td><td>
       <p>If <strong>scanClassPath</strong> is <code>true</code> and this is
       <code>true</code> the bootstrap classpath will also be scanned for Jar
       files. The default is <code>false</code>.</p>
      </td></tr><tr id="Attributes_Standard Implementation_scanManifest"><td><code class="attributeName">scanManifest</code></td><td>
       <p>If <code>true</code>, the Manifest files of any JARs found will be
       scanned for additional class path entries and those entries will be added
       to the URLs to scan. The default is <code>true</code>.</p>
      </td></tr></table>

  </div></div>


</div><h3 id="Nested_Components">Nested Components</h3><div class="text">
  <p>Only a <a href="jar-scan-filter.html">Jar Scan Filter</a> may be nested
  inside a <strong>Jar Scanner</strong> element.</p>
</div><h3 id="Special_Features">Special Features</h3><div class="text">
  <p>No special features are associated with a <strong>Jar Scanner</strong>
  element.</p>
</div></div></div></div></div><footer><div id="footer">
    Copyright &copy; 1999-2025, The Apache Software Foundation
    <br>
    Apache Tomcat, Tomcat, Apache, the Apache Tomcat logo and the Apache logo
    are either registered trademarks or trademarks of the Apache Software
    Foundation.
    </div></footer></div></body></html>