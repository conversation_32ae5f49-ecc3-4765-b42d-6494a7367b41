<!DOCTYPE html SYSTEM "about:legacy-compat">
<html lang="en"><head><META http-equiv="Content-Type" content="text/html; charset=UTF-8"><link href="./images/docs-stylesheet.css" rel="stylesheet" type="text/css"><title>Apache Tomcat 10 (10.1.41) - Documentation User Comments</title></head><body><div id="wrapper"><header><div id="header"><div><div><div class="logo noPrint"><a href="https://tomcat.apache.org/"><img alt="Tomcat Home" src="./images/tomcat.png"></a></div><div style="height: 1px;"></div><div class="asfLogo noPrint"><a href="https://www.apache.org/" target="_blank"><img src="./images/asf-logo.svg" alt="The Apache Software Foundation" style="width: 266px; height: 83px;"></a></div><h1>Apache Tomcat 10</h1><div class="versionInfo">
            Version 10.1.41,
            <time datetime="2025-05-08">May 8 2025</time></div><div style="height: 1px;"></div><div style="clear: left;"></div></div></div></div></header><div id="middle"><div><div id="mainLeft" class="noprint"><div><nav><div><h2>Links</h2><ul><li><a href="index.html">Docs Home</a></li><li><a href="https://cwiki.apache.org/confluence/display/TOMCAT/FAQ">FAQ</a></li></ul></div><div><h2>User Guide</h2><ul><li><a href="introduction.html">1) Introduction</a></li><li><a href="setup.html">2) Setup</a></li><li><a href="appdev/index.html">3) First webapp</a></li><li><a href="deployer-howto.html">4) Deployer</a></li><li><a href="manager-howto.html">5) Manager</a></li><li><a href="host-manager-howto.html">6) Host Manager</a></li><li><a href="realm-howto.html">7) Realms and AAA</a></li><li><a href="security-manager-howto.html">8) Security Manager</a></li><li><a href="jndi-resources-howto.html">9) JNDI Resources</a></li><li><a href="jndi-datasource-examples-howto.html">10) JDBC DataSources</a></li><li><a href="class-loader-howto.html">11) Classloading</a></li><li><a href="jasper-howto.html">12) JSPs</a></li><li><a href="ssl-howto.html">13) SSL/TLS</a></li><li><a href="ssi-howto.html">14) SSI</a></li><li><a href="cgi-howto.html">15) CGI</a></li><li><a href="proxy-howto.html">16) Proxy Support</a></li><li><a href="mbeans-descriptors-howto.html">17) MBeans Descriptors</a></li><li><a href="default-servlet.html">18) Default Servlet</a></li><li><a href="cluster-howto.html">19) Clustering</a></li><li><a href="balancer-howto.html">20) Load Balancer</a></li><li><a href="connectors.html">21) Connectors</a></li><li><a href="monitoring.html">22) Monitoring and Management</a></li><li><a href="logging.html">23) Logging</a></li><li><a href="apr.html">24) APR/Native</a></li><li><a href="virtual-hosting-howto.html">25) Virtual Hosting</a></li><li><a href="aio.html">26) Advanced IO</a></li><li><a href="maven-jars.html">27) Mavenized</a></li><li><a href="security-howto.html">28) Security Considerations</a></li><li><a href="windows-service-howto.html">29) Windows Service</a></li><li><a href="windows-auth-howto.html">30) Windows Authentication</a></li><li><a href="jdbc-pool.html">31) Tomcat's JDBC Pool</a></li><li><a href="web-socket-howto.html">32) WebSocket</a></li><li><a href="rewrite.html">33) Rewrite</a></li><li><a href="cdi.html">34) CDI 2 and JAX-RS</a></li><li><a href="graal.html">35) AOT/GraalVM Support</a></li></ul></div><div><h2>Reference</h2><ul><li><a href="RELEASE-NOTES.txt">Release Notes</a></li><li><a href="config/index.html">Configuration</a></li><li><a href="api/index.html">Tomcat Javadocs</a></li><li><a href="servletapi/index.html">Servlet 6.0 Javadocs</a></li><li><a href="jspapi/index.html">JSP 3.1 Javadocs</a></li><li><a href="elapi/index.html">EL 5.0 Javadocs</a></li><li><a href="websocketapi/index.html">WebSocket 2.1 Javadocs</a></li><li><a href="jaspicapi/index.html">Authentication 3.0 Javadocs</a></li><li><a href="annotationapi/index.html">Annotations 2.1 Javadocs</a></li><li><a href="https://tomcat.apache.org/connectors-doc/">JK 1.2 Documentation</a></li></ul></div><div><h2>Apache Tomcat Development</h2><ul><li><a href="building.html">Building</a></li><li><a href="changelog.html">Changelog</a></li><li><a href="https://cwiki.apache.org/confluence/display/TOMCAT/Tomcat+Versions">Status</a></li><li><a href="developers.html">Developers</a></li><li><a href="architecture/index.html">Architecture</a></li><li><a href="tribes/introduction.html">Tribes</a></li></ul></div></nav></div></div><div id="mainRight"><div id="content"><h2>Documentation User Comments</h2><h3 id="Introduction">Introduction</h3><div class="text">

<p>The Tomcat documentation integrates the
<a href="https://comments.apache.org/help.html">Apache Comments System</a>.
It allows users to add comments to most documentation pages. The comments
section can be found at the end of each page.</p>

</div><h3 id="Allowed_Content">Allowed Content</h3><div class="text">

<p>Please use the Apache Comments System responsibly. We can only provide
this service to the community as long as it isn't misused.</p>

<p>The comments are not for general Q&amp;A.
Comments should be pointed towards suggestions on improving the documentation
or server. Questions on how to use Apache Tomcat should be directed
to our <a href="https://tomcat.apache.org/lists.html">mailing lists</a>.</p>

<p>Comments may be removed by moderators if they are either
implemented or considered invalid/off-topic.</p>

<p>HTML is not allowed in comments, and will just display as raw source code
if attempted. Links that do not point to an Apache site (*.apache.org) will
need approval by a moderator before the comment is visible to regular visitors.</p>

</div><h3 id="License">License</h3><div class="text">

<p>Any submitted comments must be contributed under the terms of the
<a href="https://www.apache.org/licenses/LICENSE-2.0">Apache License, Version 2.0</a>.</p>

</div><h3 id="Verified_Users">Verified Users</h3><div class="text">

<p>Verified users gain the Apache feather next to their name,
and may post comments with links in them without requiring approval
by a moderator before the comments are shown. Being a verified user
in itself does not give you moderating rights. If you are interested
in becoming a verified user, please contact us on the
<a href="https://tomcat.apache.org/lists.html#tomcat-users">users mailing list</a>.</p>

<p>All ASF committers are automatically verified users.</p>

</div><h3 id="Moderators">Moderators</h3><div class="text">

<p>Moderators are allowed to mark comments as "Resolved", "Invalid"
or "Sticky", remove marks, approve comments e.g. if they contain
a link, and delete comments. Moderators can also subscribe to new
comments and comment updates and use the dashboard to gain some
overview over all comments of a site.</p>

<p>To use the moderation features, you need to login to the comments
system. Furthermore you will need to allow cookies to be set for
comments.apache.org (this is done using a secure https cookie). Once
logged in as a moderator you will see additional moderation
options attached to each comment.</p>

<p>If you are a long time follower of the Apache Tomcat projects
and you are interested in becoming a moderator, please contact us on the
<a href="https://tomcat.apache.org/lists.html#tomcat-users">users mailing list</a>.</p>

</div><h3 id="Privacy_Policy">Privacy Policy</h3><div class="text">

<p>No data except what you personally submit is kept on record.
A cookie is used to keep track of moderators and other people
who wish to create an account to avoid having to enter their
credentials whenever they wish to post a comment.</p>

<p>To prevent spam and unsolicited comments, we use a digest of
visitors' IPs to keep track of comments posted by them.</p>

<p>Entering an email address when you post a comment is completely
optional, and will not be shared with anyone. If you enter an
email address, it will be used to notify you when someone posts
a reply to one of your comments, provided you have registered
an account and validated your email address.</p>

</div></div></div></div></div><footer><div id="footer">
    Copyright &copy; 1999-2025, The Apache Software Foundation
    <br>
    Apache Tomcat, Tomcat, Apache, the Apache Tomcat logo and the Apache logo
    are either registered trademarks or trademarks of the Apache Software
    Foundation.
    </div></footer></div></body></html>