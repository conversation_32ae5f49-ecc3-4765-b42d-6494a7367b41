<!DOCTYPE html SYSTEM "about:legacy-compat">
<html lang="en"><head><META http-equiv="Content-Type" content="text/html; charset=UTF-8"><link href="./images/docs-stylesheet.css" rel="stylesheet" type="text/css"><title>Apache Tomcat 10 (10.1.41) - Windows Authentication How-To</title></head><body><div id="wrapper"><header><div id="header"><div><div><div class="logo noPrint"><a href="https://tomcat.apache.org/"><img alt="Tomcat Home" src="./images/tomcat.png"></a></div><div style="height: 1px;"></div><div class="asfLogo noPrint"><a href="https://www.apache.org/" target="_blank"><img src="./images/asf-logo.svg" alt="The Apache Software Foundation" style="width: 266px; height: 83px;"></a></div><h1>Apache Tomcat 10</h1><div class="versionInfo">
            Version 10.1.41,
            <time datetime="2025-05-08">May 8 2025</time></div><div style="height: 1px;"></div><div style="clear: left;"></div></div></div></div></header><div id="middle"><div><div id="mainLeft" class="noprint"><div><nav><div><h2>Links</h2><ul><li><a href="index.html">Docs Home</a></li><li><a href="https://cwiki.apache.org/confluence/display/TOMCAT/FAQ">FAQ</a></li></ul></div><div><h2>User Guide</h2><ul><li><a href="introduction.html">1) Introduction</a></li><li><a href="setup.html">2) Setup</a></li><li><a href="appdev/index.html">3) First webapp</a></li><li><a href="deployer-howto.html">4) Deployer</a></li><li><a href="manager-howto.html">5) Manager</a></li><li><a href="host-manager-howto.html">6) Host Manager</a></li><li><a href="realm-howto.html">7) Realms and AAA</a></li><li><a href="security-manager-howto.html">8) Security Manager</a></li><li><a href="jndi-resources-howto.html">9) JNDI Resources</a></li><li><a href="jndi-datasource-examples-howto.html">10) JDBC DataSources</a></li><li><a href="class-loader-howto.html">11) Classloading</a></li><li><a href="jasper-howto.html">12) JSPs</a></li><li><a href="ssl-howto.html">13) SSL/TLS</a></li><li><a href="ssi-howto.html">14) SSI</a></li><li><a href="cgi-howto.html">15) CGI</a></li><li><a href="proxy-howto.html">16) Proxy Support</a></li><li><a href="mbeans-descriptors-howto.html">17) MBeans Descriptors</a></li><li><a href="default-servlet.html">18) Default Servlet</a></li><li><a href="cluster-howto.html">19) Clustering</a></li><li><a href="balancer-howto.html">20) Load Balancer</a></li><li><a href="connectors.html">21) Connectors</a></li><li><a href="monitoring.html">22) Monitoring and Management</a></li><li><a href="logging.html">23) Logging</a></li><li><a href="apr.html">24) APR/Native</a></li><li><a href="virtual-hosting-howto.html">25) Virtual Hosting</a></li><li><a href="aio.html">26) Advanced IO</a></li><li><a href="maven-jars.html">27) Mavenized</a></li><li><a href="security-howto.html">28) Security Considerations</a></li><li><a href="windows-service-howto.html">29) Windows Service</a></li><li><a href="windows-auth-howto.html">30) Windows Authentication</a></li><li><a href="jdbc-pool.html">31) Tomcat's JDBC Pool</a></li><li><a href="web-socket-howto.html">32) WebSocket</a></li><li><a href="rewrite.html">33) Rewrite</a></li><li><a href="cdi.html">34) CDI 2 and JAX-RS</a></li><li><a href="graal.html">35) AOT/GraalVM Support</a></li></ul></div><div><h2>Reference</h2><ul><li><a href="RELEASE-NOTES.txt">Release Notes</a></li><li><a href="config/index.html">Configuration</a></li><li><a href="api/index.html">Tomcat Javadocs</a></li><li><a href="servletapi/index.html">Servlet 6.0 Javadocs</a></li><li><a href="jspapi/index.html">JSP 3.1 Javadocs</a></li><li><a href="elapi/index.html">EL 5.0 Javadocs</a></li><li><a href="websocketapi/index.html">WebSocket 2.1 Javadocs</a></li><li><a href="jaspicapi/index.html">Authentication 3.0 Javadocs</a></li><li><a href="annotationapi/index.html">Annotations 2.1 Javadocs</a></li><li><a href="https://tomcat.apache.org/connectors-doc/">JK 1.2 Documentation</a></li></ul></div><div><h2>Apache Tomcat Development</h2><ul><li><a href="building.html">Building</a></li><li><a href="changelog.html">Changelog</a></li><li><a href="https://cwiki.apache.org/confluence/display/TOMCAT/Tomcat+Versions">Status</a></li><li><a href="developers.html">Developers</a></li><li><a href="architecture/index.html">Architecture</a></li><li><a href="tribes/introduction.html">Tribes</a></li></ul></div></nav></div></div><div id="mainRight"><div id="content"><h2>Windows Authentication How-To</h2><h3 id="Table_of_Contents">Table of Contents</h3><div class="text">
<ul><li><a href="#Overview">Overview</a></li><li><a href="#Built-in_Tomcat_support">Built-in Tomcat support</a><ol><li><a href="#Domain_Controller">Domain Controller</a></li><li><a href="#Tomcat_instance_(Windows_server)">Tomcat instance (Windows server)</a></li><li><a href="#Tomcat_instance_(Linux_server)">Tomcat instance (Linux server)</a></li><li><a href="#Web_application">Web application</a></li><li><a href="#Client">Client</a></li><li><a href="#References">References</a></li></ol></li><li><a href="#Third_party_libraries">Third party libraries</a><ol><li><a href="#Waffle">Waffle</a></li><li><a href="#Spring_Security_-_Kerberos_Extension">Spring Security - Kerberos Extension</a></li><li><a href="#Jespa">Jespa</a></li><li><a href="#SPNEGO_AD_project_at_SourceForge">SPNEGO AD project at SourceForge</a></li></ol></li><li><a href="#Reverse_proxies">Reverse proxies</a><ol><li><a href="#Microsoft_IIS">Microsoft IIS</a></li><li><a href="#Apache_httpd">Apache httpd</a></li></ol></li></ul>
</div><h3 id="Overview">Overview</h3><div class="text">
<p>Integrated Windows authentication is most frequently used within intranet
environments since it requires that the server performing the authentication and
the user being authenticated are part of the same domain. For the user to be
authenticated automatically, the client machine used by the user must also be
part of the domain.</p>
<p>There are several options for implementing integrated Windows authentication
with Apache Tomcat. They are:</p>
<ul>
<li>Built-in Tomcat support.</li>
<li>Use a third party library such as Waffle.</li>
<li>Use a reverse proxy that supports Windows authentication to perform the
authentication step such as IIS or httpd.</li>
</ul>
<p>The configuration of each of these options is discussed in the following
sections.</p>
</div><h3 id="Built-in_Tomcat_support">Built-in Tomcat support</h3><div class="text">
<p>Kerberos (the basis for integrated Windows authentication) requires careful
configuration. If the steps in this guide are followed exactly, then a working
configuration will result. It is important that the steps below are followed
exactly. There is very little scope for flexibility in the configuration. From
the testing to date it is known that:</p>
<ul>
<li>The host name used to access the Tomcat server must match the host name in
the SPN exactly else authentication will fail. A checksum error may be reported
in the debug logs in this case.</li>
<li>The client must be of the view that the server is part of the local trusted
intranet.</li>
<li>The SPN must be HTTP/&lt;hostname&gt; and it must be exactly the same in all
the places it is used.</li>
<li>The port number must not be included in the SPN.</li>
<li>No more than one SPN may be mapped to a domain user.</li>
<li>Tomcat must run as the domain account with which the SPN has been associated
or as domain admin. It is <strong>NOT</strong> recommended to run Tomcat under a
domain admin user.</li>
<li>Convention is that the domain name (<code>dev.local</code>) is always used in
lower case. The domain name is typically not case sensitive.</li>
<li>Convention is that the Kerberos realm name (<code>DEV.LOCAL</code>) is always
used in upper case. The realm name <strong>is</strong> case sensitive.</li>
<li>The domain must be specified when using the ktpass command.</li>
</ul>
<p>There are four components to the configuration of the built-in Tomcat
support for Windows authentication. The domain controller, the server hosting
Tomcat, the web application wishing to use Windows authentication and the client
machine. The following sections describe the configuration required for each
component.</p>
<p>The names of the three machines used in the configuration examples below are
win-dc01.dev.local (the domain controller), win-tc01.dev.local (the Tomcat
instance) and win-pc01.dev.local (client). All are members of the
<code>dev.local</code> domain.</p>
<p>Note: In order to use the passwords in the steps below, the domain password
policy had to be relaxed. This is not recommended for production environments.
</p>

  <div class="subsection"><h4 id="Domain_Controller">Domain Controller</h4><div class="text">
  <p>These steps assume that the server has already been configured to act as a
  domain controller. Configuration of a Windows server as a domain controller is
  outside the scope of this how-to. The steps to configure the domain controller
  to enable Tomcat to support Windows authentication are as follows:
  </p>
  <ul>
  <li>Create a domain user that will be mapped to the service name used by the
  Tomcat server. In this how-to, this user is called <code>tc01</code> and has a
  password of <code>tc01pass</code>.</li>
  <li>Map the service principal name (SPN) to the user account. SPNs take the
  form <code>
  &lt;service class&gt;/&lt;host&gt;:&lt;port&gt;/&lt;service name&gt;</code>.
  The SPN used in this how-to is <code>HTTP/win-tc01.dev.local</code>. To
  map the user to the SPN, run the following:
  <div class="codeBox"><pre><code>setspn -A HTTP/win-tc01.dev.local tc01</code></pre></div>
  </li>
  <li>Generate the keytab file that the Tomcat server will use to authenticate
  itself to the domain controller. This file contains the Tomcat private key for
  the service provider account and should be protected accordingly. To generate
  the file, run the following command (all on a single line):
  <div class="codeBox"><pre><code>ktpass /out c:\tomcat.keytab /mapuser <EMAIL>
          /princ HTTP/<EMAIL>
          /pass tc01pass /kvno 0</code></pre></div></li>
  <li>Create a domain user to be used on the client. In this how-to the domain
  user is <code>test</code> with a password of <code>testpass</code>.</li>
  </ul>
  <p>The above steps have been tested on a domain controller running Windows
  Server 2019 Standard using the Windows Server 2016 functional level
  for both the forest and the domain.
  </p>
  </div></div>

  <div class="subsection"><h4 id="Tomcat_instance_(Windows_server)">Tomcat instance (Windows server)</h4><div class="text">
  <p>These steps assume that Tomcat and an appropriate Java JDK/JRE have already
  been installed and configured and that Tomcat is running <NAME_EMAIL>
  user. The steps to configure the Tomcat instance for Windows authentication
  are as follows:
  </p>
  <ul>
  <li>Copy the <code>tomcat.keytab</code> file created on the domain controller
  to <code>$CATALINA_BASE/conf/tomcat.keytab</code>.</li>
  <li>Create the kerberos configuration file
  <code>$CATALINA_BASE/conf/krb5.ini</code>. The file used in this how-to
  contained:<div class="codeBox"><pre><code>[libdefaults]
default_realm = DEV.LOCAL
default_keytab_name = FILE:c:\apache-tomcat-10.1.x\conf\tomcat.keytab
default_tkt_enctypes = rc4-hmac,aes256-cts-hmac-sha1-96,aes128-cts-hmac-sha1-96
default_tgs_enctypes = rc4-hmac,aes256-cts-hmac-sha1-96,aes128-cts-hmac-sha1-96
forwardable=true

[realms]
DEV.LOCAL = {
        kdc = win-dc01.dev.local:88
}

[domain_realm]
dev.local= DEV.LOCAL
.dev.local= DEV.LOCAL</code></pre></div>
  The location of this file can be changed by setting the
  <code>java.security.krb5.conf</code> system property.</li>
  <li>Create the JAAS login configuration file
  <code>$CATALINA_BASE/conf/jaas.conf</code>. The file used in this how-to
  contained:<div class="codeBox"><pre><code>com.sun.security.jgss.krb5.initiate {
    com.sun.security.auth.module.Krb5LoginModule required
    doNotPrompt=true
    principal="HTTP/<EMAIL>"
    useKeyTab=true
    keyTab="c:/apache-tomcat-10.1.x/conf/tomcat.keytab"
    storeKey=true;
};

com.sun.security.jgss.krb5.accept {
    com.sun.security.auth.module.Krb5LoginModule required
    doNotPrompt=true
    principal="HTTP/<EMAIL>"
    useKeyTab=true
    keyTab="c:/apache-tomcat-10.1.x/conf/tomcat.keytab"
    storeKey=true;
};</code></pre></div>
  The location of this file can be changed by setting the
  <code>java.security.auth.login.config</code> system property. The LoginModule
  used is a JVM specific one so ensure that the LoginModule specified matches
  the JVM being used. The name of the login configuration must match the
  value used by the <a href="config/valve.html#SPNEGO_Valve">authentication
  valve</a>.</li>
  </ul>
  <p>The SPNEGO authenticator will work with any <a href="config/realm.html">
  Realm</a> but if used with the JNDI Realm, by default the JNDI Realm will use
  the user's delegated credentials to connect to the Active Directory. If
  only the authenticated user name is required then the AuthenticatedUserRealm
  may be used that will simply return a Principal based on the authenticated
  user name that does not have any roles.</p>
  <p>The above steps have been tested on a Tomcat server running Windows Server
  2019 Standard with AdoptOpenJDK 8u232-b09 (64-bit).</p>
  </div></div>

  <div class="subsection"><h4 id="Tomcat_instance_(Linux_server)">Tomcat instance (Linux server)</h4><div class="text">
  <p>This was tested with:</p>
  <ul>
  <li>Java 1.7.0, update 45, 64-bit</li>
  <li>Ubuntu Server 12.04.3 LTS 64-bit</li>
  <li>Tomcat 8.0.x (r1546570)</li>
  </ul>
  <p>It should work with any Tomcat release although it is recommended that
  the latest stable release is used.</p>
  <p>The configuration is the same as for Windows but with the following
  changes:</p>
  <ul>
  <li>The Linux server does not have to be part of the Windows domain.</li>
  <li>The path to the keytab file in krb5.ini and jaas.conf should be updated
      to reflect the path to the keytab file on the Linux server using Linux
      style file paths (e.g. /usr/local/tomcat/...).</li>
  </ul>
  </div></div>

  <div class="subsection"><h4 id="Web_application">Web application</h4><div class="text">
  <p>The web application needs to be configured to the use Tomcat specific
  authentication method of <code>SPNEGO</code> (rather than BASIC etc.) in
  web.xml. As with the other authenticators, behaviour can be customised by
  explicitly configuring the <a href="config/valve.html#SPNEGO_Valve">
  authentication valve</a> and setting attributes on the Valve.</p>
  </div></div>

  <div class="subsection"><h4 id="Client">Client</h4><div class="text">
  <p>The client must be configured to use Kerberos authentication. For Internet
  Explorer this means making sure that the Tomcat instance is in the "Local
  intranet" security domain and that it is configured (Tools &gt; Internet
  Options &gt; Advanced) with integrated Windows authentication enabled. Note that
  this <strong>will not</strong> work if you use the same machine for the client
  and the Tomcat instance as Internet Explorer will use the unsupported NTLM
  protocol.</p>
  </div></div>

  <div class="subsection"><h4 id="References">References</h4><div class="text">
  <p>Correctly configuring Kerberos authentication can be tricky. The following
  references may prove helpful. Advice is also always available from the
  <a href="https://tomcat.apache.org/lists.html#tomcat-users">Tomcat users
  mailing list</a>.</p>
  <ol>
  <li><a href="http://www.adopenstatic.com/cs/blogs/ken/archive/2006/10/19/512.aspx">
      IIS and Kerberos</a></li>
  <li><a href="http://spnego.sourceforge.net/index.html">
      SPNEGO project at SourceForge</a></li>
  <li><a href="http://docs.oracle.com/javase/7/docs/technotes/guides/security/jgss/tutorials/index.html">
      Oracle Java GSS-API tutorial (Java 7)</a></li>
  <li><a href="http://docs.oracle.com/javase/7/docs/technotes/guides/security/jgss/tutorials/Troubleshooting.html">
      Oracle Java GSS-API tutorial - Troubleshooting (Java 7)</a></li>
  <li><a href="https://cwiki.apache.org/confluence/display/GMOxDOC21/Using+SPNEGO+in+Geronimo#UsingSPNEGOinGeronimo-SettinguptheDomainControllerMachine">
      Geronimo configuration for Windows authentication</a></li>
  <li><a href="http://blogs.msdn.com/b/openspecification/archive/2010/11/17/encryption-type-selection-in-kerberos-exchanges.aspx">
      Encryption Selection in Kerberos Exchanges</a></li>
  <li><a href="http://support.microsoft.com/kb/977321">Supported Kerberos Cipher
      Suites</a></li>
  </ol>
  </div></div>

</div><h3 id="Third_party_libraries">Third party libraries</h3><div class="text">

  <div class="subsection"><h4 id="Waffle">Waffle</h4><div class="text">
  <p>Full details of this solution can be found through the
  <a href="http://waffle.codeplex.com/" rel="nofollow">Waffle web site</a>. The
  key features are:</p>
  <ul>
  <li>Drop-in solution</li>
  <li>Simple configuration (no JAAS or Kerberos keytab configuration required)
  </li>
  <li>Uses a native library</li>
  </ul>
  </div></div>

  <div class="subsection"><h4 id="Spring_Security_-_Kerberos_Extension">Spring Security - Kerberos Extension</h4><div class="text">
  <p>Full details of this solution can be found through the
  <a href="http://static.springsource.org/spring-security/site/extensions/krb/index.html" rel="nofollow"> Kerberos extension web site</a>. The key features are:</p>
  <ul>
  <li>Extension to Spring Security</li>
  <li>Requires a Kerberos keytab file to be generated</li>
  <li>Pure Java solution</li>
  </ul>
  </div></div>

  <div class="subsection"><h4 id="Jespa">Jespa</h4><div class="text">
  <p>Full details of this solution can be found through the
  <a href="http://www.ioplex.com/" rel="nofollow">project web site.</a> The key
  features are:</p>
  <ul>
  <li>Pure Java solution</li>
  <li>Advanced Active Directory integration</li>
  </ul>
  </div></div>

  <div class="subsection"><h4 id="SPNEGO_AD_project_at_SourceForge">SPNEGO AD project at SourceForge</h4><div class="text">
  <p>Full details of this solution can be found through the
  <a href="http://tomcatspnegoad.sourceforge.net/" rel="nofollow">project
  site</a>. The key features are:</p>
  <ul>
  <li>Pure Java solution</li>
  <li>SPNEGO/Kerberos Authenticator</li>
  <li>Active Directory Realm</li>
  </ul>
  </div></div>
</div><h3 id="Reverse_proxies">Reverse proxies</h3><div class="text">

  <div class="subsection"><h4 id="Microsoft_IIS">Microsoft IIS</h4><div class="text">
  <p>There are three steps to configuring IIS to provide Windows authentication.
  They are:</p>
  <ol>
  <li>Configure IIS as a reverse proxy for Tomcat (see the
  <a href="https://tomcat.apache.org/connectors-doc/webserver_howto/iis.html">
  IIS Web Server How-To)</a>.</li>
  <li>Configure IIS to use Windows authentication</li>
  <li>Configure Tomcat to use the authentication user information from IIS by
  setting the tomcatAuthentication attribute on the <a href="config/ajp.html">
  AJP connector</a> to <code>false</code>. Alternatively, set the
  tomcatAuthorization attribute to <code>true</code> to allow IIS to
  authenticate, while Tomcat performs the authorization.</li>
  </ol>
  </div></div>

  <div class="subsection"><h4 id="Apache_httpd">Apache httpd</h4><div class="text">
  <p>Apache httpd does not support Windows authentication out of the box but
  there are a number of third-party modules that can be used. These include:</p>
  <ol>
  <li><a href="http://sourceforge.net/projects/mod-auth-sspi/" rel="nofollow">mod_auth_sspi</a> for use on Windows platforms.</li>
  <li><a href="http://adldap.sourceforge.net/wiki/doku.php?id=mod_auth_ntlm_winbind" rel="nofollow">mod_auth_ntlm_winbind</a> for non-Windows platforms. Known to
  work with httpd 2.0.x on 32-bit platforms. Some users have reported stability
  issues with both httpd 2.2.x builds and 64-bit Linux builds.</li>
  </ol>
  <p>There are three steps to configuring httpd to provide Windows
  authentication. They are:</p>
  <ol>
  <li>Configure httpd as a reverse proxy for Tomcat (see the
  <a href="https://tomcat.apache.org/connectors-doc/webserver_howto/apache.html">
  Apache httpd Web Server How-To)</a>.</li>
  <li>Configure httpd to use Windows authentication</li>
  <li>Configure Tomcat to use the authentication user information from httpd by
  setting the tomcatAuthentication attribute on the <a href="config/ajp.html">
  AJP connector</a> to <code>false</code>.</li>
  </ol>
  </div></div>

</div></div></div></div></div><footer><div id="footer">
    Copyright &copy; 1999-2025, The Apache Software Foundation
    <br>
    Apache Tomcat, Tomcat, Apache, the Apache Tomcat logo and the Apache logo
    are either registered trademarks or trademarks of the Apache Software
    Foundation.
    </div></footer></div></body></html>