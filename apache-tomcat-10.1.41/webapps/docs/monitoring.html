<!DOCTYPE html SYSTEM "about:legacy-compat">
<html lang="en"><head><META http-equiv="Content-Type" content="text/html; charset=UTF-8"><link href="./images/docs-stylesheet.css" rel="stylesheet" type="text/css"><title>Apache Tomcat 10 (10.1.41) - Monitoring and Managing Tomcat</title></head><body><div id="wrapper"><header><div id="header"><div><div><div class="logo noPrint"><a href="https://tomcat.apache.org/"><img alt="Tomcat Home" src="./images/tomcat.png"></a></div><div style="height: 1px;"></div><div class="asfLogo noPrint"><a href="https://www.apache.org/" target="_blank"><img src="./images/asf-logo.svg" alt="The Apache Software Foundation" style="width: 266px; height: 83px;"></a></div><h1>Apache Tomcat 10</h1><div class="versionInfo">
            Version 10.1.41,
            <time datetime="2025-05-08">May 8 2025</time></div><div style="height: 1px;"></div><div style="clear: left;"></div></div></div></div></header><div id="middle"><div><div id="mainLeft" class="noprint"><div><nav><div><h2>Links</h2><ul><li><a href="index.html">Docs Home</a></li><li><a href="https://cwiki.apache.org/confluence/display/TOMCAT/FAQ">FAQ</a></li></ul></div><div><h2>User Guide</h2><ul><li><a href="introduction.html">1) Introduction</a></li><li><a href="setup.html">2) Setup</a></li><li><a href="appdev/index.html">3) First webapp</a></li><li><a href="deployer-howto.html">4) Deployer</a></li><li><a href="manager-howto.html">5) Manager</a></li><li><a href="host-manager-howto.html">6) Host Manager</a></li><li><a href="realm-howto.html">7) Realms and AAA</a></li><li><a href="security-manager-howto.html">8) Security Manager</a></li><li><a href="jndi-resources-howto.html">9) JNDI Resources</a></li><li><a href="jndi-datasource-examples-howto.html">10) JDBC DataSources</a></li><li><a href="class-loader-howto.html">11) Classloading</a></li><li><a href="jasper-howto.html">12) JSPs</a></li><li><a href="ssl-howto.html">13) SSL/TLS</a></li><li><a href="ssi-howto.html">14) SSI</a></li><li><a href="cgi-howto.html">15) CGI</a></li><li><a href="proxy-howto.html">16) Proxy Support</a></li><li><a href="mbeans-descriptors-howto.html">17) MBeans Descriptors</a></li><li><a href="default-servlet.html">18) Default Servlet</a></li><li><a href="cluster-howto.html">19) Clustering</a></li><li><a href="balancer-howto.html">20) Load Balancer</a></li><li><a href="connectors.html">21) Connectors</a></li><li><a href="monitoring.html">22) Monitoring and Management</a></li><li><a href="logging.html">23) Logging</a></li><li><a href="apr.html">24) APR/Native</a></li><li><a href="virtual-hosting-howto.html">25) Virtual Hosting</a></li><li><a href="aio.html">26) Advanced IO</a></li><li><a href="maven-jars.html">27) Mavenized</a></li><li><a href="security-howto.html">28) Security Considerations</a></li><li><a href="windows-service-howto.html">29) Windows Service</a></li><li><a href="windows-auth-howto.html">30) Windows Authentication</a></li><li><a href="jdbc-pool.html">31) Tomcat's JDBC Pool</a></li><li><a href="web-socket-howto.html">32) WebSocket</a></li><li><a href="rewrite.html">33) Rewrite</a></li><li><a href="cdi.html">34) CDI 2 and JAX-RS</a></li><li><a href="graal.html">35) AOT/GraalVM Support</a></li></ul></div><div><h2>Reference</h2><ul><li><a href="RELEASE-NOTES.txt">Release Notes</a></li><li><a href="config/index.html">Configuration</a></li><li><a href="api/index.html">Tomcat Javadocs</a></li><li><a href="servletapi/index.html">Servlet 6.0 Javadocs</a></li><li><a href="jspapi/index.html">JSP 3.1 Javadocs</a></li><li><a href="elapi/index.html">EL 5.0 Javadocs</a></li><li><a href="websocketapi/index.html">WebSocket 2.1 Javadocs</a></li><li><a href="jaspicapi/index.html">Authentication 3.0 Javadocs</a></li><li><a href="annotationapi/index.html">Annotations 2.1 Javadocs</a></li><li><a href="https://tomcat.apache.org/connectors-doc/">JK 1.2 Documentation</a></li></ul></div><div><h2>Apache Tomcat Development</h2><ul><li><a href="building.html">Building</a></li><li><a href="changelog.html">Changelog</a></li><li><a href="https://cwiki.apache.org/confluence/display/TOMCAT/Tomcat+Versions">Status</a></li><li><a href="developers.html">Developers</a></li><li><a href="architecture/index.html">Architecture</a></li><li><a href="tribes/introduction.html">Tribes</a></li></ul></div></nav></div></div><div id="mainRight"><div id="content"><h2>Monitoring and Managing Tomcat</h2><h3 id="Table_of_Contents">Table of Contents</h3><div class="text">
<ul><li><a href="#Introduction">Introduction</a></li><li><a href="#Enabling_JMX_Remote">Enabling JMX Remote</a></li><li><a href="#Manage_Tomcat_with_JMX_remote_Ant_Tasks">Manage Tomcat with JMX remote Ant Tasks</a></li><li><a href="#JMXAccessorOpenTask_-_JMX_open_connection_task">JMXAccessorOpenTask - JMX open connection task</a></li><li><a href="#JMXAccessorGetTask:__get_attribute_value_Ant_task">JMXAccessorGetTask:  get attribute value Ant task</a></li><li><a href="#JMXAccessorSetTask:__set_attribute_value_Ant_task">JMXAccessorSetTask:  set attribute value Ant task</a></li><li><a href="#JMXAccessorInvokeTask:__invoke_MBean_operation_Ant_task">JMXAccessorInvokeTask:  invoke MBean operation Ant task</a></li><li><a href="#JMXAccessorQueryTask:__query_MBean_Ant_task">JMXAccessorQueryTask:  query MBean Ant task</a></li><li><a href="#JMXAccessorCreateTask:__remote_create_MBean_Ant_task">JMXAccessorCreateTask:  remote create MBean Ant task</a></li><li><a href="#JMXAccessorUnregisterTask:__remote_unregister_MBean_Ant_task">JMXAccessorUnregisterTask:  remote unregister MBean Ant task</a></li><li><a href="#JMXAccessorCondition:__express_condition">JMXAccessorCondition:  express condition</a></li><li><a href="#JMXAccessorEqualsCondition:__equals_MBean_Ant_condition">JMXAccessorEqualsCondition:  equals MBean Ant condition</a></li><li><a href="#Using_the_JMXProxyServlet">Using the JMXProxyServlet</a></li></ul>
</div><h3 id="Introduction">Introduction</h3><div class="text">

  <p>Monitoring is a key aspect of system administration. Looking inside a
     running server, obtaining some statistics or reconfiguring some aspects of
     an application are all daily administration tasks.</p>

  </div><h3 id="Enabling_JMX_Remote">Enabling JMX Remote</h3><div class="text">

    <p><strong>Note:</strong> This configuration is needed only if you are
    going to monitor Tomcat remotely. It is not needed if you are going
    to monitor it locally, using the same user that Tomcat runs with.</p>

    <p>The Oracle website includes the list of options and how to configure
    JMX Remote on Java 11:
        <a href="https://docs.oracle.com/en/java/javase/11/management/monitoring-and-management-using-jmx-technology.html">
        http://docs.oracle.com/javase/6/docs/technotes/guides/management/agent.html</a>.
    </p>
    <p>The following is a quick configuration guide for Java 11:</p>
    <p>Add the following parameters to <code>setenv.bat</code> script of your
    Tomcat (see <a href="RUNNING.txt">RUNNING.txt</a> for details).<br>
    <em>Note:</em> This syntax is for Microsoft Windows. The command has
    to be on the same line. It is wrapped to be more readable. If Tomcat is
    running as a Windows service, use its configuration dialog to set
    java options for the service.
    For Linux, MacOS, etc, remove <code>"set "</code> from beginning of the
    line.
    </p>
<div class="codeBox"><pre><code>set CATALINA_OPTS=-Dcom.sun.management.jmxremote.port=%my.jmx.port%
  -Dcom.sun.management.jmxremote.rmi.port=%my.rmi.port%
  -Dcom.sun.management.jmxremote.ssl=false
  -Dcom.sun.management.jmxremote.authenticate=false</code></pre></div>
<p>If you don't set <code>com.sun.management.jmxremote.rmi.port</code> then the
JSR 160 JMX-Adaptor will select a port at random which will may it difficult to
configure a firewall to allow access.</p>

<p>If you require TLS:</p>
    <ol>
    <li>change and add this:
<div class="codeBox"><pre><code>  -Dcom.sun.management.jmxremote.ssl=true
  -Dcom.sun.management.jmxremote.registry.ssl=true
</code></pre></div></li>
    <li>to configure the protocols and/or cipher suites use:
<div class="codeBox"><pre><code>  -Dcom.sun.management.jmxremote.ssl.enabled.protocols=%my.jmx.ssl.protocols%
  -Dcom.sun.management.jmxremote.ssl.enabled.cipher.suites=%my.jmx.cipher.suites%
</code></pre></div></li>
    <li>to client certificate authentication use:
<div class="codeBox"><pre><code>  -Dcom.sun.management.jmxremote.ssl.need.client.auth=%my.jmx.ssl.clientauth%</code></pre></div></li>
    </ol>
<p>If you require authorization (it is strongly recommended that TLS is always
used with authentication):</p>
    <ol>
    <li>change and add this:
<div class="codeBox"><pre><code>  -Dcom.sun.management.jmxremote.authenticate=true
  -Dcom.sun.management.jmxremote.password.file=../conf/jmxremote.password
  -Dcom.sun.management.jmxremote.access.file=../conf/jmxremote.access</code></pre></div>
    </li>
    <li>edit the access authorization file <em>$CATALINA_BASE/conf/jmxremote.access</em>:
<div class="codeBox"><pre><code>monitorRole readonly
controlRole readwrite</code></pre></div>
    </li>
    <li>edit the password file <em>$CATALINA_BASE/conf/jmxremote.password</em>:
<div class="codeBox"><pre><code>monitorRole tomcat
controlRole tomcat</code></pre></div>
    <b>Tip</b>: The password file should be read-only and only accessible by the
    operating system user Tomcat is running as.
    </li>
    <li>Alternatively, you can configure a JAAS login module with:
<div class="codeBox"><pre><code>  -Dcom.sun.management.jmxremote.login.config=%login.module.name%</code></pre></div></li>
    </ol>

<p>If you need to specify a host name to be used in the RMI stubs sent to the
client (e.g. because the public host name that must be used to connect is not
the same as the local host name) then you can set:</p>
<div class="codeBox"><pre><code>set CATALINA_OPTS=-Djava.rmi.server.hostname</code></pre></div>

<p>If you need to specify a specific interface for the JMX service to bind to
then you can set:</p>
<div class="codeBox"><pre><code>set CATALINA_OPTS=-Dcom.sun.management.jmxremote.host</code></pre></div>

  </div><h3 id="Manage_Tomcat_with_JMX_remote_Ant_Tasks">Manage Tomcat with JMX remote Ant Tasks</h3><div class="text">
   <p>To simplify JMX usage with Ant, a set of tasks is provided that may
   be used with antlib.</p>
   <p><b>antlib</b>: Copy your catalina-ant.jar from $CATALINA_HOME/lib to $ANT_HOME/lib.</p>
   <p>The following example shows the JMX Accessor usage:<br>
   <em>Note:</em> The <code>name</code> attribute value was wrapped here to be
   more readable. It has to be all on the same line, without spaces.</p>
   <div class="codeBox"><pre><code>&lt;project name="Catalina Ant JMX"
      xmlns:jmx="antlib:org.apache.catalina.ant.jmx"
      default="state"
      basedir="."&gt;
  &lt;property name="jmx.server.name" value="localhost" /&gt;
  &lt;property name="jmx.server.port" value="9012" /&gt;
  &lt;property name="cluster.server.address" value="************" /&gt;
  &lt;property name="cluster.server.port" value="9025" /&gt;

  &lt;target name="state" description="Show JMX Cluster state"&gt;
    &lt;jmx:open
      host="${jmx.server.name}"
      port="${jmx.server.port}"
      username="controlRole"
      password="tomcat"/&gt;
    &lt;jmx:get
      name=
"Catalina:type=IDataSender,host=localhost,
senderAddress=${cluster.server.address},senderPort=${cluster.server.port}"
      attribute="connected"
      resultproperty="IDataSender.backup.connected"
      echo="false"
    /&gt;
    &lt;jmx:get
      name="Catalina:type=ClusterSender,host=localhost"
      attribute="senderObjectNames"
      resultproperty="senderObjectNames"
      echo="false"
    /&gt;
    &lt;!-- get current maxActiveSession from ClusterTest application
       echo it to Ant output and store at
       property &lt;em&gt;clustertest.maxActiveSessions.original&lt;/em&gt;
    --&gt;
    &lt;jmx:get
      name="Catalina:type=Manager,context=/ClusterTest,host=localhost"
      attribute="maxActiveSessions"
      resultproperty="clustertest.maxActiveSessions.original"
      echo="true"
    /&gt;
    &lt;!-- set maxActiveSession to 100
    --&gt;
    &lt;jmx:set
      name="Catalina:type=Manager,context=/ClusterTest,host=localhost"
      attribute="maxActiveSessions"
      value="100"
      type="int"
    /&gt;
    &lt;!-- get all sessions and split result as delimiter &lt;em&gt;SPACE&lt;/em&gt; for easy
       access all session ids directly with Ant property sessions.[0..n].
    --&gt;
    &lt;jmx:invoke
      name="Catalina:type=Manager,context=/ClusterTest,host=localhost"
      operation="listSessionIds"
      resultproperty="sessions"
      echo="false"
      delimiter=" "
    /&gt;
    &lt;!-- Access session attribute &lt;em&gt;Hello&lt;/em&gt; from first session.
    --&gt;
    &lt;jmx:invoke
      name="Catalina:type=Manager,context=/ClusterTest,host=localhost"
      operation="getSessionAttribute"
      resultproperty="Hello"
      echo="false"
    &gt;
      &lt;arg value="${sessions.0}"/&gt;
      &lt;arg value="Hello"/&gt;
    &lt;/jmx:invoke&gt;
    &lt;!-- Query for all application manager.of the server from all hosts
       and bind all attributes from all found manager MBeans.
    --&gt;
    &lt;jmx:query
      name="Catalina:type=Manager,*"
      resultproperty="manager"
      echo="true"
      attributebinding="true"
    /&gt;
    &lt;!-- echo the create properties --&gt;
&lt;echo&gt;
senderObjectNames: ${senderObjectNames.0}
IDataSender.backup.connected: ${IDataSender.backup.connected}
session: ${sessions.0}
manager.length: ${manager.length}
manager.0.name: ${manager.0.name}
manager.1.name: ${manager.1.name}
hello: ${Hello}
manager.ClusterTest.0.name: ${manager.ClusterTest.0.name}
manager.ClusterTest.0.activeSessions: ${manager.ClusterTest.0.activeSessions}
manager.ClusterTest.0.counterSend_EVT_SESSION_EXPIRED:
 ${manager.ClusterTest.0.counterSend_EVT_SESSION_EXPIRED}
manager.ClusterTest.0.counterSend_EVT_GET_ALL_SESSIONS:
 ${manager.ClusterTest.0.counterSend_EVT_GET_ALL_SESSIONS}
&lt;/echo&gt;

  &lt;/target&gt;

&lt;/project&gt;</code></pre></div>
   <p><b>import:</b> Import the JMX Accessor Project with
   <em>&lt;import file="${CATALINA.HOME}/bin/catalina-tasks.xml" /&gt;</em> and
   reference the tasks with <em>jmxOpen</em>, <em>jmxSet</em>, <em>jmxGet</em>,
   <em>jmxQuery</em>, <em>jmxInvoke</em>, <em>jmxEquals</em> and <em>jmxCondition</em>.</p>

  </div><h3 id="JMXAccessorOpenTask_-_JMX_open_connection_task">JMXAccessorOpenTask - JMX open connection task</h3><div class="text">
<p>
List of Attributes
</p>
<table class="defaultTable">

  <tr>
    <th>Attribute</th>
    <th>Description</th>
    <th>Default value</th>
  </tr>

  <tr>
    <td>url</td>
    <td>Set JMX connection URL - <em>service:jmx:rmi:///jndi/rmi://localhost:8050/jmxrmi</em>
    </td>
    <td></td>
  </tr>

  <tr>
    <td>host</td>
    <td>Set the host, shortcut the very long URL syntax.
    </td>
    <td><code class="noHighlight">localhost</code></td>
  </tr>

  <tr>
    <td>port</td>
    <td>Set the remote connection port
    </td>
    <td><code class="noHighlight">8050</code></td>
  </tr>

  <tr>
    <td>username</td>
    <td>remote JMX connection user name.
    </td>
    <td></td>
  </tr>

  <tr>
    <td>password</td>
    <td>remote JMX connection password.
    </td>
    <td></td>
  </tr>

  <tr>
    <td>ref</td>
    <td>Name of the internal connection reference. With this attribute you can
        configure more the one connection inside the same Ant project.
    </td>
    <td><code class="noHighlight">jmx.server</code></td>
  </tr>

  <tr>
    <td>echo</td>
    <td>Echo the command usage (for access analysis or debugging)
    </td>
    <td><code class="noHighlight">false</code></td>
  </tr>

  <tr>
    <td>if</td>
    <td>Only execute if a property of the given name <b>exists</b> in the current project.
    </td>
    <td></td>
  </tr>

  <tr>
    <td>unless</td>
    <td>Only execute if a property of the given name <b>not exists</b> in the current project.
    </td>
    <td></td>
  </tr>

</table>

<p>
Example to open a new JMX connection
</p>
<div class="codeBox"><pre><code>  &lt;jmx:open
    host="${jmx.server.name}"
    port="${jmx.server.port}"
  /&gt;</code></pre></div>

<p>
Example to open a JMX connection from URL, with authorization and
store at other reference
</p>
<div class="codeBox"><pre><code>  &lt;jmx:open
    url="service:jmx:rmi:///jndi/rmi://localhost:9024/jmxrmi"
    ref="jmx.server.9024"
    username="controlRole"
    password="tomcat"
  /&gt;</code></pre></div>

<p>
Example to open a JMX connection from URL, with authorization and
store at other reference, but only when property <em>jmx.if</em> exists and
<em>jmx.unless</em> not exists
</p>
<div class="codeBox"><pre><code>  &lt;jmx:open
    url="service:jmx:rmi:///jndi/rmi://localhost:9024/jmxrmi"
    ref="jmx.server.9024"
    username="controlRole"
    password="tomcat"
    if="jmx.if"
    unless="jmx.unless"
  /&gt;</code></pre></div>

<p><b>Note</b>: All properties from <em>jmxOpen</em> task also exists at all
other tasks and conditions.
</p>

</div><h3 id="JMXAccessorGetTask:__get_attribute_value_Ant_task">JMXAccessorGetTask:  get attribute value Ant task</h3><div class="text">
<p>
List of Attributes
</p>
<table class="defaultTable">

  <tr>
    <th>Attribute</th>
    <th>Description</th>
    <th>Default value</th>
  </tr>

  <tr>
    <td>name</td>
    <td>Full qualified JMX ObjectName -- <em>Catalina:type=Server</em>
    </td>
    <td></td>
  </tr>

  <tr>
    <td>attribute</td>
    <td>Existing MBean attribute (see Tomcat MBean description above)
    </td>
    <td></td>
  </tr>

  <tr>
    <td>ref</td>
    <td>JMX Connection reference
    </td>
    <td><code class="noHighlight">jmx.server</code></td>
  </tr>

  <tr>
    <td>echo</td>
    <td>Echo command usage (access and result)
    </td>
    <td><code class="noHighlight">false</code></td>
  </tr>

  <tr>
    <td>resultproperty</td>
    <td>Save result at this project property
    </td>
    <td></td>
  </tr>

  <tr>
    <td>delimiter</td>
    <td>Split result with delimiter (java.util.StringTokenizer)
        and use resultproperty as prefix to store tokens.
    </td>
    <td></td>
  </tr>

  <tr>
    <td>separatearrayresults</td>
    <td>When return value is an array, save result as property list
    (<em>$resultproperty.[0..N]</em> and <em>$resultproperty.length</em>)
    </td>
    <td><code class="noHighlight">true</code></td>
  </tr>

</table>

<p>
Example to get remote MBean attribute from default JMX connection
</p>
<div class="codeBox"><pre><code>  &lt;jmx:get
    name="Catalina:type=Manager,context=/servlets-examples,host=localhost"
    attribute="maxActiveSessions"
    resultproperty="servlets-examples.maxActiveSessions"
  /&gt;</code></pre></div>

<p>
Example to get and result array and split it at separate properties
</p>
<div class="codeBox"><pre><code>  &lt;jmx:get
      name="Catalina:type=ClusterSender,host=localhost"
      attribute="senderObjectNames"
      resultproperty="senderObjectNames"
  /&gt;</code></pre></div>
<p>
Access the senderObjectNames properties with:
</p>
<div class="codeBox"><pre><code>  ${senderObjectNames.length} give the number of returned sender list.
  ${senderObjectNames.[0..N]} found all sender object names</code></pre></div>


<p>
Example to get IDataSender attribute connected only when cluster is configured.<br>
<em>Note:</em> The <code>name</code> attribute value was wrapped here to be
more readable. It has to be all on the same line, without spaces.
</p>
<div class="codeBox"><pre><code>
  &lt;jmx:query
    failonerror="false"
    name="Catalina:type=Cluster,host=${tomcat.application.host}"
    resultproperty="cluster"
  /&gt;
  &lt;jmx:get
    name=
"Catalina:type=IDataSender,host=${tomcat.application.host},
senderAddress=${cluster.backup.address},senderPort=${cluster.backup.port}"
    attribute="connected"
    resultproperty="datasender.connected"
    if="cluster.0.name" /&gt;</code></pre></div>

</div><h3 id="JMXAccessorSetTask:__set_attribute_value_Ant_task">JMXAccessorSetTask:  set attribute value Ant task</h3><div class="text">
<p>
List of Attributes
</p>
<table class="defaultTable">

  <tr>
    <th>Attribute</th>
    <th>Description</th>
    <th>Default value</th>
  </tr>

  <tr>
    <td>name</td>
    <td>Full qualified JMX ObjectName -- <em>Catalina:type=Server</em>
    </td>
    <td></td>
  </tr>

  <tr>
    <td>attribute</td>
    <td>Existing MBean attribute (see Tomcat MBean description above)
    </td>
    <td></td>
  </tr>

  <tr>
    <td>value</td>
    <td>value that set to attribute
    </td>
    <td></td>
  </tr>

  <tr>
    <td>type</td>
    <td>type of the attribute.
    </td>
    <td><code class="noHighlight">java.lang.String</code></td>
  </tr>

  <tr>
    <td>ref</td>
    <td>JMX Connection reference
    </td>
    <td><code class="noHighlight">jmx.server</code></td>
  </tr>

  <tr>
    <td>echo</td>
    <td>Echo command usage (access and result)
    </td>
    <td><code class="noHighlight">false</code></td>
  </tr>

</table>

<p>
Example to set remote MBean attribute value
</p>
<div class="codeBox"><pre><code>  &lt;jmx:set
    name="Catalina:type=Manager,context=/servlets-examples,host=localhost"
    attribute="maxActiveSessions"
    value="500"
    type="int"
  /&gt;</code></pre></div>


</div><h3 id="JMXAccessorInvokeTask:__invoke_MBean_operation_Ant_task">JMXAccessorInvokeTask:  invoke MBean operation Ant task</h3><div class="text">
<p>
List of Attributes
</p>
<table class="defaultTable">

  <tr>
    <th>Attribute</th>
    <th>Description</th>
    <th>Default value</th>
  </tr>

  <tr>
    <td>name</td>
    <td>Full qualified JMX ObjectName -- <em>Catalina:type=Server</em>
    </td>
    <td></td>
  </tr>

  <tr>
    <td>operation</td>
    <td>Existing MBean operation
    </td>
    <td></td>
  </tr>

  <tr>
    <td>ref</td>
    <td>JMX Connection reference
    </td>
    <td><code class="noHighlight">jmx.server</code></td>
  </tr>

  <tr>
    <td>echo</td>
    <td>Echo command usage (access and result)
    </td>
    <td><code class="noHighlight">false</code></td>
  </tr>

  <tr>
    <td>resultproperty</td>
    <td>Save result at this project property
    </td>
    <td></td>
  </tr>

  <tr>
    <td>delimiter</td>
    <td>Split result with delimiter (java.util.StringTokenizer)
        and use resultproperty as prefix to store tokens.
    </td>
    <td></td>
  </tr>

  <tr>
    <td>separatearrayresults</td>
    <td>When return value is an array, save result as property list
    (<em>$resultproperty.[0..N]</em> and <em>$resultproperty.length</em>)
    </td>
    <td><code class="noHighlight">true</code></td>
  </tr>

</table>

<p>
stop an application
</p>
<div class="codeBox"><pre><code>  &lt;jmx:invoke
    name="Catalina:type=Manager,context=/servlets-examples,host=localhost"
    operation="stop"/&gt;</code></pre></div>
<p>
Now you can find the sessionid at <em>${sessions.[0..N}</em> properties and access the count
with ${sessions.length} property.
</p>
<p>
Example to get all sessionids
</p>
<div class="codeBox"><pre><code>  &lt;jmx:invoke
    name="Catalina:type=Manager,context=/servlets-examples,host=localhost"
    operation="listSessionIds"
    resultproperty="sessions"
    delimiter=" "
  /&gt;</code></pre></div>
<p>
Now you can find the sessionid at <em>${sessions.[0..N}</em> properties and access the count
with ${sessions.length} property.
</p>
<p>
Example to get remote MBean session attribute from session ${sessionid.0}
</p>
<div class="codeBox"><pre><code>  &lt;jmx:invoke
    name="Catalina:type=Manager,context=/ClusterTest,host=localhost"
    operation="getSessionAttribute"
    resultproperty="hello"&gt;
     &lt;arg value="${sessionid.0}"/&gt;
     &lt;arg value="Hello" /&gt;
  &lt;/jmx:invoke&gt;</code></pre></div>

<p>
Example to create a new access logger valve at vhost <em>localhost</em>
</p>
<div class="codeBox"><pre><code> &lt;jmx:invoke
         name="Catalina:type=MBeanFactory"
         operation="createAccessLoggerValve"
         resultproperty="accessLoggerObjectName"
 &gt;
     &lt;arg value="Catalina:type=Host,host=localhost"/&gt;
 &lt;/jmx:invoke&gt;</code></pre></div>
<p>
Now you can find new MBean with name stored at <em>${accessLoggerObjectName}</em>
property.
</p>

</div><h3 id="JMXAccessorQueryTask:__query_MBean_Ant_task">JMXAccessorQueryTask:  query MBean Ant task</h3><div class="text">
<p>
List of Attributes
</p>
<table class="defaultTable">

  <tr>
    <th>Attribute</th>
    <th>Description</th>
    <th>Default value</th>
  </tr>

  <tr>
    <td>name</td>
    <td>JMX  ObjectName query string -- <em>Catalina:type=Manager,*</em>
    </td>
    <td></td>
  </tr>

  <tr>
    <td>ref</td>
    <td>JMX Connection reference
    </td>
    <td><code class="noHighlight">jmx.server</code></td>
  </tr>

  <tr>
    <td>echo</td>
    <td>Echo command usage (access and result)
    </td>
    <td><code class="noHighlight">false</code></td>
  </tr>

  <tr>
    <td>resultproperty</td>
    <td>Prefix project property name to all founded MBeans (<em>mbeans.[0..N].objectname</em>)
    </td>
    <td></td>
  </tr>

  <tr>
    <td>attributebinding</td>
    <td>bind ALL MBean attributes in addition to <em>name</em>
    </td>
    <td><code class="noHighlight">false</code></td>
  </tr>

  <tr>
    <td>delimiter</td>
    <td>Split result with delimiter (java.util.StringTokenizer)
        and use resultproperty as prefix to store tokens.
    </td>
    <td></td>
  </tr>

  <tr>
    <td>separatearrayresults</td>
    <td>When return value is an array, save result as property list
    (<em>$resultproperty.[0..N]</em> and <em>$resultproperty.length</em>)
    </td>
    <td><code class="noHighlight">true</code></td>
  </tr>

</table>

<p>
Get all Manager ObjectNames from all services and Hosts
</p>
<div class="codeBox"><pre><code>  &lt;jmx:query
    name="Catalina:type=Manager,*
    resultproperty="manager" /&gt;</code></pre></div>
<p>
Now you can find the Session Manager at <em>${manager.[0..N].name}</em>
properties and access the result object counter with ${manager.length} property.
</p>
<p>
Example to get the Manager from <em>servlet-examples</em> application an bind all MBean properties
</p>
<div class="codeBox"><pre><code>  &lt;jmx:query
    name="Catalina:type=Manager,context=/servlet-examples,host=localhost*"
    attributebinding="true"
    resultproperty="manager.servletExamples" /&gt;</code></pre></div>
<p>
Now you can find the manager at <em>${manager.servletExamples.0.name}</em> property
and can access all properties from this manager with <em>${manager.servletExamples.0.[manager attribute names]</em>}.
The result object counter from MBeans is stored ad ${manager.length} property.
</p>

<p>
Example to get all MBeans from a server and store inside an external XML property file
</p>
<div class="codeBox"><pre><code>&lt;project name="jmx.query"
            xmlns:jmx="antlib:org.apache.catalina.ant.jmx"
            default="query-all" basedir="."&gt;
&lt;property name="jmx.host" value="localhost"/&gt;
&lt;property name="jmx.port" value="8050"/&gt;
&lt;property name="jmx.username" value="controlRole"/&gt;
&lt;property name="jmx.password" value="tomcat"/&gt;

&lt;target name="query-all" description="Query all MBeans of a server"&gt;
  &lt;!-- Configure connection --&gt;
  &lt;jmx:open
    host="${jmx.host}"
    port="${jmx.port}"
    ref="jmx.server"
    username="${jmx.username}"
    password="${jmx.password}"/&gt;

  &lt;!-- Query MBean list --&gt;
  &lt;jmx:query
    name="*:*"
    resultproperty="mbeans"
    attributebinding="false"/&gt;

  &lt;echoproperties
    destfile="mbeans.properties"
    prefix="mbeans."
    format="xml"/&gt;

  &lt;!-- Print results --&gt;
  &lt;echo message=
    "Number of MBeans in server ${jmx.host}:${jmx.port} is ${mbeans.length}"/&gt;
&lt;/target&gt;
&lt;/project&gt;</code></pre></div>
<p>
Now you can find all MBeans inside the file <em>mbeans.properties</em>.
</p>

</div><h3 id="JMXAccessorCreateTask:__remote_create_MBean_Ant_task">JMXAccessorCreateTask:  remote create MBean Ant task</h3><div class="text">
<p>
List of Attributes
</p>
<table class="defaultTable">

  <tr>
    <th>Attribute</th>
    <th>Description</th>
    <th>Default value</th>
  </tr>

  <tr>
    <td>name</td>
    <td>Full qualified JMX ObjectName -- <em>Catalina:type=MBeanFactory</em>
    </td>
    <td></td>
  </tr>

  <tr>
    <td>className</td>
    <td>Existing MBean full qualified class name (see Tomcat MBean description above)
    </td>
    <td></td>
  </tr>

  <tr>
    <td>classLoader</td>
    <td>ObjectName of server or web application classloader <br>
    ( <em>Catalina:type=ServerClassLoader,name=[server,common,shared]</em> or<br>
     <em>Catalina:type=WebappClassLoader,context=/myapps,host=localhost</em>)
    </td>
    <td></td>
  </tr>

  <tr>
    <td>ref</td>
    <td>JMX Connection reference
    </td>
    <td><code class="noHighlight">jmx.server</code></td>
  </tr>

  <tr>
    <td>echo</td>
    <td>Echo command usage (access and result)
    </td>
    <td><code class="noHighlight">false</code></td>
  </tr>

</table>

<p>
Example to create remote MBean
</p>
<div class="codeBox"><pre><code>  &lt;jmx:create
    ref="${jmx.reference}"
    name="Catalina:type=MBeanFactory"
    className="org.apache.commons.modeler.BaseModelMBean"
    classLoader="Catalina:type=ServerClassLoader,name=server"&gt;
    &lt;arg value="org.apache.catalina.mbeans.MBeanFactory" /&gt;
  &lt;/jmx:create&gt;</code></pre></div>

<p>
    <b>Warning</b>: Many Tomcat MBeans can't be linked to their parent once<br>
    created. The Valve, Cluster and Realm MBeans are not automatically<br>
    connected with their parent. Use the <em>MBeanFactory</em> create<br>
    operation instead.
</p>

</div><h3 id="JMXAccessorUnregisterTask:__remote_unregister_MBean_Ant_task">JMXAccessorUnregisterTask:  remote unregister MBean Ant task</h3><div class="text">
<p>
List of Attributes
</p>
<table class="defaultTable">

  <tr>
    <th>Attribute</th>
    <th>Description</th>
    <th>Default value</th>
  </tr>

  <tr>
    <td>name</td>
    <td>Full qualified JMX ObjectName -- <em>Catalina:type=MBeanFactory</em>
    </td>
    <td></td>
  </tr>

  <tr>
    <td>ref</td>
    <td>JMX Connection reference
    </td>
    <td><code class="noHighlight">jmx.server</code></td>
  </tr>

  <tr>
    <td>echo</td>
    <td>Echo command usage (access and result)
    </td>
    <td><code class="noHighlight">false</code></td>
  </tr>

</table>

<p>
Example to unregister remote MBean
</p>
<div class="codeBox"><pre><code>  &lt;jmx:unregister
    name="Catalina:type=MBeanFactory"
  /&gt;</code></pre></div>

<p>
    <b>Warning</b>: A lot of Tomcat MBeans can't be unregister.<br>
    The MBeans are not unlinked from their parent. Use <em>MBeanFactory</em><br>
    remove operation instead.
</p>

</div><h3 id="JMXAccessorCondition:__express_condition">JMXAccessorCondition:  express condition</h3><div class="text">
<p>
List of Attributes
</p>
<table class="defaultTable">

  <tr>
    <th>Attribute</th>
    <th>Description</th>
    <th>Default value</th>
  </tr>

 <tr>
    <td>url</td>
    <td>Set JMX connection URL - <em>service:jmx:rmi:///jndi/rmi://localhost:8050/jmxrmi</em>
    </td>
    <td></td>
  </tr>

  <tr>
    <td>host</td>
    <td>Set the host, shortcut the very long URL syntax.
    </td>
    <td><code class="noHighlight">localhost</code></td>
  </tr>

  <tr>
    <td>port</td>
    <td>Set the remote connection port
    </td>
    <td><code class="noHighlight">8050</code></td>
  </tr>

  <tr>
    <td>username</td>
    <td>remote JMX connection user name.
    </td>
    <td></td>
  </tr>

  <tr>
    <td>password</td>
    <td>remote JMX connection password.
    </td>
    <td></td>
  </tr>

  <tr>
    <td>ref</td>
    <td>Name of the internal connection reference. With this attribute you can
        configure more the one connection inside the same Ant project.
    </td>
    <td><code class="noHighlight">jmx.server</code></td>
  </tr>

  <tr>
    <td>name</td>
    <td>Full qualified JMX ObjectName -- <em>Catalina:type=Server</em>
    </td>
    <td></td>
  </tr>

  <tr>
    <td>echo</td>
    <td>Echo condition usage (access and result)
    </td>
    <td><code class="noHighlight">false</code></td>
  </tr>

  <tr>
    <td>if</td>
    <td>Only execute if a property of the given name <b>exists</b> in the current project.
    </td>
    <td></td>
  </tr>

  <tr>
    <td>unless</td>
    <td>Only execute if a property of the given name <b>not exists</b> in the current project.
    </td>
    <td></td>
  </tr>

  <tr>
    <td>value (required)</td>
    <td>Second arg for operation
    </td>
    <td></td>
  </tr>

  <tr>
    <td>type</td>
    <td>Value type to express operation (support <em>long</em> and <em>double</em>)
    </td>
    <td><code class="noHighlight">long</code></td>
  </tr>

  <tr>
    <td>operation</td>
    <td> express one
    <ul>
    <li>==  equals</li>
    <li>!=  not equals</li>
    <li>&gt; greater than (&amp;gt;)</li>
    <li>&gt;= greater than or equals (&amp;gt;=)</li>
    <li>&lt; lesser than (&amp;lt;)</li>
    <li>&lt;= lesser than or equals (&amp;lt;=)</li>
    </ul>
    </td>
    <td><code class="noHighlight">==</code></td>
  </tr>

</table>

<p>
Wait for server connection and that cluster backup node is accessible
</p>
<div class="codeBox"><pre><code>&lt;target name="wait"&gt;
  &lt;waitfor maxwait="${maxwait}" maxwaitunit="second" timeoutproperty="server.timeout" &gt;
    &lt;and&gt;
      &lt;socket server="${server.name}" port="${server.port}"/&gt;
      &lt;http url="${url}"/&gt;
      &lt;jmx:condition
        operation="=="
        host="localhost"
        port="9014"
        username="controlRole"
        password="tomcat"
        name=
"Catalina:type=IDataSender,host=localhost,senderAddress=*************,senderPort=9025"
        attribute="connected"
        value="true"
      /&gt;
    &lt;/and&gt;
  &lt;/waitfor&gt;
  &lt;fail if="server.timeout" message="Server ${url} don't answer inside ${maxwait} sec" /&gt;
  &lt;echo message="Server ${url} alive" /&gt;
&lt;/target&gt;</code></pre></div>

</div><h3 id="JMXAccessorEqualsCondition:__equals_MBean_Ant_condition">JMXAccessorEqualsCondition:  equals MBean Ant condition</h3><div class="text">
<p>
List of Attributes
</p>
<table class="defaultTable">

  <tr>
    <th>Attribute</th>
    <th>Description</th>
    <th>Default value</th>
  </tr>

 <tr>
    <td>url</td>
    <td>Set JMX connection URL - <em>service:jmx:rmi:///jndi/rmi://localhost:8050/jmxrmi</em>
    </td>
    <td></td>
  </tr>

  <tr>
    <td>host</td>
    <td>Set the host, shortcut the very long URL syntax.
    </td>
    <td><code class="noHighlight">localhost</code></td>
  </tr>

  <tr>
    <td>port</td>
    <td>Set the remote connection port
    </td>
    <td><code class="noHighlight">8050</code></td>
  </tr>

  <tr>
    <td>username</td>
    <td>remote JMX connection user name.
    </td>
    <td></td>
  </tr>

  <tr>
    <td>password</td>
    <td>remote JMX connection password.
    </td>
    <td></td>
  </tr>

  <tr>
    <td>ref</td>
    <td>Name of the internal connection reference. With this attribute you can
        configure more the one connection inside the same Ant project.
    </td>
    <td><code class="noHighlight">jmx.server</code></td>
  </tr>

  <tr>
    <td>name</td>
    <td>Full qualified JMX ObjectName -- <em>Catalina:type=Server</em>
    </td>
    <td></td>
  </tr>


  <tr>
    <td>echo</td>
    <td>Echo condition usage (access and result)
    </td>
    <td><code class="noHighlight">false</code></td>
  </tr>

</table>

<p>
Wait for server connection and that cluster backup node is accessible
</p>
<div class="codeBox"><pre><code>&lt;target name="wait"&gt;
  &lt;waitfor maxwait="${maxwait}" maxwaitunit="second" timeoutproperty="server.timeout" &gt;
    &lt;and&gt;
      &lt;socket server="${server.name}" port="${server.port}"/&gt;
      &lt;http url="${url}"/&gt;
      &lt;jmx:equals
        host="localhost"
        port="9014"
        username="controlRole"
        password="tomcat"
        name=
"Catalina:type=IDataSender,host=localhost,senderAddress=*************,senderPort=9025"
        attribute="connected"
        value="true"
      /&gt;
    &lt;/and&gt;
  &lt;/waitfor&gt;
  &lt;fail if="server.timeout" message="Server ${url} don't answer inside ${maxwait} sec" /&gt;
  &lt;echo message="Server ${url} alive" /&gt;
&lt;/target&gt;</code></pre></div>

</div><h3 id="Using_the_JMXProxyServlet">Using the JMXProxyServlet</h3><div class="text">

    <p>
      Tomcat offers an alternative to using remote (or even local) JMX
      connections while still giving you access to everything JMX has to offer:
      Tomcat's
      <a href="api/org/apache/catalina/manager/JMXProxyServlet.html">JMXProxyServlet</a>.
    </p>

    <p>
      The JMXProxyServlet allows a client to issue JMX queries via an HTTP
      interface. This technique offers the following advantages over using
      JMX directly from a client program:
    </p>

    <ul>
      <li>You don't have to launch a full JVM and make a remote JMX connection
      just to ask for one small piece of data from a running server</li>
      <li>You don't have to know how to work with JMX connections</li>
      <li>You don't need any of the complex configuration covered in the rest
      of this page</li>
      <li>Your client program does not have to be written in Java</li>
    </ul>

    <p>
      A perfect example of JMX overkill can be seen in the case of popular
      server-monitoring software such as Nagios or Icinga: if you want to
      monitor 10 items via JMX, you will have to launch 10 JVMs, make 10 JMX
      connections, and then shut them all down every few minutes. With the
      JMXProxyServlet, you can make 10 HTTP connections and be done with it.
    </p>

    <p>
      You can find out more information about the JMXProxyServlet in the
      documentation for the
      <a href="manager-howto.html#Using_the_JMX_Proxy_Servlet">Tomcat
      manager</a>.
    </p>
  </div></div></div></div></div><footer><div id="footer">
    Copyright &copy; 1999-2025, The Apache Software Foundation
    <br>
    Apache Tomcat, Tomcat, Apache, the Apache Tomcat logo and the Apache logo
    are either registered trademarks or trademarks of the Apache Software
    Foundation.
    </div></footer></div></body></html>