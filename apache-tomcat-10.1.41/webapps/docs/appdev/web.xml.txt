<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->
<web-app xmlns="https://jakarta.ee/xml/ns/jakartaee"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="https://jakarta.ee/xml/ns/jakartaee
                      https://jakarta.ee/xml/ns/jakartaee/web-app_6_0.xsd"
  version="6.0">


    <!-- General description of your web application -->

    <display-name>My Web Application</display-name>
    <description>
      This is version X.X of an application to perform
      a wild and wonderful task, based on servlets and
      JSP pages.  It was written by <PERSON> Developer
      (<EMAIL>), who should be contacted for
      more information.
    </description>


    <!-- Context initialization parameters that define shared
         String constants used within your application, which
         can be customized by the system administrator who is
         installing your application.  The values actually
         assigned to these parameters can be retrieved in a
         servlet or JSP page by calling:

             String value =
               getServletContext().getInitParameter("name");

         where "name" matches the <param-name> element of
         one of these initialization parameters.

         You can define any number of context initialization
         parameters, including zero.
    -->

    <context-param>
      <param-name>webadmin</param-name>
      <param-value><EMAIL></param-value>
      <description>
        The EMAIL address of the administrator to whom questions
        and comments about this application should be addressed.
      </description>
    </context-param>


    <!-- Servlet definitions for the servlets that make up
         your web application, including initialization
         parameters.  With Tomcat, you can also send requests
         to servlets not listed here with a request like this:

           http://localhost:8080/{context-path}/servlet/{classname}

         but this usage is not guaranteed to be portable.  It also
         makes relative references to images and other resources
         required by your servlet more complicated, so defining
         all of your servlets (and defining a mapping to them with
         a servlet-mapping element) is recommended.

         Servlet initialization parameters can be retrieved in a
         servlet or JSP page by calling:

             String value =
               getServletConfig().getInitParameter("name");

         where "name" matches the <param-name> element of
         one of these initialization parameters.

         You can define any number of servlets, including zero.
    -->

    <servlet>
      <servlet-name>controller</servlet-name>
      <description>
        This servlet plays the "controller" role in the MVC architecture
        used in this application.  It is generally mapped to the ".do"
        filename extension with a servlet-mapping element, and all form
        submits in the app will be submitted to a request URI like
        "saveCustomer.do", which will therefore be mapped to this servlet.

        The initialization parameter names for this servlet are the
        "servlet path" that will be received by this servlet (after the
        filename extension is removed).  The corresponding value is the
        name of the action class that will be used to process this request.
      </description>
      <servlet-class>com.mycompany.mypackage.ControllerServlet</servlet-class>
      <init-param>
        <param-name>listOrders</param-name>
        <param-value>com.mycompany.myactions.ListOrdersAction</param-value>
      </init-param>
      <init-param>
        <param-name>saveCustomer</param-name>
        <param-value>com.mycompany.myactions.SaveCustomerAction</param-value>
      </init-param>
      <!-- Load this servlet at server startup time -->
      <load-on-startup>5</load-on-startup>
    </servlet>

    <servlet>
      <servlet-name>graph</servlet-name>
      <description>
        This servlet produces GIF images that are dynamically generated
        graphs, based on the input parameters included on the request.
        It is generally mapped to a specific request URI like "/graph".
      </description>
    </servlet>


    <!-- Define mappings that are used by the servlet container to
         translate a particular request URI (context-relative) to a
         particular servlet.  The examples below correspond to the
         servlet descriptions above.  Thus, a request URI like:

           http://localhost:8080/{contextpath}/graph

         will be mapped to the "graph" servlet, while a request like:

           http://localhost:8080/{contextpath}/saveCustomer.do

         will be mapped to the "controller" servlet.

         You may define any number of servlet mappings, including zero.
         It is also legal to define more than one mapping for the same
         servlet, if you wish to.
    -->

    <servlet-mapping>
      <servlet-name>controller</servlet-name>
      <url-pattern>*.do</url-pattern>
    </servlet-mapping>

    <servlet-mapping>
      <servlet-name>graph</servlet-name>
      <url-pattern>/graph</url-pattern>
    </servlet-mapping>


    <!-- Define the default session timeout for your application,
         in minutes.  From a servlet or JSP page, you can modify
         the timeout for a particular session dynamically by using
         HttpSession.getMaxInactiveInterval(). -->

    <session-config>
      <session-timeout>30</session-timeout>    <!-- 30 minutes -->
    </session-config>


</web-app>
