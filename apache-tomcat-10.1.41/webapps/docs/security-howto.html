<!DOCTYPE html SYSTEM "about:legacy-compat">
<html lang="en"><head><META http-equiv="Content-Type" content="text/html; charset=UTF-8"><link href="./images/docs-stylesheet.css" rel="stylesheet" type="text/css"><title>Apache Tomcat 10 (10.1.41) - Security Considerations</title></head><body><div id="wrapper"><header><div id="header"><div><div><div class="logo noPrint"><a href="https://tomcat.apache.org/"><img alt="Tomcat Home" src="./images/tomcat.png"></a></div><div style="height: 1px;"></div><div class="asfLogo noPrint"><a href="https://www.apache.org/" target="_blank"><img src="./images/asf-logo.svg" alt="The Apache Software Foundation" style="width: 266px; height: 83px;"></a></div><h1>Apache Tomcat 10</h1><div class="versionInfo">
            Version 10.1.41,
            <time datetime="2025-05-08">May 8 2025</time></div><div style="height: 1px;"></div><div style="clear: left;"></div></div></div></div></header><div id="middle"><div><div id="mainLeft" class="noprint"><div><nav><div><h2>Links</h2><ul><li><a href="index.html">Docs Home</a></li><li><a href="https://cwiki.apache.org/confluence/display/TOMCAT/FAQ">FAQ</a></li></ul></div><div><h2>User Guide</h2><ul><li><a href="introduction.html">1) Introduction</a></li><li><a href="setup.html">2) Setup</a></li><li><a href="appdev/index.html">3) First webapp</a></li><li><a href="deployer-howto.html">4) Deployer</a></li><li><a href="manager-howto.html">5) Manager</a></li><li><a href="host-manager-howto.html">6) Host Manager</a></li><li><a href="realm-howto.html">7) Realms and AAA</a></li><li><a href="security-manager-howto.html">8) Security Manager</a></li><li><a href="jndi-resources-howto.html">9) JNDI Resources</a></li><li><a href="jndi-datasource-examples-howto.html">10) JDBC DataSources</a></li><li><a href="class-loader-howto.html">11) Classloading</a></li><li><a href="jasper-howto.html">12) JSPs</a></li><li><a href="ssl-howto.html">13) SSL/TLS</a></li><li><a href="ssi-howto.html">14) SSI</a></li><li><a href="cgi-howto.html">15) CGI</a></li><li><a href="proxy-howto.html">16) Proxy Support</a></li><li><a href="mbeans-descriptors-howto.html">17) MBeans Descriptors</a></li><li><a href="default-servlet.html">18) Default Servlet</a></li><li><a href="cluster-howto.html">19) Clustering</a></li><li><a href="balancer-howto.html">20) Load Balancer</a></li><li><a href="connectors.html">21) Connectors</a></li><li><a href="monitoring.html">22) Monitoring and Management</a></li><li><a href="logging.html">23) Logging</a></li><li><a href="apr.html">24) APR/Native</a></li><li><a href="virtual-hosting-howto.html">25) Virtual Hosting</a></li><li><a href="aio.html">26) Advanced IO</a></li><li><a href="maven-jars.html">27) Mavenized</a></li><li><a href="security-howto.html">28) Security Considerations</a></li><li><a href="windows-service-howto.html">29) Windows Service</a></li><li><a href="windows-auth-howto.html">30) Windows Authentication</a></li><li><a href="jdbc-pool.html">31) Tomcat's JDBC Pool</a></li><li><a href="web-socket-howto.html">32) WebSocket</a></li><li><a href="rewrite.html">33) Rewrite</a></li><li><a href="cdi.html">34) CDI 2 and JAX-RS</a></li><li><a href="graal.html">35) AOT/GraalVM Support</a></li></ul></div><div><h2>Reference</h2><ul><li><a href="RELEASE-NOTES.txt">Release Notes</a></li><li><a href="config/index.html">Configuration</a></li><li><a href="api/index.html">Tomcat Javadocs</a></li><li><a href="servletapi/index.html">Servlet 6.0 Javadocs</a></li><li><a href="jspapi/index.html">JSP 3.1 Javadocs</a></li><li><a href="elapi/index.html">EL 5.0 Javadocs</a></li><li><a href="websocketapi/index.html">WebSocket 2.1 Javadocs</a></li><li><a href="jaspicapi/index.html">Authentication 3.0 Javadocs</a></li><li><a href="annotationapi/index.html">Annotations 2.1 Javadocs</a></li><li><a href="https://tomcat.apache.org/connectors-doc/">JK 1.2 Documentation</a></li></ul></div><div><h2>Apache Tomcat Development</h2><ul><li><a href="building.html">Building</a></li><li><a href="changelog.html">Changelog</a></li><li><a href="https://cwiki.apache.org/confluence/display/TOMCAT/Tomcat+Versions">Status</a></li><li><a href="developers.html">Developers</a></li><li><a href="architecture/index.html">Architecture</a></li><li><a href="tribes/introduction.html">Tribes</a></li></ul></div></nav></div></div><div id="mainRight"><div id="content"><h2>Security Considerations</h2><h3 id="Table_of_Contents">Table of Contents</h3><div class="text">
<ul><li><a href="#Introduction">Introduction</a></li><li><a href="#Non-Tomcat_settings">Non-Tomcat settings</a><ol><li><a href="#JMX">JMX</a></li></ol></li><li><a href="#Default_web_applications">Default web applications</a><ol><li><a href="#Default_web_applications/General">General</a></li><li><a href="#ROOT">ROOT</a></li><li><a href="#Documentation">Documentation</a></li><li><a href="#Examples">Examples</a></li><li><a href="#Default_web_applications/Manager">Manager</a></li><li><a href="#Host_Manager">Host Manager</a></li><li><a href="#Securing_Management_Applications">Securing Management Applications</a></li></ol></li><li><a href="#Security_manager">Security manager</a></li><li><a href="#server.xml">server.xml</a><ol><li><a href="#server.xml/General">General</a></li><li><a href="#Server">Server</a></li><li><a href="#Listeners">Listeners</a></li><li><a href="#Connectors">Connectors</a></li><li><a href="#Host">Host</a></li><li><a href="#Context">Context</a></li><li><a href="#Valves">Valves</a></li><li><a href="#Realms">Realms</a></li><li><a href="#server.xml/Manager">Manager</a></li><li><a href="#Cluster">Cluster</a></li></ol></li><li><a href="#web.xml">web.xml</a></li><li><a href="#Embedded_Tomcat">Embedded Tomcat</a></li><li><a href="#General">General</a></li></ul>
</div><h3 id="Introduction">Introduction</h3><div class="text">
    <p>Tomcat is configured to be reasonably secure for most use cases by
    default. Some environments may require more, or less, secure configurations.
    This page is to provide a single point of reference for configuration
    options that may impact security and to offer some commentary on the
    expected impact of changing those options. The intention is to provide a
    list of configuration options that should be considered when assessing the
    security of a Tomcat installation.</p>

    <p><strong>Note</strong>: Reading this page is not a substitute for reading
    and understanding the detailed configuration documentation. Fuller
    descriptions of these attributes may be found in the relevant documentation
    pages.</p>
  </div><h3 id="Non-Tomcat_settings">Non-Tomcat settings</h3><div class="text">
    <p>Tomcat configuration should not be the only line of defense. The other
    components in the system (operating system, network, database, etc.) should
    also be secured.</p>
    <p>Tomcat should not be run under the root user. Create a dedicated user for
    the Tomcat process and provide that user with the minimum necessary
    permissions for the operating system. For example, it should not be possible
    to log on remotely using the Tomcat user.</p>
    <p>File permissions should also be suitably restricted. In the
    <code>.tar.gz</code> distribution, files and directories are not world
    readable and the group does not have write access. On Unix like operating
    systems, Tomcat runs with a default umask of <code>0027</code> to maintain
    these permissions for files created while Tomcat is running (e.g. log files,
    expanded WARs, etc.).</p>
    <p>Taking the Tomcat instances at the ASF as an example (where
    auto-deployment is disabled and web applications are deployed as exploded
    directories), the standard configuration is to have all Tomcat files owned
    by root with group Tomcat and whilst owner has read/write privileges, group
    only has read and world has no permissions. The exceptions are the logs,
    temp and work directory that are owned by the Tomcat user rather than root.
    This means that even if an attacker compromises the Tomcat process, they
    can't change the Tomcat configuration, deploy new web applications or
    modify existing web applications. The Tomcat process runs with a umask of
    007 to maintain these permissions.</p>
    <p>At the network level, consider using a firewall to limit both incoming
    and outgoing connections to only those connections you  expect to be
    present.</p>

    <div class="subsection"><h4 id="JMX">JMX</h4><div class="text">
      <p>The security of the JMX connection is dependent on the implementation
      provided by the JRE and therefore falls outside the control of Tomcat.</p>

      <p>Typically, access control is very limited (either read-only to
      everything or read-write to everything). Tomcat exposes a large amount
      of internal information and control via JMX to aid debugging, monitoring
      and management. Given the limited access control available, JMX access
      should be treated as equivalent to local root/admin access and restricted
      accordingly.</p>

      <p>The JMX access control provided by most (all?) JRE vendors does not
      log failed authentication attempts, nor does it provide an account
      lock-out feature after repeated failed authentications. This makes a
      brute force attack easy to mount and difficult to detect.</p>

      <p>Given all of the above, care should be taken to ensure that, if used,
      the JMX interface is appropriately secured. Options you may wish to
      consider to secure the JMX interface include:</p>

      <ul>
        <li>configuring a strong password for all JMX users;</li>
        <li>binding the JMX listener only to an internal network;</li>
        <li>limiting network access to the JMX port to trusted clients; and</li>
        <li>providing an application specific health page for use by external
            monitoring systems.</li>
      </ul>
    </div></div>

  </div><h3 id="Default_web_applications">Default web applications</h3><div class="text">

    <div class="subsection"><h4 id="Default_web_applications/General">General</h4><div class="text">
      <p>Tomcat ships with a number of web applications that are enabled by
      default. Vulnerabilities have been discovered in these applications in the
      past. Applications that are not required should be removed so the system
      will not be at risk if another vulnerability is discovered.</p>
    </div></div>

    <div class="subsection"><h4 id="ROOT">ROOT</h4><div class="text">
      <p>The ROOT web application presents a very low security risk but it does
      include the version of Tomcat that is being used. The ROOT web application
      should normally be removed from a publicly accessible Tomcat instance, not
      for security reasons, but so that a more appropriate default page is shown
      to users.</p>
    </div></div>

    <div class="subsection"><h4 id="Documentation">Documentation</h4><div class="text">
      <p>The documentation web application presents a very low security risk but
      it does identify the version of Tomcat that is being used. It should
      normally be removed from a publicly accessible Tomcat instance.</p>
    </div></div>

    <div class="subsection"><h4 id="Examples">Examples</h4><div class="text">
      <p>The examples web application should always be removed from any security
      sensitive installation. While the examples web application does not
      contain any known vulnerabilities, it is known to contain features
      (particularly the cookie examples that display the contents of all cookies
      received and allow new cookies to be set) that may be used by an attacker
      in conjunction with a vulnerability in another application deployed on the
      Tomcat instance to obtain additional information that would otherwise be
      unavailable.</p>
    </div></div>

    <div class="subsection"><h4 id="Default_web_applications/Manager">Manager</h4><div class="text">
      <p>The Manager application allows the remote deployment of web
      applications and is frequently targeted by attackers due to the widespread
      use of weak passwords and publicly accessible Tomcat instances with the
      Manager application enabled. The Manager application is not accessible by
      default as no users are configured with the necessary access. If the
      Manager application is enabled then guidance in the section
      <strong>Securing Management Applications</strong> section should be
      followed.</p>
    </div></div>

    <div class="subsection"><h4 id="Host_Manager">Host Manager</h4><div class="text">
      <p>The Host Manager application allows the creation and management of
      virtual hosts - including the enabling of the Manager application for a
      virtual host. The Host Manager application is not accessible by default
      as no users are configured with the necessary access. If the Host Manager
      application is enabled then guidance in the section <strong>Securing
      Management Applications</strong> section should be followed.</p>
    </div></div>

   <div class="subsection"><h4 id="Securing_Management_Applications">Securing Management Applications</h4><div class="text">
     <p>When deploying a web application that provides management functions for
     the Tomcat instance, the following guidelines should be followed:</p>
     <ul>
       <li>Ensure that any users permitted to access the management application
           have strong passwords.</li>
       <li>Do not remove the use of the <a href="config/realm.html#LockOut_Realm_-_org.apache.catalina.realm.LockOutRealm">LockOutRealm</a>
           which prevents brute force attacks against user passwords.</li>
       <li>Configure the <a href="config/valve.html#Remote_Address_Valve">RemoteAddrValve</a>
           in the <a href="config/context.html">context.xml</a> file for the
           management application which limits access to localhost by default.
           If remote access is required, limit it to specific IP addresses using
           this valve.</li>
     </ul>
   </div></div>
  </div><h3 id="Security_manager">Security manager</h3><div class="text">
    <p>Enabling the security manager causes web applications to be run in a
    sandbox, significantly limiting a web application's ability to perform
    malicious actions such as calling System.exit(), establishing network
    connections or accessing the file system outside of the web application's
    root and temporary directories. However, it should be noted that there are
    some malicious actions, such as triggering high CPU consumption via an
    infinite loop, that the security manager cannot prevent.</p>

    <p>Enabling the security manager is usually done to limit the potential
    impact, should an attacker find a way to compromise a trusted web
    application . A security manager may also be used to reduce the risks of
    running untrusted web applications (e.g. in hosting environments) but it
    should be noted that the security manager only reduces the risks of
    running untrusted web applications, it does not eliminate them. If running
    multiple untrusted web applications, it is recommended that each web
    application is deployed to a separate Tomcat instance (and ideally separate
    hosts) to reduce the ability of a malicious web application impacting the
    availability of other applications.</p>

    <p>Tomcat is tested with the security manager enabled; but the majority of
    Tomcat users do not run with a security manager, so Tomcat is not as well
    user-tested in this configuration. There have been, and continue to be,
    bugs reported that are triggered by running under a security manager.</p>

    <p>The restrictions imposed by a security manager are likely to break most
    applications if the security manager is enabled. The security manager should
    not be used without extensive testing. Ideally, the use of a security
    manager should be introduced at the start of the development cycle as it can
    be time-consuming to track down and fix issues caused by enabling a security
    manager for a mature application.</p>

    <p>Enabling the security manager changes the defaults for the following
    settings:</p>
    <ul>
      <li>The default value for the <strong>deployXML</strong> attribute of the
      <strong>Host</strong> element is changed to <code>false</code>.</li>
    </ul>
  </div><h3 id="server.xml">server.xml</h3><div class="text">
    <div class="subsection"><h4 id="server.xml/General">General</h4><div class="text">
      <p>The default server.xml contains a large number of comments, including
      some example component definitions that are commented out. Removing these
      comments makes it considerably easier to read and comprehend
      server.xml.</p>
      <p>If a component type is not listed, then there are no settings for that
      type that directly impact security.</p>
    </div></div>

    <div class="subsection"><h4 id="Server">Server</h4><div class="text">
      <p>Setting the <strong>port</strong> attribute to <code>-1</code> disables
      the shutdown port.</p>
      <p>If the shutdown port is not disabled, a strong password should be
      configured for <strong>shutdown</strong>.</p>
    </div></div>

    <div class="subsection"><h4 id="Listeners">Listeners</h4><div class="text">
      <p>The APR Lifecycle Listener is not stable if compiled on Solaris using
      gcc. If using the APR/native connector on Solaris, compile it with the
      Sun Studio compiler.</p>
      <p>The JNI Library Loading Listener may be used to load native code. It should
      only be used to load trusted libraries.</p>
      <p>The Security Lifecycle Listener should be enabled and configured as appropriate.
      </p>
    </div></div>

    <div class="subsection"><h4 id="Connectors">Connectors</h4><div class="text">
      <p>By default, a non-TLS, HTTP/1.1 connector is configured on port 8080.
      Connectors that will not be used should be removed from server.xml.</p>

      <p>AJP is a clear text protocol. AJP Connectors should normally only be
      used on trusted networks. If used on an untrusted network, use of the
      <code>secret</code> attribute will limit access to authorised clients but
      the <code>secret</code> attribute will be visible to anyone who can
      observe network traffic.</p>

      <p>AJP Connectors block forwarded requests with unknown request
      attributes. Known safe and/or expected attributes may be allowed by
      configuration an appropriate regular expression for the
      <code>allowedRequestAttributesPattern</code> attribute.</p>

      <p>The <strong>address</strong> attribute may be used to control which IP
      address a connector listens on for connections. By default, a connector
      listens on all configured IP addresses.</p>

      <p>The <strong>allowBackslash</strong> attribute allows non-standard
      parsing of the request URI. Setting this attribute to a non-default value
      when behind a reverse proxy may enable an attacker to bypass any security
      constraints enforced by the proxy.</p>

      <p>The <strong>allowTrace</strong> attribute may be used to enable TRACE
      requests which can be useful for debugging. Due to the way some browsers
      handle the response from a TRACE request (which exposes the browser to an
      XSS attack), support for TRACE requests is disabled by default.</p>

      <p>The <strong>discardFacades</strong> attribute set to <code>true</code>
      will cause a new facade object to be created for each request. This is
      the default value, and this reduces the chances of a bug in an
      application exposing data from one request to another.</p>

      <p>The <strong>encodedSolidusHandling</strong> attribute allows
      non-standard parsing of the request URI. Setting this attribute to a
      non-default value when behind a reverse proxy may enable an attacker to
      bypass any security constraints enforced by the proxy.</p>

      <p>The <strong>enforceEncodingInGetWriter</strong> attribute has security
      implications if set to <code>false</code>. Many user agents, in breach of
      RFC 7230, try to guess the character encoding of text media types when the
      specification-mandated default of ISO-8859-1 should be used. Some browsers
      will interpret as UTF-7 a response containing characters that are safe for
      ISO-8859-1 but trigger an XSS vulnerability if interpreted as UTF-7.</p>

      <p>The <strong>maxPostSize</strong> attribute controls the maximum size
      of a POST request that will be parsed for parameters. The parameters are
      cached for the duration of the request so this is limited to 2 MiB by
      default to reduce exposure to a DOS attack.</p>

      <p>The <strong>maxSavePostSize</strong> attribute controls the saving of
      the request body during FORM and CLIENT-CERT authentication and HTTP/1.1
      upgrade. For FORM authentication, the request body is cached in the HTTP
      session for the duration of the authentication so the cached request body
      is limited to 4 KiB by default to reduce exposure to a DOS attack. To
      further reduce exposure to a DoS attack by limiting the permitted duration
      of the FORM authentication, a reduced session timeout is used if the
      session is created by the FORM authentication. This reduced timeout is
      controlled by the <code>authenticationSessionTimeout</code> attribute of
      the <a href="config/valve.html#Form_Authenticator_Valve">FORM
      authenticator</a>.</p>

      <p>The <strong>maxParameterCount</strong> attribute controls the maximum
      total number of request parameters (including uploaded files) obtained
      from the query string and, for POST requests, the request body if the
      content type is <code>application/x-www-form-urlencoded</code> or
      <code>multipart/form-data</code>. Excessive parameters are ignored. If you
      want to reject such requests, configure a
      <a href="config/filter.html">FailedRequestFilter</a>.</p>

      <p>The <strong>xpoweredBy</strong> attribute controls whether or not the
      X-Powered-By HTTP header is sent with each request. If sent, the value of
      the header contains the Servlet and JSP specification versions, the full
      Tomcat version (e.g. Apache Tomcat/10.1), the name of
      the JVM vendor and
      the version of the JVM. This header is disabled by default. This header
      can provide useful information to both legitimate clients and attackers.
      </p>

      <p>The <strong>server</strong> attribute controls the value of the Server
      HTTP header. The default value of this header for Tomcat 4.1.x to
      8.0.x is Apache-Coyote/1.1. From 8.5.x onwards this header is not set by
      default. This header can provide limited information to both legitimate
      clients and attackers.</p>

      <p>The <strong>SSLEnabled</strong>, <strong>scheme</strong> and
      <strong>secure</strong> attributes may all be independently set. These are
      normally used when Tomcat is located behind a reverse proxy and the proxy
      is connecting to Tomcat via HTTP or HTTPS. They allow Tomcat to see the
      SSL attributes of the connections between the client and the proxy rather
      than the proxy and Tomcat. For example, the client may connect to the
      proxy over HTTPS but the proxy connects to Tomcat using HTTP. If it is
      necessary for Tomcat to be able to distinguish between secure and
      non-secure connections received by a proxy, the proxy must use separate
      connectors to pass secure and non-secure requests to Tomcat. If the
      proxy uses AJP then the SSL attributes of the client connection are
      passed via the AJP protocol and separate connectors are not needed.</p>

      <p>The <strong>tomcatAuthentication</strong> and
      <strong>tomcatAuthorization</strong> attributes are used with the
      AJP connectors to determine if Tomcat should handle all authentication and
      authorisation or if authentication should be delegated to the reverse
      proxy (the authenticated user name is passed to Tomcat as part of the AJP
      protocol) with the option for Tomcat to still perform authorization.</p>

      <p>The <strong>requiredSecret</strong> attribute in AJP connectors
      configures shared secret between Tomcat and reverse proxy in front of
      Tomcat. It is used to prevent unauthorized connections over AJP protocol.</p>
    </div></div>

    <div class="subsection"><h4 id="Host">Host</h4><div class="text">
      <p>The host element controls deployment. Automatic deployment allows for
      simpler management but also makes it easier for an attacker to deploy a
      malicious application. Automatic deployment is controlled by the
      <strong>autoDeploy</strong> and <strong>deployOnStartup</strong>
      attributes. If both are <code>false</code>, only Contexts defined in
      server.xml will be deployed and any changes will require a Tomcat restart.
      </p>

      <p>In a hosted environment where web applications may not be trusted, set
      the <strong>deployXML</strong> attribute to <code>false</code> to ignore
      any context.xml packaged with the web application that may try to assign
      increased privileges to the web application. Note that if the security
      manager is enabled that the <strong>deployXML</strong> attribute will
      default to <code>false</code>.</p>
    </div></div>

    <div class="subsection"><h4 id="Context">Context</h4><div class="text">
      <p>This applies to <a href="config/context.html">Context</a>
      elements in all places where they can be defined:
      <code>server.xml</code> file,
      default <code>context.xml</code> file,
      per-host <code>context.xml.default</code> file,
      web application context file in per-host configuration directory
      or inside the web application.</p>

      <p>The <strong>crossContext</strong> attribute controls if a context is
      allowed to access the resources of another context. It is
      <code>false</code> by default and should only be changed for trusted web
      applications.</p>

      <p>The <strong>privileged</strong> attribute controls if a context is
      allowed to use container provided servlets like the Manager servlet. It is
      <code>false</code> by default and should only be changed for trusted web
      applications.</p>

      <p>The <strong>allowLinking</strong> attribute of a nested
      <a href="config/resources.html">Resources</a> element controls if a context
      is allowed to use linked files. If enabled and the context is undeployed,
      the links will be followed when deleting the context resources. Changing
      this setting from the default of <code>false</code> on case insensitive
      operating systems (this includes Windows) will disable a number of
      security measures and allow, among other things, direct access to the
      WEB-INF directory.</p>

      <p>The <strong>sessionCookiePathUsesTrailingSlash</strong> can be used to
      work around a bug in a number of browsers (Internet Explorer, Safari and
      Edge) to prevent session cookies being exposed across applications when
      applications share a common path prefix. However, enabling this option
      can create problems for applications with Servlets mapped to
      <code>/*</code>. It should also be noted the RFC6265 section 8.5 makes it
      clear that different paths should not be considered sufficient to isolate
      cookies from other applications.</p>

      <p>When <strong>antiResourceLocking</strong> is enabled, Tomcat will copy
      the unpacked web application to the directory defined by the
      <code>java.io.tmpdir</code> system property
      (<code>$CATALINA_BASE/temp</code> by default). This location should be
      secured with appropriate file permissions - typically read/write for the
      Tomcat user and no access for other users.</p>

      <p>When <strong>mapperContextRootRedirectEnabled</strong> and/or
      <strong>mapperDirectoryRedirectEnabled</strong> are enabled, request
      processing will be more efficient but there are security side effects.
      First, the existence of a web application or a directory may be confirmed
      even though the user does not have access to that directory. Secondly, any
      Valves and/or Filters - including those providing security functionality -
      will not have an opportunity to process the request.</p>

    </div></div>

    <div class="subsection"><h4 id="Valves">Valves</h4><div class="text">
      <p>It is strongly recommended that an AccessLogValve is configured. The
      default Tomcat configuration includes an AccessLogValve. These are
      normally configured per host but may also be configured per engine or per
      context as required.</p>

      <p>Any administrative application should be protected by a
      RemoteAddrValve (this Valve is also available as a Filter).
      The <strong>allow</strong> attribute should be used to limit access to a
      set of known trusted hosts.</p>

      <p>The default ErrorReportValve includes the Tomcat version number in the
      response sent to clients. To avoid this, custom error handling can be
      configured within each web application. Alternatively, you can explicitly
      configure an <a href="config/valve.html">ErrorReportValve</a> and set its
      <strong>showServerInfo</strong> attribute to <code>false</code>.
      Alternatively, the version number can be changed by creating the file
      CATALINA_BASE/lib/org/apache/catalina/util/ServerInfo.properties with
      content as follows:</p>
      <div class="codeBox"><pre><code>server.info=Apache Tomcat/10.1.x</code></pre></div>
      <p>Modify the values as required. Note that this will also change the version
      number reported in some of the management tools and may make it harder to
      determine the real version installed. The CATALINA_HOME/bin/version.bat|sh
      script will still report the correct version number.</p>

      <p>The default ErrorReportValve can display stack traces and/or JSP
      source code to clients when an error occurs. To avoid this, custom error
      handling can be configured within each web application. Alternatively, you
      can explicitly configure an <a href="config/valve.html">ErrorReportValve</a>
      and set its <strong>showReport</strong> attribute to <code>false</code>.</p>

      <p>The RewriteValve uses regular expressions and poorly formed regex
      patterns may be vulnerable to "catastrophic backtracking" or "ReDoS". See
      <a href="rewrite.html">Rewrite docs</a> for more details.</p>
    </div></div>

    <div class="subsection"><h4 id="Realms">Realms</h4><div class="text">
      <p>The MemoryRealm is not intended for production use as any changes to
      tomcat-users.xml require a restart of Tomcat to take effect.</p>

      <p>The UserDatabaseRealm is not intended for large-scale installations. It
      is intended for small-scale, relatively static environments.</p>

      <p>The JAASRealm is not widely used and therefore the code is not as
      mature as the other realms. Additional testing is recommended before using
      this realm.</p>

      <p>By default, the realms do not implement any form of account lock-out.
      This means that brute force attacks can be successful. To prevent a brute
      force attack, the chosen realm should be wrapped in a LockOutRealm.</p>
    </div></div>

    <div class="subsection"><h4 id="server.xml/Manager">Manager</h4><div class="text">
      <p>The manager component is used to generate session IDs.</p>

      <p>The class used to generate random session IDs may be changed with
      the <strong>randomClass</strong> attribute.</p>

      <p>The length of the session ID may be changed with the
      <strong>sessionIdLength</strong> attribute.</p>

      <p>The <strong>persistAuthentication</strong> controls whether the
      authenticated Principal associated with the session (if any) is included
      when the session is persisted during a restart or to a Store.</p>

      <p>When using the <strong>JDBCStore</strong>, the session store should be
      secured (dedicated credentials, appropriate permissions) such that only
      the <strong>JDBCStore</strong> is able to access the persisted session
      data. In particular, the <strong>JDBCStore</strong> should not be
      accessible via any credentials available to a web application.</p>
    </div></div>

    <div class="subsection"><h4 id="Cluster">Cluster</h4><div class="text">
      <p>The cluster implementation is written on the basis that a secure,
      trusted network is used for all of the cluster related network traffic. It
      is not safe to run a cluster on a insecure, untrusted network.</p>

      <p>If you require confidentiality and/or integrity protection then you can
      use the
      <a href="config/cluster-interceptor.html#org.apache.catalina.tribes.group.interceptors.EncryptInterceptor_Attributes">EncryptInterceptor</a>
      to encrypt traffic between nodes. This interceptor does not protect
      against all the risks of running on an untrusted network, particularly
      DoS attacks.</p>
    </div></div>
  </div><h3 id="web.xml">web.xml</h3><div class="text">
    <p>This applies to the default <code>conf/web.xml</code> file, the
    <code>/WEB-INF/tomcat-web.xml</code> and the <code>/WEB-INF/web.xml</code>
    files in web applications if they define the components mentioned here.</p>

    <p>The <a href="default-servlet.html">DefaultServlet</a> is configured
    with <strong>readonly</strong> set to
    <code>true</code>. Changing this to <code>false</code> allows clients to
    delete or modify static resources on the server and to upload new
    resources. This should not normally be changed without requiring
    authentication.</p>

    <p>The DefaultServlet is configured with <strong>listings</strong> set to
    <code>false</code>. This isn't because allowing directory listings is
    considered unsafe but because generating listings of directories with
    thousands of files can consume significant CPU leading to a DOS attack.
    </p>

    <p>The DefaultServlet is configured with <strong>showServerInfo</strong>
    set to <code>true</code>. When the directory listings is enabled the Tomcat
    version number is included in the response sent to clients. To avoid this,
    you can explicitly configure a DefaultServlet and set its
    <strong>showServerInfo</strong> attribute to false.
    Alternatively, the version number can be changed by creating the file
    CATALINA_BASE/lib/org/apache/catalina/util/ServerInfo.properties with
    content as follows:</p>
    <div class="codeBox"><pre><code>server.info=Apache Tomcat/10.1.x</code></pre></div>
    <p>Modify the values as required. Note that this will also change the version
    number reported in some of the management tools and may make it harder to
    determine the real version installed. The CATALINA_HOME/bin/version.bat|sh
    script will still report the correct version number.
    </p>

    <p>The CGI Servlet is disabled by default. If enabled, the debug
    initialisation parameter should not be set to <code>10</code> or higher on a
    production system because the debug page is not secure.</p>

    <p>When using the CGI Servlet on Windows with
    <code>enableCmdLineArguments</code> enabled, review the setting of
    <code>cmdLineArgumentsDecoded</code> carefully and ensure that it is
    appropriate for your environment. The default value is secure. Insecure
    configurations may expose the server to remote code execution. Further
    information on the potential risks and mitigations may be found by
    following the links in the <a href="cgi-howto.html">CGI How To</a>.</p>

    <p><a href="config/filter.html">FailedRequestFilter</a>
    can be configured and used to reject requests that had errors during
    request parameter parsing. Without the filter the default behaviour is
    to ignore invalid or excessive parameters.</p>

    <p><a href="config/filter.html">HttpHeaderSecurityFilter</a> can be
    used to add headers to responses to improve security. If clients access
    Tomcat directly, then you probably want to enable this filter and all the
    headers it sets unless your application is already setting them. If Tomcat
    is accessed via a reverse proxy, then the configuration of this filter needs
    to be co-ordinated with any headers that the reverse proxy sets.</p>

    <p>The WebDAV servlet enables edit functionality for web application
    content. If the WebDAV servlet is enabled, the WebDAV functionality should
    be appropriately secured.</p>

    <p>When configuring security constraints, care should be taken if the URL
    pattern for one or more constraints covers any segment of the URL that
    becomes part of the pathInfo for a servlet and the servlet uses the pathInfo
    to identify some other resource (like the default servlet does). In those
    circumstances, correct application of the security constraint depends on the
    implementation of the Servlet. All servlets included with Tomcat will behave
    correctly in this scenario.</p>
  </div><h3 id="Embedded_Tomcat">Embedded Tomcat</h3><div class="text">
    <p>When using embedded Tomcat, the typical defaults provided by the scripts,
    server.xml and other configuration are not set. Users of embedded Tomcat may
    wish to consider the following:</p>
    <ul>
      <li>The listeners normally configured in server.xml, including
      <code>org.apache.catalina.security.SecurityListener</code>, will not be
      configured by default. They must be explicitly enabled if required.</li>
      <li>The <code>java.io.tmpdir</code> will not be set (it is normally set to
      <code>$CATALINA_BASE/temp</code>). This directory is used for various
      temporary files that may be security sensitive including file uploads and
      a copy of the web application if anti-resource locking is enabled.
      Consider setting the <code>java.io.tmpdir</code> system property to an
      appropriately secured directory.</li>
    </ul>
  </div><h3 id="General">General</h3><div class="text">
    <p>BASIC and FORM authentication pass user names and passwords in clear
    text. Web applications using these authentication mechanisms with clients
    connecting over untrusted networks should use SSL.</p>

    <p>The session cookie for a session with an authenticated user is nearly as
    useful as the user's password to an attacker and should be afforded the same
    level of protection as the password itself. This usually means
    authenticating over SSL and continuing to use SSL until the session
    ends.</p>

    <p>Tomcat's implementation of the Servlet API's file upload support may use
    the directory defined by the <code>java.io.tmpdir</code> system property
    (<code>$CATALINA_BASE/temp</code> by default) to store temporary files. This
    location should be secured with appropriate file permissions - typically
    read/write for the Tomcat user and no access for other users.</p>
  </div></div></div></div></div><footer><div id="footer">
    Copyright &copy; 1999-2025, The Apache Software Foundation
    <br>
    Apache Tomcat, Tomcat, Apache, the Apache Tomcat logo and the Apache logo
    are either registered trademarks or trademarks of the Apache Software
    Foundation.
    </div></footer></div></body></html>