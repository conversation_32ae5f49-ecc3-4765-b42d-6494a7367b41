# Script pour corriger les taglibs JSTL dans tous les fichiers JSP
# Conversion de Java EE vers Jakarta EE

Write-Host "🔧 Correction des taglibs JSTL pour Jakarta EE..." -ForegroundColor Green

# Rechercher tous les fichiers JSP
$jspFiles = Get-ChildItem -Path "src\main\webapp" -Filter "*.jsp" -Recurse

$totalFiles = $jspFiles.Count
$correctedFiles = 0

Write-Host "📁 Trouvé $totalFiles fichiers JSP à vérifier..." -ForegroundColor Yellow

foreach ($file in $jspFiles) {
    $content = Get-Content $file.FullName -Raw -Encoding UTF8
    $originalContent = $content

    # Remplacer les anciennes taglibs par les nouvelles
    $content = $content -replace 'http://java\.sun\.com/jsp/jstl/core', 'jakarta.tags.core'
    $content = $content -replace 'http://java\.sun\.com/jsp/jstl/fmt', 'jakarta.tags.fmt'
    $content = $content -replace 'http://java\.sun\.com/jsp/jstl/functions', 'jakarta.tags.functions'

    # Si le contenu a changé, sauvegarder le fichier
    if ($content -ne $originalContent) {
        Set-Content -Path $file.FullName -Value $content -Encoding UTF8
        Write-Host "✅ Corrigé: $($file.Name)" -ForegroundColor Green
        $correctedFiles++
    } else {
        Write-Host "⏭️  Ignoré: $($file.Name) (pas de taglib à corriger)" -ForegroundColor Gray
    }
}

Write-Host ""
Write-Host "🎯 Résumé:" -ForegroundColor Cyan
Write-Host "   📁 Fichiers vérifiés: $totalFiles" -ForegroundColor White
Write-Host "   ✅ Fichiers corrigés: $correctedFiles" -ForegroundColor Green
Write-Host "   ⏭️  Fichiers ignorés: $($totalFiles - $correctedFiles)" -ForegroundColor Gray

if ($correctedFiles -gt 0) {
    Write-Host ""
    Write-Host "Correction terminee ! Recompilation necessaire..." -ForegroundColor Green
} else {
    Write-Host ""
    Write-Host "Aucune correction necessaire." -ForegroundColor Blue
}
